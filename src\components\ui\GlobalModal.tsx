"use client";

import React from "react";
import { useModal } from "@/store/compatibility";
import { Dialog, DialogContent } from "./dialog";

export function GlobalModal() {
  const { isOpen, modalContent, modalProps, closeModal } = useModal();

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && closeModal()}>
      <DialogContent showCloseButton={true} {...modalProps}>
        {modalContent}
      </DialogContent>
    </Dialog>
  );
}
