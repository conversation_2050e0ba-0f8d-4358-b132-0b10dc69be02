// Test utility for slug generation and extraction
import { generateSlug, extractIdFromSlug } from "@/utils/slug-utils";

// Test cases for slug generation and extraction
export const testSlugUtils = () => {
  console.log("🧪 Testing Slug Utilities");
  
  // Test cases with different ID formats
  const testCases = [
    {
      title: "iPhone 14 Pro Max",
      id: "123",
      description: "Simple numeric ID"
    },
    {
      title: "iPhone 14 Pro Max",
      id: "abc123",
      description: "Alphanumeric ID"
    },
    {
      title: "iPhone 14 Pro Max",
      id: "550e8400-e29b-41d4-a716-************",
      description: "UUID"
    },
    {
      title: "MacBook Air M2",
      id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
      description: "Another UUID"
    },
    {
      title: "Gaming Chair RGB",
      id: "chair-001",
      description: "ID with hyphen"
    }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`\n--- Test Case ${index + 1}: ${testCase.description} ---`);
    console.log(`Title: "${testCase.title}"`);
    console.log(`ID: "${testCase.id}"`);
    
    // Generate slug
    const slug = generateSlug(testCase.title, testCase.id);
    console.log(`Generated Slug: "${slug}"`);
    
    // Extract ID back
    const extractedId = extractIdFromSlug(slug);
    console.log(`Extracted ID: "${extractedId}"`);
    
    // Check if extraction was successful
    const success = extractedId === testCase.id;
    console.log(`✅ Success: ${success ? 'PASS' : 'FAIL'}`);
    
    if (!success) {
      console.log(`❌ Expected: "${testCase.id}", Got: "${extractedId}"`);
    }
  });

  // Test edge cases
  console.log("\n--- Edge Cases ---");
  
  const edgeCases = [
    { slug: "", expected: null, description: "Empty slug" },
    { slug: "just-a-title", expected: null, description: "No ID in slug" },
    { slug: "title-with-123", expected: "123", description: "Simple ID" },
    { slug: "complex-title-with-many-words-550e8400-e29b-41d4-a716-************", expected: "550e8400-e29b-41d4-a716-************", description: "UUID at end" },
    { slug: "title-550e8400-e29b-41d4-a716-************-extra", expected: null, description: "UUID not at end" },
  ];

  edgeCases.forEach((testCase, index) => {
    console.log(`\nEdge Case ${index + 1}: ${testCase.description}`);
    console.log(`Slug: "${testCase.slug}"`);
    
    const result = extractIdFromSlug(testCase.slug);
    console.log(`Result: ${result}`);
    console.log(`Expected: ${testCase.expected}`);
    
    const success = result === testCase.expected;
    console.log(`✅ Success: ${success ? 'PASS' : 'FAIL'}`);
  });

  console.log("\n🎉 Slug utility tests completed!");
};

// Test with real advertisement data format
export const testWithAdvertisementData = (advertisements: any[]) => {
  console.log("\n🧪 Testing with Real Advertisement Data");
  
  advertisements.slice(0, 3).forEach((ad, index) => {
    console.log(`\n--- Advertisement ${index + 1} ---`);
    console.log(`Title: "${ad.title}"`);
    console.log(`ID: "${ad.id}"`);
    
    const slug = generateSlug(ad.title, ad.id);
    console.log(`Generated Slug: "${slug}"`);
    
    const extractedId = extractIdFromSlug(slug);
    console.log(`Extracted ID: "${extractedId}"`);
    
    const success = extractedId === ad.id;
    console.log(`✅ Success: ${success ? 'PASS' : 'FAIL'}`);
    
    if (!success) {
      console.log(`❌ Expected: "${ad.id}", Got: "${extractedId}"`);
    }
  });
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).testSlugUtils = testSlugUtils;
  (window as any).testWithAdvertisementData = testWithAdvertisementData;
}
