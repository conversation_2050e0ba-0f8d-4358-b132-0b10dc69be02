"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { FormField } from "./shared/FormField";
import { SkillsManager } from "./shared/SkillsManager";

interface EditJobProfileModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditJobProfileModal({
  open,
  onOpenChange,
}: EditJobProfileModalProps) {
  const [formData, setFormData] = useState({
    fullName: "Ram Shrestha",
    email: "<EMAIL>",
    mobileNumber: "9876543210",
    address: "Madhyapur Thimi",
    professionalBio: "",
  });

  const [cvFile, setCvFile] = useState<File | null>(null);
  const [coverLetterFile, setCoverLetterFile] = useState<File | null>(null);

  // Skills & Expertise state
  const [technicalSkills, setTechnicalSkills] = useState<string[]>([
    "React.js",
    "Node.js",
    "TypeScript",
    "Next.js",
    "MongoDB",
    "AWS",
  ]);
  const [softSkills, setSoftSkills] = useState<string[]>([
    "Team Leadership",
    "Problem Solving",
    "Communication",
    "Project Management",
  ]);
  const [experienceLevel, setExperienceLevel] = useState<string[]>([
    "5+ Years Experience",
    "Senior Developer",
  ]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleFileChange = (type: "cv" | "coverLetter", file: File | null) => {
    if (type === "cv") {
      setCvFile(file);
    } else {
      setCoverLetterFile(file);
    }
  };

  const handleSave = () => {
    // Handle save logic here
    console.log("Saving profile data:", formData);
    console.log("CV File:", cvFile);
    console.log("Cover Letter File:", coverLetterFile);
    console.log("Technical Skills:", technicalSkills);
    console.log("Soft Skills:", softSkills);
    console.log("Experience Level:", experienceLevel);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] p-0 flex flex-col">
        <DialogHeader className="px-6 py-4 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-lg font-semibold">
                Edit Job Profile
              </DialogTitle>
              <p className="text-sm text-gray-500 mt-1">
                Update your professional details for job applications.
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
              className="h-6 w-6 p-0"
            ></Button>
          </div>
        </DialogHeader>

        <div className="px-6 py-4 space-y-4 overflow-y-auto flex-1">
          <FormField
            id="fullName"
            label="Full Name"
            value={formData.fullName}
            onChange={(value) => handleInputChange("fullName", value)}
            placeholder="Ram Shrestha"
          />

          <FormField
            id="email"
            label="Email"
            type="email"
            value={formData.email}
            onChange={(value) => handleInputChange("email", value)}
            placeholder="<EMAIL>"
          />

          <FormField
            id="mobileNumber"
            label="Phone"
            value={formData.mobileNumber}
            onChange={(value) => handleInputChange("mobileNumber", value)}
            placeholder="9876543210"
          />

          <FormField
            id="address"
            label="Address"
            value={formData.address}
            onChange={(value) => handleInputChange("address", value)}
            placeholder="Madhyapur Thimi"
          />

          <FormField
            id="professionalBio"
            label="Professional Bio"
            type="textarea"
            value={formData.professionalBio}
            onChange={(value) => handleInputChange("professionalBio", value)}
            placeholder="About Yourself"
            rows={3}
          />

          {/* Upload CV */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Upload CV</Label>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => document.getElementById("cv-upload")?.click()}
                className="bg-gray-100"
              >
                Choose File
              </Button>
              <span className="text-sm text-gray-500">
                {cvFile ? cvFile.name : "No File Chosen"}
              </span>
              <input
                id="cv-upload"
                type="file"
                accept=".pdf,.doc,.docx"
                className="hidden"
                onChange={(e) =>
                  handleFileChange("cv", e.target.files?.[0] || null)
                }
              />
            </div>
          </div>

          {/* Upload Cover Letter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Upload Cover Letter</Label>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  document.getElementById("cover-letter-upload")?.click()
                }
                className="bg-gray-100"
              >
                Choose File
              </Button>
              <span className="text-sm text-gray-500">
                {coverLetterFile ? coverLetterFile.name : "No File Chosen"}
              </span>
              <input
                id="cover-letter-upload"
                type="file"
                accept=".pdf,.doc,.docx"
                className="hidden"
                onChange={(e) =>
                  handleFileChange("coverLetter", e.target.files?.[0] || null)
                }
              />
            </div>
          </div>

          {/* Skills & Expertise Section */}
          <div className="space-y-4 pt-4 border-t border-gray-200">
            <h3 className="text-lg font-semibold text-gray-700">
              Skills & Expertise
            </h3>

            <SkillsManager
              label="Technical Skills"
              skills={technicalSkills}
              onSkillsChange={setTechnicalSkills}
              placeholder="Add technical skill"
              badgeClassName="bg-blue-50 text-blue-700 border-blue-200"
            />

            <SkillsManager
              label="Soft Skills"
              skills={softSkills}
              onSkillsChange={setSoftSkills}
              placeholder="Add soft skill"
              badgeClassName="bg-green-50 text-green-700 border-green-200"
            />

            <SkillsManager
              label="Experience Level"
              skills={experienceLevel}
              onSkillsChange={setExperienceLevel}
              placeholder="Add experience level"
              badgeClassName="bg-purple-50 text-purple-700 border-purple-200"
            />
          </div>
        </div>

        {/* Footer Buttons */}
        <div className="px-6 py-4 flex justify-end gap-2 border-t border-gray-200 flex-shrink-0">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            className="bg-teal-600 hover:bg-teal-700"
          >
            Save
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
