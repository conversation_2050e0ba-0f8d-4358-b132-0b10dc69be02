"use client";
import Link from "next/link";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";

export function BalanceSection() {
  return (
    <div className="hidden lg:flex items-center space-x-2 lg:space-x-3">
      {/* Balance Display */}
      <div className="flex items-center bg-[#1F5E64] hover:bg-[#1a5157] backdrop-blur-md border border-white rounded-xl px-3 lg:px-4 py-1.5 lg:py-2 whitespace-nowrap">
        <Icon
          icon="tdesign:money-filled"
          className="w-3 h-3 lg:w-4 lg:h-4 text-white mr-1 lg:mr-2 flex-shrink-0"
        />
        <span className="text-white text-xs lg:text-sm font-medium whitespace-nowrap">
          Balance: Rs. 123456
        </span>
      </div>

      {/* Top Up Button */}
      <Link href="/top-up">
        <Button className="bg-[#1F5E64] hover:bg-[#1a5157] text-white rounded-xl px-3 lg:px-4 py-1.5 lg:py-2 text-xs lg:text-sm font-medium transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 flex items-center space-x-1 lg:space-x-2 border border-white">
          <Icon
            icon="material-symbols:add-card"
            className="w-3 h-3 lg:w-4 lg:h-4"
          />
          <span>Top Up</span>
        </Button>
      </Link>
    </div>
  );
}
