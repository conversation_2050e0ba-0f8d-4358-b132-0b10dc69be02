/**
 * Utility functions for profile components
 */

/**
 * Format a date string to a human-readable relative format
 */
export function formatDate(date: string): string {
  const now = new Date();
  const targetDate = new Date(date);
  const diffInDays = Math.floor(
    (now.getTime() - targetDate.getTime()) / (1000 * 60 * 60 * 24)
  );

  if (diffInDays === 0) return "Today";
  if (diffInDays === 1) return "Yesterday";
  if (diffInDays < 7) return `${diffInDays} days ago`;
  return targetDate.toLocaleDateString();
}

/**
 * Format a date string to a simple date format
 */
export function formatSimpleDate(date: string): string {
  return new Date(date).toLocaleDateString();
}

/**
 * Extract numeric value from a price string (e.g., "₹. 1000" -> "1000")
 */
export function extractNumericPrice(price: string): string {
  return price.replace(/[^\d]/g, "");
}

/**
 * Format a numeric value to a price string with currency symbol
 */
export function formatProfilePrice(
  amount: string | number,
  currency: string = "₹."
): string {
  return `${currency} ${amount}`;
}
