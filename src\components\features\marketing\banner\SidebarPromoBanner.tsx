"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react";

interface SidebarPromoBannerProps {
  className?: string;
}

export default function SidebarPromoBanner({
  className = "",
}: SidebarPromoBannerProps) {
  return (
    <div
      className={`relative w-full aspect-[4/5] overflow-hidden bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 rounded-3xl shadow-2xl ${className}`}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-black/10">
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"></div>
        <div className="absolute top-0 right-0 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-purple-300/10 rounded-full blur-2xl"></div>
      </div>

      <div className="relative h-full flex flex-col justify-between p-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex items-center justify-center space-x-2 text-yellow-300 mb-4">
            <Icon icon="lucide:zap" className="w-4 h-4" />
            <span className="text-xs font-semibold uppercase tracking-wider">
              Business Growth
            </span>
          </div>

          <h1 className="text-3xl font-bold text-white leading-tight mb-3">
            Ready to{" "}
            <span className="bg-gradient-to-r from-yellow-300 to-orange-400 bg-clip-text text-transparent">
              Boost
            </span>
            <br />
            Your Company?
          </h1>

          <p className="text-blue-100 text-sm leading-relaxed">
            Transform your business with our proven growth strategies and expert
            solutions
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 gap-3 my-6">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 text-center">
            <div className="flex items-center justify-center mb-2">
              <Icon
                icon="lucide:trending-up"
                className="w-5 h-5 text-green-400"
              />
            </div>
            <div className="text-2xl font-bold text-white">500+</div>
            <div className="text-xs text-blue-200">Happy Clients</div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 text-center">
            <div className="flex items-center justify-center mb-2">
              <Icon icon="lucide:star" className="w-5 h-5 text-yellow-400" />
            </div>
            <div className="text-2xl font-bold text-white">95%</div>
            <div className="text-xs text-blue-200">Success Rate</div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center space-y-4">
          <Button
            size="lg"
            className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-black font-bold text-base px-6 py-4 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
          >
            Contact Us Now
            <Icon icon="lucide:arrow-right" className="ml-2 w-4 h-4" />
          </Button>

          <div className="text-center">
            <div className="text-white text-sm font-semibold">
              Get Your Free Consultation
            </div>
            <div className="text-blue-200 text-xs mt-1">📞 1-800-BOOST-CO</div>
          </div>
        </div>

        {/* Bottom Badge */}
        <div className="absolute bottom-4 left-4 right-4">
          <div className="bg-black/30 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
            <div className="text-center">
              <div className="text-yellow-300 text-xs font-semibold">
                ⚡ 24/7 Support Available
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-6 right-6 w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full opacity-20 animate-pulse"></div>
      <div className="absolute bottom-20 left-6 w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full opacity-15 animate-pulse delay-1000"></div>
    </div>
  );
}
