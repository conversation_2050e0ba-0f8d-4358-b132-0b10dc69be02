"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Icon } from "@iconify/react";
import { BackButton } from "@/components/ui";

type PaymentMethod = "credit-card" | "paypal" | "local-wallet";

interface FormData {
  amount: string;
  paymentMethod: PaymentMethod;
  paypalEmail: string;
  cardNumber: string;
  expiryDate: string;
  securityCode: string;
  localWalletId: string;
}

export default function TopUpBalance() {
  const router = useRouter();
  const [formData, setFormData] = useState<FormData>({
    amount: "",
    paymentMethod: "credit-card",
    paypalEmail: "",
    cardNumber: "",
    expiryDate: "",
    securityCode: "",
    localWalletId: "",
  });

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handlePaymentMethodChange = (method: PaymentMethod) => {
    setFormData((prev) => ({
      ...prev,
      paymentMethod: method,
    }));
  };

  const handleSubmit = () => {
    console.log("Form submitted:", formData);
    // Handle form submission logic here
  };

  const PaymentMethodButton = ({
    method,
    label,
    isSelected,
  }: {
    method: PaymentMethod;
    label: string;
    isSelected: boolean;
  }) => (
    <button
      type="button"
      onClick={() => handlePaymentMethodChange(method)}
      className={`px-3 sm:px-4 py-2 sm:py-3 rounded-md border text-sm sm:text-base font-medium transition-colors ${
        isSelected
          ? "border-[#356267] bg-[#356267]/10 text-[#356267]"
          : "border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
      }`}
    >
      {label}
    </button>
  );

  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <div className="container-responsive-sm py-4 sm:py-6 lg:py-8">
        {/* Back button - responsive positioning */}
        <div className="mb-4 sm:mb-6">
          <BackButton onClick={() => router.back()} size="lg" />
        </div>

        <div className="max-w-4xl mx-auto">
          {/* Page Header */}
          <div className="mb-6 sm:mb-8">
            <div className="flex items-center mb-3 sm:mb-4">
              <Icon
                icon="material-symbols:add-card"
                className="w-6 h-6 sm:w-8 sm:h-8 text-[#356267] mr-2 sm:mr-3"
              />
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">
                Top Up Balance
              </h1>
            </div>
            <p className="text-sm sm:text-base text-gray-600">
              Add funds to your account using your preferred payment method.
            </p>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-4 sm:p-6 lg:p-8 shadow-sm">
            {/* Current Balance Display */}
            <div className="mb-6 sm:mb-8 p-3 sm:p-4 bg-[#356267]/5 rounded-lg border border-[#356267]/20">
              <div className="flex items-center justify-between flex-wrap gap-2">
                <div className="flex items-center">
                  <Icon
                    icon="tdesign:money-filled"
                    className="w-5 h-5 sm:w-6 sm:h-6 text-[#356267] mr-2 sm:mr-3"
                  />
                  <span className="text-base sm:text-lg font-semibold text-gray-900">
                    Current Balance
                  </span>
                </div>
                <span className="text-xl sm:text-2xl font-bold text-[#356267]">
                  Rs 2,500.75
                </span>
              </div>
            </div>

            {/* Amount Field */}
            <div className="mb-6 sm:mb-8">
              <Label
                htmlFor="amount"
                className="block text-base sm:text-lg font-semibold text-gray-900 mb-2 sm:mb-3"
              >
                Top Up Amount
              </Label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium text-sm sm:text-base">
                  Rs
                </span>
                <Input
                  id="amount"
                  type="number"
                  placeholder="Enter amount (e.g., 10000)"
                  value={formData.amount}
                  onChange={(e) => handleInputChange("amount", e.target.value)}
                  className="w-full pl-10 sm:pl-12 py-2 sm:py-3 text-base sm:text-lg border-2 border-gray-200 focus:border-[#356267] rounded-lg"
                />
              </div>
              {/* Quick Amount Buttons */}
              <div className="grid grid-cols-2 sm:flex gap-2 mt-3">
                {[1000, 5000, 10000, 25000].map((amount) => (
                  <button
                    key={amount}
                    type="button"
                    onClick={() =>
                      handleInputChange("amount", amount.toString())
                    }
                    className="px-3 sm:px-4 py-2 text-xs sm:text-sm bg-gray-100 hover:bg-[#356267] hover:text-white rounded-lg transition-colors"
                  >
                    Rs {amount.toLocaleString()}
                  </button>
                ))}
              </div>
            </div>

            {/* Payment Method Selection */}
            <div className="mb-6 sm:mb-8">
              <Label className="block text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">
                Payment Method
              </Label>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
                <PaymentMethodButton
                  method="credit-card"
                  label="Credit Card"
                  isSelected={formData.paymentMethod === "credit-card"}
                />
                <PaymentMethodButton
                  method="paypal"
                  label="PayPal"
                  isSelected={formData.paymentMethod === "paypal"}
                />
                <PaymentMethodButton
                  method="local-wallet"
                  label="Local Wallet"
                  isSelected={formData.paymentMethod === "local-wallet"}
                />
              </div>
            </div>

            {/* Conditional Fields Based on Payment Method */}
            {formData.paymentMethod === "paypal" && (
              <div className="mb-6 sm:mb-8 p-4 sm:p-6 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center mb-3 sm:mb-4">
                  <Icon
                    icon="logos:paypal"
                    className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3"
                  />
                  <h3 className="text-base sm:text-lg font-semibold text-gray-900">
                    PayPal Payment
                  </h3>
                </div>
                <Label
                  htmlFor="paypal-email"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  PayPal Email Address
                </Label>
                <Input
                  id="paypal-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.paypalEmail}
                  onChange={(e) =>
                    handleInputChange("paypalEmail", e.target.value)
                  }
                  className="w-full py-2 sm:py-3 border-2 border-gray-200 focus:border-blue-500 rounded-lg"
                />
              </div>
            )}

            {formData.paymentMethod === "credit-card" && (
              <div className="mb-6 sm:mb-8 p-4 sm:p-6 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-center mb-3 sm:mb-4">
                  <Icon
                    icon="material-symbols:credit-card"
                    className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-green-600"
                  />
                  <h3 className="text-base sm:text-lg font-semibold text-gray-900">
                    Credit Card Payment
                  </h3>
                </div>
                <div className="space-y-3 sm:space-y-4">
                  <div>
                    <Label
                      htmlFor="card-number"
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      Card Number
                    </Label>
                    <Input
                      id="card-number"
                      type="text"
                      placeholder="1234 5678 9012 3456"
                      value={formData.cardNumber}
                      onChange={(e) =>
                        handleInputChange("cardNumber", e.target.value)
                      }
                      className="w-full py-2 sm:py-3 border-2 border-gray-200 focus:border-green-500 rounded-lg"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-3 sm:gap-4">
                    <div>
                      <Label
                        htmlFor="expiry-date"
                        className="block text-sm font-medium text-gray-700 mb-2"
                      >
                        Expiry Date
                      </Label>
                      <Input
                        id="expiry-date"
                        type="text"
                        placeholder="MM/YY"
                        value={formData.expiryDate}
                        onChange={(e) =>
                          handleInputChange("expiryDate", e.target.value)
                        }
                        className="w-full py-2 sm:py-3 border-2 border-gray-200 focus:border-green-500 rounded-lg"
                      />
                    </div>
                    <div>
                      <Label
                        htmlFor="security-code"
                        className="block text-sm font-medium text-gray-700 mb-2"
                      >
                        Security Code
                      </Label>
                      <Input
                        id="security-code"
                        type="text"
                        placeholder="CVC"
                        value={formData.securityCode}
                        onChange={(e) =>
                          handleInputChange("securityCode", e.target.value)
                        }
                        className="w-full py-2 sm:py-3 border-2 border-gray-200 focus:border-green-500 rounded-lg"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {formData.paymentMethod === "local-wallet" && (
              <div className="mb-6 sm:mb-8 p-4 sm:p-6 bg-purple-50 rounded-lg border border-purple-200">
                <div className="flex items-center mb-3 sm:mb-4">
                  <Icon
                    icon="material-symbols:account-balance-wallet"
                    className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-purple-600"
                  />
                  <h3 className="text-base sm:text-lg font-semibold text-gray-900">
                    Local Wallet Payment
                  </h3>
                </div>
                <Label
                  htmlFor="local-wallet-id"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Local Wallet ID
                </Label>
                <Input
                  id="local-wallet-id"
                  type="text"
                  placeholder="Enter your Local Wallet ID"
                  value={formData.localWalletId}
                  onChange={(e) =>
                    handleInputChange("localWalletId", e.target.value)
                  }
                  className="w-full py-2 sm:py-3 border-2 border-gray-200 focus:border-purple-500 rounded-lg"
                />
              </div>
            )}

            {/* Submit Button */}
            <Button
              onClick={handleSubmit}
              disabled={!formData.amount}
              className="w-full bg-[#356267] hover:bg-[#2d5459] text-white font-semibold py-3 sm:py-4 text-base sm:text-lg rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-[1.02]"
            >
              <Icon
                icon="material-symbols:add-card"
                className="w-4 h-4 sm:w-5 sm:h-5 mr-2"
              />
              <span className="hidden sm:inline">Proceed To Top Up</span>
              <span className="sm:hidden">Top Up</span>
              {formData.amount && (
                <span className="ml-1">
                  Rs {parseInt(formData.amount).toLocaleString()}
                </span>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
