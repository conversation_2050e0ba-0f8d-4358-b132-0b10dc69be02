import type { Transaction } from "../types";

const ITEMS = [
  "iPhone 14 Pro Max 256GB",
  "Samsung Galaxy S23 Ultra",
  "MacBook Pro M2",
  "iPad Air 5th Gen",
  "AirPods Pro 2nd Gen",
  "Sony WH-1000XM4",
  "Dell XPS 13",
  "Nintendo Switch OLED",
  "PlayStation 5",
  "Xbox Series X",
  "Apple Watch Series 8",
  "Canon EOS R5",
  "Tesla Model 3",
  "Gaming Chair",
  "Mechanical Keyboard",
];

const PEOPLE = [
  "<PERSON> Sharma",
  "<PERSON><PERSON>",
  "<PERSON>ha<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON><PERSON> Joshi",
  "Amit Rai",
  "<PERSON>ita <PERSON>",
  "<PERSON><PERSON><PERSON> Shrestha",
  "<PERSON> Magar",
];

// Simple seeded random number generator for consistent results
const seededRandom = (seed: number) => {
  const x = Math.sin(seed) * 10000;
  return x - Math.floor(x);
};

export const generateMockTransactions = (): Transaction[] => {
  const transactions: Transaction[] = [];

  for (let i = 1; i <= 31; i++) {
    // Use the index as seed for consistent results
    const seed = i * 12345; // Multiply by a large number for better distribution

    const item = ITEMS[Math.floor(seededRandom(seed) * ITEMS.length)];
    const person = PEOPLE[Math.floor(seededRandom(seed + 1) * PEOPLE.length)];
    const type = seededRandom(seed + 2) > 0.5 ? "Sold" : "Bought";
    const status = seededRandom(seed + 3) > 0.3 ? "Completed" : "Pending";
    const price = `Rs ${(
      seededRandom(seed + 4) * 200000 +
      10000
    ).toLocaleString()}`;
    const rating = Math.floor(seededRandom(seed + 5) * 5) + 1;

    transactions.push({
      id: i.toString(),
      item,
      image: "/placeholder.svg?height=40&width=40&text=" + item.split(" ")[0],
      type,
      price,
      person,
      role: type === "Sold" ? "Buyer" : "Seller",
      date: `2024-01-${String(
        Math.floor(seededRandom(seed + 6) * 28) + 1
      ).padStart(2, "0")}`,
      status,
      rating,
    });
  }

  return transactions;
};

export const filterTransactions = (
  transactions: Transaction[],
  searchTerm: string,
  statusFilter: string
): Transaction[] => {
  return transactions.filter((transaction) => {
    const matchesSearch =
      transaction.item.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.person.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === "all" ||
      transaction.status.toLowerCase() === statusFilter.toLowerCase();

    return matchesSearch && matchesStatus;
  });
};

export const paginateTransactions = (
  transactions: Transaction[],
  currentPage: number,
  itemsPerPage: number
): Transaction[] => {
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  return transactions.slice(startIndex, endIndex);
};
