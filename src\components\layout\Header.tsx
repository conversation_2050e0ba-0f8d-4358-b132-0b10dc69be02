"use client";
import Link from "next/link";
import { Icon } from "@iconify/react";
// Redux imports - replacing context API
import { useAppSelector } from "@/store/hooks";
import { selectCartItems, selectCartTotalItems } from "@/store/selectors";
import { useUser } from "@/store/compatibility";
import { Logo } from "./header/Logo";
import { SearchBar } from "./header/SearchBar";
import { NavigationActions } from "./header/NavigationActions";
import { MobileNavigationDrawer } from "./header/MobileNavigationDrawer";
import { CategoriesSection } from "./CategoriesSection";
import { ClientOnlyWrapper } from "./ClientOnlyWrapper";

export default function Header() {
  // Redux selectors - replace context usage
  const cartItems = useAppSelector(selectCartItems);
  const cartTotalItems = useAppSelector(selectCartTotalItems);
  const { isAuthenticated, currentUser, logout } = useUser();

  return (
    <>
      {/* Sticky Header Container */}
      <div className="sticky top-0 z-50 w-full mb-2 sm:mb-3 md:mb-4 shadow-lg">
        {/* Main Header */}
        <header className="bg-[#478085] border-b border-white/10">
          {/* Desktop Header */}
          <div className="hidden md:flex items-center justify-between h-14 sm:h-16 lg:h-18 xl:h-20">
            {/* Logo - Aligned to left margin */}
            <div className="ml-[3%] sm:ml-[4%] md:ml-[5%]">
              <Logo />
            </div>

            {/* Search Section - Centered */}
            <div className="flex-1 mx-6 lg:mx-8 xl:mx-12 min-w-0">
              <SearchBar />
            </div>

            {/* Navigation Actions - Aligned to right margin */}
            <div className="mr-[3%] sm:mr-[4%] md:mr-[5%]">
              <ClientOnlyWrapper
                fallback={
                  <NavigationActions
                    isAuthenticated={false}
                    currentUser={null}
                    cartTotalItems={0}
                    onLogout={logout}
                  />
                }
              >
                <NavigationActions
                  isAuthenticated={isAuthenticated}
                  currentUser={currentUser}
                  cartTotalItems={cartTotalItems}
                  onLogout={logout}
                />
              </ClientOnlyWrapper>
            </div>
          </div>

          {/* Tablet Header (768px - 1024px) */}
          <div className="hidden sm:flex md:hidden items-center justify-between h-14 sm:h-16 px-4 sm:px-6">
            {/* Logo */}
            <Logo />

            {/* Search Bar - Wider on tablet */}
            <div className="flex-1 ml-8 mr-3 max-w-md">
              <div className="relative">
                <Icon
                  icon="material-symbols:search"
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-5 h-5 z-10"
                />
                <input
                  type="search"
                  placeholder="Search products..."
                  className="pl-11 pr-4 py-2.5 w-full bg-gray-50  rounded-xl focus:bg-white focus:ring-2 focus:ring-black/20 transition-all duration-300 text-lg sm:text-xl font-medium"
                  style={{
                    fontFamily:
                      "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
                  }}
                />
              </div>
            </div>

            {/* Tablet Actions */}
            <div className="flex items-center space-x-3">
              {/* Cart */}
              <Link href="/cart">
                <button className="relative text-white hover:text-gray-200 p-2 rounded-full hover:bg-white/10 transition-all duration-200 min-w-[44px] min-h-[44px] flex items-center justify-center">
                  <Icon
                    icon="material-symbols:shopping-cart"
                    className="w-6 h-6"
                  />
                  <ClientOnlyWrapper>
                    {cartTotalItems > 0 && (
                      <span
                        className="absolute -top-1 -right-1 bg-red-500 text-white text-sm rounded-full w-6 h-6 flex items-center justify-center font-bold"
                        style={{
                          fontFamily:
                            "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
                        }}
                      >
                        {cartTotalItems > 9 ? "9+" : cartTotalItems}
                      </span>
                    )}
                  </ClientOnlyWrapper>
                </button>
              </Link>

              {/* Profile/Auth */}
              <ClientOnlyWrapper
                fallback={
                  <Link href="/login">
                    <button
                      className="bg-white/10 hover:bg-white/20 text-white px-6 py-3 rounded-full transition-all duration-200 text-lg font-bold"
                      style={{
                        fontFamily:
                          "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
                      }}
                    >
                      Sign In
                    </button>
                  </Link>
                }
              >
                {isAuthenticated ? (
                  <button className="text-white hover:text-gray-200 p-2 rounded-full hover:bg-white/10 transition-all duration-200 min-w-[44px] min-h-[44px] flex items-center justify-center">
                    <Icon icon="material-symbols:person" className="w-6 h-6" />
                  </button>
                ) : (
                  <Link href="/login">
                    <button
                      className="bg-white/10 hover:bg-white/20 text-white px-6 py-3 rounded-full transition-all duration-200 text-lg font-bold"
                      style={{
                        fontFamily:
                          "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
                      }}
                    >
                      Sign In
                    </button>
                  </Link>
                )}
              </ClientOnlyWrapper>
            </div>
          </div>

          {/* Mobile Header */}
          <div className="sm:hidden flex items-center justify-between h-14 px-3">
            {/* Logo/Menu Button */}
            <Logo />

            {/* Search Bar */}
            <div className="flex-1 mx-2 min-w-0">
              <div className="relative">
                <Icon
                  icon="material-symbols:search"
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-4 h-4 z-10"
                />
                <input
                  type="search"
                  placeholder="Search..."
                  className="pl-10 pr-3 py-2 w-full bg-gray-50 border-0 rounded-xl focus:bg-white focus:ring-2 focus:ring-black/20 transition-all duration-300 text-base font-medium"
                  style={{
                    fontFamily:
                      "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
                  }}
                />
              </div>
            </div>

            {/* Mobile Actions - Touch-friendly */}
            <div className="flex items-center space-x-1 flex-shrink-0">
              {/* Cart Quick Access - only show when authenticated and has items */}
              <ClientOnlyWrapper>
                {isAuthenticated && cartTotalItems > 0 && (
                  <Link href="/cart">
                    <button className="relative text-white hover:text-gray-200 p-2 rounded-full hover:bg-white/10 transition-all duration-200 min-w-[44px] min-h-[44px] flex items-center justify-center">
                      <Icon
                        icon="material-symbols:shopping-cart"
                        className="w-5 h-5"
                      />
                      <span
                        className="absolute -top-1 -right-1 bg-red-500 text-white text-sm rounded-full w-5 h-5 flex items-center justify-center font-bold"
                        style={{
                          fontFamily:
                            "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
                        }}
                      >
                        {cartTotalItems > 9 ? "9+" : cartTotalItems}
                      </span>
                    </button>
                  </Link>
                )}
              </ClientOnlyWrapper>
            </div>
          </div>
        </header>

        {/* Categories Section - Only show on mobile and tablet as part of sticky header */}
        <div className="md:hidden">
          <CategoriesSection />
        </div>
      </div>

      {/* Mobile Navigation Drawer */}
      <MobileNavigationDrawer />
    </>
  );
}
