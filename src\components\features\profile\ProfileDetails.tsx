"use client";

import React, { useRef } from "react";
import Image from "next/image";
import { Icon } from "@iconify/react";
import type { UserProfile } from "@/types/ecommerce";

interface ProfileDetailsProps {
  userProfile: UserProfile;
  onProfilePictureChange?: (file: File) => void;
}

const UserProfileCard = ({
  userProfile,
  onProfilePictureChange,
}: ProfileDetailsProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
    });
  };

  const formatLastLogin = (date: string) => {
    const now = new Date();
    const targetDate = new Date(date);
    const diffInHours = Math.floor(
      (now.getTime() - targetDate.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) {
      return "Just now";
    } else if (diffInHours < 24) {
      return `${diffInHours} Hours Ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} Days Ago`;
    }
  };

  const handleEditClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && onProfilePictureChange) {
      onProfilePictureChange(file);
    }
  };

  return (
    <div className="flex gap-6 flex-col md:flex-row  p-6">
      {/* Profile Card - 2/3 Width */}
      <div className="w-2/3">
        <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
          {/* Header with Avatar and Basic Info */}
          <div className="flex items-start gap-4 mb-6">
            <div className="relative">
              <div className="w-20 h-20 rounded-md overflow-hidden bg-gray-100">
                <Image
                  src={
                    userProfile.profilePicture ||
                    userProfile.avatar ||
                    "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face"
                  }
                  alt={userProfile.username}
                  width={64}
                  height={64}
                  className="w-full h-full object-cover"
                />
              </div>
              {/* Edit button */}
              <button
                onClick={handleEditClick}
                className="absolute -top-1.5 -right-2 w-6 h-6 bg-white rounded-sn shadow-md flex items-center justify-center border border-gray-200 hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
                title="Change profile picture"
              >
                <Icon icon="mdi:pencil" className="w-6 h-6 text-gray-600" />
              </button>

              {/* Hidden file input */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                className="hidden"
              />
            </div>

            <div className="flex-2">
              <h2 className="text-2xl font-semibold text-gray-900 mb-1">
                {userProfile.username}
              </h2>
              <p className="text-gray-500 text-md">{userProfile.email}</p>
            </div>
          </div>

          {/* Contact Information - Two Column Layout */}
          <div className="flex justify-center  gap-50 items-center mb-8">
            {/* Left Side - Phone and Address */}
            <div className="space-y-2 ">
              <div className="flex items-center gap-2">
                <span className="text-lg font-medium text-gray-900">
                  Phone:
                </span>
                <span className="text-lg text-gray-600">
                  {userProfile.phoneNumber || "Not provided"}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-lg font-medium text-gray-900">
                  Address:
                </span>
                <span className="text-lg text-gray-600">
                  {userProfile.address?.city || "Not provided"}
                </span>
              </div>
            </div>

            {/* Right Side - Member Since and Last Login */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-lg font-medium text-gray-900">
                  Member Since:
                </span>
                <span className="text-lg text-gray-600">
                  {formatDate(userProfile.createdAt)}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-lg font-medium text-gray-900">
                  Last Login:
                </span>
                <span className="text-lg text-gray-600">
                  {formatLastLogin(userProfile.updatedAt)}
                </span>
              </div>
            </div>
          </div>

          {/* About Me Section */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              About Me :
            </h3>
            <p className="text-md text-gray-600 leading-relaxed">
              I&apos;m a passionate individual who loves exploring new
              opportunities and connecting with people. I enjoy technology,
              reading, and outdoor activities.
            </p>
          </div>

          {/* Profile Completion Badge */}
          <div className="flex justify-start">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
              Profile Completed
            </span>
          </div>
        </div>
      </div>

      {/* Analytics Section - 1/3 Width */}
      <div className="w-1/3">
        <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
          <h3 className="text-2xl font-semibold text-gray-900 mb-6">
            Analytics Dashboard
          </h3>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-md font-medium text-blue-600">
                    Total Views
                  </p>
                  <p className="text-2xl font-bold text-blue-900">2,847</p>
                </div>
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <Icon
                    icon="mdi:eye-outline"
                    className="w-6 h-6 text-blue-600"
                  />
                </div>
              </div>
              <p className="text-xs text-blue-600 mt-2">+12% from last month</p>
            </div>

            <div className="bg-green-50 rounded-lg p-4 border border-green-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-md font-medium text-green-600">
                    Active Listings
                  </p>
                  <p className="text-2xl font-bold text-green-900">24</p>
                </div>
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <Icon
                    icon="mdi:check-circle-outline"
                    className="w-6 h-6 text-green-600"
                  />
                </div>
              </div>
              <p className="text-xs text-green-600 mt-2">+3 new this week</p>
            </div>

            <div className="bg-purple-50 rounded-lg p-4 border border-purple-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-md font-medium text-purple-600">
                    Messages
                  </p>
                  <p className="text-2xl font-bold text-purple-900">156</p>
                </div>
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <Icon
                    icon="mdi:message-outline"
                    className="w-6 h-6 text-purple-600"
                  />
                </div>
              </div>
              <p className="text-xs text-purple-600 mt-2">8 unread</p>
            </div>

            <div className="bg-orange-50 rounded-lg p-4 border border-orange-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-md font-medium text-orange-600">Revenue</p>
                  <p className="text-2xl font-bold text-orange-900">$3,247</p>
                </div>
                <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                  <Icon
                    icon="mdi:currency-usd"
                    className="w-6 h-6 text-orange-600"
                  />
                </div>
              </div>
              <p className="text-xs text-orange-600 mt-2">
                +18% from last month
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfileCard;
