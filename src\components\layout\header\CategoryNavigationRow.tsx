"use client";
import { useState } from "react";
import Link from "next/link";
import { Icon } from "@iconify/react";

import { getCategoryUrl } from "@/utils/category-utils";

// Map of category IDs to their display labels and icons for mobile navigation
const categoryDisplayMap = {
  "electronics-appliances": {
    label: "Electronics, TVs, & More",
    icon: "material-symbols:devices",
  },
  property: {
    label: "Real Estate",
    icon: "material-symbols:home-work",
  },
  "mobile-phones": {
    label: "Mobile Phones",
    icon: "material-symbols:smartphone",
  },
  vehicles: {
    label: "Vehicles",
    icon: "material-symbols:directions-car",
  },
  jobs: {
    label: "Jobs",
    icon: "material-symbols:work",
  },
  "pets-animals": {
    label: "Pet & Animal",
    icon: "material-symbols:pets",
  },
  photography: {
    label: "Photography",
    icon: "material-symbols:photo-camera",
  },
  "art-crafts": {
    label: "Art & Crafts",
    icon: "material-symbols:palette",
  },
};

// Get featured categories for mobile navigation
const getFeaturedCategories = (categories: any[]) => {
  return categories
    .filter(
      (category) =>
        categoryDisplayMap[category.id as keyof typeof categoryDisplayMap]
    )
    .map((category) => ({
      id: category.id,
      slug: category.slug,
      label:
        categoryDisplayMap[category.id as keyof typeof categoryDisplayMap]
          ?.label || category.name,
      href: getCategoryUrl(category),
      icon:
        categoryDisplayMap[category.id as keyof typeof categoryDisplayMap]
          ?.icon || "material-symbols:category",
    }));
};

export function CategoryNavigationRow() {
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  // For now, return empty array since we don't have access to categories here
  // This component might need to be updated to use Redux or RTK Query
  const categories = getFeaturedCategories([]);

  return (
    <div className="md:hidden bg-white border-b border-gray-200 shadow-sm">
      <div className="container-responsive px-4 py-2">
        {/* Horizontal scrollable category tabs */}
        <div
          className="flex overflow-x-auto scrollbar-hide space-x-1"
          style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
        >
          {categories.map((category) => (
            <Link
              key={category.id}
              href={category.href}
              onClick={() => setActiveCategory(category.id)}
              className={`
                flex items-center gap-2 px-3 py-2 rounded-full whitespace-nowrap text-sm font-medium transition-all duration-200 flex-shrink-0
                ${
                  activeCategory === category.id
                    ? "bg-[#478085] text-white shadow-md"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }
              `}
            >
              <Icon
                icon={category.icon}
                className={`w-4 h-4 ${
                  activeCategory === category.id
                    ? "text-white"
                    : "text-gray-500"
                }`}
              />
              <span className="text-xs">{category.label}</span>
            </Link>
          ))}
        </div>
      </div>

      {/* Custom scrollbar styles */}
      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
}
