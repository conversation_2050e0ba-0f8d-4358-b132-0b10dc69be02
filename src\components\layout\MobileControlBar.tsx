"use client";
import { Icon } from "@iconify/react";
import { useEcommerce } from "@/store/compatibility";
import type { Category } from "@/types/ecommerce";

interface MobileControlBarProps {
  onFilterClick: () => void;
  viewMode?: "grid" | "list";
  onViewModeChange?: (mode: "grid" | "list") => void;
}

export function MobileControlBar({
  onFilterClick,
  viewMode = "grid",
  onViewModeChange,
}: MobileControlBarProps) {
  const { state } = useEcommerce();
  const { selectedCategory, categories } = state.category;

  // Get selected category data
  const selectedCategoryData = selectedCategory
    ? categories.find((cat: Category) => cat.id === selectedCategory)
    : null;

  return (
    <div className="md:hidden bg-white border-b border-gray-200 shadow-sm">
      <div className="container mx-auto px-3 sm:px-4 py-3">
        <div className="flex items-center justify-between gap-3 sm:gap-4">
          {/* Selected Categories Section */}
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <span className="text-xs sm:text-sm font-medium text-gray-600 whitespace-nowrap">
              Category:
            </span>
            <div className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-1.5 min-w-0">
              {selectedCategoryData ? (
                <>
                  <span className="text-base sm:text-lg">
                    {selectedCategoryData.icon}
                  </span>
                  <span className="text-xs sm:text-sm font-medium text-gray-700 truncate">
                    {selectedCategoryData.name}
                  </span>
                </>
              ) : (
                <span className="text-xs sm:text-sm font-medium text-gray-700">
                  All Categories
                </span>
              )}
            </div>
          </div>

          {/* Right Controls */}
          <div className="flex items-center gap-2 flex-shrink-0">
            {/* Grid/List Toggle */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              {viewMode === "grid" ? (
                <button
                  onClick={() => onViewModeChange?.("list")}
                  className="p-2 rounded-md transition-colors bg-white text-[#478085] shadow-sm min-w-[44px] min-h-[44px] flex items-center justify-center"
                  aria-label="Switch to List view"
                >
                  <Icon icon="mingcute:grid-fill" className="w-5 h-5" />
                </button>
              ) : (
                <button
                  onClick={() => onViewModeChange?.("grid")}
                  className="p-2 rounded-md transition-colors bg-white text-[#478085] shadow-sm min-w-[44px] min-h-[44px] flex items-center justify-center"
                  aria-label="Switch to Grid view"
                >
                  <Icon icon="gg:list" className="w-5 h-5" />
                </button>
              )}
            </div>

            {/* Filter Button */}
            <button
              onClick={onFilterClick}
              className="flex items-center gap-2 px-3 py-2 bg-[#478085] text-white rounded-lg hover:bg-[#356267] transition-colors min-w-[44px] min-h-[44px] justify-center"
              aria-label="Open filters"
            >
              <Icon icon="lsicon:filter-filled" className="w-5 h-5" />
              <span className="hidden sm:inline text-sm">Filter</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
