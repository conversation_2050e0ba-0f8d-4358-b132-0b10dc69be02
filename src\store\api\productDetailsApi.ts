// Product Details API endpoints using RTK Query
// Handles reviews, Q&A, seller info, similar products, and related features

import { apiSlice } from "./index";
import type {
  Review,
  ReviewStats,
  CreateReviewRequest,
  UpdateReviewRequest,
  ReviewHelpfulRequest,
  PaginatedReviewsResponse,
  ProductQuestion,
  CreateQuestionRequest,
  CreateAnswerRequest,
  QuestionHelpfulRequest,
  PaginatedQuestionsResponse,
  SellerStats,
  SellerReview,
  SellerReviewsResponse,
  SimilarProductsRequest,
  SimilarProductsResponse,
  ContactSellerRequest,
  ContactSellerResponse,
  ProductViewRequest,
  ProductViewResponse,
  WishlistRequest,
  WishlistResponse,
  ReportProductRequest,
  ReportProductResponse,
  GetReviewsParams,
  GetQuestionsParams,
  GetSellerReviewsParams,
} from "@/types/product-details";
import { buildQueryParams } from "@/constants/api-constants";

export const productDetailsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // ===== REVIEWS ENDPOINTS =====
    
    // Get product reviews with pagination and filtering
    getProductReviews: builder.query<PaginatedReviewsResponse, GetReviewsParams>({
      query: ({ productId, ...params }) => ({
        url: `/products/${productId}/reviews`,
        params: buildQueryParams(params),
      }),
      providesTags: (result, error, { productId }) => [
        { type: "Product", id: `${productId}-reviews` },
        "Review",
      ],
    }),

    // Get review statistics for a product
    getReviewStats: builder.query<ReviewStats, string>({
      query: (productId) => `/products/${productId}/reviews/stats`,
      providesTags: (result, error, productId) => [
        { type: "Product", id: `${productId}-review-stats` },
      ],
    }),

    // Create a new review
    createReview: builder.mutation<Review, CreateReviewRequest>({
      query: (data) => ({
        url: `/products/${data.productId}/reviews`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: (result, error, { productId }) => [
        { type: "Product", id: `${productId}-reviews` },
        { type: "Product", id: `${productId}-review-stats` },
        "Review",
      ],
    }),

    // Update an existing review
    updateReview: builder.mutation<
      Review,
      { reviewId: string; data: UpdateReviewRequest }
    >({
      query: ({ reviewId, data }) => ({
        url: `/reviews/${reviewId}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: "Review", id: reviewId },
        "Review",
      ],
    }),

    // Mark review as helpful/not helpful
    markReviewHelpful: builder.mutation<
      { success: boolean; helpfulCount: number; notHelpfulCount: number },
      ReviewHelpfulRequest
    >({
      query: ({ reviewId, helpful }) => ({
        url: `/reviews/${reviewId}/helpful`,
        method: "POST",
        body: { helpful },
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: "Review", id: reviewId },
      ],
    }),

    // Delete a review
    deleteReview: builder.mutation<{ success: boolean; message: string }, string>({
      query: (reviewId) => ({
        url: `/reviews/${reviewId}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, reviewId) => [
        { type: "Review", id: reviewId },
        "Review",
      ],
    }),

    // ===== Q&A ENDPOINTS =====

    // Get product questions with pagination
    getProductQuestions: builder.query<PaginatedQuestionsResponse, GetQuestionsParams>({
      query: ({ productId, ...params }) => ({
        url: `/products/${productId}/questions`,
        params: buildQueryParams(params),
      }),
      providesTags: (result, error, { productId }) => [
        { type: "Product", id: `${productId}-questions` },
        "Question",
      ],
    }),

    // Create a new question
    createQuestion: builder.mutation<ProductQuestion, CreateQuestionRequest>({
      query: (data) => ({
        url: `/products/${data.productId}/questions`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: (result, error, { productId }) => [
        { type: "Product", id: `${productId}-questions` },
        "Question",
      ],
    }),

    // Answer a question (seller only)
    answerQuestion: builder.mutation<ProductQuestion, CreateAnswerRequest>({
      query: ({ questionId, answer }) => ({
        url: `/questions/${questionId}/answer`,
        method: "POST",
        body: { answer },
      }),
      invalidatesTags: (result, error, { questionId }) => [
        { type: "Question", id: questionId },
        "Question",
      ],
    }),

    // Mark question as helpful
    markQuestionHelpful: builder.mutation<
      { success: boolean; helpfulCount: number },
      QuestionHelpfulRequest
    >({
      query: ({ questionId, helpful }) => ({
        url: `/questions/${questionId}/helpful`,
        method: "POST",
        body: { helpful },
      }),
      invalidatesTags: (result, error, { questionId }) => [
        { type: "Question", id: questionId },
      ],
    }),

    // Delete a question
    deleteQuestion: builder.mutation<{ success: boolean; message: string }, string>({
      query: (questionId) => ({
        url: `/questions/${questionId}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, questionId) => [
        { type: "Question", id: questionId },
        "Question",
      ],
    }),

    // ===== SELLER INFORMATION ENDPOINTS =====

    // Get seller statistics and information
    getSellerStats: builder.query<SellerStats, string>({
      query: (sellerId) => `/sellers/${sellerId}/stats`,
      providesTags: (result, error, sellerId) => [
        { type: "User", id: `${sellerId}-stats` },
      ],
    }),

    // Get seller reviews
    getSellerReviews: builder.query<SellerReviewsResponse, GetSellerReviewsParams>({
      query: ({ sellerId, ...params }) => ({
        url: `/sellers/${sellerId}/reviews`,
        params: buildQueryParams(params),
      }),
      providesTags: (result, error, { sellerId }) => [
        { type: "User", id: `${sellerId}-reviews` },
      ],
    }),

    // ===== SIMILAR PRODUCTS ENDPOINTS =====

    // Get similar products
    getSimilarProducts: builder.query<SimilarProductsResponse, SimilarProductsRequest>({
      query: ({ productId, ...params }) => ({
        url: `/products/${productId}/similar`,
        params: buildQueryParams(params),
      }),
      providesTags: (result, error, { productId }) => [
        { type: "Product", id: `${productId}-similar` },
      ],
    }),

    // ===== INTERACTION ENDPOINTS =====

    // Contact seller
    contactSeller: builder.mutation<ContactSellerResponse, ContactSellerRequest>({
      query: (data) => ({
        url: `/products/${data.productId}/contact-seller`,
        method: "POST",
        body: data,
      }),
    }),

    // Track product view
    trackProductView: builder.mutation<ProductViewResponse, ProductViewRequest>({
      query: ({ productId, viewerInfo }) => ({
        url: `/products/${productId}/view`,
        method: "POST",
        body: { viewerInfo },
      }),
      invalidatesTags: (result, error, { productId }) => [
        { type: "Product", id: productId },
      ],
    }),

    // Add/remove from wishlist
    toggleWishlist: builder.mutation<WishlistResponse, WishlistRequest>({
      query: ({ productId }) => ({
        url: `/products/${productId}/wishlist`,
        method: "POST",
      }),
      invalidatesTags: (result, error, { productId }) => [
        { type: "Product", id: productId },
        "Wishlist",
      ],
    }),

    // Report product
    reportProduct: builder.mutation<ReportProductResponse, ReportProductRequest>({
      query: ({ productId, ...data }) => ({
        url: `/products/${productId}/report`,
        method: "POST",
        body: data,
      }),
    }),

    // Get user's wishlist status for a product
    getWishlistStatus: builder.query<{ isInWishlist: boolean }, string>({
      query: (productId) => `/products/${productId}/wishlist/status`,
      providesTags: (result, error, productId) => [
        { type: "Product", id: `${productId}-wishlist` },
      ],
    }),
  }),
});

// Export hooks for use in components
export const {
  // Reviews
  useGetProductReviewsQuery,
  useGetReviewStatsQuery,
  useCreateReviewMutation,
  useUpdateReviewMutation,
  useMarkReviewHelpfulMutation,
  useDeleteReviewMutation,
  
  // Q&A
  useGetProductQuestionsQuery,
  useCreateQuestionMutation,
  useAnswerQuestionMutation,
  useMarkQuestionHelpfulMutation,
  useDeleteQuestionMutation,
  
  // Seller Info
  useGetSellerStatsQuery,
  useGetSellerReviewsQuery,
  
  // Similar Products
  useGetSimilarProductsQuery,
  
  // Interactions
  useContactSellerMutation,
  useTrackProductViewMutation,
  useToggleWishlistMutation,
  useReportProductMutation,
  useGetWishlistStatusQuery,
} = productDetailsApi;

export default productDetailsApi;
