"use client";

import { useState } from "react";
import Image, { ImageProps } from "next/image";
import { useImageHandler } from "@/hooks";
import { getFallbackDataUrl } from "@/utils";
import { LoadingSpinner } from "@/components/ui";

interface SafeImageProps extends Omit<ImageProps, "onError" | "onLoad"> {
  fallbackSrc?: string;
  showLoadingState?: boolean;
  enableLogging?: boolean;
  className?: string;
}

/**
 * SafeImage component that handles image loading errors gracefully
 * Provides consistent fallback behavior across the application
 */
export function SafeImage({
  src,
  alt,
  fallbackSrc = "/assets/images/placeholders/placeholder.jpg",
  showLoadingState = false,
  enableLogging = process.env.NODE_ENV === "development",
  className = "",
  ...props
}: SafeImageProps) {
  const [currentSrc, setCurrentSrc] = useState(src);
  const { handleImageError, handleImageLoad, isLoading, hasError } =
    useImageHandler({
      fallbackSrc,
      enableLogging,
      onError: (e) => {
        // Update the current src to the fallback or data URL
        if (e.currentTarget.src !== fallbackSrc) {
          setCurrentSrc(fallbackSrc);
        } else {
          setCurrentSrc(getFallbackDataUrl());
        }
      },
    });

  return (
    <div className={`relative ${className}`}>
      <Image
        {...props}
        src={currentSrc}
        alt={alt}
        onError={handleImageError}
        onLoad={handleImageLoad}
      />

      {showLoadingState && isLoading && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
          <LoadingSpinner size="sm" />
        </div>
      )}

      {hasError && enableLogging && (
        <div className="absolute top-2 right-2 bg-red-100 text-red-600 text-xs px-2 py-1 rounded">
          Failed to load
        </div>
      )}
    </div>
  );
}
