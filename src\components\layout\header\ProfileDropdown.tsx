"use client";
import Link from "next/link";
import { Icon } from "@iconify/react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { UserProfile } from "@/types/ecommerce";

interface ProfileDropdownProps {
  isAuthenticated: boolean;
  currentUser: UserProfile | null;
  onLogout: () => void;
}

export function ProfileDropdown({
  isAuthenticated,
  currentUser,
  onLogout,
}: ProfileDropdownProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="text-white hover:text-gray-200 hover:bg-white/10 transition-all duration-300 group p-0 w-10 h-10"
        >
          <div className="w-full h-full bg-[#1F5E64] border border-white rounded-lg flex items-center justify-center hover:bg-[#1a5157] transition-all duration-300">
            <Icon
              icon="material-symbols:person"
              className="w-5 h-5 group-hover:scale-110 transition-transform duration-300"
            />
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="bg-white border border-gray-200 shadow-lg rounded-lg w-64 p-0"
      >
        {/* User Profile Header */}
        <div className="flex items-center p-4 border-b border-gray-100">
          <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center mr-3">
            <Icon
              icon="material-symbols:person"
              className="w-6 h-6 text-gray-600"
            />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900">
              {isAuthenticated ? currentUser?.username || "User" : "User"}
            </h3>
            <Link
              href="/profile"
              className="text-blue-600 hover:text-blue-700 text-sm flex items-center group"
            >
              Visit Profile
              <Icon
                icon="material-symbols:arrow-forward"
                className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform"
              />
            </Link>
          </div>
        </div>

        {/* Balance and Top Up Section */}
        {isAuthenticated && (
          <div className="p-4 border-b border-gray-100 bg-gradient-to-r from-[#478085]/5 to-[#356267]/5">
            <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-3">
              Wallet
            </h3>

            {/* Balance Display */}
            <div className="bg-white rounded-lg p-3 mb-3 border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Icon
                    icon="tdesign:money-filled"
                    className="w-5 h-5 text-[#478085]"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    Balance
                  </span>
                </div>
                <span className="text-lg font-bold text-[#478085]">
                  Rs. 123456
                </span>
              </div>
            </div>

            {/* Top Up Button */}
            <Link href="/top-up">
              <button className="w-full bg-gradient-to-r from-[#478085] to-[#356267] hover:from-[#356267] hover:to-[#2d5459] text-white rounded-lg px-4 py-2.5 text-sm font-medium transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center gap-2">
                <Icon
                  icon="material-symbols:add-card"
                  className="w-4 h-4"
                />
                <span>Top Up</span>
              </button>
            </Link>
          </div>
        )}

        {/* Menu Items */}
        <div className="py-2">
          <DropdownMenuItem asChild>
            <Link
              href="/settings"
              className="flex items-center w-full px-4 py-3 text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <Icon
                icon="material-symbols:settings"
                className="w-5 h-5 mr-3 text-gray-500"
              />
              <span className="text-sm font-medium">Setting</span>
            </Link>
          </DropdownMenuItem>
          {/* Help center  */}
          <DropdownMenuItem asChild>
            <Link
              href="/help-center"
              className="flex items-center w-full px-4 py-3 text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <Icon
                icon="material-symbols:help"
                className="w-5 h-5 mr-3 text-gray-500"
              />
              <span className="text-sm font-medium">Help Center</span>
            </Link>
          </DropdownMenuItem>
          {/* Community Forum  */}
          <DropdownMenuItem asChild>
            <Link
              href="/community"
              className="flex items-center w-full px-4 py-3 text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <Icon
                icon="material-symbols:forum"
                className="w-5 h-5 mr-3 text-gray-500"
              />
              <span className="text-sm font-medium">Community Forum</span>
            </Link>
          </DropdownMenuItem>

          <DropdownMenuItem asChild>
            <button
              onClick={isAuthenticated ? onLogout : () => {}}
              className="flex items-center w-full px-4 py-3 text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <Icon
                icon="material-symbols:logout"
                className="w-5 h-5 mr-3 text-gray-500"
              />
              <span className="text-sm font-medium">Log Out</span>
            </button>
          </DropdownMenuItem>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
