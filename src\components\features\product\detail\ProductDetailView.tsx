"use client";

import type React from "react";
import BaseProductDetailView from "../BaseProductDetailView";
import ProductDetailErrorBoundary from "./product-detail-error-boundary";
import { ProductDetailLoading } from "./product-detail-loading";
import type { Product } from "@/types/ecommerce";

interface ProductDetailViewProps {
  product: Product;
  similarProducts?: Product[];
  onBack?: () => void;
  isLoading?: boolean;
}

const ProductDetailView: React.FC<ProductDetailViewProps> = ({
  product,
  similarProducts = [],
  onBack,
  isLoading = false,
}) => {
  // Show loading state
  if (isLoading) {
    return <ProductDetailLoading />;
  }

  return (
    <ProductDetailErrorBoundary>
      <BaseProductDetailView
        product={product}
        similarProducts={similarProducts}
        onBack={onBack}
        config={{
          showSimilarProducts: true,
          showReviews: false, // Disabled - no backend support needed
          showQA: false, // Disabled - no backend support needed
          showSellerVerification: true,
          showSafetyTips: true,
          showPurchaseOptions: true,
          tabsEnabled: true, // Keep enabled to show sections
        }}
      />
    </ProductDetailErrorBoundary>
  );
};

export default ProductDetailView;
