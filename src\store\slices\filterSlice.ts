import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { ActiveFilters, FilterOptions } from "@/types/ecommerce";
import { mockFilterOptions } from "@/constants/mock-data";
import { getCategoryFilterOptions } from "@/constants/category-filters";

// Define the filter state interface
export interface FilterState {
  activeFilters: ActiveFilters;
  availableFilters: FilterOptions;
  loading: boolean;
  error?: string;
}

// Initial state
const initialState: FilterState = {
  activeFilters: {},
  availableFilters: {
    ...mockFilterOptions,
    ...getCategoryFilterOptions("default"),
  },
  loading: false,
  error: undefined,
};

// Async thunks for filter operations
export const updateAvailableFilters = createAsyncThunk(
  "filter/updateAvailableFilters",
  async (categoryId: string | null) => {
    // Simulate API call - replace with actual API call
    await new Promise((resolve) => setTimeout(resolve, 200));

    const newFilters = categoryId
      ? { ...mockFilterOptions, ...getCategoryFilterOptions(categoryId) }
      : { ...mockFilterOptions, ...getCategoryFilterOptions("default") };

    return newFilters;
  }
);

// Create the filter slice
const filterSlice = createSlice({
  name: "filter",
  initialState,
  reducers: {
    setActiveFilters: (state, action: PayloadAction<ActiveFilters>) => {
      state.activeFilters = action.payload;
    },
    updateSingleFilter: (
      state,
      action: PayloadAction<{ key: string; value: any }>
    ) => {
      const { key, value } = action.payload;
      if (value === null || value === undefined || value === "") {
        delete (state.activeFilters as any)[key];
      } else {
        (state.activeFilters as any)[key] = value;
      }
    },
    clearFilters: (state) => {
      state.activeFilters = {};
    },
    clearSingleFilter: (state, action: PayloadAction<string>) => {
      delete (state.activeFilters as any)[action.payload];
    },
    setAvailableFilters: (state, action: PayloadAction<FilterOptions>) => {
      state.availableFilters = action.payload;
    },
    setFilterLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setFilterError: (state, action: PayloadAction<string | undefined>) => {
      state.error = action.payload;
    },
    clearFilterError: (state) => {
      state.error = undefined;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(updateAvailableFilters.pending, (state) => {
        state.loading = true;
        state.error = undefined;
      })
      .addCase(updateAvailableFilters.fulfilled, (state, action) => {
        state.loading = false;
        state.availableFilters = action.payload;
      })
      .addCase(updateAvailableFilters.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to update filters";
      });
  },
});

// Export actions
export const {
  setActiveFilters,
  updateSingleFilter,
  clearFilters,
  clearSingleFilter,
  setAvailableFilters,
  setFilterLoading,
  setFilterError,
  clearFilterError,
} = filterSlice.actions;

// Export reducer
export default filterSlice.reducer;
