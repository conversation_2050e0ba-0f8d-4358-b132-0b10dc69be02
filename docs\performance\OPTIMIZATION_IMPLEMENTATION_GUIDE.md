# Performance Optimization Implementation Guide

## 🚀 Quick Wins (Immediate Implementation)

### 1. Remove Duplicate Icon Libraries

**Current Issue**: Using both `@iconify/react` and `lucide-react`

```bash
# Remove lucide-react
npm uninstall lucide-react @types/lucide-react

# Update package.json will automatically remove the dependency
```

**Files to Update**:

- `src/components/layout/Header.tsx`
- `src/components/common/ErrorBoundary.tsx`
- `src/components/ui/button.tsx`
- All components importing from `lucide-react`

**Migration Example**:

```typescript
// Before
import { Search, User, ShoppingCart } from 'lucide-react';

// After
import { Icon } from '@iconify/react';

// Usage
<Icon icon="lucide:search" className="h-5 w-5" />
<Icon icon="lucide:user" className="h-5 w-5" />
<Icon icon="lucide:shopping-cart" className="h-5 w-5" />
```

### 2. Implement Dynamic Imports

**Create**: `src/components/dynamic/index.ts`

```typescript
import dynamic from "next/dynamic";
import { LoadingSpinner, ProductCardSkeleton } from "@/components/ui";

// Heavy components that should be loaded dynamically
export const ImageCropModal = dynamic(
  () => import("@/components/forms/ui/ImageCropModal"),
  {
    loading: () => <LoadingSpinner message="Loading image editor..." />,
    ssr: false,
  }
);

export const ProductDetailView = dynamic(
  () => import("@/components/product/detail/ProductDetailView"),
  {
    loading: () => <ProductCardSkeleton />,
  }
);

export const EnhancedImageGallery = dynamic(
  () => import("@/components/product/detail/enhanced-image-gallery"),
  {
    loading: () => (
      <div className="animate-pulse bg-gray-200 aspect-[3/1] rounded-lg" />
    ),
  }
);

export const BannerCarousel = dynamic(
  () => import("@/components/banner/BannerCarousel"),
  {
    loading: () => (
      <div className="animate-pulse bg-gray-200 h-64 rounded-lg" />
    ),
  }
);
```

### 3. Optimize Next.js Configuration

**Update**: `next.config.ts`

```typescript
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Existing config...
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Enhanced image optimization
  images: {
    formats: ["image/webp", "image/avif"],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "via.placeholder.com",
        port: "",
        pathname: "/**",
      },
    ],
    dangerouslyAllowSVG: true,
    contentDispositionType: "attachment",
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // Performance optimizations
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ["@iconify/react"],
  },

  // Production optimizations
  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
  },

  // Bundle analysis
  ...(process.env.ANALYZE === "true" && {
    webpack: (config, { isServer }) => {
      if (!isServer) {
        const { BundleAnalyzerPlugin } = require("webpack-bundle-analyzer");
        config.plugins.push(
          new BundleAnalyzerPlugin({
            analyzerMode: "static",
            openAnalyzer: false,
            reportFilename: "../bundle-analysis.html",
          })
        );
      }
      return config;
    },
  }),
};

export default nextConfig;
```

### 4. Add Bundle Analysis

**Install**: Bundle analyzer

```bash
npm install --save-dev webpack-bundle-analyzer
```

**Add Script**: `package.json`

```json
{
  "scripts": {
    "analyze": "ANALYZE=true npm run build",
    "build:analyze": "npm run analyze"
  }
}
```

### 5. Optimize Image Loading

**Update**: `src/components/common/SmartImage.tsx`

```typescript
export function SmartImage({
  src,
  alt,
  width,
  height,
  className = "",
  onError,
  onLoad,
  loading = "lazy", // Default to lazy loading
  priority = false,
  fill = false,
  style,
  sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw", // Better default sizes
}: SmartImageProps) {
  // ... existing code

  return (
    <Image
      src={src}
      alt={alt}
      width={width || 300}
      height={height || 225}
      className={className}
      onError={handleImageError}
      onLoad={handleImageLoad}
      loading={loading}
      priority={priority}
      style={style}
      sizes={sizes}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
    />
  );
}
```

## 🔧 Medium Priority Optimizations

### 6. Implement Web Vitals Monitoring

**Create**: `src/lib/analytics.ts`

```typescript
import { getCLS, getFID, getFCP, getLCP, getTTFB } from "web-vitals";

interface Metric {
  name: string;
  value: number;
  id: string;
  delta: number;
}

function sendToAnalytics(metric: Metric) {
  // Send to your analytics service
  if (process.env.NODE_ENV === "production") {
    // Example: Send to Google Analytics
    gtag("event", metric.name, {
      value: Math.round(metric.value),
      metric_id: metric.id,
      metric_delta: metric.delta,
    });
  } else {
    console.log("Web Vital:", metric);
  }
}

export function initWebVitals() {
  getCLS(sendToAnalytics);
  getFID(sendToAnalytics);
  getFCP(sendToAnalytics);
  getLCP(sendToAnalytics);
  getTTFB(sendToAnalytics);
}
```

**Update**: `src/app/layout.tsx`

```typescript
import { initWebVitals } from '@/lib/analytics';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    if (process.env.NODE_ENV === 'production') {
      initWebVitals();
    }
  }, []);

  return (
    // ... existing layout
  );
}
```

### 7. Optimize Redux Store

**Update**: `src/store/index.ts`

```typescript
// Add performance middleware only in development
const middleware = getDefaultMiddleware({
  serializableCheck: {
    ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
  },
  thunk: true,
});

// Only add logger in development
if (process.env.NODE_ENV === "development") {
  const logger = createLogger({
    collapsed: true,
    duration: true,
    timestamp: false, // Reduce overhead
    diff: false, // Reduce overhead
  });
  return middleware.concat(logger);
}

return middleware;
```

### 8. Add Performance Budget

**Create**: `.lighthouserc.js`

```javascript
module.exports = {
  ci: {
    collect: {
      url: ["http://localhost:3000"],
      startServerCommand: "npm start",
    },
    assert: {
      assertions: {
        "categories:performance": ["warn", { minScore: 0.9 }],
        "categories:accessibility": ["error", { minScore: 0.9 }],
        "categories:best-practices": ["warn", { minScore: 0.9 }],
        "categories:seo": ["warn", { minScore: 0.9 }],
      },
    },
    upload: {
      target: "temporary-public-storage",
    },
  },
};
```

## 📊 Measurement & Monitoring

### Performance Testing Script

**Create**: `scripts/performance-test.js`

```javascript
const lighthouse = require("lighthouse");
const chromeLauncher = require("chrome-launcher");

async function runLighthouse() {
  const chrome = await chromeLauncher.launch({ chromeFlags: ["--headless"] });
  const options = {
    logLevel: "info",
    output: "html",
    onlyCategories: ["performance"],
  };
  const runnerResult = await lighthouse("http://localhost:3000", options);

  console.log(
    "Performance Score:",
    runnerResult.lhr.categories.performance.score * 100
  );

  await chrome.kill();
}

runLighthouse();
```

### Bundle Size Monitoring

**Add to CI/CD**:

```yaml
# .github/workflows/performance.yml
name: Performance Check
on: [pull_request]

jobs:
  bundle-size:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: "18"
      - name: Install dependencies
        run: npm ci
      - name: Build and analyze
        run: npm run analyze
      - name: Upload bundle analysis
        uses: actions/upload-artifact@v2
        with:
          name: bundle-analysis
          path: bundle-analysis.html
```

## 🎯 Expected Results

After implementing these optimizations:

1. **Bundle Size**: Reduce by 100-150KB
2. **First Contentful Paint**: Improve by 0.3-0.5s
3. **Largest Contentful Paint**: Improve by 0.5-0.8s
4. **Time to Interactive**: Improve by 0.5-1s
5. **Lighthouse Score**: Increase to 90+

## 📋 Implementation Checklist

- [ ] Remove lucide-react dependency
- [ ] Update all icon imports to use Iconify
- [ ] Implement dynamic imports for heavy components
- [ ] Update Next.js configuration
- [ ] Add bundle analyzer
- [ ] Optimize image loading with better defaults
- [ ] Add Web Vitals monitoring
- [ ] Optimize Redux store configuration
- [ ] Set up performance budgets
- [ ] Add performance testing scripts
