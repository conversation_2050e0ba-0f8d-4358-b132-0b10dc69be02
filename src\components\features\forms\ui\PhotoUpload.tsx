import type React from "react";
import { useState } from "react";
import { Icon } from "@iconify/react";
import { LoadingSpinner } from "@/components/ui";
import { SmartImage } from "@/components/common/SmartImage";
import { getFileInputAccept } from "@/utils/imageValidation";

import { PhotoUploadProgress } from "./PhotoUploadProgress";
import { ImageCropModal } from "@/components/dynamic";
import { MAX_PHOTOS } from "../types";

interface PhotoUploadProps {
  uploadedImages: string[];
  onImageUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onImageUpdate?: (index: number, newImageUrl: string) => void;
  onImageDelete?: (index: number) => void;
  isUploading?: boolean;
}

export const PhotoUpload: React.FC<PhotoUploadProps> = ({
  uploadedImages,
  onImageUpload,
  onImageUpdate,
  onImageDelete,
  isUploading = false,
}) => {
  const [cropModalOpen, setCropModalOpen] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(
    null
  );
  const [selectedImageSrc, setSelectedImageSrc] = useState<string>("");

  const handleEditClick = (index: number, imageSrc: string) => {
    if (!imageSrc || imageSrc.trim() === "") {
      console.error("Cannot open crop modal: No image source provided");
      return;
    }

    setSelectedImageIndex(index);
    setSelectedImageSrc(imageSrc);
    setCropModalOpen(true);
  };

  const handleCropComplete = (croppedImageUrl: string) => {
    if (selectedImageIndex !== null && onImageUpdate) {
      onImageUpdate(selectedImageIndex, croppedImageUrl);
    }
    setCropModalOpen(false);
    setSelectedImageIndex(null);
    setSelectedImageSrc("");
  };

  const handleCropCancel = () => {
    setCropModalOpen(false);
    setSelectedImageIndex(null);
    setSelectedImageSrc("");
  };

  const handleDeleteClick = (index: number) => {
    if (onImageDelete) {
      onImageDelete(index);
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {uploadedImages.map((image, index) => (
          <div
            key={index}
            className="relative aspect-square rounded-2xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 border-3 border-gray-200 hover:border-blue-400 transition-all duration-300 group shadow-lg hover:shadow-xl"
          >
            <SmartImage
              src={image || "/images/placeholder.jpg"}
              alt={`Upload ${index + 1}`}
              width={200}
              height={200}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200" />

            {/* Edit Button */}
            <button
              onClick={() => handleEditClick(index, image)}
              className="absolute bottom-3 right-3 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 opacity-0 group-hover:opacity-100 hover:scale-110"
              title="Crop Image"
            >
              <Icon icon="lucide:edit-2" className="w-4 h-4 text-gray-700" />
            </button>

            {/* Delete Button */}
            <button
              onClick={() => handleDeleteClick(index)}
              className="absolute bottom-3 left-3 w-10 h-10 bg-red-500/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 opacity-0 group-hover:opacity-100 hover:scale-110"
              title="Delete Image"
            >
              <Icon icon="lucide:trash-2" className="w-4 h-4 text-white" />
            </button>
            {index === 0 && (
              <div className="absolute top-3 left-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs px-3 py-1 rounded-full font-bold shadow-lg">
                MAIN
              </div>
            )}
          </div>
        ))}
        {uploadedImages.length < MAX_PHOTOS && (
          <label className="aspect-square border-3 border-dashed border-gray-300 rounded-2xl flex flex-col items-center justify-center cursor-pointer hover:border-blue-400 hover:bg-gradient-to-br hover:from-blue-50 hover:to-purple-50 transition-all duration-300 group shadow-lg hover:shadow-xl relative">
            {isUploading ? (
              <>
                <LoadingSpinner size="lg" />
                <span className="text-md font-bold text-gray-500 mt-3">
                  Uploading...
                </span>
              </>
            ) : (
              <>
                <div className="p-6 rounded-full bg-gradient-to-br from-gray-100 to-gray-200 group-hover:from-blue-100 group-hover:to-purple-100 transition-all duration-300 mb-3">
                  <Icon
                    icon="lucide:plus"
                    className="w-8 h-8 text-gray-400 group-hover:text-blue-500 transition-colors duration-200"
                  />
                </div>
                <span className="text-md font-bold text-gray-500 group-hover:text-blue-600 transition-colors duration-200">
                  Add Photo
                </span>
                <span className="text-xs text-gray-400 mt-1">
                  {MAX_PHOTOS - uploadedImages.length} remaining
                </span>
              </>
            )}
            <input
              type="file"
              multiple
              accept={getFileInputAccept()}
              onChange={onImageUpload}
              className="hidden"
              disabled={isUploading}
            />
          </label>
        )}
      </div>

      <PhotoUploadProgress uploadedImages={uploadedImages} />

      {/* Image Crop Modal */}
      <ImageCropModal
        isOpen={cropModalOpen}
        imageSrc={selectedImageSrc}
        onCropComplete={handleCropComplete}
        onCancel={handleCropCancel}
      />
    </div>
  );
};
