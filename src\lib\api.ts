import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from "axios";

// API Configuration
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL ||
  "https://sasto-api.webstudiomatrix.com/api/v1";
const API_TIMEOUT = 30000; // 30 seconds for better compatibility with backend

// Token management
const TOKEN_KEY = "auth_token";
const REFRESH_TOKEN_KEY = "refresh_token";

/**
 * Get stored authentication token
 */
export const getAuthToken = (): string | null => {
  if (typeof window === "undefined") return null;
  return localStorage.getItem(TOKEN_KEY);
};

/**
 * Get stored refresh token
 */
export const getRefreshToken = (): string | null => {
  if (typeof window === "undefined") return null;
  return localStorage.getItem(REFRESH_TOKEN_KEY);
};

/**
 * Set authentication token
 */
export const setAuthToken = (token: string): void => {
  if (typeof window === "undefined") return;
  localStorage.setItem(TOKEN_KEY, token);
};

/**
 * Set refresh token
 */
export const setRefreshToken = (token: string): void => {
  if (typeof window === "undefined") return;
  localStorage.setItem(REFRESH_TOKEN_KEY, token);
};

/**
 * Remove authentication token
 */
export const removeAuthToken = (): void => {
  if (typeof window === "undefined") return;
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(REFRESH_TOKEN_KEY);
};

/**
 * Create axios instance with default configuration
 */
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: API_BASE_URL,
    timeout: API_TIMEOUT,
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
  });

  // Request interceptor to add auth token
  instance.interceptors.request.use(
    (config) => {
      const token = getAuthToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor for error handling and token refresh
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      return response;
    },
    async (error: AxiosError) => {
      const originalRequest = error.config as AxiosRequestConfig & {
        _retry?: boolean;
      };

      // Handle 401 Unauthorized - try to refresh token first
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        const refreshToken = getRefreshToken();
        if (refreshToken) {
          try {
            // Try to refresh the token
            const refreshResponse = await axios.post(
              `${API_BASE_URL}/auth/refresh`,
              { refreshToken }
            );

            const { accessToken, refreshToken: newRefreshToken } =
              refreshResponse.data;
            setAuthToken(accessToken);
            if (newRefreshToken) {
              setRefreshToken(newRefreshToken);
            }

            // Retry the original request with new token
            if (originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${accessToken}`;
            }
            return instance(originalRequest);
          } catch (refreshError) {
            // Refresh failed, remove tokens and redirect
            removeAuthToken();
            if (typeof window !== "undefined") {
              window.location.href = "/login";
            }
          }
        } else {
          // No refresh token, remove invalid token and redirect
          removeAuthToken();
          if (typeof window !== "undefined") {
            window.location.href = "/login";
          }
        }
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// Create the main API instance
export const apiClient = createApiInstance();

/**
 * API Error class for better error handling
 */
export class ApiError extends Error {
  public status: number;
  public data: unknown;

  constructor(message: string, status: number, data?: unknown) {
    super(message);
    this.name = "ApiError";
    this.status = status;
    this.data = data;
  }
}

/**
 * Handle API errors and convert to ApiError
 */
export const handleApiError = (error: AxiosError): ApiError => {
  // Log the error for debugging
  console.error("API Error:", {
    message: error.message,
    code: error.code,
    status: error.response?.status,
    data: error.response?.data,
    url: error.config?.url,
    method: error.config?.method,
  });

  if (error.response) {
    // Server responded with error status
    const status = error.response.status;
    const data = error.response.data;

    // Extract message from various possible response formats
    let message = "An error occurred";

    if (typeof data === "string") {
      message = data;
    } else if (data && typeof data === "object") {
      // Try different common message fields
      message =
        (data as any).message ||
        (data as any).error ||
        (data as any).detail ||
        (data as any).msg ||
        error.message ||
        `HTTP ${status} Error`;
    }

    return new ApiError(message, status, data);
  } else if (error.request) {
    // Request was made but no response received (network error)
    const networkMessage =
      error.code === "ECONNABORTED"
        ? "Request timeout. Please try again."
        : error.code === "ERR_NETWORK"
        ? "Network error. Please check your internet connection."
        : error.code === "ERR_INTERNET_DISCONNECTED"
        ? "No internet connection. Please check your network."
        : "Network error. Please check your connection and try again.";

    return new ApiError(networkMessage, 0, { code: error.code });
  } else {
    // Something else happened in setting up the request
    return new ApiError(
      error.message || "An unexpected error occurred while making the request",
      0,
      { code: error.code }
    );
  }
};

/**
 * Generic API request wrapper with error handling
 */
export const apiRequest = async <T>(
  requestFn: () => Promise<AxiosResponse<T>>
): Promise<T> => {
  try {
    const response = await requestFn();
    return response.data;
  } catch (error) {
    throw handleApiError(error as AxiosError);
  }
};

/**
 * API endpoints configuration matching backend structure
 */
export const API_ENDPOINTS = {
  // Authentication
  LOGIN: "/auth/login",
  REGISTER: "/auth/register",
  LOGOUT: "/auth/logout",
  REFRESH_TOKEN: "/auth/refresh",
  FORGOT_PASSWORD: "/auth/forgot-password",
  RESET_PASSWORD: "/auth/reset-password",
  CHANGE_PASSWORD: "/auth/change-password",
  AUTH_PROFILE: "/auth/profile",

  // Users
  USER_PROFILE: "/users/profile",
  USER_PROFILE_BY_USERNAME: (username: string) => `/users/profile/${username}`,
  UPDATE_PROFILE: "/users/profile",
  UPDATE_PRIVACY_SETTINGS: "/users/privacy-settings",
  UPDATE_NOTIFICATION_PREFERENCES: "/users/notification-preferences",
  FOLLOW_USER: (userId: string) => `/users/follow/${userId}`,
  UNFOLLOW_USER: (userId: string) => `/users/follow/${userId}`,
  GET_FOLLOWERS: "/users/followers",
  GET_FOLLOWING: "/users/following",

  // Categories
  CATEGORIES: {
    LIST: "/categories",
    BY_ID: (id: string) => `/categories/${id}`,
    BY_SLUG: (slug: string) => `/categories/slug/${slug}`,
    SUBCATEGORIES: (categoryId: string) =>
      `/categories/${categoryId}/subcategories`,
    SUBCATEGORY_BY_ID: (id: string) => `/categories/subcategories/${id}`,
  },

  // Advertisements
  ADVERTISEMENTS: "/advertisements",
  ADVERTISEMENT_BY_ID: (id: string) => `/advertisements/${id}`,
  MY_ADVERTISEMENTS: "/advertisements/my-ads",
  FAVORITE_ADVERTISEMENTS: "/advertisements/favorites",
  ADD_TO_FAVORITES: (id: string) => `/advertisements/${id}/favorite`,
  REMOVE_FROM_FAVORITES: (id: string) => `/advertisements/${id}/favorite`,
  UPLOAD_AD_IMAGES: (id: string) => `/advertisements/${id}/images`,
  SUBMIT_FOR_APPROVAL: (id: string) =>
    `/advertisements/${id}/submit-for-approval`,
  MARK_AS_SOLD: (id: string) => `/advertisements/${id}/mark-sold`,
  AD_STATISTICS: (id: string) => `/advertisements/${id}/stats`,
  REPORT_ADVERTISEMENT: (id: string) => `/advertisements/${id}/report`,
  FEATURED_ADVERTISEMENTS: "/advertisements/featured",
  POPULAR_ADVERTISEMENTS: "/advertisements/popular",
  RECENT_ADVERTISEMENTS: "/advertisements/recent",
  SIMILAR_ADVERTISEMENTS: (id: string) => `/advertisements/${id}/similar`,
  LOCATION_SEARCH: "/advertisements/search/location",

  // Admin Advertisement endpoints
  APPROVE_ADVERTISEMENT: (id: string) => `/advertisements/${id}/approve`,
  REJECT_ADVERTISEMENT: (id: string) => `/advertisements/${id}/reject`,
  PENDING_ADVERTISEMENTS: "/advertisements/admin/pending",
  SUSPEND_ADVERTISEMENT: (id: string) => `/advertisements/${id}/suspend`,
  REACTIVATE_ADVERTISEMENT: (id: string) => `/advertisements/${id}/reactivate`,
  ADMIN_AD_STATISTICS: "/advertisements/admin/stats",
  BULK_APPROVE_ADS: "/advertisements/admin/bulk-approve",
  BULK_REJECT_ADS: "/advertisements/admin/bulk-reject",

  // Image Management
  REMOVE_AD_IMAGE: (imageId: string) => `/advertisements/images/${imageId}`,
  SET_PRIMARY_IMAGE: (imageId: string) =>
    `/advertisements/images/${imageId}/set-primary`,
  REORDER_IMAGES: (id: string) => `/advertisements/${id}/images/reorder`,

  // Cart
  CART: {
    GET: "/cart",
    ADD_ITEM: "/cart/items",
    UPDATE_ITEM: (itemId: string) => `/cart/items/${itemId}`,
    REMOVE_ITEM: (itemId: string) => `/cart/items/${itemId}`,
    BULK_REMOVE: "/cart/items/bulk",
    CLEAR: "/cart",
    SYNC: "/cart/sync",
  },

  // File Upload
  UPLOAD_FILES: "/files/upload",
  UPLOAD_PROFILE_PICTURE: "/files/upload/profile-picture",
  UPLOAD_AD_IMAGES_FILE: "/files/upload/advertisement-images",

  // Search
  SEARCH: {
    GLOBAL: "/search",
    ADVERTISEMENTS: "/search/advertisements",
    USERS: "/search/users",
    SUGGESTIONS: "/search/suggestions",
  },
  // Legacy search endpoints for backward compatibility
  GLOBAL_SEARCH: "/search",
  SEARCH_ADVERTISEMENTS: "/search/advertisements",
  SEARCH_USERS: "/search/users",
  SEARCH_SUGGESTIONS: "/search/suggestions",

  // Orders & Addresses
  ORDERS: {
    LIST: "/orders",
    BY_ID: (id: string) => `/orders/${id}`,
    CANCEL: (id: string) => `/orders/${id}/cancel`,
    UPDATE_STATUS: (id: string) => `/orders/${id}/status`,
  },
  ADDRESSES: {
    LIST: "/users/addresses",
    CREATE: "/users/addresses",
    BY_ID: (id: string) => `/users/addresses/${id}`,
    UPDATE: (id: string) => `/users/addresses/${id}`,
    DELETE: (id: string) => `/users/addresses/${id}`,
    SET_DEFAULT: (id: string) => `/users/addresses/${id}/default`,
  },
  CHECKOUT: {
    VALIDATE: "/checkout/validate",
    PROCESS: "/checkout",
  },

  // Payments & Transactions
  PAYMENTS: {
    TRANSACTIONS: "/payments/transactions",
    CREATE_TRANSACTION: "/payments/transactions",
    WALLET: "/payments/wallet",
    WALLET_TOPUP: "/payments/wallet/topup",
    WALLET_WITHDRAW: "/payments/wallet/withdraw",
    WALLET_TRANSFER: "/payments/wallet/transfer",
    PAYMENT_METHODS: "/payments/payment-methods",
    WEBHOOKS: (gateway: string) => `/payments/webhooks/${gateway}`,
  },

  // Communications
  MESSAGES: "/communications/messages",
  CONVERSATIONS: "/communications/conversations",
  MARK_MESSAGE_READ: (id: string) => `/communications/messages/${id}/read`,

  // Subscriptions
  SUBSCRIPTION_PLANS: "/subscriptions/plans",
  SUBSCRIBE: "/subscriptions/subscribe",
  CURRENT_SUBSCRIPTION: "/subscriptions/current",
  CANCEL_SUBSCRIPTION: "/subscriptions/cancel",

  // Health Check
  HEALTH: "/health",
  HEALTH_DB: "/health/db",
  HEALTH_REDIS: "/health/redis",

  // Email Verification
  VERIFY_EMAIL: "/email/verify",
  RESEND_VERIFICATION: "/email/resend-verification",
} as const;

export default apiClient;
