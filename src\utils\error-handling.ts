import { ApiError } from "@/lib/api";

/**
 * Error handling utilities for consistent error management across the application
 */

// Error types enum
export enum ErrorType {
  NETWORK = "NETWORK",
  VALIDATION = "VALIDATION",
  AUTHENTICATION = "AUTHENTICATION",
  AUTH<PERSON><PERSON>ZATION = "AU<PERSON><PERSON><PERSON>ZATION",
  NOT_FOUND = "NOT_FOUND",
  SERVER = "SERVER",
  UNKNOWN = "UNKNOWN",
}

// User-friendly error messages
export const ERROR_MESSAGES = {
  // Network errors
  NETWORK_ERROR:
    "Unable to connect to the server. Please check your internet connection and try again.",
  TIMEOUT_ERROR: "Request timed out. Please try again.",

  // Authentication errors
  INVALID_CREDENTIALS:
    "Invalid email or password. Please check your credentials and try again.",
  TOKEN_EXPIRED: "Your session has expired. Please log in again.",
  UNAUTHORIZED: "You are not authorized to perform this action.",

  // Validation errors
  VALIDATION_ERROR: "Please check your input and try again.",
  REQUIRED_FIELD: "This field is required.",
  INVALID_EMAIL: "Please enter a valid email address.",
  WEAK_PASSWORD:
    "Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character.",

  // Server errors
  SERVER_ERROR: "Something went wrong on our end. Please try again later.",
  NOT_FOUND: "The requested resource was not found.",

  // Generic errors
  UNKNOWN_ERROR: "An unexpected error occurred. Please try again.",
  FORM_SUBMISSION_ERROR: "Failed to submit form. Please try again.",
} as const;

/**
 * Determine error type based on status code
 */
export const getErrorType = (statusCode: number): ErrorType => {
  if (statusCode === 0) return ErrorType.NETWORK;
  if (statusCode === 401) return ErrorType.AUTHENTICATION;
  if (statusCode === 403) return ErrorType.AUTHORIZATION;
  if (statusCode === 404) return ErrorType.NOT_FOUND;
  if (statusCode === 422) return ErrorType.VALIDATION;
  if (statusCode >= 500) return ErrorType.SERVER;
  return ErrorType.UNKNOWN;
};

/**
 * Get user-friendly error message based on error type and status
 */
export const getUserFriendlyMessage = (error: ApiError | Error): string => {
  if (error instanceof ApiError) {
    const errorType = getErrorType(error.status);

    switch (errorType) {
      case ErrorType.NETWORK:
        return ERROR_MESSAGES.NETWORK_ERROR;
      case ErrorType.AUTHENTICATION:
        return ERROR_MESSAGES.INVALID_CREDENTIALS;
      case ErrorType.AUTHORIZATION:
        return ERROR_MESSAGES.UNAUTHORIZED;
      case ErrorType.NOT_FOUND:
        return ERROR_MESSAGES.NOT_FOUND;
      case ErrorType.VALIDATION:
        return error.message || ERROR_MESSAGES.VALIDATION_ERROR;
      case ErrorType.SERVER:
        return ERROR_MESSAGES.SERVER_ERROR;
      default:
        return error.message || ERROR_MESSAGES.UNKNOWN_ERROR;
    }
  }

  return error.message || ERROR_MESSAGES.UNKNOWN_ERROR;
};

/**
 * Extract validation errors from API response
 */
export const extractValidationErrors = (
  error: ApiError
): Record<string, string> => {
  const errors: Record<string, string> = {};

  // Type guard to check if error.data has errors property
  if (
    error.data &&
    typeof error.data === "object" &&
    "errors" in error.data &&
    error.data.errors &&
    typeof error.data.errors === "object"
  ) {
    const errorData = error.data as { errors: Record<string, string[]> };
    Object.keys(errorData.errors).forEach((field) => {
      const fieldErrors = errorData.errors[field];
      if (Array.isArray(fieldErrors) && fieldErrors.length > 0) {
        errors[field] = fieldErrors[0]; // Take the first error message
      }
    });
  }

  return errors;
};

/**
 * Check if error is retryable
 */
export const isRetryableError = (error: ApiError | Error): boolean => {
  if (error instanceof ApiError) {
    const errorType = getErrorType(error.status);
    return errorType === ErrorType.NETWORK || errorType === ErrorType.SERVER;
  }
  return false;
};

/**
 * Retry mechanism for API calls
 */
export const withRetry = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;

      // Don't retry if it's not a retryable error
      if (!isRetryableError(lastError)) {
        throw lastError;
      }

      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        throw lastError;
      }

      // Wait before retrying with exponential backoff
      await new Promise((resolve) => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError!;
};

/**
 * Log error for debugging (in development) and monitoring (in production)
 */
export const logError = (error: Error, context?: string): void => {
  const errorInfo = {
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString(),
    userAgent:
      typeof window !== "undefined" ? window.navigator.userAgent : "server",
    url: typeof window !== "undefined" ? window.location.href : "server",
    userId:
      typeof window !== "undefined" ? localStorage.getItem("user_id") : null,
  };

  if (process.env.NODE_ENV === "development") {
    console.error("Error logged:", errorInfo);
  } else {
    // Production error logging
    try {
      // Send to multiple logging services for redundancy
      sendToLoggingService(errorInfo);

      // Fallback: still log to console for server logs
      console.error("Production error:", {
        message: errorInfo.message,
        context: errorInfo.context,
        timestamp: errorInfo.timestamp,
        url: errorInfo.url,
      });
    } catch (loggingError) {
      // If logging service fails, at least log to console
      console.error("Logging service failed:", loggingError);
      console.error("Original error:", errorInfo);
    }
  }
};

/**
 * Send error to external logging service
 */
const sendToLoggingService = async (errorInfo: any): Promise<void> => {
  // Example implementations for different logging services

  // Option 1: Send to your own backend logging endpoint
  try {
    if (typeof window !== "undefined") {
      await fetch("/api/logs/error", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(errorInfo),
      });
    }
  } catch (error) {
    // Silently fail - don't throw errors from logging
  }

  // Option 2: Send to Sentry (if configured)
  if (typeof window !== "undefined" && (window as any).Sentry) {
    (window as any).Sentry.captureException(new Error(errorInfo.message), {
      contexts: {
        errorInfo: errorInfo,
      },
    });
  }

  // Option 3: Send to LogRocket (if configured)
  if (typeof window !== "undefined" && (window as any).LogRocket) {
    (window as any).LogRocket.captureException(new Error(errorInfo.message));
  }
};

/**
 * Error boundary helper for React components
 */
export class ErrorBoundaryError extends Error {
  public componentStack?: string;

  constructor(message: string, componentStack?: string) {
    super(message);
    this.name = "ErrorBoundaryError";
    this.componentStack = componentStack;
  }
}

/**
 * Handle form submission errors
 */
export const handleFormError = (
  error: ApiError | Error,
  setFieldError: (field: string, message: string) => void,
  setGeneralError: (message: string) => void
): void => {
  if (error instanceof ApiError && error.status === 422) {
    // Handle validation errors
    const validationErrors = extractValidationErrors(error);
    Object.keys(validationErrors).forEach((field) => {
      setFieldError(field, validationErrors[field]);
    });
  } else {
    // Handle general errors
    const message = getUserFriendlyMessage(error);
    setGeneralError(message);
  }

  // Log the error for debugging
  logError(error, "Form submission");
};

/**
 * Create a toast notification for errors
 */
export const showErrorToast = (error: ApiError | Error): void => {
  const message = getUserFriendlyMessage(error);

  // You can integrate this with your toast library
  // For now, we'll just log it
  console.error("Toast error:", message);

  // Example integration with react-hot-toast:
  // toast.error(message);
};

const ErrorHandlingUtils = {
  getErrorType,
  getUserFriendlyMessage,
  extractValidationErrors,
  isRetryableError,
  withRetry,
  logError,
  handleFormError,
  showErrorToast,
  ERROR_MESSAGES,
  ErrorType,
};

export default ErrorHandlingUtils;
