"use client";

import React, {
  createContext,
  useContext,
  useReducer,
  useEffect,
  use<PERSON><PERSON>back,
  ReactNode,
} from "react";
import {
  EcommerceContextState,
  EcommerceAction,
  ActiveFilters,
  SortBy,
  Product,
} from "@/types/ecommerce";
import { mockProducts, mockFilterOptions } from "@/constants/mock-data";
import { getCategoryFilterOptions } from "@/constants/category-filters";

// Initial state
const initialState: EcommerceContextState = {
  search: {
    query: "",
    category: "",
    location: "",
    results: [],
    loading: false,
    error: undefined,
  },
  category: {
    selectedCategory: null,
    showFilters: true,
    categories: [],
    loading: false,
    error: undefined,
  },
  filter: {
    activeFilters: {},
    availableFilters: {
      ...mockFilterOptions,
      ...getCategoryFilterOptions("default"),
    },
    loading: false,
    error: undefined,
  },
  product: {
    products: [],
    filteredProducts: [],
    currentPage: 1,
    totalPages: 1,
    itemsPerPage: 12,
    sortBy: "newest",
    loading: false,
    error: undefined,
  },
  cart: {
    items: [],
    totalItems: 0,
    totalPrice: 0,
    currency: "Rs.",
    loading: false,
    error: undefined,
  },
};

// Reducer function
function ecommerceReducer(
  state: EcommerceContextState,
  action: EcommerceAction
): EcommerceContextState {
  switch (action.type) {
    // Search actions
    case "SET_SEARCH_QUERY":
      return {
        ...state,
        search: { ...state.search, query: action.payload },
      };
    case "SET_SEARCH_CATEGORY":
      return {
        ...state,
        search: { ...state.search, category: action.payload },
      };
    case "SET_SEARCH_LOCATION":
      return {
        ...state,
        search: { ...state.search, location: action.payload },
      };
    case "SET_SEARCH_RESULTS":
      return {
        ...state,
        search: { ...state.search, results: action.payload },
      };
    case "SET_SEARCH_LOADING":
      return {
        ...state,
        search: { ...state.search, loading: action.payload },
      };
    case "SET_SEARCH_ERROR":
      return {
        ...state,
        search: { ...state.search, error: action.payload },
      };

    // Category actions
    case "SELECT_CATEGORY":
      // Clear filters when changing category and update available filters
      const newAvailableFilters = action.payload
        ? { ...mockFilterOptions, ...getCategoryFilterOptions(action.payload) }
        : { ...mockFilterOptions, ...getCategoryFilterOptions("default") };

      return {
        ...state,
        category: {
          ...state.category,
          selectedCategory: action.payload,
          showFilters: true, // Always show filters (either category-specific or default)
        },
        filter: {
          ...state.filter,
          activeFilters: {}, // Clear active filters when changing category
          availableFilters: newAvailableFilters,
        },
      };
    case "TOGGLE_FILTERS":
      return {
        ...state,
        category: { ...state.category, showFilters: action.payload },
      };
    case "SET_CATEGORIES":
      return {
        ...state,
        category: { ...state.category, categories: action.payload },
      };
    case "SET_CATEGORY_LOADING":
      return {
        ...state,
        category: { ...state.category, loading: action.payload },
      };
    case "SET_CATEGORY_ERROR":
      return {
        ...state,
        category: { ...state.category, error: action.payload },
      };

    // Filter actions
    case "SET_ACTIVE_FILTERS":
      return {
        ...state,
        filter: { ...state.filter, activeFilters: action.payload },
      };
    case "CLEAR_FILTERS":
      return {
        ...state,
        filter: { ...state.filter, activeFilters: {} },
      };
    case "SET_AVAILABLE_FILTERS":
      return {
        ...state,
        filter: { ...state.filter, availableFilters: action.payload },
      };
    case "SET_FILTER_LOADING":
      return {
        ...state,
        filter: { ...state.filter, loading: action.payload },
      };
    case "SET_FILTER_ERROR":
      return {
        ...state,
        filter: { ...state.filter, error: action.payload },
      };

    // Product actions
    case "SET_PRODUCTS":
      return {
        ...state,
        product: { ...state.product, products: action.payload },
      };
    case "SET_FILTERED_PRODUCTS":
      const totalPages = Math.ceil(
        action.payload.length / state.product.itemsPerPage
      );
      return {
        ...state,
        product: {
          ...state.product,
          filteredProducts: action.payload,
          totalPages,
          currentPage: Math.min(state.product.currentPage, totalPages || 1),
        },
      };
    case "SET_CURRENT_PAGE":
      return {
        ...state,
        product: { ...state.product, currentPage: action.payload },
      };
    case "SET_SORT_BY":
      return {
        ...state,
        product: { ...state.product, sortBy: action.payload },
      };
    case "SET_PRODUCT_LOADING":
      return {
        ...state,
        product: { ...state.product, loading: action.payload },
      };
    case "SET_PRODUCT_ERROR":
      return {
        ...state,
        product: { ...state.product, error: action.payload },
      };

    // Cart actions
    case "ADD_TO_CART":
      const { product, quantity } = action.payload;
      const existingItemIndex = state.cart.items.findIndex(
        (item) => item.id === product.id
      );

      let newItems;
      if (existingItemIndex >= 0) {
        // Update existing item quantity
        newItems = state.cart.items.map((item, index) =>
          index === existingItemIndex
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
      } else {
        // Add new item
        const newItem = {
          id: product.id,
          product,
          quantity,
          addedAt: new Date().toISOString(),
        };
        newItems = [...state.cart.items, newItem];
      }

      const newTotalItems = newItems.reduce(
        (sum, item) => sum + item.quantity,
        0
      );
      const newTotalPrice = newItems.reduce(
        (sum, item) => sum + item.product.price * item.quantity,
        0
      );

      return {
        ...state,
        cart: {
          ...state.cart,
          items: newItems,
          totalItems: newTotalItems,
          totalPrice: newTotalPrice,
        },
      };

    case "REMOVE_FROM_CART":
      const filteredItems = state.cart.items.filter(
        (item) => item.id !== action.payload
      );
      const updatedTotalItems = filteredItems.reduce(
        (sum, item) => sum + item.quantity,
        0
      );
      const updatedTotalPrice = filteredItems.reduce(
        (sum, item) => sum + item.product.price * item.quantity,
        0
      );

      return {
        ...state,
        cart: {
          ...state.cart,
          items: filteredItems,
          totalItems: updatedTotalItems,
          totalPrice: updatedTotalPrice,
        },
      };

    case "UPDATE_CART_QUANTITY":
      const updatedItems = state.cart.items.map((item) =>
        item.id === action.payload.id
          ? { ...item, quantity: action.payload.quantity }
          : item
      );
      const quantityTotalItems = updatedItems.reduce(
        (sum, item) => sum + item.quantity,
        0
      );
      const quantityTotalPrice = updatedItems.reduce(
        (sum, item) => sum + item.product.price * item.quantity,
        0
      );

      return {
        ...state,
        cart: {
          ...state.cart,
          items: updatedItems,
          totalItems: quantityTotalItems,
          totalPrice: quantityTotalPrice,
        },
      };

    case "CLEAR_CART":
      return {
        ...state,
        cart: {
          ...state.cart,
          items: [],
          totalItems: 0,
          totalPrice: 0,
        },
      };

    case "SET_CART_LOADING":
      return {
        ...state,
        cart: { ...state.cart, loading: action.payload },
      };

    case "SET_CART_ERROR":
      return {
        ...state,
        cart: { ...state.cart, error: action.payload },
      };

    default:
      return state;
  }
}

// Context creation
const EcommerceContext = createContext<
  | {
      state: EcommerceContextState;
      dispatch: React.Dispatch<EcommerceAction>;
    }
  | undefined
>(undefined);

// Provider component
interface EcommerceProviderProps {
  children: ReactNode;
}

export function EcommerceProvider({ children }: EcommerceProviderProps) {
  const [state, dispatch] = useReducer(ecommerceReducer, initialState);

  // Initialize data on mount
  useEffect(() => {
    dispatch({ type: "SET_PRODUCTS", payload: mockProducts });
    dispatch({ type: "SET_FILTERED_PRODUCTS", payload: mockProducts });
  }, []);

  return (
    <EcommerceContext.Provider value={{ state, dispatch }}>
      {children}
    </EcommerceContext.Provider>
  );
}

// Custom hook to use the context
export function useEcommerce() {
  const context = useContext(EcommerceContext);
  if (context === undefined) {
    throw new Error("useEcommerce must be used within an EcommerceProvider");
  }
  return context;
}

// Helper functions for common operations
export const useEcommerceActions = () => {
  const { dispatch } = useEcommerce();

  const selectCategory = useCallback(
    (categoryId: string | null) => {
      dispatch({ type: "SELECT_CATEGORY", payload: categoryId });
    },
    [dispatch]
  );

  const updateAvailableFilters = useCallback(
    (categoryId: string | null) => {
      const newFilters = categoryId
        ? { ...mockFilterOptions, ...getCategoryFilterOptions(categoryId) }
        : { ...mockFilterOptions, ...getCategoryFilterOptions("default") };
      dispatch({ type: "SET_AVAILABLE_FILTERS", payload: newFilters });
    },
    [dispatch]
  );

  const setActiveFilters = useCallback(
    (filters: ActiveFilters) => {
      dispatch({ type: "SET_ACTIVE_FILTERS", payload: filters });
    },
    [dispatch]
  );

  const clearFilters = useCallback(() => {
    dispatch({ type: "CLEAR_FILTERS" });
  }, [dispatch]);

  const setSearchQuery = useCallback(
    (query: string) => {
      dispatch({ type: "SET_SEARCH_QUERY", payload: query });
    },
    [dispatch]
  );

  const setSearchLocation = useCallback(
    (location: string) => {
      dispatch({ type: "SET_SEARCH_LOCATION", payload: location });
    },
    [dispatch]
  );

  const setSortBy = useCallback(
    (sortBy: SortBy) => {
      dispatch({ type: "SET_SORT_BY", payload: sortBy });
    },
    [dispatch]
  );

  const setCurrentPage = useCallback(
    (page: number) => {
      dispatch({ type: "SET_CURRENT_PAGE", payload: page });
    },
    [dispatch]
  );

  const setFilteredProducts = useCallback(
    (products: Product[]) => {
      dispatch({ type: "SET_FILTERED_PRODUCTS", payload: products });
    },
    [dispatch]
  );

  const toggleFilters = useCallback(
    (showFilters: boolean) => {
      dispatch({ type: "TOGGLE_FILTERS", payload: showFilters });
    },
    [dispatch]
  );

  const addToCart = useCallback(
    (product: Product, quantity: number = 1) => {
      dispatch({ type: "ADD_TO_CART", payload: { product, quantity } });
    },
    [dispatch]
  );

  const removeFromCart = useCallback(
    (productId: string) => {
      dispatch({ type: "REMOVE_FROM_CART", payload: productId });
    },
    [dispatch]
  );

  const updateCartQuantity = useCallback(
    (id: string, quantity: number) => {
      dispatch({ type: "UPDATE_CART_QUANTITY", payload: { id, quantity } });
    },
    [dispatch]
  );

  const clearCart = useCallback(() => {
    dispatch({ type: "CLEAR_CART" });
  }, [dispatch]);

  return {
    selectCategory,
    setActiveFilters,
    clearFilters,
    setSearchQuery,
    setSearchLocation,
    setSortBy,
    setCurrentPage,
    setFilteredProducts,
    toggleFilters,
    updateAvailableFilters,
    addToCart,
    removeFromCart,
    updateCartQuantity,
    clearCart,
  };
};
