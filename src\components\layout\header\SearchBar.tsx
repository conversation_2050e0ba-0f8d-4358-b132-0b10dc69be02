"use client";
import { Icon } from "@iconify/react";
import { Input } from "@/components/ui/input";

export function SearchBar() {
  return (
    <div className="hidden md:flex items-center relative flex-1 max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl 2xl:max-w-2xl">
      <div className="relative w-full">
        <Icon
          icon="material-symbols:search"
          className="absolute left-3 md:left-3 lg:left-4 top-1/2 transform -translate-y-1/2 text-gray-500 w-4 h-4 md:w-4 md:h-4 lg:w-5 lg:h-5 z-10"
        />
        <Input
          type="search"
          placeholder="Search products..."
          className="pl-10 md:pl-10 lg:pl-12 pr-4 py-2 md:py-2 lg:py-3 text-sm md:text-sm lg:text-base h-9 md:h-10 lg:h-12 w-full bg-gray-50 backdrop-blur-md border-gray-300 shadow-md rounded-xl border focus:bg-white focus:ring-2 focus:ring-black/20 transition-all duration-300 hover:shadow-lg"
        />
      </div>
    </div>
  );
}
