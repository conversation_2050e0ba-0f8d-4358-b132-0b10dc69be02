"use client";
import { Icon } from "@iconify/react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

export function LanguageSelector() {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex items-center space-x-2 text-white hover:text-gray-200 hover:bg-[#1a5157] bg-[#1F5E64] rounded-xl px-3 py-2 transition-all duration-300 border border-white"
        >
          <div className="w-6 h-4 bg-gradient-to-b from-blue-800 via-white to-red-600 relative overflow-hidden rounded-md shadow-md">
            {/* Enhanced UK Flag */}
            <div className="absolute inset-0 bg-blue-800"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-full h-0.5 bg-white"></div>
              <div className="absolute w-0.5 h-full bg-white left-1/2 transform -translate-x-1/2"></div>
            </div>
            <div className="absolute inset-0">
              <div className="absolute top-0 left-0 w-full h-0.5 bg-red-600"></div>
              <div className="absolute bottom-0 left-0 w-full h-0.5 bg-red-600"></div>
              <div className="absolute top-0 left-1/2 w-0.5 h-full bg-red-600 transform -translate-x-1/2"></div>
            </div>
          </div>
          <div className="p-1 rounded-xl">
            <Icon
              icon="material-symbols:keyboard-arrow-down"
              className="w-4 h-4"
            />
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="bg-white/95 backdrop-blur-md border border-gray-200/50"
      >
        <DropdownMenuItem className="flex items-center space-x-2 px-3 py-2">
          <div className="w-5 h-3 bg-gradient-to-b from-blue-800 via-white to-red-600 rounded-md"></div>
          <span>English (UK)</span>
        </DropdownMenuItem>
        <DropdownMenuItem className="flex items-center space-x-2 px-3 py-2">
          <div className="w-5 h-3 bg-gradient-to-r from-red-600 via-white to-red-600 rounded-md"></div>
          <span>Français</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
