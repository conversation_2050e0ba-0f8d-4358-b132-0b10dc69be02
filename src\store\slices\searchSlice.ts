import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Product } from '@/types/ecommerce';

// Define the search state interface
export interface SearchState {
  query: string;
  category: string;
  location: string;
  results: Product[];
  loading: boolean;
  error?: string;
}

// Initial state
const initialState: SearchState = {
  query: "",
  category: "",
  location: "",
  results: [],
  loading: false,
  error: undefined,
};

// Async thunks for search operations
export const performSearch = createAsyncThunk(
  'search/performSearch',
  async (searchParams: { query: string; category?: string; location?: string }) => {
    // Simulate API call - replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock search results - replace with actual API response
    const mockResults: Product[] = [];
    return mockResults;
  }
);

// Create the search slice
const searchSlice = createSlice({
  name: 'search',
  initialState,
  reducers: {
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.query = action.payload;
    },
    setSearchCategory: (state, action: PayloadAction<string>) => {
      state.category = action.payload;
    },
    setSearchLocation: (state, action: PayloadAction<string>) => {
      state.location = action.payload;
    },
    setSearchResults: (state, action: PayloadAction<Product[]>) => {
      state.results = action.payload;
    },
    setSearchLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setSearchError: (state, action: PayloadAction<string | undefined>) => {
      state.error = action.payload;
    },
    clearSearch: (state) => {
      state.query = "";
      state.category = "";
      state.location = "";
      state.results = [];
      state.error = undefined;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(performSearch.pending, (state) => {
        state.loading = true;
        state.error = undefined;
      })
      .addCase(performSearch.fulfilled, (state, action) => {
        state.loading = false;
        state.results = action.payload;
      })
      .addCase(performSearch.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Search failed';
      });
  },
});

// Export actions
export const {
  setSearchQuery,
  setSearchCategory,
  setSearchLocation,
  setSearchResults,
  setSearchLoading,
  setSearchError,
  clearSearch,
} = searchSlice.actions;

// Export reducer
export default searchSlice.reducer;
