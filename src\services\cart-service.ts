import { apiClient, apiRequest, API_ENDPOINTS } from "@/lib/api";
import { AdvertisementResponseDto } from "./advertisements-service";
import {
  CartResponseDto,
  CartItemResponseDto,
  AddToCartDto,
  UpdateCartItemDto,
  BulkRemoveDto,
  CartValidationResponse,
  CurrencyType,
} from "@/types/orders";

// CartItemDto has been removed - use CartItemResponseDto from types/orders instead

/**
 * Cart Service
 * Handles all cart-related API calls
 */
export class CartService {
  /**
   * Get user's cart
   */
  static async getCart(): Promise<CartResponseDto> {
    return await apiRequest<CartResponseDto>(() =>
      apiClient.get(API_ENDPOINTS.CART.GET)
    );
  }

  /**
   * Add item to cart
   */
  static async addToCart(data: AddToCartDto): Promise<CartResponseDto> {
    return await apiRequest<CartResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.CART.ADD_ITEM, data)
    );
  }

  /**
   * Update cart item quantity
   */
  static async updateCartItem(
    itemId: string,
    data: UpdateCartItemDto
  ): Promise<CartResponseDto> {
    return await apiRequest<CartResponseDto>(() =>
      apiClient.put(API_ENDPOINTS.CART.UPDATE_ITEM(itemId), data)
    );
  }

  /**
   * Remove item from cart
   */
  static async removeCartItem(itemId: string): Promise<CartResponseDto> {
    return await apiRequest<CartResponseDto>(() =>
      apiClient.delete(API_ENDPOINTS.CART.REMOVE_ITEM(itemId))
    );
  }

  /**
   * Remove multiple items from cart
   */
  static async bulkRemoveItems(data: BulkRemoveDto): Promise<CartResponseDto> {
    return await apiRequest<CartResponseDto>(() =>
      apiClient.delete(API_ENDPOINTS.CART.BULK_REMOVE, { data })
    );
  }

  /**
   * Clear entire cart
   */
  static async clearCart(): Promise<void> {
    return await apiRequest<void>(() =>
      apiClient.delete(API_ENDPOINTS.CART.CLEAR)
    );
  }

  /**
   * Sync cart with server (update availability)
   */
  static async syncCart(): Promise<CartResponseDto> {
    return await apiRequest<CartResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.CART.SYNC)
    );
  }

  /**
   * Helper method to check if item is already in cart
   */
  static async isItemInCart(advertisementId: string): Promise<boolean> {
    try {
      const cart = await this.getCart();
      return cart.items.some(
        (item) => item.advertisementId === advertisementId
      );
    } catch (error) {
      return false;
    }
  }

  /**
   * Helper method to get cart item by advertisement ID
   */
  static async getCartItemByAdvertisementId(
    advertisementId: string
  ): Promise<CartItemResponseDto | null> {
    try {
      const cart = await this.getCart();
      const foundItem = cart.items.find((item) => item.advertisementId === advertisementId);
      
      if (foundItem) {
        return foundItem;
      }
      
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Helper method to get cart summary
   */
  static async getCartSummary(): Promise<{
    totalItems: number;
    totalAmount: number;
    currency: string;
    availableItems: number;
    unavailableItems: number;
  }> {
    try {
      const cart = await this.getCart();
      const availableItems = cart.items.filter(
        (item) => item.isAvailable
      ).length;
      const unavailableItems = cart.items.length - availableItems;

      return {
        totalItems: cart.totalItems,
        totalAmount: cart.totalAmount,
        currency: cart.currency,
        availableItems,
        unavailableItems,
      };
    } catch (error) {
      return {
        totalItems: 0,
        totalAmount: 0,
        currency: "NPR",
        availableItems: 0,
        unavailableItems: 0,
      };
    }
  }

  /**
   * Validate cart before checkout (using backend endpoint)
   */
  static async validateCart(): Promise<CartValidationResponse> {
    return await apiRequest<CartValidationResponse>(() =>
      apiClient.post(API_ENDPOINTS.CHECKOUT.VALIDATE)
    );
  }

  /**
   * Client-side cart validation for immediate feedback
   */
  static async validateCartClientSide(): Promise<{
    isValid: boolean;
    errors: string[];
    unavailableItems: CartItemResponseDto[];
  }> {
    try {
      const cart = await this.getCart();
      const errors: string[] = [];
      const unavailableItems: CartItemResponseDto[] = [];

      if (cart.items.length === 0) {
        errors.push("Cart is empty");
        return { isValid: false, errors, unavailableItems };
      }

      cart.items.forEach((item) => {
        if (!item.isAvailable) {
          unavailableItems.push(item);
          errors.push(`${item.productName} is no longer available`);
        }

        if (item.quantity <= 0) {
          errors.push(`Invalid quantity for ${item.productName}`);
        }

        if (item.quantity > item.maxQuantity) {
          errors.push(
            `Quantity for ${item.productName} exceeds maximum available (${item.maxQuantity})`
          );
        }
      });

      return {
        isValid: errors.length === 0,
        errors,
        unavailableItems,
      };
    } catch (error) {
      return {
        isValid: false,
        errors: ["Failed to validate cart"],
        unavailableItems: [],
      };
    }
  }

  /**
   * Helper method to remove unavailable items from cart
   */
  static async removeUnavailableItems(): Promise<CartResponseDto> {
    const cart = await this.getCart();
    const unavailableItemIds = cart.items
      .filter((item) => !item.isAvailable)
      .map((item) => item.id);

    if (unavailableItemIds.length > 0) {
      return await this.bulkRemoveItems({ itemIds: unavailableItemIds });
    }

    return cart;
  }

  /**
   * Helper method to calculate cart totals (client-side verification)
   */
  static calculateCartTotals(items: CartItemResponseDto[]): {
    totalItems: number;
    totalAmount: number;
    itemsCount: number;
  } {
    const availableItems = items.filter((item) => item.isAvailable);

    const totalItems = availableItems.reduce(
      (sum, item) => sum + item.quantity,
      0
    );
    const totalAmount = availableItems.reduce(
      (sum, item) => sum + item.subtotal,
      0
    );
    const itemsCount = availableItems.length;

    return {
      totalItems,
      totalAmount,
      itemsCount,
    };
  }

  /**
   * Helper method to format cart for display
   */
  static formatCartForDisplay(cart: CartResponseDto): {
    availableItems: CartItemResponseDto[];
    unavailableItems: CartItemResponseDto[];
    summary: {
      totalItems: number;
      totalAmount: number;
      currency: string;
      formattedTotal: string;
    };
  } {
    const availableItems = cart.items.filter((item) => item.isAvailable);
    const unavailableItems = cart.items.filter((item) => !item.isAvailable);

    const summary = {
      totalItems: cart.totalItems,
      totalAmount: cart.totalAmount,
      currency: cart.currency,
      formattedTotal: `${cart.currency} ${cart.totalAmount.toLocaleString()}`,
    };

    return {
      availableItems,
      unavailableItems,
      summary,
    };
  }

  /**
   * Helper method to check if cart has items
   */
  static async hasItems(): Promise<boolean> {
    try {
      const cart = await this.getCart();
      return cart.items.length > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Helper method to get cart item count
   */
  static async getItemCount(): Promise<number> {
    try {
      const cart = await this.getCart();
      return cart.totalItems;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Validate add to cart data
   */
  static validateAddToCart(data: AddToCartDto): string[] {
    const errors: string[] = [];

    if (!data.advertisementId?.trim()) {
      errors.push("Advertisement ID is required");
    }
    if (!data.quantity || data.quantity < 1) {
      errors.push("Quantity must be at least 1");
    }
    if (data.quantity > 100) {
      errors.push("Quantity cannot exceed 100");
    }

    return errors;
  }

  /**
   * Validate update cart item data
   */
  static validateUpdateCartItem(data: UpdateCartItemDto): string[] {
    const errors: string[] = [];

    if (!data.quantity || data.quantity < 1) {
      errors.push("Quantity must be at least 1");
    }
    if (data.quantity > 100) {
      errors.push("Quantity cannot exceed 100");
    }

    return errors;
  }

  /**
   * Validate bulk remove data
   */
  static validateBulkRemove(data: BulkRemoveDto): string[] {
    const errors: string[] = [];

    if (!data.itemIds || data.itemIds.length === 0) {
      errors.push("At least one item ID is required");
    }
    if (data.itemIds.length > 50) {
      errors.push("Cannot remove more than 50 items at once");
    }

    return errors;
  }

  /**
   * Check if cart is ready for checkout
   */
  static async isReadyForCheckout(): Promise<{
    ready: boolean;
    reasons: string[];
  }> {
    try {
      const validation = await this.validateCartClientSide();
      return {
        ready: validation.isValid,
        reasons: validation.errors,
      };
    } catch (error) {
      return {
        ready: false,
        reasons: ["Failed to validate cart"],
      };
    }
  }

  /**
   * Get cart value breakdown
   */
  static async getCartValueBreakdown(): Promise<{
    subtotal: number;
    availableItemsValue: number;
    unavailableItemsValue: number;
    currency: string;
  }> {
    try {
      const cart = await this.getCart();
      const availableItems = cart.items.filter((item) => item.isAvailable);
      const unavailableItems = cart.items.filter((item) => !item.isAvailable);

      return {
        subtotal: cart.totalAmount,
        availableItemsValue: availableItems.reduce(
          (sum, item) => sum + item.subtotal,
          0
        ),
        unavailableItemsValue: unavailableItems.reduce(
          (sum, item) => sum + item.subtotal,
          0
        ),
        currency: cart.currency,
      };
    } catch (error) {
      return {
        subtotal: 0,
        availableItemsValue: 0,
        unavailableItemsValue: 0,
        currency: "NPR",
      };
    }
  }

  /**
   * Format cart item for display
   */
  static formatCartItem(item: CartItemResponseDto): {
    displayName: string;
    displayPrice: string;
    displaySubtotal: string;
    statusText: string;
    statusColor: string;
  } {
    return {
      displayName: item.productName,
      displayPrice: `${item.price.toLocaleString()} ${CurrencyType.NPR}`,
      displaySubtotal: `${item.subtotal.toLocaleString()} ${CurrencyType.NPR}`,
      statusText: item.isAvailable ? "Available" : "Unavailable",
      statusColor: item.isAvailable ? "green" : "red",
    };
  }
}

export default CartService;
