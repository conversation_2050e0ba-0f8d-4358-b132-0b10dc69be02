"use client";

import React, { useState } from "react";
import { StatusBadge } from "./shared/StatusBadge";
import { ListingActionDropdown } from "./shared/ListingActionDropdown";
import { MarkAsSoldDialog } from "./shared/MarkAsSoldDialog";
import type { UserListing, ListingAction } from "@/types/ecommerce";

interface ListingActionsProps {
  listing: UserListing;
  onAction: (action: ListingAction) => void;
}

export default function ListingActions({
  listing,
  onAction,
}: ListingActionsProps) {
  const [showMarkSoldDialog, setShowMarkSoldDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  return (
    <>
      <div className="flex items-center gap-2">
        <StatusBadge status={listing.status} />
        <ListingActionDropdown
          listing={listing}
          onAction={onAction}
          onMarkSoldClick={() => setShowMarkSoldDialog(true)}
        />
      </div>

      <MarkAsSoldDialog
        open={showMarkSoldDialog}
        onOpenChange={setShowMarkSoldDialog}
        listing={listing}
        onAction={onAction}
        isLoading={isLoading}
        onLoadingChange={setIsLoading}
      />
    </>
  );
}
