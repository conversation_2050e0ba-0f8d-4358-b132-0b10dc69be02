"use client";

import { memo } from "react";
import ProductDisplay from "./ProductDisplay";
import type { Product } from "@/types/ecommerce";

interface VerticalProductListProps {
  products?: Product[];
  loading?: boolean;
  className?: string;
  emptyStateTitle?: string;
  emptyStateDescription?: string;
  onProductClick?: (product: Product) => void;
}

const VerticalProductList = memo(function VerticalProductList({
  products = [],
  loading = false,
  className = "",
  emptyStateTitle = "No products found",
  emptyStateDescription = "Try adjusting your search or filter criteria",
  onProductClick,
}: VerticalProductListProps) {
  return (
    <ProductDisplay
      products={products}
      config={{
        viewMode: "list",
        showAnimations: true,
      }}
      loading={loading}
      className={className}
      emptyStateTitle={emptyStateTitle}
      emptyStateDescription={emptyStateDescription}
      onProductSelect={onProductClick}
    />
  );
});

export default VerticalProductList;
