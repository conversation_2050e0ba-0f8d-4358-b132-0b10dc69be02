"use client";
import React from "react";
import Image from "next/image";
import { Icon } from "@iconify/react";
import { useImageCropModal } from "../ImageCropModalProvider";
import { CropOverlay } from "./CropOverlay";

export const CropAreaComponent: React.FC = () => {
  const {
    imageSrc,
    imageError,
    imageLoaded,
    transforms,
    colorAdjustments,
    dragState,
    imgRef,
    containerRef,
    handleMouseDown,
    onImageLoad,
    onImageError,
    onCancel,
  } = useImageCropModal();

  const imageStyle = {
    transform: `scale(${transforms.scale}) rotate(${
      transforms.rotate
    }deg) scaleX(${transforms.flipX ? -1 : 1}) scaleY(${
      transforms.flipY ? -1 : 1
    }) translate(${transforms.imagePosition.x}px, ${
      transforms.imagePosition.y
    }px)`,
    filter: `brightness(${colorAdjustments.brightness}%) contrast(${colorAdjustments.contrast}%) saturate(${colorAdjustments.saturation}%)`,
    maxWidth: "100%",
    maxHeight: "500px",
    display: "block",
    cursor: dragState.isDragging === "pan-image" ? "grabbing" : "grab",
  };

  if (imageError || !imageSrc) {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-gray-100 rounded-lg min-h-[300px]">
        <Icon
          icon="lucide:alert-circle"
          className="w-16 h-16 text-red-400 mb-4"
        />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {!imageSrc ? "No Image Selected" : "Image Failed to Load"}
        </h3>
        <p className="text-sm text-gray-600 text-center mb-4">
          {!imageSrc
            ? "No image source was provided to the crop modal."
            : "The selected image could not be loaded. This might happen if:"}
        </p>
        {!imageSrc ? null : (
          <ul className="text-sm text-gray-600 space-y-1 mb-4">
            <li>• The image file is corrupted</li>
            <li>• The image format is not supported</li>
            <li>• There was a network error</li>
            <li>• The blob URL has been revoked</li>
          </ul>
        )}
        <button
          onClick={onCancel}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Close and Try Again
        </button>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="relative inline-block"
      style={{ maxWidth: "100%", maxHeight: "500px" }}
    >
      <div className="relative">
        <Image
          ref={imgRef}
          alt="Crop me"
          src={imageSrc}
          style={imageStyle}
          onLoad={onImageLoad}
          onError={onImageError}
          draggable={false}
          className="block"
          onMouseDown={(e) => handleMouseDown(e, "pan-image")}
          width={600}
          height={500}
          unoptimized={true}
        />

        {/* Crop Overlay System */}
        {imageLoaded && <CropOverlay />}
      </div>
    </div>
  );
};
