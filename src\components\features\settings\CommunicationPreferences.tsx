"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

interface CommunicationPreferencesProps {
  communicationPrefs: {
    directMessages: boolean;
    onlineStatus: boolean;
  };
  setCommunicationPrefs: React.Dispatch<
    React.SetStateAction<{
      directMessages: boolean;
      onlineStatus: boolean;
    }>
  >;
}

export default function CommunicationPreferences({
  communicationPrefs,
  setCommunicationPrefs,
}: CommunicationPreferencesProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl font-semibold">
          Communication Preferences
        </CardTitle>
        <CardDescription className="text-md text-gray-600">
          How you want to be contacted by other users
        </CardDescription>
        {/* underline div */}
        <div className="w-full h-px bg-gray-300" />
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label className="text-md font-medium">Allow Direct Messages</Label>
            <p className="text-sm text-gray-600">
              Let other users send you direct messages
            </p>
          </div>
          <Switch
            checked={communicationPrefs.directMessages}
            onCheckedChange={(checked) =>
              setCommunicationPrefs((prev) => ({
                ...prev,
                directMessages: checked,
              }))
            }
            className={`${
              communicationPrefs.directMessages
                ? "bg-[#478085] data-[state=checked]:bg-[#478085]"
                : "bg-gray-300 data-[state=unchecked]:bg-gray-300"
            } transition-colors duration-200`}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label className="text-md font-medium">Show Online Status</Label>
            <p className="text-sm text-gray-600">
              Display when you&apos;re online to other users
            </p>
          </div>
          <Switch
            checked={communicationPrefs.onlineStatus}
            onCheckedChange={(checked) =>
              setCommunicationPrefs((prev) => ({
                ...prev,
                onlineStatus: checked,
              }))
            }
            className={`${
              communicationPrefs.onlineStatus
                ? "bg-[#478085] data-[state=checked]:bg-[#478085]"
                : "bg-gray-300 data-[state=unchecked]:bg-gray-300"
            } transition-colors duration-200`}
          />
        </div>
      </CardContent>
    </Card>
  );
}
