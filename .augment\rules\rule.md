---
type: "agent_requested"
description: "Example description"
---

🏗️ E-Commerce Project Development Rules & Best Practices

1. Project Architecture & Structure
   Framework & Technology Stack:

Next.js 15 with App Router (not Pages Router)
TypeScript with strict mode enabled
Tailwind CSS 4 for styling
React 18 with functional components and hooks
Radix UI for accessible component primitives
Folder Structure Rules:

always consider the backend code first c:\Users\<USER>\Desktop\sastobazar

Follow the established src/ structure with clear separation:
app/ - Next.js App Router pages and layouts
components/ - Organized by feature/domain (ui, layout, product, etc.)
context/ - React Context providers
hooks/ - Custom React hooks
types/ - TypeScript type definitions
utils/ - Pure utility functions
data/ - Mock data and constants 2. TypeScript & Code Quality
TypeScript Rules:

Always use strict TypeScript configuration
Define comprehensive interfaces in src/types/ecommerce.ts
Use proper typing for all props, state, and function parameters
Leverage union types for status fields ("active" | "sold" | "inactive")
Use optional properties (?) appropriately
ESLint Rules:

Follow Next.js core web vitals and TypeScript ESLint rules
Unused variables must be prefixed with \_ if intentionally unused
No unused imports or variables allowed 3. State Management
Context Pattern:

Use React Context with useReducer for global state
Maintain separate contexts for different concerns (EcommerceContext, ModalContext)
Follow the established state structure with nested objects for different domains
Always provide proper TypeScript interfaces for context state and actions
State Structure:

4. Component Development
   Component Rules:

Use functional components with hooks exclusively
Implement proper TypeScript interfaces for all props
Follow the established component organization pattern
Use Radix UI primitives for accessible components
Implement proper error boundaries where needed
Icon Usage:

Always use Iconify (@iconify/react) for all icons throughout the application
Import icons using the Icon component: import { Icon } from "@iconify/react"
Use icon names in the format: icon="lucide:icon-name" or "mingcute:icon-name"
Never use other icon libraries (Lucide React, React Icons, etc.) directly
Maintain consistency by using the same icon set (preferably Lucide) across the app
Component Structure:

Export components from components/index.ts for clean imports
Group related components in feature folders
Use composition over inheritance
Implement proper loading and error states 5. Styling & Design System
Tailwind CSS Rules:

Use Tailwind CSS 4 syntax and features
Follow the established color scheme:
Background: #EFEFEF (light gray)
Foreground: #171717 (dark gray)
Use CSS custom properties for theme values
Implement responsive design with mobile-first approach
Use established animation classes (animate-fadeIn, animate-slideIn, etc.)
Layout Rules:

Maintain 5% margin on left and right for containers
Use the established layout structure (Header → Hero → Sidebar + Main Content → Footer)
Implement proper responsive behavior for different screen sizes 6. Data Management
Product Data Rules:

Follow the comprehensive Product interface with all required fields
Implement proper category hierarchy with subcategories
Use consistent currency formatting (Rs. prefix)
Maintain proper seller information structure
Implement proper status management ("active" | "sold" | "inactive" | "hold" | "expired")
Filter System Rules:

Use the established filter architecture with base and category-specific filters
Implement dynamic filter configuration per category
Maintain filter state consistency across components
Use proper TypeScript interfaces for all filter types 7. User Experience & Functionality
Search & Filtering:

Implement debounced search functionality
Support multiple filter combinations
Maintain filter state across navigation
Provide clear filter reset functionality
Navigation Rules:

Use proper slug-based routing for products and categories
Implement breadcrumb navigation
Maintain category dropdown and sidebar functionality
Support keyboard navigation where appropriate 8. Performance & Optimization
Next.js Best Practices:

Use Next.js Image component for optimized images
Implement proper loading states and error boundaries
Use client-side rendering only when necessary ("use client")
Leverage Next.js built-in optimization features
React Performance:

Use useCallback and useMemo appropriately
Implement proper dependency arrays for hooks
Avoid unnecessary re-renders through proper state management 9. Accessibility & User Experience
Accessibility Rules:

Use Radix UI primitives for built-in accessibility
Implement proper ARIA labels and roles
Ensure keyboard navigation support
Maintain proper color contrast ratios
Provide meaningful alt text for images 10. Development Workflow
Package Management:

Use npm as the primary package manager
Always use package managers for dependency management (never edit package.json manually)
Follow semantic versioning for dependencies
Code Style:

Use meaningful variable and function names
Implement proper JSDoc comments for complex functions
Follow consistent naming conventions (camelCase for variables, PascalCase for components)
Maintain clean import organization 11. Testing Strategy
Testing Rules:

Write unit tests for utility functions
Implement integration tests for component interactions
Test context behavior and state management
Use React Testing Library for component testing
Implement E2E tests for critical user workflows 12. Error Handling
Error Management:

Implement comprehensive error boundaries
Provide meaningful error messages to users
Handle loading states consistently
Implement proper fallback UI for failed states 13. Security & Validation
Data Validation:

Validate all user inputs
Implement proper form validation
Sanitize data before processing
Use TypeScript for compile-time type safety 14. Future-Proofing
Scalability Considerations:

Design components for reusability
Implement proper separation of concerns
Plan for API integration (currently using mock data)
Consider internationalization support
Design for mobile app compatibility 15. Specific Project Preferences
Based on the memories, maintain these user preferences:

Background color: #EFEFEF
Logo font: Bungee from Google Fonts
Component organization in separate folders
Category dropdown with filter hiding functionality
Full-width footer
Hero section in right content area only
Breadcrumb navigation with arrow icons
Specific layout patterns for ProductGrid and navigation
These rules should be consistently applied across all development work on this e-commerce project to maintain code quality, consistency, and user experience standards.
