"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useGetAdvertisementsQuery, useGetAdvertisementByIdQuery } from "@/store/api/advertisementApi";
import { convertAdvertisementToProduct, extractIdFromSlug, generateSlug } from "@/utils/slug-utils";

export default function TestProductDetail() {
  const [testSlug, setTestSlug] = useState("");
  const [testId, setTestId] = useState("");

  // Get some advertisements to test with
  const { data: adsData } = useGetAdvertisementsQuery({ page: 1, limit: 5 });
  
  // Test getting advertisement by ID
  const { data: adData, error: adError, isLoading: adLoading } = useGetAdvertisementByIdQuery(testId, {
    skip: !testId,
  });

  const handleTestSlug = () => {
    const extractedId = extractIdFromSlug(testSlug);
    setTestId(extractedId || "");
  };

  const handleTestWithSampleAd = () => {
    if (adsData?.data?.[0]) {
      const sampleAd = adsData.data[0];
      const generatedSlug = generateSlug(sampleAd.title, sampleAd.id);
      setTestSlug(generatedSlug);
      setTestId(sampleAd.id);
    }
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Product Detail Debug Tool</h1>
      
      {/* Sample Advertisements */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Sample Advertisements</h2>
        {adsData?.data?.slice(0, 3).map((ad) => {
          const product = convertAdvertisementToProduct(ad);
          return (
            <div key={ad.id} className="border p-4 mb-4 rounded-lg">
              <h3 className="font-medium">{ad.title}</h3>
              <p className="text-sm text-gray-600">ID: {ad.id}</p>
              <p className="text-sm text-gray-600">Generated Slug: {product.slug}</p>
              <p className="text-sm text-gray-600">Extracted ID: {extractIdFromSlug(product.slug)}</p>
              <Button 
                size="sm" 
                onClick={() => {
                  setTestSlug(product.slug);
                  setTestId(ad.id);
                }}
                className="mt-2"
              >
                Test This Product
              </Button>
            </div>
          );
        })}
        
        <Button onClick={handleTestWithSampleAd} className="mb-4">
          Auto-Test with First Ad
        </Button>
      </div>

      {/* Manual Testing */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Manual Testing</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Test Slug:</label>
            <Input
              value={testSlug}
              onChange={(e) => setTestSlug(e.target.value)}
              placeholder="Enter a product slug to test"
            />
          </div>
          
          <Button onClick={handleTestSlug}>Extract ID from Slug</Button>
          
          <div>
            <label className="block text-sm font-medium mb-2">Extracted ID:</label>
            <Input
              value={testId}
              onChange={(e) => setTestId(e.target.value)}
              placeholder="Product ID will appear here"
            />
          </div>
        </div>
      </div>

      {/* API Test Results */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">API Test Results</h2>
        
        {testId && (
          <div className="border p-4 rounded-lg">
            <h3 className="font-medium mb-2">Testing ID: {testId}</h3>
            
            {adLoading && <p>Loading...</p>}
            
            {adError && (
              <div className="text-red-600">
                <p>Error: {JSON.stringify(adError)}</p>
              </div>
            )}
            
            {adData && (
              <div className="text-green-600">
                <p>✅ Advertisement found!</p>
                <p>Title: {adData.title}</p>
                <p>Status: {adData.status}</p>
                <p>Created: {adData.createdAt}</p>
                
                <div className="mt-4">
                  <h4 className="font-medium">Converted Product:</h4>
                  <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                    {JSON.stringify(convertAdvertisementToProduct(adData), null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Debug Info */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Debug Information</h2>
        <div className="bg-gray-100 p-4 rounded-lg">
          <p><strong>API Base URL:</strong> {process.env.NEXT_PUBLIC_API_BASE_URL}</p>
          <p><strong>Total Ads Available:</strong> {adsData?.meta?.total || 'Loading...'}</p>
          <p><strong>Current Test Slug:</strong> {testSlug || 'None'}</p>
          <p><strong>Current Test ID:</strong> {testId || 'None'}</p>
        </div>
      </div>

      {/* Test URL Generation */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Test URL Generation</h2>
        <div className="space-y-2">
          <p><strong>Product Detail URL:</strong> /product/{testSlug || '[slug]'}</p>
          <p><strong>Direct Link:</strong> 
            <a 
              href={`/product/${testSlug}`} 
              className="text-blue-600 hover:underline ml-2"
              target="_blank"
              rel="noopener noreferrer"
            >
              {testSlug ? 'Test Link' : 'No slug to test'}
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
