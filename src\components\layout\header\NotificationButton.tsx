"use client";
import { useState } from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Mock notification data
const mockNotifications = [
  {
    id: "1",
    title: "New message from <PERSON>",
    description: "Interested in your iPhone 14 Pro listing",
    time: "2 minutes ago",
    isRead: false,
    type: "message",
    icon: "material-symbols:message",
  },
  {
    id: "2",
    title: "Price drop alert",
    description: "MacBook Pro M3 is now 15% off",
    time: "1 hour ago",
    isRead: false,
    type: "price",
    icon: "material-symbols:trending-down",
  },
  {
    id: "3",
    title: "Your ad expires soon",
    description: "Samsung Galaxy S24 listing expires in 2 days",
    time: "3 hours ago",
    isRead: true,
    type: "warning",
    icon: "material-symbols:warning",
  },
  {
    id: "4",
    title: "New review received",
    description: "You received a 5-star review from <PERSON>",
    time: "1 day ago",
    isRead: true,
    type: "review",
    icon: "material-symbols:star",
  },
  {
    id: "5",
    title: "Payment received",
    description: "Rs. 25,000 payment confirmed for your sale",
    time: "2 days ago",
    isRead: true,
    type: "payment",
    icon: "material-symbols:payments",
  },
];

export function NotificationButton() {
  const [notifications, setNotifications] = useState(mockNotifications);
  const unreadCount = notifications.filter((n) => !n.isRead).length;

  const markAsRead = (id: string) => {
    setNotifications((prev) =>
      prev.map((notification) =>
        notification.id === id
          ? { ...notification, isRead: true }
          : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications((prev) =>
      prev.map((notification) => ({ ...notification, isRead: true }))
    );
  };

  const getNotificationTypeColor = (type: string) => {
    switch (type) {
      case "message":
        return "text-blue-600";
      case "price":
        return "text-green-600";
      case "warning":
        return "text-orange-600";
      case "review":
        return "text-yellow-600";
      case "payment":
        return "text-purple-600";
      default:
        return "text-gray-600";
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="text-white hover:text-gray-200 hover:bg-white/10 transition-all duration-300 group p-0 relative w-10 h-10"
        >
          <div className="w-full h-full bg-[#1F5E64] border border-white rounded-lg flex items-center justify-center hover:bg-[#1a5157] transition-all duration-300">
            <Icon
              icon="material-symbols:notifications"
              className="w-5 h-5 group-hover:scale-110 transition-transform duration-300"
            />
          </div>
          {unreadCount > 0 && (
            <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 bg-red-500 hover:bg-red-600 text-white text-xs rounded-full flex items-center justify-center animate-pulse">
              {unreadCount > 99 ? "99+" : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-80 bg-white border border-gray-200 shadow-lg rounded-lg p-0 max-h-96 overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-100">
          <h3 className="font-semibold text-gray-900">Notifications</h3>
          {unreadCount > 0 && (
            <button
              onClick={markAllAsRead}
              className="text-sm text-blue-600 hover:text-blue-700 font-medium"
            >
              Mark all as read
            </button>
          )}
        </div>

        {/* Notifications List */}
        <div className="max-h-80 overflow-y-auto">
          {notifications.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              <Icon
                icon="material-symbols:notifications-off"
                className="w-12 h-12 mx-auto mb-3 text-gray-300"
              />
              <p>No notifications yet</p>
            </div>
          ) : (
            <div className="space-y-0">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  onClick={() => markAsRead(notification.id)}
                  className={`p-4 border-b border-gray-50 cursor-pointer hover:bg-gray-50 transition-colors ${
                    !notification.isRead
                      ? "bg-blue-25 border-l-4 border-l-blue-500"
                      : ""
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <div
                      className={`p-2 rounded-full bg-gray-100 ${getNotificationTypeColor(
                        notification.type
                      )}`}
                    >
                      <Icon icon={notification.icon} className="w-4 h-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <p className="font-medium text-gray-900 text-sm truncate">
                          {notification.title}
                        </p>
                        {!notification.isRead && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-1 line-clamp-2">
                        {notification.description}
                      </p>
                      <p className="text-xs text-gray-500">
                        {notification.time}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        {notifications.length > 0 && (
          <div className="p-3 border-t border-gray-100 bg-gray-50">
            <button className="w-full text-center text-sm text-blue-600 hover:text-blue-700 font-medium py-1">
              View all notifications
            </button>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
