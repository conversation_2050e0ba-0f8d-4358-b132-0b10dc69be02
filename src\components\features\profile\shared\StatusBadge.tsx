"use client";

import React from "react";
import { Icon } from "@iconify/react";
import { Badge } from "@/components/ui/badge";

interface StatusBadgeProps {
  status: string;
  className?: string;
}

export function StatusBadge({ status, className = "" }: StatusBadgeProps) {
  const getStatusBadge = (status: string) => {
    const normalizedStatus = status.toLowerCase();
    
    switch (normalizedStatus) {
      case "active":
        return (
          <Badge className={`bg-emerald-100 text-emerald-800 border-emerald-200 shadow-sm ${className}`}>
            <div className="w-2 h-2 bg-emerald-500 rounded-full mr-1"></div>
            Active
          </Badge>
        );
      case "sold":
        return (
          <Badge className={`bg-blue-100 text-blue-800 border-blue-200 shadow-sm ${className}`}>
            <Icon icon="lucide:check-circle-2" className="w-3 h-3 mr-1" />
            Sold
          </Badge>
        );
      case "hold":
        return (
          <Badge className={`bg-amber-100 text-amber-800 border-amber-200 shadow-sm ${className}`}>
            <div className="w-2 h-2 bg-amber-500 rounded-full mr-1"></div>
            Hold
          </Badge>
        );
      case "expired":
        return (
          <Badge className={`bg-red-100 text-red-800 border-red-200 shadow-sm ${className}`}>
            <div className="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
            Expired
          </Badge>
        );
      case "inactive":
        return (
          <Badge className={`bg-gray-100 text-gray-800 border-gray-200 shadow-sm ${className}`}>
            Inactive
          </Badge>
        );
      default:
        return (
          <Badge className={`bg-gray-100 text-gray-800 border-gray-200 shadow-sm ${className}`}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
        );
    }
  };

  return getStatusBadge(status);
}
