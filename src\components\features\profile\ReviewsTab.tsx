"use client";

import React, { useState } from "react";
import Image from "next/image";
import { Icon } from "@iconify/react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface Review {
  id: string;
  reviewer: {
    name: string;
    avatar?: string;
    verified: boolean;
  };
  rating: number;
  comment: string;
  date: Date;
  product: {
    title: string;
    image: string;
  };
  type: "seller" | "buyer";
  helpful: number;
}

const mockReviews: Review[] = [
  {
    id: "1",
    reviewer: {
      name: "<PERSON>",
      avatar:
        "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face",
      verified: true,
    },
    rating: 5,
    comment:
      "Excellent seller! The item was exactly as described and shipped quickly. Great communication throughout the process.",
    date: new Date("2024-01-15"),
    product: {
      title: "iPhone 14 Pro Max",
      image:
        "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=60&h=60&fit=crop",
    },
    type: "seller",
    helpful: 12,
  },
  {
    id: "2",
    reviewer: {
      name: "<PERSON>",
      avatar:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face",
      verified: true,
    },
    rating: 4,
    comment:
      "Good buyer, paid promptly and was very understanding about the delivery delay. Would recommend!",
    date: new Date("2024-01-10"),
    product: {
      title: "MacBook Air M2",
      image:
        "https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=60&h=60&fit=crop",
    },
    type: "buyer",
    helpful: 8,
  },
  {
    id: "3",
    reviewer: {
      name: "Emily Davis",
      verified: false,
    },
    rating: 5,
    comment:
      "Amazing experience! The product was in perfect condition and the seller was very helpful with all my questions.",
    date: new Date("2024-01-05"),
    product: {
      title: "Sony WH-1000XM4",
      image:
        "https://images.unsplash.com/photo-1583394838336-acd977736f90?w=60&h=60&fit=crop",
    },
    type: "seller",
    helpful: 15,
  },
];

export default function ReviewsTab() {
  const [filterType, setFilterType] = useState<"all" | "seller" | "buyer">(
    "all"
  );
  const [sortBy, setSortBy] = useState<"recent" | "rating" | "helpful">(
    "recent"
  );

  const filteredReviews = mockReviews.filter(
    (review) => filterType === "all" || review.type === filterType
  );

  const averageRating =
    mockReviews.reduce((sum, review) => sum + review.rating, 0) /
    mockReviews.length;
  const sellerReviews = mockReviews.filter((r) => r.type === "seller");
  const buyerReviews = mockReviews.filter((r) => r.type === "buyer");

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Icon
        key={i}
        icon="mdi:star"
        className={`w-4 h-4 ${
          i < rating ? "text-yellow-400" : "text-gray-300"
        }`}
        style={{ fill: i < rating ? "#facc15" : undefined }}
      />
    ));
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">
            Reviews & Ratings
          </h2>
          <p className="text-gray-600 mt-2">
            Customer reviews and ratings for your transactions
          </p>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-gray-200 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-yellow-100 rounded-lg">
                <Icon icon="mdi:star" className="w-6 h-6 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Average Rating</p>
                <div className="flex items-center gap-2">
                  <p className="text-2xl font-bold text-gray-900">
                    {averageRating.toFixed(1)}
                  </p>
                  <div className="flex">
                    {renderStars(Math.round(averageRating))}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-gray-200 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Icon
                  icon="mdi:message-outline"
                  className="w-6 h-6 text-blue-600"
                />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Reviews</p>
                <p className="text-2xl font-bold text-gray-900">
                  {mockReviews.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-gray-200 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-green-100 rounded-lg">
                <Icon
                  icon="mdi:package-variant"
                  className="w-6 h-6 text-green-600"
                />
              </div>
              <div>
                <p className="text-sm text-gray-600">As Seller</p>
                <p className="text-2xl font-bold text-gray-900">
                  {sellerReviews.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-gray-200 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-purple-100 rounded-lg">
                <Icon
                  icon="mdi:shopping-outline"
                  className="w-6 h-6 text-purple-600"
                />
              </div>
              <div>
                <p className="text-sm text-gray-600">As Buyer</p>
                <p className="text-2xl font-bold text-gray-900">
                  {buyerReviews.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Rating Breakdown */}
      <Card className="border-gray-200 shadow-sm">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Rating Breakdown
          </h3>
          <div className="space-y-3">
            {[5, 4, 3, 2, 1].map((rating) => {
              const count = mockReviews.filter(
                (r) => r.rating === rating
              ).length;
              const percentage = (count / mockReviews.length) * 100;

              return (
                <div key={rating} className="flex items-center gap-4">
                  <div className="flex items-center gap-1 w-16">
                    <span className="text-sm font-medium">{rating}</span>
                    <Icon
                      icon="mdi:star"
                      className="w-3 h-3 text-yellow-400"
                      style={{ fill: "#facc15" }}
                    />
                  </div>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 w-12">{count}</span>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card className="border-gray-200 shadow-sm">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4 justify-between">
            {/* Type Filter */}
            <div className="flex gap-2">
              {[
                { id: "all", label: "All Reviews" },
                { id: "seller", label: "As Seller" },
                { id: "buyer", label: "As Buyer" },
              ].map((filter) => (
                <button
                  key={filter.id}
                  onClick={() =>
                    setFilterType(filter.id as "all" | "seller" | "buyer")
                  }
                  className={`
                    px-4 py-2 rounded-lg transition-all duration-300 border-none cursor-pointer
                    ${
                      filterType === filter.id
                        ? "bg-teal-100 text-teal-700 border border-teal-200"
                        : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                    }
                  `}
                >
                  {filter.label}
                </button>
              ))}
            </div>

            {/* Sort Options */}
            <div className="flex items-center gap-2">
              <Icon
                icon="mdi:filter-variant"
                className="w-4 h-4 text-gray-400"
              />
              <select
                value={sortBy}
                onChange={(e) =>
                  setSortBy(e.target.value as "recent" | "rating" | "helpful")
                }
                className="border border-gray-200 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
              >
                <option value="recent">Most Recent</option>
                <option value="rating">Highest Rating</option>
                <option value="helpful">Most Helpful</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reviews List */}
      <div className="space-y-4">
        {filteredReviews.map((review) => (
          <Card key={review.id} className="border-gray-200 shadow-sm">
            <CardContent className="p-6">
              <div className="flex gap-4">
                {/* Reviewer Avatar */}
                <div className="flex-shrink-0">
                  {review.reviewer.avatar ? (
                    <Image
                      src={review.reviewer.avatar}
                      alt={review.reviewer.name}
                      width={48}
                      height={48}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                      <Icon
                        icon="mdi:account"
                        className="w-6 h-6 text-gray-400"
                      />
                    </div>
                  )}
                </div>

                {/* Review Content */}
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-semibold text-gray-900">
                          {review.reviewer.name}
                        </h4>
                        {review.reviewer.verified && (
                          <Badge className="bg-blue-100 text-blue-800 border-blue-200 text-xs">
                            Verified
                          </Badge>
                        )}
                        <Badge
                          className={`text-xs ${
                            review.type === "seller"
                              ? "bg-green-100 text-green-800 border-green-200"
                              : "bg-purple-100 text-purple-800 border-purple-200"
                          }`}
                        >
                          {review.type === "seller"
                            ? "Reviewed you as seller"
                            : "Reviewed you as buyer"}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 mb-2">
                        <div className="flex">{renderStars(review.rating)}</div>
                        <span className="text-sm text-gray-500">
                          {formatDate(review.date)}
                        </span>
                      </div>
                    </div>
                  </div>

                  <p className="text-gray-700 mb-3">{review.comment}</p>

                  {/* Product Info */}
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg mb-3">
                    <Image
                      src={review.product.image}
                      alt={review.product.title}
                      width={40}
                      height={40}
                      className="w-10 h-10 rounded object-cover"
                    />
                    <span className="text-sm text-gray-600">
                      Product: {review.product.title}
                    </span>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-4 text-sm">
                    <button className="flex items-center gap-1 text-gray-500 hover:text-gray-700">
                      <Icon icon="mdi:thumb-up-outline" className="w-4 h-4" />
                      <span>Helpful ({review.helpful})</span>
                    </button>
                    <button className="text-gray-500 hover:text-gray-700">
                      Reply
                    </button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredReviews.length === 0 && (
        <Card className="border-gray-200 shadow-sm">
          <CardContent className="p-12">
            <div className="text-center">
              <Icon
                icon="mdi:star"
                className="w-16 h-16 text-gray-400 mx-auto mb-4"
              />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                No reviews yet
              </h3>
              <p className="text-gray-600 max-w-md mx-auto">
                Start selling or buying to receive your first reviews. Great
                reviews help build trust with other users!
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
