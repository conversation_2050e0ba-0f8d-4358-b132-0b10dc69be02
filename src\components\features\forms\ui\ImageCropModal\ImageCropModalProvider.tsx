"use client";
import React, {
  createContext,
  useContext,
  useState,
  useRef,
  useCallback,
  useEffect,
} from "react";
import { useImageHandler } from "@/hooks";
import {
  CropArea,
  ImageDimensions,
  HistoryState,
  ImageTransforms,
  ColorAdjustments,
  DragState,
  ImageCropModalContextType,
} from "./types";

const ImageCropModalContext = createContext<ImageCropModalContextType | null>(
  null
);

export const useImageCropModal = () => {
  const context = useContext(ImageCropModalContext);
  if (!context) {
    throw new Error(
      "useImageCropModal must be used within ImageCropModalProvider"
    );
  }
  return context;
};

interface ImageCropModalProviderProps {
  children: React.ReactNode;
  imageSrc: string;
  onCropComplete: (croppedImageUrl: string) => void;
  onCancel: () => void;
}

export const ImageCropModalProvider: React.FC<ImageCropModalProviderProps> = ({
  children,
  imageSrc,
  onCropComplete,
  onCancel,
}) => {
  // State
  const [crop, setCrop] = useState<CropArea>({
    x: 50,
    y: 50,
    width: 200,
    height: 200,
  });

  const [aspect, setAspect] = useState<number | undefined>(1);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [imageDimensions, setImageDimensions] = useState<ImageDimensions>({
    width: 0,
    height: 0,
  });

  const [transforms, setTransforms] = useState<ImageTransforms>({
    scale: 1,
    rotate: 0,
    flipX: false,
    flipY: false,
    imagePosition: { x: 0, y: 0 },
  });

  const [colorAdjustments, setColorAdjustments] = useState<ColorAdjustments>({
    brightness: 100,
    contrast: 100,
    saturation: 100,
  });

  const [history, setHistory] = useState<HistoryState[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  const [dragState, setDragState] = useState<DragState>({
    isDragging: null,
    dragStart: { x: 0, y: 0 },
  });

  // Refs
  const imgRef = useRef<HTMLImageElement>(null);
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);
  const hiddenCanvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Image handler for error handling
  const { handleImageError, handleImageLoad: handleImageLoadHook } =
    useImageHandler({
      enableLogging: true,
      onError: () => setImageError(true),
      onLoad: () => setImageError(false),
    });

  // Helper functions
  const updateTransforms = useCallback((updates: Partial<ImageTransforms>) => {
    setTransforms((prev) => ({ ...prev, ...updates }));
  }, []);

  const updateColorAdjustments = useCallback(
    (updates: Partial<ColorAdjustments>) => {
      setColorAdjustments((prev) => ({ ...prev, ...updates }));
    },
    []
  );

  // Save state to history
  const saveToHistory = useCallback(() => {
    const newState: HistoryState = {
      crop,
      scale: transforms.scale,
      rotate: transforms.rotate,
      flipX: transforms.flipX,
      flipY: transforms.flipY,
      imagePosition: transforms.imagePosition,
    };

    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(newState);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  }, [crop, transforms, history, historyIndex]);

  // Initialize crop area when image loads
  const onImageLoad = useCallback(
    (e: React.SyntheticEvent<HTMLImageElement>) => {
      handleImageLoadHook();

      const img = e.currentTarget;
      const rect = img.getBoundingClientRect();
      setImageDimensions({ width: rect.width, height: rect.height });
      setImageLoaded(true);

      // Center the crop area based on actual displayed image size
      const cropSize = Math.min(rect.width, rect.height) * 0.6;
      const newCrop = {
        x: (rect.width - cropSize) / 2,
        y: (rect.height - cropSize) / 2,
        width: cropSize,
        height: cropSize,
      };
      setCrop(newCrop);

      // Initialize history
      setTimeout(() => saveToHistory(), 100);
    },
    [saveToHistory, handleImageLoadHook]
  );

  // Handle image error
  const onImageError = useCallback(
    (e: React.SyntheticEvent<HTMLImageElement>) => {
      handleImageError(e);
      setImageLoaded(false);
    },
    [handleImageError]
  );

  // Undo functionality
  const handleUndo = useCallback(() => {
    if (historyIndex > 0) {
      const prevState = history[historyIndex - 1];
      setCrop(prevState.crop);
      setTransforms({
        scale: prevState.scale,
        rotate: prevState.rotate,
        flipX: prevState.flipX,
        flipY: prevState.flipY,
        imagePosition: prevState.imagePosition,
      });
      setHistoryIndex(historyIndex - 1);
    }
  }, [history, historyIndex]);

  // Redo functionality
  const handleRedo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const nextState = history[historyIndex + 1];
      setCrop(nextState.crop);
      setTransforms({
        scale: nextState.scale,
        rotate: nextState.rotate,
        flipX: nextState.flipX,
        flipY: nextState.flipY,
        imagePosition: nextState.imagePosition,
      });
      setHistoryIndex(historyIndex + 1);
    }
  }, [history, historyIndex]);

  // Zoom functionality
  const handleZoom = useCallback(
    (delta: number) => {
      updateTransforms({
        scale: Math.max(0.1, Math.min(5, transforms.scale + delta)),
      });
      setTimeout(() => saveToHistory(), 100);
    },
    [transforms.scale, updateTransforms, saveToHistory]
  );

  const handleRotate = useCallback(
    (degrees: number) => {
      updateTransforms({
        rotate: (transforms.rotate + degrees) % 360,
      });
      setTimeout(() => saveToHistory(), 100);
    },
    [transforms.rotate, updateTransforms, saveToHistory]
  );

  const handleFlipHorizontal = useCallback(() => {
    updateTransforms({
      flipX: !transforms.flipX,
    });
    setTimeout(() => saveToHistory(), 100);
  }, [transforms.flipX, updateTransforms, saveToHistory]);

  const handleFlipVertical = useCallback(() => {
    updateTransforms({
      flipY: !transforms.flipY,
    });
    setTimeout(() => saveToHistory(), 100);
  }, [transforms.flipY, updateTransforms, saveToHistory]);

  const handleAspectChange = useCallback(
    (newAspect: number | undefined) => {
      setAspect(newAspect);
      if (newAspect && imageLoaded) {
        const { width, height } = crop;
        const currentAspect = width / height;

        if (Math.abs(currentAspect - newAspect) > 0.01) {
          if (currentAspect > newAspect) {
            setCrop((prev) => ({ ...prev, height: width / newAspect }));
          } else {
            setCrop((prev) => ({ ...prev, width: height * newAspect }));
          }
        }
      }
      setTimeout(() => saveToHistory(), 100);
    },
    [crop, imageLoaded, saveToHistory]
  );

  const resetAll = useCallback(() => {
    setTransforms({
      scale: 1,
      rotate: 0,
      flipX: false,
      flipY: false,
      imagePosition: { x: 0, y: 0 },
    });
    setColorAdjustments({
      brightness: 100,
      contrast: 100,
      saturation: 100,
    });

    if (imageLoaded && imgRef.current) {
      const rect = imgRef.current.getBoundingClientRect();
      const cropSize = Math.min(rect.width, rect.height) * 0.6;
      setCrop({
        x: (rect.width - cropSize) / 2,
        y: (rect.height - cropSize) / 2,
        width: cropSize,
        height: cropSize,
      });
      setImageDimensions({ width: rect.width, height: rect.height });
    }

    setTimeout(() => saveToHistory(), 100);
  }, [imageLoaded, saveToHistory]);

  // Crop completion handler
  const handleCropComplete = useCallback(async () => {
    if (!imgRef.current || !hiddenCanvasRef.current || !imageLoaded) return;

    const canvas = hiddenCanvasRef.current;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const img = imgRef.current;
    const { x, y, width, height } = crop;

    // Set final canvas size
    const scaleX = img.naturalWidth / img.clientWidth;
    const scaleY = img.naturalHeight / img.clientHeight;

    canvas.width = width * scaleX;
    canvas.height = height * scaleY;

    // Apply transformations
    ctx.save();

    // Move to center for transformations
    ctx.translate(canvas.width / 2, canvas.height / 2);

    // Apply transformations
    ctx.scale(transforms.flipX ? -1 : 1, transforms.flipY ? -1 : 1);
    ctx.rotate((transforms.rotate * Math.PI) / 180);

    // Apply filters
    ctx.filter = `brightness(${colorAdjustments.brightness}%) contrast(${colorAdjustments.contrast}%) saturate(${colorAdjustments.saturation}%)`;

    // Draw the cropped area
    ctx.drawImage(
      img,
      x * scaleX,
      y * scaleY,
      width * scaleX,
      height * scaleY,
      -canvas.width / 2,
      -canvas.height / 2,
      canvas.width,
      canvas.height
    );

    ctx.restore();

    canvas.toBlob(
      (blob) => {
        if (blob) {
          const croppedImageUrl = URL.createObjectURL(blob);
          onCropComplete(croppedImageUrl);
        }
      },
      "image/jpeg",
      0.95
    );
  }, [
    crop,
    onCropComplete,
    colorAdjustments,
    imageLoaded,
    transforms,
    imgRef,
    hiddenCanvasRef,
  ]);

  const handleMouseDown = useCallback((e: React.MouseEvent, action: string) => {
    e.preventDefault();
    e.stopPropagation();

    setDragState({
      isDragging: action,
      dragStart: { x: e.clientX, y: e.clientY },
    });
  }, []);

  // Mouse move handler
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!dragState.isDragging || !imgRef.current) return;

      const deltaX = e.clientX - dragState.dragStart.x;
      const deltaY = e.clientY - dragState.dragStart.y;

      // Constrain crop to aspect ratio
      const constrainCrop = (newCrop: CropArea): CropArea => {
        if (!aspect) return newCrop;

        const { width, height } = newCrop;
        const currentAspect = width / height;

        if (Math.abs(currentAspect - aspect) > 0.01) {
          if (currentAspect > aspect) {
            return { ...newCrop, height: width / aspect };
          } else {
            return { ...newCrop, width: height * aspect };
          }
        }

        return newCrop;
      };

      if (dragState.isDragging === "move-crop") {
        setCrop((prev) =>
          constrainCrop({
            ...prev,
            x: Math.max(
              0,
              Math.min(imageDimensions.width - prev.width, prev.x + deltaX)
            ),
            y: Math.max(
              0,
              Math.min(imageDimensions.height - prev.height, prev.y + deltaY)
            ),
          })
        );
      } else if (dragState.isDragging.startsWith("resize-")) {
        // Handle resize operations
        const direction = dragState.isDragging.replace("resize-", "");

        setCrop((prev) => {
          const newCrop = { ...prev };

          switch (direction) {
            case "se":
              newCrop.width = Math.max(
                50,
                Math.min(prev.width + deltaX, imageDimensions.width - prev.x)
              );
              newCrop.height = Math.max(
                50,
                Math.min(prev.height + deltaY, imageDimensions.height - prev.y)
              );
              break;
            case "nw":
              const newWidth = Math.max(50, prev.width - deltaX);
              const newHeight = Math.max(50, prev.height - deltaY);
              newCrop.x = Math.max(0, prev.x + deltaX);
              newCrop.y = Math.max(0, prev.y + deltaY);
              newCrop.width = newWidth;
              newCrop.height = newHeight;
              break;
            case "ne":
              newCrop.width = Math.max(
                50,
                Math.min(prev.width + deltaX, imageDimensions.width - prev.x)
              );
              newCrop.height = Math.max(50, prev.height - deltaY);
              newCrop.y = Math.max(0, prev.y + deltaY);
              break;
            case "sw":
              newCrop.width = Math.max(50, prev.width - deltaX);
              newCrop.height = Math.max(
                50,
                Math.min(prev.height + deltaY, imageDimensions.height - prev.y)
              );
              newCrop.x = Math.max(0, prev.x + deltaX);
              break;
            case "n":
              newCrop.height = Math.max(50, prev.height - deltaY);
              newCrop.y = Math.max(0, prev.y + deltaY);
              break;
            case "s":
              newCrop.height = Math.max(
                50,
                Math.min(prev.height + deltaY, imageDimensions.height - prev.y)
              );
              break;
            case "w":
              newCrop.width = Math.max(50, prev.width - deltaX);
              newCrop.x = Math.max(0, prev.x + deltaX);
              break;
            case "e":
              newCrop.width = Math.max(
                50,
                Math.min(prev.width + deltaX, imageDimensions.width - prev.x)
              );
              break;
          }

          return constrainCrop(newCrop);
        });
      } else if (dragState.isDragging === "pan-image") {
        updateTransforms({
          imagePosition: {
            x: transforms.imagePosition.x + deltaX,
            y: transforms.imagePosition.y + deltaY,
          },
        });
      }

      setDragState((prev) => ({
        ...prev,
        dragStart: { x: e.clientX, y: e.clientY },
      }));
    },
    [
      dragState,
      aspect,
      imageDimensions,
      setCrop,
      updateTransforms,
      transforms.imagePosition,
      imgRef,
    ]
  );

  const handleMouseUp = useCallback(() => {
    if (dragState.isDragging) {
      setDragState((prev) => ({ ...prev, isDragging: null }));
      setTimeout(() => saveToHistory(), 100);
    }
  }, [dragState.isDragging, saveToHistory]);

  // Add global mouse event listeners
  useEffect(() => {
    if (dragState.isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [dragState.isDragging, handleMouseMove, handleMouseUp]);

  // Update image dimensions when transformations change
  useEffect(() => {
    if (imageLoaded && imgRef.current) {
      const rect = imgRef.current.getBoundingClientRect();
      setImageDimensions({ width: rect.width, height: rect.height });
    }
  }, [transforms.scale, transforms.rotate, imageLoaded]);

  // Reset states when modal opens/closes
  useEffect(() => {
    setImageError(false);
    setImageLoaded(false);
    setHistory([]);
    setHistoryIndex(-1);

    // Validate image source
    if (!imageSrc || imageSrc.trim() === "") {
      console.warn("ImageCropModal: No image source provided");
      setImageError(true);
    }
  }, [imageSrc]);

  const contextValue: ImageCropModalContextType = {
    // Image state
    imageSrc,
    imageLoaded,
    imageError,
    imageDimensions,

    // Crop state
    crop,
    aspect,

    // Transform state
    transforms,

    // Color adjustments
    colorAdjustments,

    // History
    history,
    historyIndex,

    // Drag state
    dragState,

    // Refs
    imgRef,
    previewCanvasRef,
    hiddenCanvasRef,
    containerRef,

    // Actions
    setCrop,
    setAspect,
    updateTransforms,
    updateColorAdjustments,
    handleUndo,
    handleRedo,
    handleZoom,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
    handleAspectChange,
    resetAll,
    handleCropComplete,
    handleMouseDown,

    // Image handlers
    onImageLoad,
    onImageError,

    // Callbacks
    onCancel,
  };

  return (
    <ImageCropModalContext.Provider value={contextValue}>
      {children}
    </ImageCropModalContext.Provider>
  );
};
