"use client";

import { useState } from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Product } from "@/types/ecommerce";
import Image from "next/image";

interface ProductComparisonProps {
  currentProduct: Product;
  similarProducts: Product[];
  className?: string;
}

interface ComparisonFeature {
  key: string;
  label: string;
  getValue: (product: Product) => string | number | boolean | Date;
  format?: (value: unknown) => string;
}

const comparisonFeatures: ComparisonFeature[] = [
  {
    key: "price",
    label: "Price",
    getValue: (product) => product.price,
    format: (value) => `${(value as number).toLocaleString()}`,
  },
  {
    key: "condition",
    label: "Condition",
    getValue: (product) => product.condition,
    format: (value) =>
      (value as string).charAt(0).toUpperCase() + (value as string).slice(1),
  },
  {
    key: "brand",
    label: "Brand",
    getValue: (product) => product.brand || "Not specified",
  },
  {
    key: "location",
    label: "Location",
    getValue: (product) => product.location,
  },
  {
    key: "delivery",
    label: "Delivery Available",
    getValue: (product) => product.delivery.available,
    format: (value) => (value ? "Yes" : "No"),
  },
  {
    key: "seller_rating",
    label: "Seller Rating",
    getValue: (product) => product.seller.rating || 0,
    format: (value) => `${value}/5`,
  },
  {
    key: "posted_date",
    label: "Posted",
    getValue: (product) => product.postedAt,
    format: (value) => new Date(value as string).toLocaleDateString(),
  },
];

const generatePlaceholderUrl = (
  width: number,
  height: number,
  text: string
) => {
  return `https://via.placeholder.com/${width}x${height}?text=${encodeURIComponent(
    text
  )}`;
};

export function ProductComparison({
  currentProduct,
  similarProducts,
  className = "",
}: ProductComparisonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<Product[]>([
    currentProduct,
  ]);
  const maxComparisons = 3;

  const addToComparison = (product: Product) => {
    if (
      selectedProducts.length < maxComparisons &&
      !selectedProducts.find((p) => p.id === product.id)
    ) {
      setSelectedProducts([...selectedProducts, product]);
    }
  };

  const removeFromComparison = (productId: string) => {
    if (selectedProducts.length > 1) {
      setSelectedProducts(selectedProducts.filter((p) => p.id !== productId));
    }
  };

  const getComparisonValue = (feature: ComparisonFeature, product: Product) => {
    const value = feature.getValue(product);
    return feature.format ? feature.format(value) : String(value);
  };

  const getBestValue = (feature: ComparisonFeature) => {
    if (feature.key === "price") {
      return Math.min(
        ...selectedProducts.map((p) => feature.getValue(p) as number)
      );
    }
    if (feature.key === "seller_rating") {
      return Math.max(
        ...selectedProducts.map((p) => feature.getValue(p) as number)
      );
    }
    return null;
  };

  const isHighlighted = (feature: ComparisonFeature, product: Product) => {
    const bestValue = getBestValue(feature);
    if (bestValue === null) return false;

    const currentValue = feature.getValue(product);
    return currentValue === bestValue;
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={`text-blue-600 border-blue-200 hover:bg-blue-50 ${className}`}
        >
          <Icon icon="mdi:arrow-left-right" className="h-4 w-4 mr-1" />
          Compare
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Icon
              icon="mdi:arrow-left-right"
              className="h-5 w-5 text-blue-600"
            />
            Product Comparison
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Add Products Section */}
          {selectedProducts.length < maxComparisons && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">
                Add Products to Compare (Max {maxComparisons})
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {similarProducts
                  .filter(
                    (product) =>
                      !selectedProducts.find((p) => p.id === product.id)
                  )
                  .slice(0, 8)
                  .map((product) => (
                    <div
                      key={product.id}
                      className="border rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => addToComparison(product)}
                    >
                      <div className="aspect-square bg-gray-100 rounded-lg mb-2 relative overflow-hidden">
                        <Image
                          src={
                            product.images?.[0] ||
                            generatePlaceholderUrl(100, 100, product.title)
                          }
                          alt={product.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <h5 className="text-xs font-medium text-gray-900 line-clamp-2 mb-1">
                        {product.title}
                      </h5>
                      <p className="text-xs text-gray-600">
                        {product.currency}
                        {product.price.toLocaleString()}
                      </p>
                      <Button size="sm" className="w-full mt-2 h-6 text-xs">
                        <Icon icon="mdi:plus" className="h-3 w-3 mr-1" />
                        Add
                      </Button>
                    </div>
                  ))}
              </div>
            </div>
          )}

          {/* Comparison Table */}
          <div className="border rounded-lg overflow-hidden">
            {/* Product Headers */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-0 bg-gray-50 border-b">
              {selectedProducts.map((product, index) => (
                <div key={product.id} className="p-4 border-r last:border-r-0">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      {index === 0 && (
                        <Badge className="bg-blue-100 text-blue-800 text-xs mb-2">
                          Current Product
                        </Badge>
                      )}
                      <div className="aspect-square bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                        <Image
                          src={
                            product.images?.[0] ||
                            generatePlaceholderUrl(150, 150, product.title)
                          }
                          alt={product.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <h4 className="font-medium text-gray-900 text-sm line-clamp-2 mb-1">
                        {product.title}
                      </h4>
                      <p className="text-lg font-semibold text-gray-900">
                        {product.currency}
                        {product.price.toLocaleString()}
                      </p>
                    </div>
                    {selectedProducts.length > 1 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFromComparison(product.id)}
                        className="text-gray-400 hover:text-red-600 p-1 h-auto"
                      >
                        <Icon icon="mdi:close" className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Comparison Features */}
            {comparisonFeatures.map((feature) => (
              <div
                key={feature.key}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-0 border-b last:border-b-0"
              >
                {selectedProducts.map((product, index) => (
                  <div
                    key={`${product.id}-${feature.key}`}
                    className={`p-4 border-r last:border-r-0 ${
                      index === 0 ? "bg-blue-50" : ""
                    }`}
                  >
                    {index === 0 && (
                      <div className="font-medium text-gray-900 text-sm mb-2">
                        {feature.label}
                      </div>
                    )}
                    <div
                      className={`flex items-center gap-2 ${
                        isHighlighted(feature, product)
                          ? "text-green-600 font-medium"
                          : "text-gray-700"
                      }`}
                    >
                      {isHighlighted(feature, product) && (
                        <Icon
                          icon="mdi:check"
                          className="h-4 w-4 text-green-600"
                        />
                      )}
                      <span className="text-sm">
                        {getComparisonValue(feature, product)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Close
            </Button>
            <Button className="bg-teal-600 hover:bg-teal-700">
              View Selected Products
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
