// Layout components
export * from "./layout";

// Feature-based components
export * from "./features/auth";
export * from "./features/cart";
export * from "./features/category";
export * from "./features/forms";
export * from "./features/marketing";
export * from "./features/product";
export * from "./features/profile";
export * from "./features/settings";

// Common/shared components
export * from "./common";
export * from "./ui";

// Dynamic components (selective exports to avoid conflicts)
export {
  ImageCropModal,
  PostAdsForm as DynamicPostAdsForm,
  EnhancedImageGallery,
  // Export dynamic versions with "Dynamic" prefix to avoid conflicts
  BannerCarousel as DynamicBannerCarousel,
  ContactFormModal as DynamicContactFormModal,
  EditJobProfileModal as DynamicEditJobProfileModal,
  EditProfileModal as DynamicEditProfileModal,
  ProductDetailView as DynamicProductDetailView,
  JobDetailView as DynamicJobDetailView,
  JobApplyForm as DynamicJobApplyForm,
} from "./dynamic";

// Main page component
export { default as HomePage } from "./features/marketing/home-page";

// Legacy exports for backward compatibility
export { Header, Footer, SubHeader } from "./layout";
export { CategorySidebar, CategoryDropdown } from "./features/category";
export {
  ProductGrid,
  ProductCardDetailed as ProductCard,
  ProductCardDetailed as ProductCard2,
  ProductCard as ProductCardHorizontal,
  VerticalProductList,
} from "./features/product";
export {
  FilterPanel,
  SortDropdown,
  ErrorBoundary,
  SimpleErrorBoundary,
} from "./common";
export { LoginForm, SignupForm, PostAdsForm } from "./features/forms";
export { Hero } from "./features/marketing/hero";
