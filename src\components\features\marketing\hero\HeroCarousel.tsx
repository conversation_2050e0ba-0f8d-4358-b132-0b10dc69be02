// "use client";
// import { useState, useEffect } from "react";
// import Image from "next/image";
// import { Button } from "@/components/ui/button";
// import { Icon } from "@iconify/react";

// interface HeroCarouselProps {
//   className?: string;
//   onSortChange?: (sortBy: string) => void;
//   sortBy?: string;
//   categoryTitle?: string;
//   productCount?: number;
// }

// export default function HeroCarousel({
//   className = "",
//   onSortChange,
//   sortBy = "newest",
//   categoryTitle = "All Products",
//   productCount = 0,
// }: HeroCarouselProps) {
//   const [currentSlide, setCurrentSlide] = useState(0);

//   // Hero slides with different themes
//   const heroSlides = [
//     {
//       image: "https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=800&h=600&fit=crop",
//     },
//     {
//       image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop",
//     },
//     {
//       image: "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop",
//     },
//   ];

//   const nextSlide = () => {
//     setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
//   };

//   const prevSlide = () => {
//     setCurrentSlide(
//       (prev) => (prev - 1 + heroSlides.length) % heroSlides.length
//     );
//   };

//   // Auto-slide functionality
//   useEffect(() => {
//     const interval = setInterval(() => {
//       setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
//     }, 6000);
//     return () => clearInterval(interval);
//   }, [heroSlides.length]);

//   const currentSlideData = heroSlides[currentSlide];

//   return (
//     <div className={`relative ${className}`}>
//       {/* Enhanced Header with Category Title and Sort - Positioned Above Image */}
//       <div className="relative z-20 bg-gradient-to-r from-white/98 via-white/95 to-white/98 backdrop-blur-lg border-b border-gray-200/60 shadow-lg">
//         <div className="flex items-center justify-between p-4 md:p-6">
//           {/* Left Section - Category Info */}
//           <div className="flex items-center gap-4">
//             {/* Animated Category Badge */}
//             <div className="relative">
//               <div className="w-12 h-12 bg-gradient-to-br from-teal-500 via-teal-600 to-teal-700 rounded-2xl shadow-lg flex items-center justify-center transform hover:scale-105 transition-all duration-300">
//                 <svg
//                   className="w-6 h-6 text-white"
//                   fill="none"
//                   stroke="currentColor"
//                   viewBox="0 0 24 24"
//                 >
//                   <path
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     strokeWidth={2}
//                     d="M19 11H5m14-7H5a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2V6a2 2 0 00-2-2z"
//                   />
//                 </svg>
//               </div>
//               {/* Pulse ring animation */}
//               <div className="absolute inset-0 w-12 h-12 bg-teal-400 rounded-2xl animate-ping opacity-20"></div>
//             </div>

//             <div className="space-y-1">
//               {/* Category Title with Gradient */}
//               <div className="flex items-center gap-3">
//                 <h2 className="text-xl md:text-2xl font-bold bg-gradient-to-r from-gray-800 via-gray-700 to-gray-600 bg-clip-text text-transparent">
//                   {categoryTitle}
//                 </h2>
//                 {/* Live Status Badge */}
//                 <div className="flex items-center gap-1.5 px-3 py-1 bg-gradient-to-r from-emerald-100 to-green-100 rounded-full border border-emerald-200">
//                   <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse shadow-sm"></div>
//                 </div>
//               </div>

//               {/* Product Count with Animation */}
//               <div className="flex items-center gap-3">
//                 <p className="text-gray-600 text-sm md:text-base font-medium">
//                   <span className="inline-block animate-pulse">📦</span>
//                   <span className="ml-1 font-semibold text-teal-600">
//                     {productCount.toLocaleString()}
//                   </span>
//                   <span className="ml-1">products found</span>
//                 </p>

//                 {/* Progress Indicator */}
//                 <div className="hidden md:flex items-center gap-2">
//                   <div className="w-20 h-2 bg-gray-200 rounded-full overflow-hidden shadow-inner">
//                     <div
//                       className="h-full bg-gradient-to-r from-teal-400 via-teal-500 to-teal-600 rounded-full transition-all duration-1000 ease-out shadow-sm"
//                       style={{
//                         width: `${Math.min((productCount / 1000) * 100, 100)}%`,
//                       }}
//                     ></div>
//                   </div>
//                   <span className="text-xs font-medium text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
//                     {Math.min(Math.round((productCount / 1000) * 100), 100)}%
//                   </span>
//                 </div>
//               </div>
//             </div>
//           </div>

//           {/* Right Section - Sort Controls */}
//           <div className="flex items-center gap-3">
//             {/* Enhanced Sort Dropdown */}
//             <div className="relative group">
//               <select
//                 value={sortBy}
//                 onChange={(e) => onSortChange?.(e.target.value)}
//                 className="appearance-none bg-white hover:bg-gray-50 border-2 border-gray-200 hover:border-teal-300 focus:border-teal-500 focus:ring-4 focus:ring-teal-100 rounded-xl px-4 py-3 pr-12 text-sm md:text-base font-semibold text-gray-700 cursor-pointer transition-all duration-300 shadow-sm hover:shadow-md group-hover:shadow-lg min-w-[160px] md:min-w-[180px]"
//               >
//                 <option value="newest">Newest First</option>
//                 <option value="oldest">Oldest First</option>
//                 <option value="price-low">Price: Low → High</option>
//                 <option value="price-high">Price: High → Low</option>
//                 <option value="relevance">Most Relevant</option>
//               </select>

//               {/* Custom Dropdown Arrow with Animation */}
//               <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
//                 <div className="w-4 h-4 bg-gradient-to-b from-gray-500 to-gray-600 group-hover:from-teal-500 group-hover:to-teal-600 rounded-sm flex items-center justify-center transition-all duration-300">
//                   <svg
//                     className="w-3 h-3 text-white"
//                     fill="none"
//                     stroke="currentColor"
//                     viewBox="0 0 24 24"
//                   >
//                     <path
//                       strokeLinecap="round"
//                       strokeLinejoin="round"
//                       strokeWidth={2.5}
//                       d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"
//                     />
//                   </svg>
//                 </div>
//               </div>

//               {/* Hover Glow Effect */}
//               <div className="absolute inset-0 bg-gradient-to-r from-teal-400/20 to-teal-600/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 blur-sm"></div>
//             </div>
//           </div>
//         </div>

//         {/* Decorative Bottom Border */}
//         <div className="h-1 bg-gradient-to-r from-transparent via-teal-500 to-transparent opacity-60"></div>
//       </div>

//       {/* Hero Section with Full Image Display */}
//       <div className="relative h-[500px] md:h-[600px] bg-gray-100">
//         {/* Background Image - Full Display */}
//         <Image
//           src={currentSlideData.image || "/assets/images/placeholders/placeholder.jpg"}
//           alt={`Hero slide ${currentSlide + 1}`}
//           fill
//           className="object-contain"
//           priority={currentSlide === 0}
//           unoptimized
//           onError={(e) => {
//             console.error("Image failed to load:", currentSlideData.image);
//             console.error("Error details:", e);
//           }}
//           onLoad={() => {
//             console.log("Image loaded successfully:", currentSlideData.image);
//           }}
//         />

//         {/* Navigation Arrows - On Top of Image */}
//         <div className="absolute top-1/3 left-4 md:left-6 transform -translate-y-1/2 z-30">
//           <Button
//             onClick={prevSlide}
//             variant="outline"
//             size="lg"
//             className="bg-white/90 hover:bg-white border-white/50 shadow-lg hover:shadow-xl transition-all duration-200 rounded-full w-12 h-12 md:w-14 md:h-14 p-0"
//           >
//             <ChevronLeft className="h-5 w-5 md:h-6 md:w-6 text-gray-700" />
//           </Button>
//         </div>

//         <div className="absolute top-1/3 right-4 md:right-6 transform -translate-y-1/2 z-30">
//           <Button
//             onClick={nextSlide}
//             variant="outline"
//             size="lg"
//             className="bg-white/90 hover:bg-white border-white/50 shadow-lg hover:shadow-xl transition-all duration-200 rounded-full w-12 h-12 md:w-14 md:h-14 p-0"
//           >
//             <ChevronRight className="h-5 w-5 md:h-6 md:w-6 text-gray-700" />
//           </Button>
//         </div>

//         {/* Slide Indicators */}
//         <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20">
//           <div className="flex space-x-3">
//             {heroSlides.map((_, index) => (
//               <button
//                 key={index}
//                 onClick={() => setCurrentSlide(index)}
//                 className={`w-3 h-3 md:w-4 md:h-4 rounded-full transition-all duration-300 hover:scale-110 ${
//                   index === currentSlide
//                     ? "bg-white shadow-lg scale-110"
//                     : "bg-white/60 hover:bg-white/80"
//                 }`}
//               />
//             ))}
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }
