"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Icon } from "@iconify/react";
import type { ContactFormData, PublicProfileData } from "@/types/ecommerce";

interface ContactFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  recipientProfile: PublicProfileData;
  productId?: string;
  productTitle?: string;
  onSendMessage: (data: ContactFormData) => Promise<void>;
}

// Star Rating Component
function StarRating({
  rating,
  size = "w-5 h-5",
}: {
  rating: number;
  size?: string;
}) {
  return (
    <div className="flex items-center gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Icon
          key={star}
          icon="lucide:star"
          className={`${size} ${
            star <= rating
              ? "fill-yellow-500 text-yellow-500"
              : "fill-gray-200 text-gray-200"
          }`}
        />
      ))}
    </div>
  );
}

export default function ContactFormModal({
  isOpen,
  onClose,
  recipientProfile,
  productId,
  productTitle,
  onSendMessage,
}: ContactFormModalProps) {
  const [formData, setFormData] = useState<ContactFormData>({
    name: "",
    email: "",
    phone: "",
    message: productTitle
      ? `Hi, I'm interested in your "${productTitle}". Is it still available?`
      : "",
    productId,
  });

  const [errors, setErrors] = useState<Partial<ContactFormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const handleInputChange = (field: keyof ContactFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ContactFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.message.trim()) {
      newErrors.message = "Message is required";
    } else if (formData.message.trim().length < 10) {
      newErrors.message = "Message must be at least 10 characters long";
    }

    if (
      formData.phone &&
      !/^[\+]?[1-9][\d]{0,15}$/.test(formData.phone.replace(/\s/g, ""))
    ) {
      newErrors.phone = "Please enter a valid phone number";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      await onSendMessage(formData);
      setIsSuccess(true);

      // Auto close after 2 seconds
      setTimeout(() => {
        onClose();
        setIsSuccess(false);
        setFormData({
          name: "",
          email: "",
          phone: "",
          message: "",
          productId,
        });
      }, 2000);
    } catch (error) {
      console.error("Error sending message:", error);
      // Handle error (could show toast notification)
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
      setErrors({});
      setIsSuccess(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] p-6 overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-gray-900">
            Contact {recipientProfile.username}
          </DialogTitle>
          <DialogDescription>
            Send a direct message to this user. They will receive your message
            in their inbox.
          </DialogDescription>
        </DialogHeader>

        {isSuccess ? (
          <div className="py-8 text-center">
            <div className="flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mx-auto mb-4">
              <Icon
                icon="lucide:check-circle"
                className="w-8 h-8 text-green-600"
              />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Message Sent Successfully!
            </h3>
            <p className="text-gray-600">
              Your message has been sent to {recipientProfile.username}. They
              will be notified and can respond through their inbox.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Recipient Profile Card */}
            <Card className="bg-gray-50 p-6">
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-teal-400 to-teal-600 rounded-full flex items-center justify-center">
                    <Icon icon="lucide:user" className="w-6 h-6 text-white" />
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold text-gray-900">
                        {recipientProfile.username}
                      </h4>
                      {recipientProfile.isVerified && (
                        <Badge className="bg-green-100 text-green-800 text-xs">
                          <Icon
                            icon="lucide:check-circle"
                            className="w-3 h-3 mr-1"
                          />
                          Verified
                        </Badge>
                      )}
                    </div>

                    <div className="flex items-center gap-4 text-md text-gray-600">
                      <div className="flex items-center gap-1">
                        <StarRating
                          rating={recipientProfile.overallRating || 0}
                          size="w-3 h-3"
                        />
                        <span>
                          {(recipientProfile.overallRating || 0).toFixed(1)}
                        </span>
                      </div>

                      <div className="flex items-center gap-1">
                        <Icon icon="lucide:map-pin" className="w-3 h-3" />
                        <span>{recipientProfile.address?.city}</span>
                      </div>

                      <span>
                        {recipientProfile.responseRate}% response rate
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Product Context (if applicable) */}
            {productTitle && (
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 text-blue-800">
                    <Icon icon="lucide:message-square" className="w-5 h-5" />
                    <span className="font-medium">About: {productTitle}</span>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Contact Form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Name */}
                <div className="space-y-2">
                  <Label
                    htmlFor="name"
                    className="text-md font-medium text-gray-700"
                  >
                    Your Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="Enter your full name"
                    className={`h-11 ${errors.name ? "border-red-500" : ""}`}
                    disabled={isSubmitting}
                  />
                  {errors.name && (
                    <p className="text-md text-red-500">{errors.name}</p>
                  )}
                </div>

                {/* Email */}
                <div className="space-y-2">
                  <Label
                    htmlFor="email"
                    className="text-md font-medium text-gray-700"
                  >
                    Your Email <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    placeholder="<EMAIL>"
                    className={`h-11 ${errors.email ? "border-red-500" : ""}`}
                    disabled={isSubmitting}
                  />
                  {errors.email && (
                    <p className="text-md text-red-500">{errors.email}</p>
                  )}
                </div>
              </div>

              {/* Phone (Optional) */}
              <div className="space-y-2">
                <Label
                  htmlFor="phone"
                  className="text-md font-medium text-gray-700"
                >
                  Your Phone Number (Optional)
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  placeholder="+****************"
                  className={`h-11 ${errors.phone ? "border-red-500" : ""}`}
                  disabled={isSubmitting}
                />
                {errors.phone && (
                  <p className="text-md text-red-500">{errors.phone}</p>
                )}
              </div>

              {/* Message */}
              <div className="space-y-2">
                <Label
                  htmlFor="message"
                  className="text-md font-medium text-gray-700"
                >
                  Message <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="message"
                  value={formData.message}
                  onChange={(e) => handleInputChange("message", e.target.value)}
                  placeholder="Write your message here..."
                  rows={4}
                  className={`resize-none ${
                    errors.message ? "border-red-500" : ""
                  }`}
                  disabled={isSubmitting}
                />
                {errors.message && (
                  <p className="text-md text-red-500">{errors.message}</p>
                )}
                <p className="text-xs text-gray-500">
                  Minimum 10 characters ({formData.message.length}/10)
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  disabled={isSubmitting}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex-1 bg-teal-600 hover:bg-teal-700 text-white"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Icon icon="lucide:send" className="w-5 h-5 mr-2" />
                      Send Message
                    </>
                  )}
                </Button>
              </div>
            </form>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
