import React from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

interface FormProgressProps {
  currentStep: number
  totalSteps: number
  completedFields: number
}

export const FormProgress: React.FC<FormProgressProps> = ({
  currentStep,
  totalSteps,
  completedFields,
}) => {
  const progress = (completedFields / totalSteps) * 100

  return (
    <Card className="mb-8 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="font-semibold text-gray-900">Form Progress</h3>
            <p className="text-sm text-gray-600">
              {completedFields} of {totalSteps} sections completed
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-blue-600">{Math.round(progress)}%</div>
          </div>
        </div>
        <Progress value={progress} className="h-2" />
      </CardContent>
    </Card>
  )
}
