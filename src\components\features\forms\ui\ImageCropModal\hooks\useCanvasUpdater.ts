import { useEffect } from "react";
import { useImageCropModal } from "../ImageCropModalProvider";

export const useCanvasUpdater = () => {
  const {
    imageLoaded,
    imgRef,
    previewCanvasRef,
    crop,
    colorAdjustments,
  } = useImageCropModal();

  // Update preview canvas
  useEffect(() => {
    if (!imageLoaded || !imgRef.current || !previewCanvasRef.current) return;

    const canvas = previewCanvasRef.current;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const img = imgRef.current;
    const { x, y, width, height } = crop;

    // Set canvas size
    canvas.width = width;
    canvas.height = height;

    // Apply filters
    ctx.filter = `brightness(${colorAdjustments.brightness}%) contrast(${colorAdjustments.contrast}%) saturate(${colorAdjustments.saturation}%)`;

    // Calculate source coordinates based on transformations
    const scaleX = img.naturalWidth / img.clientWidth;
    const scaleY = img.naturalHeight / img.clientHeight;

    ctx.drawImage(
      img,
      x * scaleX,
      y * scaleY,
      width * scaleX,
      height * scaleY,
      0,
      0,
      width,
      height
    );

    // Reset canvas display size for preview
    canvas.style.width = "100%";
    canvas.style.height = "100%";
  }, [crop, colorAdjustments, imageLoaded, imgRef, previewCanvasRef]);
};
