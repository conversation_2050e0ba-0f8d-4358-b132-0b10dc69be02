import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Image from "next/image";
import type { Transaction, TransactionFilters, PaginationInfo } from "../types";

interface TransactionHistoryProps {
  transactions: Transaction[];
  filters: TransactionFilters;
  pagination: PaginationInfo;
  onSearchChange: (value: string) => void;
  onStatusFilterChange: (value: string) => void;
  onPageChange: (page: number) => void;
}

const getStatusBadge = (status: string) => {
  if (status === "Completed") {
    return (
      <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
        Completed
      </span>
    );
  } else if (status === "Pending") {
    return (
      <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
        Pending
      </span>
    );
  }
  return null;
};

const getTypeBadge = (type: string) => {
  if (type === "Sold") {
    return (
      <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
        Sold
      </span>
    );
  } else if (type === "Bought") {
    return (
      <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
        Bought
      </span>
    );
  }
  return null;
};

const renderStars = (rating: number) => {
  return (
    <div className="flex">
      {[1, 2, 3, 4, 5].map((star) => (
        <Icon
          key={star}
          icon="mdi:star"
          className={`w-4 h-4 ${
            star <= rating ? "text-yellow-400" : "text-gray-300"
          }`}
        />
      ))}
    </div>
  );
};

const EmptyState = () => (
  <tr>
    <td colSpan={7} className="px-6 py-12 text-center">
      <div className="flex flex-col items-center">
        <Icon
          icon="mdi:database-search"
          className="w-12 h-12 text-gray-400 mb-4"
        />
        <p className="text-gray-500 text-lg font-medium">
          No transactions found
        </p>
        <p className="text-gray-400 text-sm mt-1">
          Try adjusting your search or filter criteria
        </p>
      </div>
    </td>
  </tr>
);

const TransactionRow = ({ transaction }: { transaction: Transaction }) => (
  <tr key={transaction.id} className="hover:bg-gray-50">
    <td className="px-6 py-4 whitespace-nowrap">
      <div className="flex items-center">
        <Image
          src={transaction.image || "/placeholder.svg"}
          alt={transaction.item}
          width={40}
          height={40}
          className="rounded-lg mr-3"
        />
        <div>
          <p className="text-sm font-medium text-gray-900">
            {transaction.item}
          </p>
          <p className="text-xs text-gray-500">& Accessories</p>
        </div>
      </div>
    </td>
    <td className="px-6 py-4 whitespace-nowrap">
      {getTypeBadge(transaction.type)}
    </td>
    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
      {transaction.price}
    </td>
    <td className="px-6 py-4 whitespace-nowrap">
      <div>
        <p className="text-sm font-medium text-gray-900">
          {transaction.person}
        </p>
        <p className="text-xs text-gray-500">{transaction.role}</p>
      </div>
    </td>
    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
      {transaction.date}
    </td>
    <td className="px-6 py-4 whitespace-nowrap">
      {getStatusBadge(transaction.status)}
    </td>
    <td className="px-6 py-4 whitespace-nowrap">
      {renderStars(transaction.rating)}
    </td>
  </tr>
);

const Pagination = ({
  pagination,
  onPageChange,
}: {
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
}) => {
  const { currentPage, totalPages, totalItems, startIndex, endIndex } =
    pagination;

  return (
    <div className="px-6 py-4 border-t border-gray-100">
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-700">
          Showing{" "}
          <span className="font-medium">
            {totalItems === 0 ? 0 : startIndex + 1}
          </span>{" "}
          to{" "}
          <span className="font-medium">{Math.min(endIndex, totalItems)}</span>{" "}
          of <span className="font-medium">{totalItems}</span> results
        </p>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="p-2"
          >
            <Icon icon="mdi:chevron-left" className="w-4 h-4" />
          </Button>

          {/* Dynamic page numbers */}
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            let pageNumber;
            if (totalPages <= 5) {
              pageNumber = i + 1;
            } else if (currentPage <= 3) {
              pageNumber = i + 1;
            } else if (currentPage >= totalPages - 2) {
              pageNumber = totalPages - 4 + i;
            } else {
              pageNumber = currentPage - 2 + i;
            }

            return (
              <Button
                key={pageNumber}
                variant={currentPage === pageNumber ? "default" : "outline"}
                size="sm"
                onClick={() => onPageChange(pageNumber)}
                className={`w-8 h-8 p-0 ${
                  currentPage === pageNumber
                    ? "bg-gray-900 text-white"
                    : "text-gray-700 hover:bg-gray-100"
                }`}
              >
                {pageNumber}
              </Button>
            );
          })}

          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="p-2"
          >
            <Icon icon="mdi:chevron-right" className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export const TransactionHistory = ({
  transactions,
  filters,
  pagination,
  onSearchChange,
  onStatusFilterChange,
  onPageChange,
}: TransactionHistoryProps) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100">
      <div className="p-6 border-b border-gray-100">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h2 className="text-xl font-semibold text-gray-900">
            Transaction History
          </h2>
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="relative">
              <Icon
                icon="mdi:magnify"
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"
              />
              <Input
                placeholder="Search"
                value={filters.searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-10 w-full sm:w-64"
              />
            </div>
            <Select
              value={filters.statusFilter}
              onValueChange={onStatusFilterChange}
            >
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Sort By: Status" />
              </SelectTrigger>
              <SelectContent className="bg-white text-black">
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Item
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Price
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Buyers or Sellers
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Rating
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {transactions.length === 0 ? (
              <EmptyState />
            ) : (
              transactions.map((transaction) => (
                <TransactionRow
                  key={transaction.id}
                  transaction={transaction}
                />
              ))
            )}
          </tbody>
        </table>
      </div>

      <Pagination pagination={pagination} onPageChange={onPageChange} />
    </div>
  );
};
