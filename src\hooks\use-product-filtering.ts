"use client";

import { useEffect } from "react";
// Redux imports - replacing context API
import { useAppSelector } from "@/store/hooks";
import { useEcommerceActions } from "@/context/EcommerceContext";
import {
  selectProducts,
  selectActiveFilters,
  selectSortBy,
  selectSelectedCategory,
  selectSearchQuery,
  selectSearchLocation,
} from "@/store/selectors";
import { applyFiltersAndSort } from "@/utils/product-filters";

export function useProductFiltering() {
  // Redux selectors - replace context usage
  const products = useAppSelector(selectProducts);
  const activeFilters = useAppSelector(selectActiveFilters);
  const sortBy = useAppSelector(selectSortBy);
  const selectedCategory = useAppSelector(selectSelectedCategory);
  const searchQuery = useAppSelector(selectSearchQuery);
  const searchLocation = useAppSelector(selectSearchLocation);

  // Redux actions
  const { setFilteredProducts, clearFilters, setCurrentPage } =
    useEcommerceActions();

  // Apply filters whenever dependencies change
  useEffect(() => {
    const filteredProducts = applyFiltersAndSort(
      products,
      selectedCategory,
      searchQuery,
      "", // searchLocation - need to add this selector if needed
      activeFilters,
      sortBy
    );

    setFilteredProducts(filteredProducts);
  }, [
    products,
    selectedCategory,
    searchQuery,
    activeFilters,
    sortBy,
    setFilteredProducts,
  ]);

  // Reset filters when category changes
  useEffect(() => {
    if (selectedCategory !== null) {
      clearFilters();
    }
  }, [selectedCategory, clearFilters]);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [
    activeFilters,
    searchQuery,
    searchLocation,
    selectedCategory,
    setCurrentPage,
  ]);

  return {
    // State
    products,
    filteredProducts: useAppSelector((state) => state.product.filteredProducts),
    selectedCategory,
    activeFilters,
    searchQuery,
    searchLocation,
    sortBy,
    currentPage: useAppSelector((state) => state.product.currentPage),
    totalPages: useAppSelector((state) => state.product.totalPages),
    loading: useAppSelector((state) => state.product.loading),

    // Actions
    setFilteredProducts,
    clearFilters,
    setCurrentPage,
  };
}
