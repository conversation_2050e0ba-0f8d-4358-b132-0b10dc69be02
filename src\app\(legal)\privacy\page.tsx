import Link from "next/link";
import { Icon } from "@iconify/react";

export default function PrivacyPage() {
  const privacyFeatures = [
    {
      icon: "lucide:shield-check",
      title: "Data Protection",
      description:
        "Your personal information is encrypted and securely stored using industry-standard protocols.",
    },
    {
      icon: "lucide:eye-off",
      title: "Privacy Controls",
      description:
        "Control what information you share and with whom through comprehensive privacy settings.",
    },
    {
      icon: "lucide:lock",
      title: "Secure Transactions",
      description:
        "All payment information is processed through secure, PCI-compliant payment processors.",
    },
    {
      icon: "lucide:user-check",
      title: "Account Security",
      description:
        "Two-factor authentication and account monitoring to keep your account safe.",
    },
  ];

  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <div className="container-responsive spacing-responsive-lg">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Your Privacy Matters
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            We&apos;re committed to protecting your privacy and being
            transparent about how we collect, use, and share your information on
            Nepal Marketplace.
          </p>
        </div>

        {/* Privacy Features */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {privacyFeatures.map((feature, index) => (
            <div
              key={index}
              className="bg-white rounded-xl p-6 shadow-lg text-center"
            >
              <div className="bg-teal-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Icon icon={feature.icon} className="h-8 w-8 text-teal-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Privacy Summary */}
          <div className="lg:col-span-2 bg-white rounded-xl shadow-lg p-8">
            <h2 className="text-3xl font-bold text-gray-800 mb-6">
              Privacy at a Glance
            </h2>

            <div className="space-y-6">
              <div>
                <h3 className="text-xl font-semibold text-gray-800 mb-3">
                  What We Collect
                </h3>
                <ul className="text-gray-600 space-y-2">
                  <li>• Account information (name, email, phone)</li>
                  <li>• Purchase and browsing history</li>
                  <li>• Device and location information</li>
                  <li>• Communication preferences</li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-semibold text-gray-800 mb-3">
                  How We Use It
                </h3>
                <ul className="text-gray-600 space-y-2">
                  <li>• To provide and improve our services</li>
                  <li>• To process orders and payments</li>
                  <li>• To send important updates and notifications</li>
                  <li>• To personalize your shopping experience</li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-semibold text-gray-800 mb-3">
                  Your Rights
                </h3>
                <ul className="text-gray-600 space-y-2">
                  <li>• Access and download your data</li>
                  <li>• Correct inaccurate information</li>
                  <li>• Delete your account and data</li>
                  <li>• Control marketing communications</li>
                </ul>
              </div>

              <div className="bg-teal-50 border-l-4 border-teal-400 p-6">
                <h3 className="text-lg font-semibold text-teal-800 mb-2">
                  Our Promise
                </h3>
                <p className="text-teal-700 text-sm">
                  We never sell your personal information to third parties. Your
                  data is used solely to improve your experience on Nepal
                  Marketplace and is protected with bank-level security.
                </p>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-4">
                Privacy Controls
              </h3>
              <div className="space-y-3">
                <Link
                  href="/settings/privacy"
                  className="block w-full bg-teal-600 text-white text-center py-3 rounded-lg hover:bg-teal-700 transition-colors"
                >
                  Manage Privacy Settings
                </Link>
                <Link
                  href="/settings/data"
                  className="block w-full border border-gray-300 text-gray-700 text-center py-3 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Download My Data
                </Link>
                <Link
                  href="/settings/delete-account"
                  className="block w-full border border-red-300 text-red-600 text-center py-3 rounded-lg hover:bg-red-50 transition-colors"
                >
                  Delete Account
                </Link>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-4">
                Need Help?
              </h3>
              <div className="space-y-3">
                <Link
                  href="/privacy-policy"
                  className="block text-teal-600 hover:text-teal-700 hover:underline"
                >
                  → Read Full Privacy Policy
                </Link>
                <Link
                  href="/cookies-policy"
                  className="block text-teal-600 hover:text-teal-700 hover:underline"
                >
                  → Cookie Policy
                </Link>
                <Link
                  href="/contact"
                  className="block text-teal-600 hover:text-teal-700 hover:underline"
                >
                  → Contact Privacy Team
                </Link>
                <Link
                  href="/faqs"
                  className="block text-teal-600 hover:text-teal-700 hover:underline"
                >
                  → Privacy FAQs
                </Link>
              </div>
            </div>

            <div className="bg-gradient-to-br from-teal-600 to-teal-700 rounded-xl p-6 text-white">
              <h3 className="text-lg font-bold mb-3">Stay Informed</h3>
              <p className="text-sm opacity-90 mb-4">
                Get notified about important privacy updates and new features.
              </p>
              <button className="w-full bg-white text-teal-600 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                Subscribe to Updates
              </button>
            </div>
          </div>
        </div>

        {/* Data Transparency */}
        <div className="mt-16 bg-white rounded-xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-6 text-center">
            Data Transparency
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Icon
                  icon="lucide:database"
                  className="h-8 w-8 text-blue-600"
                />
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                Data Collection
              </h3>
              <p className="text-gray-600 text-sm">
                We only collect data that&apos;s necessary to provide our
                services and improve your experience.
              </p>
            </div>
            <div className="text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Icon
                  icon="lucide:share-2"
                  className="h-8 w-8 text-green-600"
                />
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                Data Sharing
              </h3>
              <p className="text-gray-600 text-sm">
                We share data only with trusted partners and only when necessary
                to complete transactions.
              </p>
            </div>
            <div className="text-center">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Icon
                  icon="lucide:trash-2"
                  className="h-8 w-8 text-purple-600"
                />
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                Data Deletion
              </h3>
              <p className="text-gray-600 text-sm">
                You can request deletion of your data at any time, and
                we&apos;ll process it according to legal requirements.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
