"use client";
import { useState } from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { SmartImage } from "@/components/common/SmartImage";

interface ImageGalleryProps {
  images?: string[];
  alt?: string;
  title?: string;
}

export function ImageGallery({
  images = [],
  alt = "Product",
  title,
}: ImageGalleryProps) {
  // Only show gallery if there are real images
  if (!images || images.length === 0) {
    return (
      <div className="w-full h-96 bg-gray-50 rounded-lg flex items-center justify-center">
        <div className="text-center text-gray-500">
          <Icon icon="lucide:image" className="w-16 h-16 mx-auto mb-2" />
          <p>No images available</p>
        </div>
      </div>
    );
  }

  const displayImages = images;
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const goToPrevious = () => {
    setCurrentImageIndex((prevIndex) =>
      prevIndex === 0 ? displayImages.length - 1 : prevIndex - 1
    );
  };

  const goToNext = () => {
    setCurrentImageIndex((prevIndex) =>
      prevIndex === displayImages.length - 1 ? 0 : prevIndex + 1
    );
  };

  const goToImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Main Image Display */}
      <div className="relative bg-gray-50 rounded-lg overflow-hidden mb-4">
        <div className="aspect-[5/2] relative">
          <SmartImage
            src={displayImages[currentImageIndex]}
            alt={`${alt} - Image ${currentImageIndex + 1}`}
            fill
            className="object-contain"
            priority
          />

          {/* Navigation Arrows */}
          <Button
            variant="ghost"
            size="sm"
            onClick={goToPrevious}
            className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white/80 hover:bg-white shadow-md"
          >
            <Icon icon="mdi:chevron-left" className="h-5 w-5" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={goToNext}
            className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white/80 hover:bg-white shadow-md"
          >
            <Icon icon="mdi:chevron-right" className="h-5 w-5" />
          </Button>

          {/* Image Counter */}
          <div className="absolute bottom-4 right-4 bg-black/60 text-white px-3 py-1 rounded-full text-sm">
            {currentImageIndex + 1}/{images.length}
          </div>
        </div>
      </div>

      {/* Thumbnail Navigation */}
      <div className="flex justify-center items-center gap-3 overflow-x-auto pb-2">
        {displayImages.map((image, index) => (
          <button
            key={index}
            onClick={() => goToImage(index)}
            className={`relative flex-shrink-0 w-24 h-16 rounded-lg overflow-hidden border-2 transition-all ${
              index === currentImageIndex
                ? "border-teal-500 ring-2 ring-teal-200"
                : "border-gray-200 hover:border-gray-300"
            }`}
          >
            <SmartImage
              src={image}
              alt={`${alt} thumbnail ${index + 1}`}
              fill
              className="object-cover"
            />
            {index === currentImageIndex && (
              <div className="absolute inset-0 bg-teal-500/20" />
            )}
          </button>
        ))}
      </div>
    </div>
  );
}
