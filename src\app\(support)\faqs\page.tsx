"use client";

import { useState } from "react";
import { Icon } from "@iconify/react";

export default function FAQsPage() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const faqCategories = [
    {
      title: "General Questions",
      faqs: [
        {
          question: "What is Nepal Marketplace?",
          answer:
            "Nepal Marketplace is a trusted e-commerce platform connecting buyers and sellers across Nepal. We offer a wide range of products from electronics to handmade crafts, ensuring quality and authenticity.",
        },
        {
          question: "How do I create an account?",
          answer:
            "Click on the &apos;Sign Up&apos; button in the top right corner, fill in your details including email, phone number, and create a secure password. You&apos;ll receive a verification email to activate your account.",
        },
        {
          question: "Is it free to use Nepal Marketplace?",
          answer:
            "Yes, browsing and buying on Nepal Marketplace is completely free for customers. Sellers pay a small commission only when they make a sale.",
        },
      ],
    },
    {
      title: "Shopping & Orders",
      faqs: [
        {
          question: "How do I place an order?",
          answer:
            "Browse products, add items to your cart, proceed to checkout, enter your delivery address, choose payment method, and confirm your order. You&apos;ll receive an order confirmation email.",
        },
        {
          question: "What payment methods do you accept?",
          answer:
            "We accept eSewa, Khalti, bank transfers, credit/debit cards, and cash on delivery (COD) for most locations in Nepal.",
        },
        {
          question: "How can I track my order?",
          answer:
            "After placing an order, you&apos;ll receive a tracking number via email and SMS. You can also track your order in the &apos;My Orders&apos; section of your account.",
        },
        {
          question: "What is your return policy?",
          answer:
            "We offer a 7-day return policy for most items. Products must be in original condition with tags attached. Digital products and perishable items are not returnable.",
        },
      ],
    },
    {
      title: "Selling on Nepal Marketplace",
      faqs: [
        {
          question: "How do I become a seller?",
          answer:
            "Click &apos;Become a Seller&apos;, complete the registration form with your business details, upload required documents (citizenship, PAN), and wait for verification (usually 2-3 business days).",
        },
        {
          question: "What are the selling fees?",
          answer:
            "We charge a 5-8% commission on successful sales depending on the category. There are no listing fees or monthly charges.",
        },
        {
          question: "How do I get paid?",
          answer:
            "Payments are processed weekly to your registered bank account or digital wallet. You can view your earnings in the Seller Dashboard.",
        },
      ],
    },
    {
      title: "Technical Support",
      faqs: [
        {
          question: "I forgot my password. How do I reset it?",
          answer:
            "Click &apos;Forgot Password&apos; on the login page, enter your email address, and follow the instructions in the reset email sent to you.",
        },
        {
          question: "Why can't I access my account?",
          answer:
            "This could be due to incorrect login credentials, account suspension, or technical issues. Try resetting your password or contact our support team.",
        },
        {
          question: "The website is loading slowly. What should I do?",
          answer:
            "Clear your browser cache, check your internet connection, try a different browser, or contact our technical support if the issue persists.",
        },
      ],
    },
  ];

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <div className="container-responsive spacing-responsive-lg">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Frequently Asked Questions
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Find answers to common questions about Nepal Marketplace. Can&apos;t
            find what you&apos;re looking for? Contact our support team.
          </p>
        </div>

        {/* Search Bar */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="relative">
            <Icon
              icon="lucide:search"
              className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"
            />
            <input
              type="text"
              placeholder="Search for answers..."
              className="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-transparent text-lg"
            />
          </div>
        </div>

        {/* FAQ Categories */}
        <div className="space-y-8">
          {faqCategories.map((category, categoryIndex) => (
            <div
              key={categoryIndex}
              className="bg-white rounded-xl shadow-lg overflow-hidden"
            >
              <div className="bg-teal-600 text-white p-6">
                <h2 className="text-2xl font-bold">{category.title}</h2>
              </div>
              <div className="divide-y divide-gray-200">
                {category.faqs.map((faq, faqIndex) => {
                  const globalIndex = categoryIndex * 100 + faqIndex;
                  return (
                    <div key={faqIndex}>
                      <button
                        onClick={() => toggleFAQ(globalIndex)}
                        className="w-full text-left p-6 hover:bg-gray-50 transition-colors duration-200"
                      >
                        <div className="flex justify-between items-center">
                          <h3 className="text-lg font-semibold text-gray-800 pr-4">
                            {faq.question}
                          </h3>
                          <Icon
                            icon={
                              openFAQ === globalIndex
                                ? "lucide:chevron-up"
                                : "lucide:chevron-down"
                            }
                            className="h-5 w-5 text-gray-500 flex-shrink-0"
                          />
                        </div>
                      </button>
                      {openFAQ === globalIndex && (
                        <div className="px-6 pb-6">
                          <p className="text-gray-600 leading-relaxed">
                            {faq.answer}
                          </p>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Contact Support */}
        <div className="mt-16 bg-gradient-to-r from-teal-600 to-teal-700 rounded-xl p-8 text-white text-center">
          <h2 className="text-3xl font-bold mb-4">Still Need Help?</h2>
          <p className="text-lg mb-6 opacity-90">
            Our support team is here to help you with any questions or issues.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="bg-white text-teal-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300"
            >
              Contact Support
            </a>
            <a
              href="/help-center"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-teal-600 transition-colors duration-300"
            >
              Visit Help Center
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
