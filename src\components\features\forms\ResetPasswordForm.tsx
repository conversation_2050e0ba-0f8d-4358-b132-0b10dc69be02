"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Icon } from "@iconify/react";

import { LoadingButton } from "@/components/ui";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import PasswordStrengthIndicator from "@/components/ui/PasswordStrengthIndicator";
import { AuthService } from "@/services/auth-service";
import { ApiError } from "@/lib/api";

interface ResetPasswordFormProps {
  token?: string;
}

export default function ResetPasswordForm({ token }: ResetPasswordFormProps) {
  const _router = useRouter();
  const [formData, setFormData] = useState({
    password: "",
    confirmPassword: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [apiError, setApiError] = useState<string | null>(null);

  const validatePassword = (password: string) => {
    const errors: string[] = [];
    if (password.length < 8) {
      errors.push("At least 8 characters");
    }
    if (!/[A-Z]/.test(password)) {
      errors.push("One uppercase letter");
    }
    if (!/[a-z]/.test(password)) {
      errors.push("One lowercase letter");
    }
    if (!/\d/.test(password)) {
      errors.push("One number");
    }
    return errors;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear errors when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }

    // Clear API error when user starts typing
    if (apiError) {
      setApiError(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setApiError(null);

    // Check if token is provided
    if (!token) {
      setApiError(
        "Invalid or missing reset token. Please request a new password reset."
      );
      return;
    }

    const newErrors: Record<string, string> = {};

    // Validate password
    const passwordErrors = validatePassword(formData.password);
    if (passwordErrors.length > 0) {
      newErrors.password = `Password must have: ${passwordErrors.join(", ")}`;
    }

    // Validate password confirmation
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsLoading(true);

    try {
      await AuthService.resetPassword(token, formData.password);
      setIsSuccess(true);
    } catch (err) {
      if (err instanceof ApiError) {
        setApiError(err.message);
      } else {
        setApiError("Failed to reset password. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return (
      <div className="mx-auto max-w-md space-y-6 p-6">
        <div className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
            <Icon
              icon="lucide:check-circle"
              className="h-8 w-8 text-green-600"
            />
          </div>
          <h1 className="text-3xl font-semibold text-gray-900">
            Password Reset Successful
          </h1>
          <p className="mt-4 text-gray-600">
            Your password has been successfully reset. You can now log in with
            your new password.
          </p>
        </div>

        <div className="text-center">
          <Link
            href="/login"
            className="inline-flex items-center justify-center h-12 w-full bg-teal-600 text-white hover:bg-teal-700 rounded-md font-medium"
          >
            Continue to Login
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-md space-y-6 p-6">
      <div className="text-center">
        <h1 className="text-3xl font-semibold text-gray-900">Reset Password</h1>
        <p className="mt-4 text-gray-600">Enter your new password below.</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-5">
        {apiError && (
          <div className="flex items-center gap-2 p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
            <Icon
              icon="lucide:alert-circle"
              className="h-4 w-4 flex-shrink-0"
            />
            <span>{apiError}</span>
          </div>
        )}

        <div className="space-y-3">
          <Label
            htmlFor="password"
            className="text-md font-medium text-gray-700"
          >
            New Password
          </Label>
          <div className="relative">
            <Input
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              value={formData.password}
              onChange={handleInputChange}
              placeholder="••••••••••••••••••"
              className={`h-12 text-lg mt-2 pr-10 focus:border-gray-300 focus:ring-0 ${
                errors.password ? "border-red-300" : "border-gray-200"
              }`}
              required
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showPassword ? (
                <Icon icon="lucide:eye-off" className="h-5 w-5" />
              ) : (
                <Icon icon="lucide:eye" className="h-5 w-5" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="text-sm text-red-600">{errors.password}</p>
          )}
          {formData.password && (
            <PasswordStrengthIndicator password={formData.password} />
          )}
        </div>

        <div className="space-y-3">
          <Label
            htmlFor="confirmPassword"
            className="text-md font-medium text-gray-700"
          >
            Confirm New Password
          </Label>
          <div className="relative">
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              value={formData.confirmPassword}
              onChange={handleInputChange}
              placeholder="••••••••••••••••••"
              className={`h-12 text-lg mt-2 pr-10 focus:border-gray-300 focus:ring-0 ${
                errors.confirmPassword ? "border-red-300" : "border-gray-200"
              }`}
              required
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showConfirmPassword ? (
                <Icon icon="lucide:eye-off" className="h-5 w-5" />
              ) : (
                <Icon icon="lucide:eye" className="h-5 w-5" />
              )}
            </button>
          </div>
          {errors.confirmPassword && (
            <p className="text-sm text-red-600">{errors.confirmPassword}</p>
          )}
        </div>

        <LoadingButton
          type="submit"
          isLoading={isLoading}
          loadingText="Resetting..."
          disabled={!formData.password || !formData.confirmPassword}
          className="h-12 w-full bg-teal-600 text-white hover:bg-teal-700"
        >
          Reset Password
        </LoadingButton>
      </form>

      <div className="text-center">
        <Link
          href="/login"
          className="text-sm text-gray-600 hover:text-gray-800 underline"
        >
          Back to Login
        </Link>
      </div>
    </div>
  );
}
