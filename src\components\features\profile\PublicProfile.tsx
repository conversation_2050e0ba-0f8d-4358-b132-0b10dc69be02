"use client";

import React, { useState } from "react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Icon } from "@iconify/react";
import type { PublicProfileData } from "@/types/ecommerce";

interface PublicProfileProps {
  profileData: PublicProfileData;
  onContactUser: () => void;
  isOwnProfile?: boolean;
}

// Star Rating Component
function StarRating({
  rating,
  size = "w-5 h-5",
}: {
  rating: number;
  size?: string;
}) {
  return (
    <div className="flex items-center gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Icon
          key={star}
          icon="mdi:star"
          className={`${size} ${
            star <= rating
              ? "fill-yellow-500 text-yellow-500"
              : "fill-gray-200 text-gray-200"
          }`}
        />
      ))}
    </div>
  );
}

// Rating Breakdown Component
function RatingBreakdown({
  ratings,
  title,
}: {
  ratings: NonNullable<PublicProfileData["ratings"]>["asSeller"];
  title: string;
}) {
  const total = ratings.total;

  return (
    <div className="space-y-3">
      <h4 className="font-medium text-gray-900">{title}</h4>
      <div className="flex items-center gap-3 mb-3">
        <div className="text-2xl font-bold text-gray-900">
          {ratings.average.toFixed(1)}
        </div>
        <div>
          <StarRating rating={ratings.average} size="w-5 h-5" />
          <p className="text-md text-gray-600 mt-1">
            Based on {total} review{total !== 1 ? "s" : ""}
          </p>
        </div>
      </div>

      {/* Rating bars */}
      <div className="space-y-2">
        {[5, 4, 3, 2, 1].map((stars) => {
          const count =
            ratings.breakdown[stars as keyof typeof ratings.breakdown];
          const percentage = total > 0 ? (count / total) * 100 : 0;

          return (
            <div key={stars} className="flex items-center gap-3 text-md">
              <div className="flex items-center gap-1 min-w-[60px]">
                <span>{stars}</span>
                <Icon
                  icon="mdi:star"
                  className="w-3 h-3 fill-yellow-500 text-yellow-500"
                />
              </div>
              <div className="flex-1 bg-gray-200 rounded-full h-2 overflow-hidden">
                <div
                  className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(percentage, 100)}%` }}
                />
              </div>
              <span className="text-gray-600 min-w-[30px]">{count}</span>
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default function PublicProfile({
  profileData,
  onContactUser,
  isOwnProfile = false,
}: PublicProfileProps) {
  const [activeTab, setActiveTab] = useState<"overview" | "ratings">(
    "overview"
  );

  // Format dates
  const formatDate = (date: string) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }).format(new Date(date));
  };

  const formatLastLogin = (date?: string) => {
    if (!date) return "Never";

    const now = new Date();
    const targetDate = new Date(date);
    const diffInHours = Math.floor(
      (now.getTime() - targetDate.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24)
      return `${diffInHours} hour${diffInHours !== 1 ? "s" : ""} ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7)
      return `${diffInDays} day${diffInDays !== 1 ? "s" : ""} ago`;

    return formatDate(date);
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Profile Header */}
      <Card className=" border border-gray-200 shadow-sm bg-white/20">
        <CardContent className="p-8">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Profile Picture */}
            <div className="flex-shrink-0">
              <div className="relative w-32 h-32 mx-auto md:mx-0">
                {profileData.profilePicture ? (
                  <Image
                    src={profileData.profilePicture}
                    alt={`${profileData.username}'s profile`}
                    fill
                    className="rounded-full object-cover  border-gray-100"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-teal-400 to-teal-600 rounded-full flex items-center justify-center  border-gray-100">
                    <Icon icon="mdi:account" className="w-16 h-16 text-white" />
                  </div>
                )}
              </div>
            </div>

            {/* Profile Info */}
            <div className="flex-1 space-y-4">
              <div className="text-center md:text-left">
                <div className="flex items-center justify-center md:justify-start gap-3 mb-2">
                  <h1 className="text-3xl font-bold text-gray-900">
                    {profileData.username}
                  </h1>
                  {profileData.isVerified && (
                    <Badge className="bg-gradient-to-r from-green-500 to-green-600 text-white">
                      <Icon icon="mdi:check-circle" className="w-3 h-3 mr-1" />
                      Verified
                    </Badge>
                  )}
                </div>

                {/* Overall Rating */}
                <div className="flex items-center justify-center md:justify-start gap-2 mb-3">
                  <StarRating
                    rating={profileData.overallRating || 0}
                    size="w-5 h-5"
                  />
                  <span className="text-lg font-semibold text-gray-900">
                    {(profileData.overallRating || 0).toFixed(1)}
                  </span>
                  <span className="text-gray-600">
                    (
                    {(profileData.ratings?.asSeller.total || 0) +
                      (profileData.ratings?.asBuyer.total || 0)}{" "}
                    reviews)
                  </span>
                </div>
              </div>

              {/* Contact Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-md">
                <div className="space-y-2">
                  {profileData.email && (
                    <div className="flex items-center gap-2 text-gray-600">
                      <Icon icon="mdi:email-outline" className="w-5 h-5" />
                      <span>{profileData.email}</span>
                    </div>
                  )}

                  {profileData.phoneNumber && (
                    <div className="flex items-center gap-2 text-gray-600">
                      <Icon icon="mdi:phone-outline" className="w-5 h-5" />
                      <span>{profileData.phoneNumber}</span>
                    </div>
                  )}

                  <div className="flex items-center gap-2 text-gray-600">
                    <Icon icon="mdi:map-marker" className="w-5 h-5" />
                    <span>{profileData.address?.city}</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-gray-600">
                    <Icon icon="mdi:calendar" className="w-5 h-5" />
                    <span>
                      Member since {formatDate(profileData.createdAt)}
                    </span>
                  </div>

                  <div className="flex items-center gap-2 text-gray-600">
                    <Icon icon="mdi:clock-outline" className="w-5 h-5" />
                    <span>
                      Last login: {formatLastLogin(profileData.lastLogin)}
                    </span>
                  </div>

                  <div className="flex items-center gap-2 text-gray-600">
                    <Icon icon="mdi:eye-outline" className="w-5 h-5" />
                    <span>{profileData.profileViews} profile views</span>
                  </div>
                </div>
              </div>

              {/* Action Button */}
              {!isOwnProfile && (
                <div className="pt-4">
                  <Button
                    onClick={onContactUser}
                    className="w-full md:w-auto bg-teal-600 hover:bg-teal-700 text-white px-8 py-3"
                  >
                    <Icon icon="mdi:message-outline" className="w-5 h-5 mr-2" />
                    Contact
                  </Button>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="border border-gray-200 shadow-sm bg-white/20 rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mx-auto mb-3">
              <Icon
                icon="mdi:shopping-outline"
                className="w-6 h-6 text-blue-600"
              />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {profileData.totalListings}
            </div>
            <div className="text-md text-gray-600">Total Listings</div>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mx-auto mb-3">
              <Icon icon="mdi:trending-up" className="w-6 h-6 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {profileData.totalSold}
            </div>
            <div className="text-md text-gray-600">Items Sold</div>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mx-auto mb-3">
              <Icon
                icon="mdi:trophy-award"
                className="w-6 h-6 text-purple-600"
              />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {profileData.totalBought}
            </div>
            <div className="text-md text-gray-600">Items Bought</div>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-full mx-auto mb-3">
              <Icon
                icon="mdi:clock-outline"
                className="w-6 h-6 text-orange-600"
              />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {profileData.responseRate}%
            </div>
            <div className="text-md text-gray-600">Response Rate</div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Card className=" border border-gray-200 shadow-sm bg-white/20">
        <CardHeader>
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            <button
              onClick={() => setActiveTab("overview")}
              className={`flex-1 py-2 px-4 rounded-md text-md font-medium transition-colors ${
                activeTab === "overview"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab("ratings")}
              className={`flex-1 py-2 px-4 rounded-md text-md font-medium transition-colors ${
                activeTab === "ratings"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              Ratings & Reviews
            </button>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          {activeTab === "overview" && (
            <div className="space-y-6">
              <div className="text-center py-8">
                <Icon
                  icon="mdi:shield-check"
                  className="w-16 h-16 text-gray-400 mx-auto mb-4"
                />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Trusted Member
                </h3>
                <p className="text-gray-600 max-w-md mx-auto">
                  This user has been an active member of our community since{" "}
                  {formatDate(profileData.createdAt)}
                  and maintains a {profileData.responseRate}% response rate.
                </p>
              </div>
            </div>
          )}

          {activeTab === "ratings" && profileData.ratings && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <RatingBreakdown
                ratings={profileData.ratings.asSeller}
                title="As Seller"
              />
              <RatingBreakdown
                ratings={profileData.ratings.asBuyer}
                title="As Buyer"
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
