import { Suspense } from "react";
import ResetPasswordForm from "@/components/features/forms/ResetPasswordForm";
import { Header } from "@/components";
import { PageLoading } from "@/components/ui";

interface ResetPasswordPageProps {
  searchParams: Promise<{ token?: string }>;
}

async function ResetPasswordContent({ searchParams }: ResetPasswordPageProps) {
  const params = await searchParams;

  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <Header />
      <div className="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-md">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <ResetPasswordForm token={params.token} />
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ResetPasswordPage({
  searchParams,
}: ResetPasswordPageProps) {
  return (
    <Suspense
      fallback={<PageLoading message="Loading reset password page..." />}
    >
      <ResetPasswordContent searchParams={searchParams} />
    </Suspense>
  );
}

export const metadata = {
  title: "Reset Password",
  description: "Set your new password",
};
