"use client";

import type React from "react";
import { useState, useMemo } from "react";
import { Head<PERSON>, Footer } from "@/components";
import { SalesStats } from "./components/SalesStats";
import { TransactionHistory } from "./components/TransactionHistory";
import {
  generateMockTransactions,
  filterTransactions,
  paginateTransactions,
} from "./utils/mockData";
import type { TransactionFilters, PaginationInfo } from "./types";
import { BackButton } from "@/components/ui";
import { useRouter } from "next/navigation";

const mockTransactions = generateMockTransactions();

export default function SalesDashboard() {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  const itemsPerPage = 3;

  // Memoized filtered transactions for performance
  const filteredTransactions = useMemo(() => {
    return filterTransactions(mockTransactions, searchTerm, statusFilter);
  }, [searchTerm, statusFilter]);

  // Memoized pagination info
  const paginationInfo: PaginationInfo = useMemo(() => {
    const totalItems = filteredTransactions.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;

    return {
      currentPage,
      itemsPerPage,
      totalItems,
      totalPages,
      startIndex,
      endIndex,
    };
  }, [currentPage, filteredTransactions.length, itemsPerPage]);

  // Get current page transactions
  const currentTransactions = useMemo(() => {
    return paginateTransactions(
      filteredTransactions,
      currentPage,
      itemsPerPage
    );
  }, [filteredTransactions, currentPage, itemsPerPage]);

  // Event handlers
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const filters: TransactionFilters = {
    searchTerm,
    statusFilter,
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
      <Header />
      <div className="mx-4 sm:mx-6 lg:mx-[5%] xl:mx-[10%] 2xl:mx-[10%]">
        {/* back button  */}
        <div className="mb-6">
          <BackButton onClick={() => router.back()} size="lg" />
        </div>
        <div className="min-h-screen bg-gray-50 p-8">
          <div className="max-w-8xl mx-auto  space-y-6">
            {/* Header */}
            <div className="mb-8 ">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Sales Dashboard
              </h1>
              <p className="text-gray-600">
                Track your marketplace performance
              </p>
            </div>

            {/* Sales Statistics */}
            <SalesStats />

            {/* Transaction History */}
            <TransactionHistory
              transactions={currentTransactions}
              filters={filters}
              pagination={paginationInfo}
              onSearchChange={handleSearchChange}
              onStatusFilterChange={handleStatusFilterChange}
              onPageChange={handlePageChange}
            />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
