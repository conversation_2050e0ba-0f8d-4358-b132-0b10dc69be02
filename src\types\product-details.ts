// Product Details API Types
// Types for reviews, Q&A, seller information, and related product detail features

// Review Types
export interface Review {
  id: string;
  productId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number; // 1-5 stars
  title?: string;
  comment: string;
  isVerifiedPurchase: boolean;
  helpfulCount: number;
  notHelpfulCount: number;
  images?: string[]; // Review images
  createdAt: string;
  updatedAt?: string;
}

export interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}

export interface CreateReviewRequest {
  productId: string;
  rating: number;
  title?: string;
  comment: string;
  images?: string[];
}

export interface UpdateReviewRequest {
  rating?: number;
  title?: string;
  comment?: string;
  images?: string[];
}

export interface ReviewHelpfulRequest {
  reviewId: string;
  helpful: boolean; // true for helpful, false for not helpful
}

export interface PaginatedReviewsResponse {
  data: Review[];
  stats: ReviewStats;
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Q&A Types
export interface ProductQuestion {
  id: string;
  productId: string;
  question: string;
  askedBy: string;
  askedByUserId: string;
  askedByAvatar?: string;
  askedAt: string;
  answer?: string;
  answeredBy?: string;
  answeredByUserId?: string;
  answeredByAvatar?: string;
  answeredAt?: string;
  helpfulCount: number;
  isSellerAnswer: boolean;
  status: 'pending' | 'answered' | 'hidden';
}

export interface CreateQuestionRequest {
  productId: string;
  question: string;
}

export interface CreateAnswerRequest {
  questionId: string;
  answer: string;
}

export interface QuestionHelpfulRequest {
  questionId: string;
  helpful: boolean;
}

export interface PaginatedQuestionsResponse {
  data: ProductQuestion[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Seller Information Types
export interface SellerStats {
  id: string;
  username: string;
  fullName?: string;
  avatar?: string;
  rating: number;
  totalRatings: number;
  responseRate: number;
  responseTime: string; // e.g., "Within 2 hours"
  memberSince: string;
  totalListings: number;
  activeListings: number;
  soldListings: number;
  location?: string;
  isVerified: boolean;
  lastActive?: string;
  badges?: string[]; // e.g., ["Top Seller", "Fast Responder"]
}

export interface SellerReview {
  id: string;
  reviewerId: string;
  reviewerName: string;
  reviewerAvatar?: string;
  rating: number;
  comment: string;
  transactionType: 'purchase' | 'sale';
  productTitle?: string;
  createdAt: string;
}

export interface SellerReviewsResponse {
  data: SellerReview[];
  stats: {
    totalReviews: number;
    averageRating: number;
    ratingDistribution: {
      5: number;
      4: number;
      3: number;
      2: number;
      1: number;
    };
  };
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Similar Products Types
export interface SimilarProductsRequest {
  productId: string;
  limit?: number;
  excludeOwn?: boolean; // Exclude products from same seller
}

export interface SimilarProductsResponse {
  data: Array<{
    id: string;
    title: string;
    slug: string;
    price: number;
    currency: string;
    images: string[];
    location: string;
    condition: string;
    createdAt: string;
    seller: {
      id: string;
      name: string;
      avatar?: string;
      rating?: number;
    };
    similarity: number; // 0-1 similarity score
    reason: string; // Why it's similar: "Same category", "Similar price", etc.
  }>;
}

// Contact Seller Types
export interface ContactSellerRequest {
  productId: string;
  sellerId: string;
  message: string;
  contactMethod: 'message' | 'phone' | 'email';
  buyerInfo?: {
    name?: string;
    phone?: string;
    email?: string;
  };
}

export interface ContactSellerResponse {
  success: boolean;
  message: string;
  conversationId?: string;
}

// Product View Tracking
export interface ProductViewRequest {
  productId: string;
  viewerInfo?: {
    location?: string;
    userAgent?: string;
    referrer?: string;
  };
}

export interface ProductViewResponse {
  success: boolean;
  totalViews: number;
}

// Wishlist/Favorites Types
export interface WishlistRequest {
  productId: string;
}

export interface WishlistResponse {
  success: boolean;
  message: string;
  isInWishlist: boolean;
  totalWishlistCount: number;
}

// Report Product Types
export interface ReportProductRequest {
  productId: string;
  reason: 'spam' | 'inappropriate' | 'fake' | 'sold' | 'duplicate' | 'other';
  description?: string;
  reporterInfo?: {
    email?: string;
    phone?: string;
  };
}

export interface ReportProductResponse {
  success: boolean;
  message: string;
  reportId: string;
}

// API Query Parameters
export interface GetReviewsParams {
  productId: string;
  page?: number;
  limit?: number;
  sortBy?: 'newest' | 'oldest' | 'rating_high' | 'rating_low' | 'helpful';
  rating?: number; // Filter by specific rating
}

export interface GetQuestionsParams {
  productId: string;
  page?: number;
  limit?: number;
  sortBy?: 'newest' | 'oldest' | 'helpful';
  status?: 'all' | 'answered' | 'pending';
}

export interface GetSellerReviewsParams {
  sellerId: string;
  page?: number;
  limit?: number;
  sortBy?: 'newest' | 'oldest' | 'rating_high' | 'rating_low';
  transactionType?: 'purchase' | 'sale' | 'all';
}

// Error Types
export interface ProductDetailsApiError {
  message: string;
  code: string;
  statusCode: number;
  details?: Record<string, any>;
}
