"use client";

import React from "react";
import { PageLayout, CategoryProductSectionsWrapper } from "@/components";
import { CategoriesSection } from "@/components/layout/CategoriesSection";
import { AdBanner } from "@/components/features/marketing/banner";

export default function CategoriesPage() {
  return (
    <PageLayout
      showBreadcrumb={true}
      breadcrumbContent={
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <span className="hover:text-gray-900 transition-colors">Home</span>
          <span className="text-gray-400">/</span>
          <span className="text-gray-900 font-medium">All Categories</span>
        </div>
      }
    >
      {/* Ads Banner below navbar */}
      <div className="w-full mb-6">
        <div className="mx-[5%]">
          <AdBanner
            className="w-full"
            autoPlay={true}
            showControls={true}
            muted={true}
          />
        </div>
      </div>

      {/* Categories Grid */}
      <div className="container-responsive py-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            All Categories
          </h1>
          <p className="text-gray-600">Browse products by category</p>
        </div>

        <CategoriesSection />
      </div>

      {/* Full Width Category Product Sections */}
      <CategoryProductSectionsWrapper
        categoryProductSectionIds={undefined} // Use all available categories from API
      />
    </PageLayout>
  );
}
