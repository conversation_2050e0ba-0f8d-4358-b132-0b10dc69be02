import { Product, ActiveFilters, SortBy } from "@/types/ecommerce";

// Filter products based on active filters
export function filterProducts(
  products: Product[],
  filters: ActiveFilters
): Product[] {
  return products.filter((product) => {
    // Condition filter
    if (filters.condition && filters.condition.length > 0) {
      if (!filters.condition.includes(product.condition as any)) {
        return false;
      }
    }

    // Type filter (subcategory)
    if (filters.type && filters.type.length > 0) {
      const productType = product.subcategory || product.category;
      const matchesType = filters.type.some(
        (type) =>
          type.toLowerCase().includes(productType.toLowerCase()) ||
          productType.toLowerCase().includes(type.toLowerCase()) ||
          product.title.toLowerCase().includes(type.toLowerCase())
      );
      if (!matchesType) {
        return false;
      }
    }

    // Location filter
    if (filters.location && filters.location.length > 0) {
      if (!filters.location.includes(product.location)) {
        return false;
      }
    }

    // Delivery filter
    if (filters.delivery && filters.delivery.length > 0) {
      const hasHomeDelivery =
        product.delivery.available &&
        (product.delivery.type === "home" || product.delivery.type === "both");
      const hasPickup =
        product.delivery.type === "pickup" || product.delivery.type === "both";

      const matchesDelivery = filters.delivery.some((deliveryType) => {
        if (deliveryType === "home") return hasHomeDelivery;
        if (deliveryType === "pickup") return hasPickup;
        return false;
      });

      if (!matchesDelivery) {
        return false;
      }
    }

    // Brand filter
    if (filters.brand && filters.brand.length > 0) {
      if (!product.brand || !filters.brand.includes(product.brand)) {
        return false;
      }
    }

    // Price range filter
    if (filters.priceRange) {
      const { min, max } = filters.priceRange;
      if (min !== undefined && product.price < min) {
        return false;
      }
      if (max !== undefined && product.price > max) {
        return false;
      }
    }

    // Posted within filter
    if (filters.postedWithin) {
      const now = new Date();
      const postedDate = new Date(product.postedAt);
      const diffInDays = Math.floor(
        (now.getTime() - postedDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      switch (filters.postedWithin) {
        case "1day":
          if (diffInDays > 1) return false;
          break;
        case "3days":
          if (diffInDays > 3) return false;
          break;
        case "7days":
          if (diffInDays > 7) return false;
          break;
        case "30days":
          if (diffInDays > 30) return false;
          break;
      }
    }

    // Category filter (for subcategory filtering within a main category)
    if (filters.category && filters.category.length > 0) {
      const productCategory = product.subcategory || product.category;
      const matchesCategory = filters.category.some(
        (cat) =>
          cat.toLowerCase().includes(productCategory.toLowerCase()) ||
          productCategory.toLowerCase().includes(cat.toLowerCase()) ||
          product.title.toLowerCase().includes(cat.toLowerCase())
      );
      if (!matchesCategory) {
        return false;
      }
    }

    // Breed filter (for pets)
    if (filters.breed && filters.breed.length > 0) {
      const matchesBreed = filters.breed.some(
        (breed) =>
          product.title.toLowerCase().includes(breed.toLowerCase()) ||
          product.description.toLowerCase().includes(breed.toLowerCase())
      );
      if (!matchesBreed) {
        return false;
      }
    }

    // Gender filter
    if (filters.gender && filters.gender.length > 0) {
      const matchesGender = filters.gender.some(
        (gender) =>
          product.title.toLowerCase().includes(gender.toLowerCase()) ||
          product.description.toLowerCase().includes(gender.toLowerCase())
      );
      if (!matchesGender) {
        return false;
      }
    }

    // Materials filter (for jewelry, art & crafts)
    if (filters.materials && filters.materials.length > 0) {
      const matchesMaterials = filters.materials.some(
        (material) =>
          product.title.toLowerCase().includes(material.toLowerCase()) ||
          product.description.toLowerCase().includes(material.toLowerCase())
      );
      if (!matchesMaterials) {
        return false;
      }
    }

    // Size filter (for IT, fashion)
    if (filters.size && filters.size.length > 0) {
      const matchesSize = filters.size.some(
        (size) =>
          product.title.toLowerCase().includes(size.toLowerCase()) ||
          product.description.toLowerCase().includes(size.toLowerCase())
      );
      if (!matchesSize) {
        return false;
      }
    }

    // Year range filter (for vehicles)
    if (filters.yearRange) {
      const { min, max } = filters.yearRange;
      // Extract year from product title or description
      const yearMatch =
        product.title.match(/\b(19|20)\d{2}\b/) ||
        product.description.match(/\b(19|20)\d{2}\b/);
      if (yearMatch) {
        const productYear = parseInt(yearMatch[0]);
        if (min !== undefined && productYear < min) {
          return false;
        }
        if (max !== undefined && productYear > max) {
          return false;
        }
      }
    }

    // Salary range filter (for jobs)
    if (filters.salaryRange) {
      const { min, max } = filters.salaryRange;
      // For jobs, we might use the price field as salary
      if (min !== undefined && product.price < min) {
        return false;
      }
      if (max !== undefined && product.price > max) {
        return false;
      }
    }

    // Destination type filter (for travel)
    if (filters.destinationType && filters.destinationType.length > 0) {
      const matchesDestination = filters.destinationType.some(
        (dest) =>
          product.title.toLowerCase().includes(dest.toLowerCase()) ||
          product.description.toLowerCase().includes(dest.toLowerCase())
      );
      if (!matchesDestination) {
        return false;
      }
    }

    // Travel type filter
    if (filters.travelType && filters.travelType.length > 0) {
      const matchesTravelType = filters.travelType.some(
        (travelType) =>
          product.title.toLowerCase().includes(travelType.toLowerCase()) ||
          product.description.toLowerCase().includes(travelType.toLowerCase())
      );
      if (!matchesTravelType) {
        return false;
      }
    }

    // Duration filter (for travel)
    if (filters.duration && filters.duration.length > 0) {
      const matchesDuration = filters.duration.some(
        (duration) =>
          product.title.toLowerCase().includes(duration.toLowerCase()) ||
          product.description.toLowerCase().includes(duration.toLowerCase())
      );
      if (!matchesDuration) {
        return false;
      }
    }

    // Purpose filter (for property)
    if (filters.purpose && filters.purpose.length > 0) {
      const matchesPurpose = filters.purpose.some(
        (purpose) =>
          product.title.toLowerCase().includes(purpose.toLowerCase()) ||
          product.description.toLowerCase().includes(purpose.toLowerCase())
      );
      if (!matchesPurpose) {
        return false;
      }
    }

    return true;
  });
}

// Sort products based on sort criteria
export function sortProducts(products: Product[], sortBy: SortBy): Product[] {
  const sortedProducts = [...products];

  switch (sortBy) {
    case "newest":
      return sortedProducts.sort(
        (a, b) =>
          new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime()
      );

    case "oldest":
      return sortedProducts.sort(
        (a, b) =>
          new Date(a.postedAt).getTime() - new Date(b.postedAt).getTime()
      );

    case "price-low":
      return sortedProducts.sort((a, b) => a.price - b.price);

    case "price-high":
      return sortedProducts.sort((a, b) => b.price - a.price);

    case "relevance":
      // For relevance, we can prioritize featured products and then by newest
      return sortedProducts.sort((a, b) => {
        if (a.featured && !b.featured) return -1;
        if (!a.featured && b.featured) return 1;
        return new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime();
      });

    case "top-review":
      // Sort by seller's overall rating (highest first), then by newest
      return sortedProducts.sort((a, b) => {
        const ratingA = a.seller.rating || 0;
        const ratingB = b.seller.rating || 0;

        if (ratingA !== ratingB) {
          return ratingB - ratingA; // Higher rating first
        }

        // If ratings are equal, sort by newest
        return new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime();
      });

    default:
      return sortedProducts;
  }
}

// Filter products by category
export function filterByCategory(
  products: Product[],
  categoryId: string | null
): Product[] {
  if (!categoryId) {
    return products;
  }

  return products.filter((product) => product.category === categoryId);
}

// Search products by query
export function searchProducts(products: Product[], query: string): Product[] {
  if (!query.trim()) {
    return products;
  }

  const searchTerm = query.toLowerCase().trim();

  return products.filter((product) => {
    return (
      product.title.toLowerCase().includes(searchTerm) ||
      product.description.toLowerCase().includes(searchTerm) ||
      product.location.toLowerCase().includes(searchTerm) ||
      product.seller.name.toLowerCase().includes(searchTerm) ||
      (product.brand && product.brand.toLowerCase().includes(searchTerm)) ||
      (product.subcategory &&
        product.subcategory.toLowerCase().includes(searchTerm))
    );
  });
}

// Get available filter options based on current products
export function getAvailableFilterOptions(products: Product[]) {
  const conditions = new Set<string>();
  const types = new Set<string>();
  const locations = new Set<string>();
  const brands = new Set<string>();
  let minPrice = Infinity;
  let maxPrice = -Infinity;

  products.forEach((product) => {
    conditions.add(product.condition);
    if (product.subcategory) types.add(product.subcategory);
    locations.add(product.location);
    if (product.brand) brands.add(product.brand);
    minPrice = Math.min(minPrice, product.price);
    maxPrice = Math.max(maxPrice, product.price);
  });

  return {
    condition: Array.from(conditions) as Array<"new" | "used" | "refurbished">,
    type: Array.from(types),
    location: Array.from(locations),
    delivery: ["home", "pickup"] as Array<"home" | "pickup">,
    brand: Array.from(brands),
    priceRange: {
      min: minPrice === Infinity ? 0 : minPrice,
      max: maxPrice === -Infinity ? 100000 : maxPrice,
    },
    postedWithin: "7days" as const,
  };
}

// Filter products by location
export function filterByLocation(
  products: Product[],
  location: string
): Product[] {
  if (!location || location === "all") {
    return products;
  }

  return products.filter((product) =>
    product.location.toLowerCase().includes(location.toLowerCase())
  );
}

// Combine all filtering logic
export function applyFiltersAndSort(
  products: Product[],
  categoryId: string | null,
  searchQuery: string,
  searchLocation: string,
  filters: ActiveFilters,
  sortBy: SortBy
): Product[] {
  let filteredProducts = products;

  // Apply category filter
  filteredProducts = filterByCategory(filteredProducts, categoryId);

  // Apply location filter
  filteredProducts = filterByLocation(filteredProducts, searchLocation);

  // Apply search filter
  filteredProducts = searchProducts(filteredProducts, searchQuery);

  // Apply other filters
  filteredProducts = filterProducts(filteredProducts, filters);

  // Apply sorting
  filteredProducts = sortProducts(filteredProducts, sortBy);

  return filteredProducts;
}
