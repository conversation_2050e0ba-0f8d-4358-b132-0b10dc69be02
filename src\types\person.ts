// Person management types for the application

export interface Person {
  id: string;
  personId: string; // Display ID like "01", "02", etc.
  name: string;
  type: PersonType;
  class?: string; // Optional for non-student types
  section?: string; // Optional field
  profilePicture?: string; // URL to the profile picture
  createdAt: string;
  updatedAt: string;
}

export enum PersonType {
  STUDENT = "Student",
  TEACHER = "Teacher",
  STAFF = "Staff",
  ADMIN = "Admin",
}

export interface PersonFormData {
  personId: string;
  name: string;
  type: PersonType;
  class?: string;
  section?: string;
  profilePicture?: File | string; // File for new uploads, string for existing URLs
}

export interface EditPersonFormProps {
  person?: Person;
  onSave: (data: PersonFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export interface EditPhotoModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentImage?: string;
  onPhotoUpdate: (newImageUrl: string) => void;
  personName: string;
}

// API related types
export interface CreatePersonRequest {
  personId: string;
  name: string;
  type: PersonType;
  class?: string;
  section?: string;
}

export interface UpdatePersonRequest extends Partial<CreatePersonRequest> {
  id: string;
}

export interface PersonResponse {
  success: boolean;
  data: Person;
  message?: string;
}

export interface PersonListResponse {
  success: boolean;
  data: Person[];
  total: number;
  page: number;
  limit: number;
  message?: string;
}

// Form validation types
export interface PersonFormErrors {
  personId?: string;
  name?: string;
  type?: string;
  class?: string;
  section?: string;
  profilePicture?: string;
}

// Photo upload types
export interface PhotoUploadResponse {
  success: boolean;
  imageUrl: string;
  message?: string;
}
