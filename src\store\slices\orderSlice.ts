import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import {
  OrderResponseDto,
  AddressResponseDto,
  CreateAddressDto,
  UpdateAddressDto,
  CheckoutDto,
  CartValidationResponse,
  OrderFilterDto,
  PaginatedOrdersResponse,
  OrderStatusUpdateDto,
  CheckoutState,
  OrderStatus,
  PaymentMethod,
} from "@/types/orders";
import { OrderService } from "@/services/order-service";

// Define the order state interface
export interface OrderState {
  // Orders
  orders: OrderResponseDto[];
  currentOrder?: OrderResponseDto;
  ordersLoading: boolean;
  ordersError?: string;
  ordersPagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };

  // Addresses
  addresses: AddressResponseDto[];
  addressesLoading: boolean;
  addressesError?: string;

  // Checkout
  checkout: CheckoutState;

  // Cart validation
  cartValidation?: CartValidationResponse;
  validationLoading: boolean;
  validationError?: string;
}

// Initial state
const initialState: OrderState = {
  orders: [],
  currentOrder: undefined,
  ordersLoading: false,
  ordersError: undefined,
  ordersPagination: {
    page: 1,
    limit: 10,
    total: 0,
    hasMore: false,
  },

  addresses: [],
  addressesLoading: false,
  addressesError: undefined,

  checkout: {
    step: 'address',
    selectedAddress: undefined,
    selectedPaymentMethod: undefined,
    orderData: undefined,
    loading: false,
    error: undefined,
  },

  cartValidation: undefined,
  validationLoading: false,
  validationError: undefined,
};

// ===== ASYNC THUNKS =====

// Address Management
export const fetchAddresses = createAsyncThunk(
  "order/fetchAddresses",
  async (_, { rejectWithValue }) => {
    try {
      return await OrderService.getUserAddresses();
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to fetch addresses");
    }
  }
);

export const createAddress = createAsyncThunk(
  "order/createAddress",
  async (data: CreateAddressDto, { rejectWithValue }) => {
    try {
      return await OrderService.createAddress(data);
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to create address");
    }
  }
);

export const updateAddress = createAsyncThunk(
  "order/updateAddress",
  async (
    { addressId, data }: { addressId: string; data: UpdateAddressDto },
    { rejectWithValue }
  ) => {
    try {
      return await OrderService.updateAddress(addressId, data);
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to update address");
    }
  }
);

export const deleteAddress = createAsyncThunk(
  "order/deleteAddress",
  async (addressId: string, { rejectWithValue }) => {
    try {
      await OrderService.deleteAddress(addressId);
      return addressId;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to delete address");
    }
  }
);

export const setDefaultAddress = createAsyncThunk(
  "order/setDefaultAddress",
  async (addressId: string, { rejectWithValue }) => {
    try {
      return await OrderService.setDefaultAddress(addressId);
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to set default address");
    }
  }
);

// Cart Validation
export const validateCart = createAsyncThunk(
  "order/validateCart",
  async (_, { rejectWithValue }) => {
    try {
      return await OrderService.validateCart();
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to validate cart");
    }
  }
);

// Checkout
export const processCheckout = createAsyncThunk(
  "order/processCheckout",
  async (data: CheckoutDto, { rejectWithValue }) => {
    try {
      return await OrderService.processCheckout(data);
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to process checkout");
    }
  }
);

// Order Management
export const fetchOrders = createAsyncThunk(
  "order/fetchOrders",
  async (filters: OrderFilterDto = {}, { rejectWithValue }) => {
    try {
      return await OrderService.getUserOrders(filters);
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to fetch orders");
    }
  }
);

export const fetchOrderById = createAsyncThunk(
  "order/fetchOrderById",
  async (orderId: string, { rejectWithValue }) => {
    try {
      return await OrderService.getOrderById(orderId);
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to fetch order");
    }
  }
);

export const cancelOrder = createAsyncThunk(
  "order/cancelOrder",
  async (orderId: string, { rejectWithValue }) => {
    try {
      return await OrderService.cancelOrder(orderId);
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to cancel order");
    }
  }
);

export const updateOrderStatus = createAsyncThunk(
  "order/updateOrderStatus",
  async (
    { orderId, data }: { orderId: string; data: OrderStatusUpdateDto },
    { rejectWithValue }
  ) => {
    try {
      return await OrderService.updateOrderStatus(orderId, data);
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to update order status");
    }
  }
);

// Create the order slice
const orderSlice = createSlice({
  name: "order",
  initialState,
  reducers: {
    // Checkout actions
    setCheckoutStep: (state, action: PayloadAction<CheckoutState['step']>) => {
      state.checkout.step = action.payload;
    },
    setSelectedAddress: (state, action: PayloadAction<AddressResponseDto | undefined>) => {
      state.checkout.selectedAddress = action.payload;
    },
    setSelectedPaymentMethod: (state, action: PayloadAction<PaymentMethod | undefined>) => {
      state.checkout.selectedPaymentMethod = action.payload;
    },
    resetCheckout: (state) => {
      state.checkout = {
        step: 'address',
        selectedAddress: undefined,
        selectedPaymentMethod: undefined,
        orderData: undefined,
        loading: false,
        error: undefined,
      };
    },
    setCheckoutError: (state, action: PayloadAction<string | undefined>) => {
      state.checkout.error = action.payload;
    },
    clearCheckoutError: (state) => {
      state.checkout.error = undefined;
    },

    // Order actions
    clearOrdersError: (state) => {
      state.ordersError = undefined;
    },
    clearAddressesError: (state) => {
      state.addressesError = undefined;
    },
    clearValidationError: (state) => {
      state.validationError = undefined;
    },

    // Pagination
    setOrdersPagination: (state, action: PayloadAction<Partial<OrderState['ordersPagination']>>) => {
      state.ordersPagination = { ...state.ordersPagination, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch addresses
      .addCase(fetchAddresses.pending, (state) => {
        state.addressesLoading = true;
        state.addressesError = undefined;
      })
      .addCase(fetchAddresses.fulfilled, (state, action) => {
        state.addressesLoading = false;
        state.addresses = action.payload;
      })
      .addCase(fetchAddresses.rejected, (state, action) => {
        state.addressesLoading = false;
        state.addressesError = action.payload as string;
      })

      // Create address
      .addCase(createAddress.pending, (state) => {
        state.addressesLoading = true;
        state.addressesError = undefined;
      })
      .addCase(createAddress.fulfilled, (state, action) => {
        state.addressesLoading = false;
        state.addresses.push(action.payload);
        // If this is the first address or marked as default, update other addresses
        if (action.payload.isDefault) {
          state.addresses.forEach(addr => {
            if (addr.id !== action.payload.id) {
              addr.isDefault = false;
            }
          });
        }
      })
      .addCase(createAddress.rejected, (state, action) => {
        state.addressesLoading = false;
        state.addressesError = action.payload as string;
      })

      // Update address
      .addCase(updateAddress.pending, (state) => {
        state.addressesLoading = true;
        state.addressesError = undefined;
      })
      .addCase(updateAddress.fulfilled, (state, action) => {
        state.addressesLoading = false;
        const index = state.addresses.findIndex(addr => addr.id === action.payload.id);
        if (index !== -1) {
          state.addresses[index] = action.payload;
          // If this address is now default, update other addresses
          if (action.payload.isDefault) {
            state.addresses.forEach(addr => {
              if (addr.id !== action.payload.id) {
                addr.isDefault = false;
              }
            });
          }
        }
      })
      .addCase(updateAddress.rejected, (state, action) => {
        state.addressesLoading = false;
        state.addressesError = action.payload as string;
      })

      // Delete address
      .addCase(deleteAddress.pending, (state) => {
        state.addressesLoading = true;
        state.addressesError = undefined;
      })
      .addCase(deleteAddress.fulfilled, (state, action) => {
        state.addressesLoading = false;
        state.addresses = state.addresses.filter(addr => addr.id !== action.payload);
      })
      .addCase(deleteAddress.rejected, (state, action) => {
        state.addressesLoading = false;
        state.addressesError = action.payload as string;
      })

      // Set default address
      .addCase(setDefaultAddress.pending, (state) => {
        state.addressesLoading = true;
        state.addressesError = undefined;
      })
      .addCase(setDefaultAddress.fulfilled, (state, action) => {
        state.addressesLoading = false;
        // Update all addresses to reflect the new default
        state.addresses.forEach(addr => {
          addr.isDefault = addr.id === action.payload.id;
        });
      })
      .addCase(setDefaultAddress.rejected, (state, action) => {
        state.addressesLoading = false;
        state.addressesError = action.payload as string;
      })

      // Validate cart
      .addCase(validateCart.pending, (state) => {
        state.validationLoading = true;
        state.validationError = undefined;
      })
      .addCase(validateCart.fulfilled, (state, action) => {
        state.validationLoading = false;
        state.cartValidation = action.payload;
      })
      .addCase(validateCart.rejected, (state, action) => {
        state.validationLoading = false;
        state.validationError = action.payload as string;
      })

      // Process checkout
      .addCase(processCheckout.pending, (state) => {
        state.checkout.loading = true;
        state.checkout.error = undefined;
      })
      .addCase(processCheckout.fulfilled, (state, action) => {
        state.checkout.loading = false;
        state.checkout.orderData = action.payload;
        state.checkout.step = 'complete';
        // Add the new order to the orders list
        state.orders.unshift(action.payload);
      })
      .addCase(processCheckout.rejected, (state, action) => {
        state.checkout.loading = false;
        state.checkout.error = action.payload as string;
      })

      // Fetch orders
      .addCase(fetchOrders.pending, (state) => {
        state.ordersLoading = true;
        state.ordersError = undefined;
      })
      .addCase(fetchOrders.fulfilled, (state, action) => {
        state.ordersLoading = false;
        const { orders, total, page, limit } = action.payload;
        
        if (page === 1) {
          state.orders = orders;
        } else {
          state.orders.push(...orders);
        }
        
        state.ordersPagination = {
          page,
          limit,
          total,
          hasMore: orders.length === limit,
        };
      })
      .addCase(fetchOrders.rejected, (state, action) => {
        state.ordersLoading = false;
        state.ordersError = action.payload as string;
      })

      // Fetch order by ID
      .addCase(fetchOrderById.pending, (state) => {
        state.ordersLoading = true;
        state.ordersError = undefined;
      })
      .addCase(fetchOrderById.fulfilled, (state, action) => {
        state.ordersLoading = false;
        state.currentOrder = action.payload;
        // Update the order in the orders list if it exists
        const index = state.orders.findIndex(order => order.id === action.payload.id);
        if (index !== -1) {
          state.orders[index] = action.payload;
        }
      })
      .addCase(fetchOrderById.rejected, (state, action) => {
        state.ordersLoading = false;
        state.ordersError = action.payload as string;
      })

      // Cancel order
      .addCase(cancelOrder.pending, (state) => {
        state.ordersLoading = true;
        state.ordersError = undefined;
      })
      .addCase(cancelOrder.fulfilled, (state, action) => {
        state.ordersLoading = false;
        // Update the order in the orders list
        const index = state.orders.findIndex(order => order.id === action.payload.id);
        if (index !== -1) {
          state.orders[index] = action.payload;
        }
        // Update current order if it's the same
        if (state.currentOrder?.id === action.payload.id) {
          state.currentOrder = action.payload;
        }
      })
      .addCase(cancelOrder.rejected, (state, action) => {
        state.ordersLoading = false;
        state.ordersError = action.payload as string;
      })

      // Update order status
      .addCase(updateOrderStatus.pending, (state) => {
        state.ordersLoading = true;
        state.ordersError = undefined;
      })
      .addCase(updateOrderStatus.fulfilled, (state, action) => {
        state.ordersLoading = false;
        // Update the order in the orders list
        const index = state.orders.findIndex(order => order.id === action.payload.id);
        if (index !== -1) {
          state.orders[index] = action.payload;
        }
        // Update current order if it's the same
        if (state.currentOrder?.id === action.payload.id) {
          state.currentOrder = action.payload;
        }
      })
      .addCase(updateOrderStatus.rejected, (state, action) => {
        state.ordersLoading = false;
        state.ordersError = action.payload as string;
      });
  },
});

// Export actions
export const {
  setCheckoutStep,
  setSelectedAddress,
  setSelectedPaymentMethod,
  resetCheckout,
  setCheckoutError,
  clearCheckoutError,
  clearOrdersError,
  clearAddressesError,
  clearValidationError,
  setOrdersPagination,
} = orderSlice.actions;

// Export reducer
export default orderSlice.reducer;
