import { useState, useCallback, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useEcommerce } from "@/store/compatibility";
import { useEcommerceActions } from "@/store/hooks";
import { getCategoryUrl } from "@/utils/category-utils";
import type { Category } from "@/types/ecommerce";

/**
 * Custom hook to manage All Categories Dropdown state and logic
 */
export function useAllCategoriesDropdown() {
  const router = useRouter();
  const { state } = useEcommerce();
  const { selectCategory } = useEcommerceActions();
  const { categories, selectedCategory } = state.category;

  // Local state
  const [isOpen, setIsOpen] = useState(false);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set()
  );

  // Memoized selected category data
  const selectedCategoryData = useMemo(() => {
    return selectedCategory
      ? categories.find((cat: Category) => cat.id === selectedCategory) || null
      : null;
  }, [selectedCategory, categories]);

  // Toggle dropdown visibility
  const handleToggleDropdown = useCallback(() => {
    const newIsOpen = !isOpen;
    setIsOpen(newIsOpen);

    if (newIsOpen) {
      // When opening dropdown, clear selected category and reset expanded categories
      selectCategory(null);
      setExpandedCategories(new Set());
    }
    // Note: We don't toggle filters here anymore since we show categories in sidebar
  }, [isOpen, selectCategory]);

  // Handle category selection
  const handleCategorySelect = useCallback(
    (categoryId: string) => {
      // Find the category to get its slug and id for URL generation
      const category = categories.find(
        (cat: Category) => cat.id === categoryId
      );
      if (category) {
        // Navigate to the category page
        router.push(getCategoryUrl(category));
      }

      selectCategory(categoryId);
      setIsOpen(false);
      setExpandedCategories(new Set());
    },
    [selectCategory, categories, router]
  );

  // Toggle category expansion
  const handleToggleExpand = useCallback((categoryId: string) => {
    setExpandedCategories((prev) => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(categoryId)) {
        newExpanded.delete(categoryId);
      } else {
        newExpanded.add(categoryId);
      }
      return newExpanded;
    });
  }, []);

  // Close dropdown (for external use, e.g., click outside)
  const closeDropdown = useCallback(() => {
    if (isOpen) {
      setIsOpen(false);
    }
  }, [isOpen]);

  return {
    // State
    categories,
    selectedCategoryData,
    isOpen,
    expandedCategories,

    // Handlers
    handleToggleDropdown,
    handleCategorySelect,
    handleToggleExpand,
    closeDropdown,
  };
}
