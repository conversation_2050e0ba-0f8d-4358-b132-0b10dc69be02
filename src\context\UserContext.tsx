"use client";

import React, {
  createContext,
  useContext,
  useState,
  useC<PERSON>back,
  ReactNode,
  useEffect,
} from "react";
import { UserProfile } from "@/types/ecommerce";
import { User, LoginRequest } from "@/types/auth";
import { AuthService } from "@/services/auth-service";
import { ApiError, getAuthToken } from "@/lib/api";

interface UserContextType {
  currentUser: UserProfile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  refreshUser: () => Promise<void>;
  clearError: () => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

export function UserProvider({ children }: UserProviderProps) {
  const [currentUser, setCurrentUser] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Convert API User to UserProfile format
  const convertApiUserToProfile = (apiUser: User): UserProfile => {
    return {
      id: apiUser.id,
      username: apiUser.username,
      email: apiUser.email || "",
      phoneNumber: apiUser.phone,
      profilePicture: apiUser.profilePicture,
      address: apiUser.address
        ? {
            city: apiUser.address.city || "",
            country: apiUser.address.country || "",
          }
        : {
            city: "",
            country: "",
          },
      createdAt: apiUser.createdAt, // Store as string instead of Date object
      updatedAt: apiUser.updatedAt || new Date().toISOString(), // Store as string instead of Date object
      isVerified: apiUser.isVerified,
      preferences: {
        notifications: apiUser.preferences?.notifications || {
          email: true,
          sms: true,
          push: true,
        },
        privacy: apiUser.preferences?.privacy || {
          showEmail: false,
          showPhone: true,
        },
      },
      savedListings: [],
      totalListings: 0,
      totalSold: 0,
    };
  };

  // Initialize user on mount (check for existing session)
  useEffect(() => {
    const initializeUser = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const token = getAuthToken();
        if (token) {
          // Validate token and get current user
          const apiUser = await AuthService.getCurrentUser();
          const userProfile = convertApiUserToProfile(apiUser);
          setCurrentUser(userProfile);
        }
      } catch (error) {
        console.error("Failed to initialize user:", error);
        // Clear invalid token
        await AuthService.logout();
      } finally {
        setIsLoading(false);
      }
    };

    initializeUser();
  }, []);

  const login = useCallback(async (credentials: LoginRequest) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await AuthService.login(credentials);
      const userProfile = convertApiUserToProfile(response.user);
      setCurrentUser(userProfile);
    } catch (error) {
      if (error instanceof ApiError) {
        setError(error.message);
      } else {
        setError("Login failed. Please try again.");
      }
      throw error; // Re-throw for form handling
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      await AuthService.logout();
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      setCurrentUser(null);
      setIsLoading(false);
    }
  }, []);

  const updateProfile = useCallback(
    async (updates: Partial<UserProfile>) => {
      if (!currentUser) return;

      setIsLoading(true);
      setError(null);

      try {
        // Convert UserProfile updates to API User format
        const apiUpdates: Partial<User> = {
          username: updates.username,
          firstName: updates.username, // Assuming username is used as firstName
          lastName: "", // You might want to handle this differently
          phone: updates.phoneNumber,
          address: updates.address
            ? {
                city: updates.address.city,
                country: updates.address.country,
              }
            : undefined,
        };

        const updatedApiUser = await AuthService.updateProfile(apiUpdates);
        const updatedProfile = convertApiUserToProfile(updatedApiUser);
        setCurrentUser(updatedProfile);
      } catch (error) {
        if (error instanceof ApiError) {
          setError(error.message);
        } else {
          setError("Failed to update profile. Please try again.");
        }
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [currentUser]
  );

  const refreshUser = useCallback(async () => {
    if (!currentUser) return;

    setIsLoading(true);
    setError(null);

    try {
      const apiUser = await AuthService.getCurrentUser();
      const userProfile = convertApiUserToProfile(apiUser);
      setCurrentUser(userProfile);
    } catch (error) {
      console.error("Failed to refresh user:", error);
      if (error instanceof ApiError) {
        setError(error.message);
      } else {
        setError("Failed to refresh user data.");
      }
    } finally {
      setIsLoading(false);
    }
  }, [currentUser]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const value: UserContextType = {
    currentUser,
    isAuthenticated: !!currentUser,
    isLoading,
    error,
    login,
    logout,
    updateProfile,
    refreshUser,
    clearError,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
}

export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
}

// Helper function to get user initials
export function getUserInitials(username: string): string {
  return username
    .split(" ")
    .map((name) => name.charAt(0).toUpperCase())
    .join("")
    .slice(0, 2);
}

// Helper function to get user display name
export function getUserDisplayName(user: UserProfile): string {
  return user.username || user.email.split("@")[0];
}
