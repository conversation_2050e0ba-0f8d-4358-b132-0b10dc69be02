"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Icon } from "@iconify/react";
import type { Message } from "@/types/ecommerce";

// Mock messages data
const mockMessages: Message[] = [
  {
    id: "msg-1",
    senderId: "user-456",
    receiverId: "user-123",
    productId: "prod-1",
    subject: "Inquiry about Toyota Corolla",
    content:
      "Hi, I'm interested in your Toyota Corolla. Is it still available? Can we schedule a viewing?",
    createdAt: new Date("2024-01-20T10:30:00"),
    isRead: false,
    messageType: "inquiry",
  },
  {
    id: "msg-2",
    senderId: "user-789",
    receiverId: "user-123",
    subject: "General Question",
    content:
      "Hello! I saw your profile and wanted to ask about your selling experience. Do you have any tips for new sellers?",
    createdAt: new Date("2024-01-19T15:45:00"),
    isRead: true,
    messageType: "general",
  },
  {
    id: "msg-3",
    senderId: "user-101",
    receiverId: "user-123",
    productId: "prod-2",
    subject: "Price Negotiation",
    content:
      "Would you consider Rs. 11,000,000 for the Toyota Corolla? I'm ready to buy immediately if we can agree on this price.",
    createdAt: new Date("2024-01-18T09:15:00"),
    isRead: true,
    messageType: "offer",
  },
];

// Mock sender profiles
const mockSenderProfiles: Record<
  string,
  { name: string; avatar?: string; rating: number }
> = {
  "user-456": { name: "Sarah Smith", rating: 4.2 },
  "user-789": { name: "Mike Johnson", rating: 4.8 },
  "user-101": { name: "Alex Chen", rating: 4.5 },
};

interface InboxMessagesProps {
  currentUserId?: string;
}

export default function InboxMessages({}: InboxMessagesProps) {
  const [messages, setMessages] = useState<Message[]>(mockMessages);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [replyText, setReplyText] = useState("");
  const [isReplying, setIsReplying] = useState(false);

  const unreadCount = messages.filter((msg) => !msg.isRead).length;

  const handleMessageClick = (message: Message) => {
    setSelectedMessage(message);

    // Mark as read if it wasn't already
    if (!message.isRead) {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === message.id ? { ...msg, isRead: true } : msg
        )
      );
    }
  };

  const handleReply = async () => {
    if (!selectedMessage || !replyText.trim()) return;

    setIsReplying(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // In a real app, this would send the reply via API
    console.log("Sending reply:", {
      to: selectedMessage.senderId,
      subject: `Re: ${selectedMessage.subject}`,
      content: replyText,
      originalMessageId: selectedMessage.id,
    });

    setReplyText("");
    setIsReplying(false);

    // Show success feedback (could use toast)
    alert("Reply sent successfully!");
  };

  const handleDeleteMessage = (messageId: string) => {
    if (confirm("Are you sure you want to delete this message?")) {
      setMessages((prev) => prev.filter((msg) => msg.id !== messageId));
      if (selectedMessage?.id === messageId) {
        setSelectedMessage(null);
      }
    }
  };

  const getMessageTypeIcon = (type: Message["messageType"]) => {
    switch (type) {
      case "inquiry":
        return (
          <Icon
            icon="lucide:message-square"
            className="w-4 h-4 text-blue-500"
          />
        );
      case "offer":
        return (
          <Icon icon="lucide:package" className="w-4 h-4 text-green-500" />
        );
      case "complaint":
        return (
          <Icon icon="lucide:alert-circle" className="w-4 h-4 text-red-500" />
        );
      default:
        return (
          <Icon
            icon="lucide:message-square"
            className="w-4 h-4 text-gray-500"
          />
        );
    }
  };

  const getMessageTypeBadge = (type: Message["messageType"]) => {
    const colors = {
      inquiry: "bg-blue-100 text-blue-800",
      general: "bg-gray-100 text-gray-800",
      offer: "bg-green-100 text-green-800",
      complaint: "bg-red-100 text-red-800",
    };

    return (
      <Badge className={`text-xs ${colors[type]}`}>
        {type.charAt(0).toUpperCase() + type.slice(1)}
      </Badge>
    );
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;

    return date.toLocaleDateString();
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[600px]">
      {/* Messages List */}
      <Card className="lg:col-span-1">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Icon icon="lucide:message-square" className="w-5 h-5" />
              Messages
            </div>
            {unreadCount > 0 && (
              <Badge className="bg-red-500 text-white">{unreadCount} new</Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="max-h-[500px] overflow-y-auto">
            {messages.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                <Icon
                  icon="lucide:message-square"
                  className="w-12 h-12 mx-auto mb-3 text-gray-300"
                />
                <p>No messages yet</p>
              </div>
            ) : (
              <div className="space-y-1">
                {messages.map((message) => {
                  const sender = mockSenderProfiles[message.senderId];
                  return (
                    <div
                      key={message.id}
                      onClick={() => handleMessageClick(message)}
                      className={`p-4 border-b cursor-pointer hover:bg-gray-50 transition-colors ${
                        selectedMessage?.id === message.id
                          ? "bg-blue-50 border-blue-200"
                          : ""
                      } ${
                        !message.isRead
                          ? "bg-blue-25 border-l-4 border-l-blue-500"
                          : ""
                      }`}
                    >
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-teal-400 to-teal-600 rounded-full flex items-center justify-center text-white font-semibold">
                          {sender?.name.charAt(0) || "U"}
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <p className="font-medium text-gray-900 truncate">
                              {sender?.name || "Unknown User"}
                            </p>
                            <div className="flex items-center gap-1">
                              {getMessageTypeIcon(message.messageType)}
                              {!message.isRead && (
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                              )}
                            </div>
                          </div>

                          <p className="text-sm font-medium text-gray-700 truncate mb-1">
                            {message.subject}
                          </p>

                          <p className="text-sm text-gray-600 truncate mb-2">
                            {message.content}
                          </p>

                          <div className="flex items-center justify-between">
                            {getMessageTypeBadge(message.messageType)}
                            <span className="text-xs text-gray-500">
                              {formatDate(message.createdAt)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Message Detail */}
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            {selectedMessage ? (
              <>
                <span>Message Details</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDeleteMessage(selectedMessage.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Icon icon="lucide:trash-2" className="w-4 h-4" />
                </Button>
              </>
            ) : (
              <span>Select a message</span>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {selectedMessage ? (
            <div className="space-y-6">
              {/* Message Header */}
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-teal-400 to-teal-600 rounded-full flex items-center justify-center text-white font-semibold">
                  {mockSenderProfiles[selectedMessage.senderId]?.name.charAt(
                    0
                  ) || "U"}
                </div>

                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold text-gray-900">
                      {mockSenderProfiles[selectedMessage.senderId]?.name ||
                        "Unknown User"}
                    </h3>
                    <div className="flex items-center gap-2">
                      {getMessageTypeBadge(selectedMessage.messageType)}
                      <span className="text-sm text-gray-500">
                        {formatDate(selectedMessage.createdAt)}
                      </span>
                    </div>
                  </div>

                  <h4 className="font-medium text-gray-800 mb-1">
                    {selectedMessage.subject}
                  </h4>

                  {selectedMessage.productId && (
                    <div className="flex items-center gap-1 text-sm text-blue-600">
                      <Icon icon="lucide:package" className="w-4 h-4" />
                      <span>About a product listing</span>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* Message Content */}
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-gray-800 whitespace-pre-wrap">
                  {selectedMessage.content}
                </p>
              </div>

              {/* Reply Section */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 flex items-center gap-2">
                  <Icon icon="lucide:reply" className="w-4 h-4" />
                  Reply
                </h4>

                <Textarea
                  value={replyText}
                  onChange={(e) => setReplyText(e.target.value)}
                  placeholder="Type your reply here..."
                  rows={4}
                  className="resize-none"
                />

                <div className="flex justify-end">
                  <Button
                    onClick={handleReply}
                    disabled={!replyText.trim() || isReplying}
                    className="bg-teal-600 hover:bg-teal-700 text-white"
                  >
                    {isReplying ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Icon icon="lucide:send" className="w-4 h-4 mr-2" />
                        Send Reply
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-64 text-gray-500">
              <div className="text-center">
                <Icon
                  icon="lucide:message-square"
                  className="w-16 h-16 mx-auto mb-4 text-gray-300"
                />
                <p className="text-lg font-medium mb-2">No message selected</p>
                <p>Choose a message from the list to view its details</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
