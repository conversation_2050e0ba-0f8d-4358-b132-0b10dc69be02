import { apiClient, apiRequest, API_ENDPOINTS } from "@/lib/api";
import {
  CreateTransactionDto,
  TransactionResponseDto,
  PaginatedTransactionsResponse,
  WalletResponseDto,
  WalletTopupDto,
  WalletWithdrawDto,
  WalletTransferDto,
  WalletTransferResponse,
  CreatePaymentMethodDto,
  PaymentMethodResponseDto,
  TransactionType,
  TransactionStatus,
  PaymentGateway,
} from "@/types/orders";

/**
 * Payment Service
 * Handles all payment-related API calls including transactions, wallet operations, and payment methods
 */
export class PaymentService {
  // ===== TRANSACTION MANAGEMENT =====

  /**
   * Create a new transaction
   */
  static async createTransaction(data: CreateTransactionDto): Promise<TransactionResponseDto> {
    return await apiRequest<TransactionResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.PAYMENTS.CREATE_TRANSACTION, data)
    );
  }

  /**
   * Get user transactions with pagination
   */
  static async getUserTransactions(
    page: number = 1,
    limit: number = 20
  ): Promise<PaginatedTransactionsResponse> {
    const params = { page, limit };

    return await apiRequest<PaginatedTransactionsResponse>(() =>
      apiClient.get(API_ENDPOINTS.PAYMENTS.TRANSACTIONS, { params })
    );
  }

  /**
   * Get all user transactions (for summary calculations)
   */
  static async getAllUserTransactions(): Promise<TransactionResponseDto[]> {
    try {
      const response = await this.getUserTransactions(1, 1000); // Get large number for summary
      return response.transactions;
    } catch (error) {
      return [];
    }
  }

  // ===== WALLET OPERATIONS =====

  /**
   * Get or create user wallet
   */
  static async getWallet(): Promise<WalletResponseDto> {
    return await apiRequest<WalletResponseDto>(() =>
      apiClient.get(API_ENDPOINTS.PAYMENTS.WALLET)
    );
  }

  /**
   * Top up wallet balance
   */
  static async walletTopup(data: WalletTopupDto): Promise<TransactionResponseDto> {
    return await apiRequest<TransactionResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.PAYMENTS.WALLET_TOPUP, data)
    );
  }

  /**
   * Withdraw from wallet
   */
  static async walletWithdraw(data: WalletWithdrawDto): Promise<TransactionResponseDto> {
    return await apiRequest<TransactionResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.PAYMENTS.WALLET_WITHDRAW, data)
    );
  }

  /**
   * Transfer money to another user
   */
  static async walletTransfer(data: WalletTransferDto): Promise<WalletTransferResponse> {
    return await apiRequest<WalletTransferResponse>(() =>
      apiClient.post(API_ENDPOINTS.PAYMENTS.WALLET_TRANSFER, data)
    );
  }

  // ===== PAYMENT METHODS =====

  /**
   * Add a new payment method
   */
  static async createPaymentMethod(data: CreatePaymentMethodDto): Promise<PaymentMethodResponseDto> {
    return await apiRequest<PaymentMethodResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.PAYMENTS.PAYMENT_METHODS, data)
    );
  }

  // ===== HELPER METHODS =====

  /**
   * Get wallet balance
   */
  static async getWalletBalance(): Promise<number> {
    try {
      const wallet = await this.getWallet();
      return wallet.balance;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Check if user has sufficient wallet balance
   */
  static async hasSufficientBalance(amount: number): Promise<boolean> {
    try {
      const balance = await this.getWalletBalance();
      return balance >= amount;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get transaction summary
   */
  static async getTransactionSummary(): Promise<{
    totalTransactions: number;
    totalAmount: number;
    pendingAmount: number;
    completedAmount: number;
    currency: string;
  }> {
    try {
      const transactions = await this.getAllUserTransactions();

      const summary = {
        totalTransactions: transactions.length,
        totalAmount: transactions.reduce((sum, tx) => sum + tx.amount, 0),
        pendingAmount: transactions
          .filter(tx => tx.status === TransactionStatus.PENDING)
          .reduce((sum, tx) => sum + tx.amount, 0),
        completedAmount: transactions
          .filter(tx => tx.status === TransactionStatus.COMPLETED)
          .reduce((sum, tx) => sum + tx.amount, 0),
        currency: transactions.length > 0 ? transactions[0].currency : 'NPR',
      };

      return summary;
    } catch (error) {
      return {
        totalTransactions: 0,
        totalAmount: 0,
        pendingAmount: 0,
        completedAmount: 0,
        currency: 'NPR',
      };
    }
  }

  /**
   * Get recent transactions (last 10)
   */
  static async getRecentTransactions(): Promise<TransactionResponseDto[]> {
    try {
      const response = await this.getUserTransactions(1, 10);
      return response.transactions;
    } catch (error) {
      return [];
    }
  }

  /**
   * Format transaction type for display
   */
  static formatTransactionType(type: TransactionType): string {
    const typeMap: Record<TransactionType, string> = {
      [TransactionType.AD_PAYMENT]: 'Advertisement Payment',
      [TransactionType.WALLET_TOPUP]: 'Wallet Top-up',
      [TransactionType.WALLET_TRANSFER]: 'Wallet Transfer',
      [TransactionType.WITHDRAWAL]: 'Withdrawal',
      [TransactionType.REFUND]: 'Refund',
      [TransactionType.COMMISSION]: 'Commission',
      [TransactionType.SUBSCRIPTION_PAYMENT]: 'Subscription Payment',
    };
    return typeMap[type] || type;
  }

  /**
   * Format transaction status for display
   */
  static formatTransactionStatus(status: TransactionStatus): string {
    const statusMap: Record<TransactionStatus, string> = {
      [TransactionStatus.PENDING]: 'Pending',
      [TransactionStatus.COMPLETED]: 'Completed',
      [TransactionStatus.FAILED]: 'Failed',
      [TransactionStatus.CANCELLED]: 'Cancelled',
      [TransactionStatus.REFUNDED]: 'Refunded',
    };
    return statusMap[status] || status;
  }

  /**
   * Format payment gateway for display
   */
  static formatPaymentGateway(gateway: PaymentGateway): string {
    const gatewayMap: Record<PaymentGateway, string> = {
      [PaymentGateway.KHALTI]: 'Khalti',
      [PaymentGateway.ESEWA]: 'eSewa',
      [PaymentGateway.STRIPE]: 'Stripe',
      [PaymentGateway.PAYPAL]: 'PayPal',
    };
    return gatewayMap[gateway] || gateway;
  }

  /**
   * Get transaction status color for UI
   */
  static getTransactionStatusColor(status: TransactionStatus): string {
    const colorMap: Record<TransactionStatus, string> = {
      [TransactionStatus.PENDING]: 'yellow',
      [TransactionStatus.COMPLETED]: 'green',
      [TransactionStatus.FAILED]: 'red',
      [TransactionStatus.CANCELLED]: 'gray',
      [TransactionStatus.REFUNDED]: 'blue',
    };
    return colorMap[status] || 'gray';
  }

  /**
   * Validate transaction data
   */
  static validateTransaction(data: CreateTransactionDto): string[] {
    const errors: string[] = [];

    if (!data.type) {
      errors.push('Transaction type is required');
    }
    if (!data.amount || data.amount <= 0) {
      errors.push('Amount must be greater than 0');
    }
    if (data.amount && data.amount < 0.01) {
      errors.push('Minimum transaction amount is 0.01');
    }
    if (data.paymentMethod && data.paymentMethod.length > 50) {
      errors.push('Payment method cannot exceed 50 characters');
    }
    if (data.paymentGateway && data.paymentGateway.length > 50) {
      errors.push('Payment gateway cannot exceed 50 characters');
    }

    return errors;
  }

  /**
   * Validate wallet top-up data
   */
  static validateWalletTopup(data: WalletTopupDto): string[] {
    const errors: string[] = [];

    if (!data.amount || data.amount <= 0) {
      errors.push('Amount must be greater than 0');
    }
    if (data.amount && data.amount < 0.01) {
      errors.push('Minimum top-up amount is 0.01');
    }
    if (!data.paymentMethod?.trim()) {
      errors.push('Payment method is required');
    }

    return errors;
  }

  /**
   * Validate wallet withdrawal data
   */
  static validateWalletWithdraw(data: WalletWithdrawDto): string[] {
    const errors: string[] = [];

    if (!data.amount || data.amount <= 0) {
      errors.push('Amount must be greater than 0');
    }
    if (data.amount && data.amount < 0.01) {
      errors.push('Minimum withdrawal amount is 0.01');
    }
    if (!data.withdrawalMethod?.trim()) {
      errors.push('Withdrawal method is required');
    }

    return errors;
  }

  /**
   * Validate wallet transfer data
   */
  static validateWalletTransfer(data: WalletTransferDto): string[] {
    const errors: string[] = [];

    if (!data.recipientId?.trim()) {
      errors.push('Recipient ID is required');
    }
    if (!data.amount || data.amount <= 0) {
      errors.push('Amount must be greater than 0');
    }
    if (data.amount && data.amount < 0.01) {
      errors.push('Minimum transfer amount is 0.01');
    }

    return errors;
  }

  /**
   * Format currency amount
   */
  static formatAmount(amount: number, currency: string = 'NPR'): string {
    return `${currency} ${amount.toLocaleString()}`;
  }

  /**
   * Calculate transaction fee (if applicable)
   */
  static calculateTransactionFee(amount: number, type: TransactionType): number {
    // Define fee structure based on transaction type
    const feeRates: Partial<Record<TransactionType, number>> = {
      [TransactionType.WALLET_TRANSFER]: 0.01, // 1%
      [TransactionType.WITHDRAWAL]: 0.02, // 2%
    };

    const feeRate = feeRates[type] || 0;
    return amount * feeRate;
  }

  /**
   * Check if transaction can be cancelled
   */
  static canCancelTransaction(transaction: TransactionResponseDto): boolean {
    return transaction.status === TransactionStatus.PENDING;
  }

  /**
   * Check if transaction can be refunded
   */
  static canRefundTransaction(transaction: TransactionResponseDto): boolean {
    return transaction.status === TransactionStatus.COMPLETED &&
           [TransactionType.AD_PAYMENT, TransactionType.SUBSCRIPTION_PAYMENT].includes(transaction.type);
  }

  /**
   * Get payment gateway URL for external payment processing
   */
  static getPaymentGatewayUrl(gateway: PaymentGateway, transactionId: string): string {
    const baseUrls: Record<PaymentGateway, string> = {
      [PaymentGateway.KHALTI]: process.env.NEXT_PUBLIC_KHALTI_URL || 'https://khalti.com',
      [PaymentGateway.ESEWA]: process.env.NEXT_PUBLIC_ESEWA_URL || 'https://esewa.com.np',
      [PaymentGateway.STRIPE]: process.env.NEXT_PUBLIC_STRIPE_URL || 'https://checkout.stripe.com',
      [PaymentGateway.PAYPAL]: process.env.NEXT_PUBLIC_PAYPAL_URL || 'https://paypal.com',
    };

    return `${baseUrls[gateway]}/payment/${transactionId}`;
  }
}

export default PaymentService;
