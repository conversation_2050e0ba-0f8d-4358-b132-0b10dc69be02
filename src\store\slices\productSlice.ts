import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { SortBy } from "@/types/ecommerce";

// Define the product UI state interface (simplified for API-driven approach)
export interface ProductState {
  currentPage: number;
  itemsPerPage: number;
  sortBy: SortBy;
}

// Initial state
const initialState: ProductState = {
  currentPage: 1,
  itemsPerPage: 12,
  sortBy: "newest",
};

// Compatibility thunk for components that expect fetchProducts
// This is a no-op since we're using RTK Query for actual data fetching
export const fetchProducts = createAsyncThunk(
  "product/fetchProducts",
  async () => {
    // No-op for compatibility - RTK Query handles actual fetching
    return [];
  }
);

// Create the product slice (simplified for API-driven approach)
const productSlice = createSlice({
  name: "product",
  initialState,
  reducers: {
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setSortBy: (state, action: PayloadAction<SortBy>) => {
      state.sortBy = action.payload;
      // Reset to first page when sort changes
      state.currentPage = 1;
    },
    setItemsPerPage: (state, action: PayloadAction<number>) => {
      state.itemsPerPage = action.payload;
      // Reset to first page when items per page changes
      state.currentPage = 1;
    },
    resetPagination: (state) => {
      state.currentPage = 1;
    },
  },
  // No extraReducers needed - using RTK Query for API calls
});

// Export actions
export const { setCurrentPage, setSortBy, setItemsPerPage, resetPagination } =
  productSlice.actions;

// Export reducer
export default productSlice.reducer;
