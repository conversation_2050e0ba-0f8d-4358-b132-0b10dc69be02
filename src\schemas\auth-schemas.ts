import * as Yup from "yup";

/**
 * Validation schemas for authentication forms using Yup
 */

// Common validation patterns
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const PHONE_REGEX = /^(\+977)?[0-9]{10}$/; // Nepal phone number format
const PASSWORD_MIN_LENGTH = 8;

// Custom validation messages
const VALIDATION_MESSAGES = {
  REQUIRED: "This field is required",
  EMAIL_INVALID: "Please enter a valid email address",
  EMAIL_REQUIRED: "Email is required",
  PASSWORD_REQUIRED: "Password is required",
  PASSWORD_MIN_LENGTH: `Password must be at least ${PASSWORD_MIN_LENGTH} characters`,
  PASSWORD_UPPERCASE: "Password must contain at least one uppercase letter",
  PASSWORD_LOWERCASE: "Password must contain at least one lowercase letter",
  PASSWORD_NUMBER: "Password must contain at least one number",
  PASSWORD_SPECIAL: "Password must contain at least one special character",
  PASSWORD_MATCH: "Passwords must match",
  USERNAME_REQUIRED: "Username is required",
  USERNAME_MIN_LENGTH: "Username must be at least 3 characters",
  USERNAME_MAX_LENGTH: "Username must be less than 30 characters",
  USERNAME_INVALID:
    "Username can only contain letters, numbers, and underscores",
  PHONE_REQUIRED: "Phone number is required",
  PHONE_INVALID: "Please enter a valid phone number",
  TERMS_REQUIRED: "You must agree to the terms and conditions",
  NAME_REQUIRED: "Name is required",
  NAME_MIN_LENGTH: "Name must be at least 2 characters",
  NAME_MAX_LENGTH: "Name must be less than 50 characters",
} as const;

/**
 * Login form validation schema
 */
export const loginSchema = Yup.object().shape({
  usernameOrEmail: Yup.string()
    .required("Username or email is required")
    .trim(),

  password: Yup.string()
    .required(VALIDATION_MESSAGES.PASSWORD_REQUIRED)
    .min(PASSWORD_MIN_LENGTH, VALIDATION_MESSAGES.PASSWORD_MIN_LENGTH),
});

/**
 * Registration form validation schema
 */
export const registerSchema = Yup.object().shape({
  email: Yup.string()
    .required(VALIDATION_MESSAGES.EMAIL_REQUIRED)
    .matches(EMAIL_REGEX, VALIDATION_MESSAGES.EMAIL_INVALID)
    .trim()
    .lowercase(),

  username: Yup.string()
    .required(VALIDATION_MESSAGES.USERNAME_REQUIRED)
    .min(3, VALIDATION_MESSAGES.USERNAME_MIN_LENGTH)
    .max(30, VALIDATION_MESSAGES.USERNAME_MAX_LENGTH)
    .matches(/^[a-zA-Z0-9_]+$/, VALIDATION_MESSAGES.USERNAME_INVALID)
    .trim(),

  password: Yup.string()
    .required(VALIDATION_MESSAGES.PASSWORD_REQUIRED)
    .min(PASSWORD_MIN_LENGTH, VALIDATION_MESSAGES.PASSWORD_MIN_LENGTH)
    .matches(/[A-Z]/, VALIDATION_MESSAGES.PASSWORD_UPPERCASE)
    .matches(/[a-z]/, VALIDATION_MESSAGES.PASSWORD_LOWERCASE)
    .matches(/\d/, VALIDATION_MESSAGES.PASSWORD_NUMBER)
    .matches(/[!@#$%^&*(),.?":{}|<>]/, VALIDATION_MESSAGES.PASSWORD_SPECIAL),

  confirmPassword: Yup.string()
    .required(VALIDATION_MESSAGES.REQUIRED)
    .oneOf([Yup.ref("password")], VALIDATION_MESSAGES.PASSWORD_MATCH),

  firstName: Yup.string()
    .required(VALIDATION_MESSAGES.NAME_REQUIRED)
    .min(2, VALIDATION_MESSAGES.NAME_MIN_LENGTH)
    .max(50, VALIDATION_MESSAGES.NAME_MAX_LENGTH)
    .trim(),

  lastName: Yup.string()
    .required(VALIDATION_MESSAGES.NAME_REQUIRED)
    .min(2, VALIDATION_MESSAGES.NAME_MIN_LENGTH)
    .max(50, VALIDATION_MESSAGES.NAME_MAX_LENGTH)
    .trim(),

  phone: Yup.string()
    .required(VALIDATION_MESSAGES.PHONE_REQUIRED)
    .matches(PHONE_REGEX, VALIDATION_MESSAGES.PHONE_INVALID),

  agreeToTerms: Yup.boolean().optional(),
});

/**
 * Forgot password form validation schema
 */
export const forgotPasswordSchema = Yup.object().shape({
  email: Yup.string()
    .required(VALIDATION_MESSAGES.EMAIL_REQUIRED)
    .matches(EMAIL_REGEX, VALIDATION_MESSAGES.EMAIL_INVALID)
    .trim()
    .lowercase(),
});

/**
 * Reset password form validation schema
 */
export const resetPasswordSchema = Yup.object().shape({
  password: Yup.string()
    .required(VALIDATION_MESSAGES.PASSWORD_REQUIRED)
    .min(PASSWORD_MIN_LENGTH, VALIDATION_MESSAGES.PASSWORD_MIN_LENGTH)
    .matches(/[A-Z]/, VALIDATION_MESSAGES.PASSWORD_UPPERCASE)
    .matches(/[a-z]/, VALIDATION_MESSAGES.PASSWORD_LOWERCASE)
    .matches(/\d/, VALIDATION_MESSAGES.PASSWORD_NUMBER)
    .matches(/[!@#$%^&*(),.?":{}|<>]/, VALIDATION_MESSAGES.PASSWORD_SPECIAL),

  confirmPassword: Yup.string()
    .required(VALIDATION_MESSAGES.REQUIRED)
    .oneOf([Yup.ref("password")], VALIDATION_MESSAGES.PASSWORD_MATCH),
});

/**
 * Profile update validation schema
 */
export const profileUpdateSchema = Yup.object().shape({
  username: Yup.string()
    .required(VALIDATION_MESSAGES.USERNAME_REQUIRED)
    .min(3, VALIDATION_MESSAGES.USERNAME_MIN_LENGTH)
    .max(30, VALIDATION_MESSAGES.USERNAME_MAX_LENGTH)
    .matches(/^[a-zA-Z0-9_]+$/, VALIDATION_MESSAGES.USERNAME_INVALID)
    .trim(),

  firstName: Yup.string()
    .required(VALIDATION_MESSAGES.NAME_REQUIRED)
    .min(2, VALIDATION_MESSAGES.NAME_MIN_LENGTH)
    .max(50, VALIDATION_MESSAGES.NAME_MAX_LENGTH)
    .trim(),

  lastName: Yup.string()
    .required(VALIDATION_MESSAGES.NAME_REQUIRED)
    .min(2, VALIDATION_MESSAGES.NAME_MIN_LENGTH)
    .max(50, VALIDATION_MESSAGES.NAME_MAX_LENGTH)
    .trim(),

  phoneNumber: Yup.string()
    .optional()
    .matches(PHONE_REGEX, VALIDATION_MESSAGES.PHONE_INVALID),

  address: Yup.object()
    .shape({
      city: Yup.string().optional().trim(),
      country: Yup.string().optional().trim(),
      street: Yup.string().optional().trim(),
      zipCode: Yup.string().optional().trim(),
    })
    .optional(),
});

/**
 * Change password validation schema
 */
export const changePasswordSchema = Yup.object().shape({
  currentPassword: Yup.string().required("Current password is required"),

  newPassword: Yup.string()
    .required(VALIDATION_MESSAGES.PASSWORD_REQUIRED)
    .min(PASSWORD_MIN_LENGTH, VALIDATION_MESSAGES.PASSWORD_MIN_LENGTH)
    .matches(/[A-Z]/, VALIDATION_MESSAGES.PASSWORD_UPPERCASE)
    .matches(/[a-z]/, VALIDATION_MESSAGES.PASSWORD_LOWERCASE)
    .matches(/\d/, VALIDATION_MESSAGES.PASSWORD_NUMBER)
    .matches(/[!@#$%^&*(),.?":{}|<>]/, VALIDATION_MESSAGES.PASSWORD_SPECIAL)
    .notOneOf(
      [Yup.ref("currentPassword")],
      "New password must be different from current password"
    ),

  confirmNewPassword: Yup.string()
    .required(VALIDATION_MESSAGES.REQUIRED)
    .oneOf([Yup.ref("newPassword")], VALIDATION_MESSAGES.PASSWORD_MATCH),
});

// Export validation messages for use in components
export { VALIDATION_MESSAGES };

// Export types for form values
export type LoginFormValues = Yup.InferType<typeof loginSchema>;
export type RegisterFormValues = Yup.InferType<typeof registerSchema>;
export type ForgotPasswordFormValues = Yup.InferType<
  typeof forgotPasswordSchema
>;
export type ResetPasswordFormValues = Yup.InferType<typeof resetPasswordSchema>;
export type ProfileUpdateFormValues = Yup.InferType<typeof profileUpdateSchema>;
export type ChangePasswordFormValues = Yup.InferType<
  typeof changePasswordSchema
>;
