import { apiClient, apiRequest, API_ENDPOINTS } from "@/lib/api";
import { UPLOAD_CONFIG } from "@/constants/api-constants";

/**
 * File upload related interfaces matching backend DTOs
 */
export interface UploadedFileDto {
  originalName: string;
  filename: string;
  url: string;
  size: number;
  mimeType: string;
  width?: number; // For images
  height?: number; // For images
}

export interface FailedUploadDto {
  originalName: string;
  error: string;
}

export interface FileUploadResponseDto {
  successful: UploadedFileDto[];
  failed: FailedUploadDto[];
}

export interface UploadOptions {
  folder?: string;
  maxFiles?: number;
  allowedTypes?: string[];
  maxFileSize?: number;
  generateThumbnails?: boolean;
  resizeImages?: boolean;
  maxWidth?: number;
  maxHeight?: number;
}

/**
 * File Upload Service
 * Handles all file upload operations
 */
export class FileUploadService {
  /**
   * Upload multiple files
   */
  static async uploadFiles(
    files: File[],
    options: UploadOptions = {}
  ): Promise<FileUploadResponseDto> {
    const formData = new FormData();

    files.forEach((file) => {
      formData.append("files", file);
    });

    // Add options as form data if provided
    if (options.folder) {
      formData.append("folder", options.folder);
    }
    if (options.generateThumbnails !== undefined) {
      formData.append(
        "generateThumbnails",
        options.generateThumbnails.toString()
      );
    }
    if (options.resizeImages !== undefined) {
      formData.append("resizeImages", options.resizeImages.toString());
    }
    if (options.maxWidth) {
      formData.append("maxWidth", options.maxWidth.toString());
    }
    if (options.maxHeight) {
      formData.append("maxHeight", options.maxHeight.toString());
    }

    return await apiRequest<FileUploadResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.UPLOAD_FILES, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      })
    );
  }

  /**
   * Upload single file
   */
  static async uploadFile(
    file: File,
    options: UploadOptions = {}
  ): Promise<UploadedFileDto> {
    const response = await this.uploadFiles([file], options);

    if (response.failed.length > 0) {
      throw new Error(response.failed[0].error);
    }

    if (response.successful.length === 0) {
      throw new Error("No files were uploaded successfully");
    }

    return response.successful[0];
  }

  /**
   * Upload profile picture
   */
  static async uploadProfilePicture(file: File): Promise<UploadedFileDto> {
    const formData = new FormData();
    formData.append("profilePicture", file);

    const response = await apiRequest<FileUploadResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.UPLOAD_PROFILE_PICTURE, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      })
    );

    if (response.failed.length > 0) {
      throw new Error(response.failed[0].error);
    }

    if (response.successful.length === 0) {
      throw new Error("Profile picture upload failed");
    }

    return response.successful[0];
  }

  /**
   * Upload advertisement images
   */
  static async uploadAdvertisementImages(
    files: File[],
    advertisementId?: string
  ): Promise<FileUploadResponseDto> {
    const formData = new FormData();

    files.forEach((file) => {
      formData.append("images", file);
    });

    if (advertisementId) {
      formData.append("advertisementId", advertisementId);
    }

    return await apiRequest<FileUploadResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.UPLOAD_AD_IMAGES_FILE, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      })
    );
  }

  /**
   * Validate file before upload
   */
  static validateFile(
    file: File,
    options: UploadOptions = {}
  ): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    const maxFileSize = options.maxFileSize || UPLOAD_CONFIG.MAX_FILE_SIZE;
    const allowedTypes =
      options.allowedTypes || UPLOAD_CONFIG.ALLOWED_IMAGE_TYPES;

    // Check file size
    if (file.size > maxFileSize) {
      errors.push(
        `File size must be less than ${this.formatFileSize(maxFileSize)}`
      );
    }

    // Check file type
    if (!allowedTypes.includes(file.type as any)) {
      errors.push(
        `File type ${
          file.type
        } is not allowed. Allowed types: ${allowedTypes.join(", ")}`
      );
    }

    // Check file name length
    if (file.name.length > 255) {
      errors.push("File name is too long (max 255 characters)");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate multiple files before upload
   */
  static validateFiles(
    files: File[],
    options: UploadOptions = {}
  ): {
    isValid: boolean;
    errors: string[];
    validFiles: File[];
    invalidFiles: { file: File; errors: string[] }[];
  } {
    const maxFiles = options.maxFiles || UPLOAD_CONFIG.MAX_FILES_PER_UPLOAD;
    const errors: string[] = [];
    const validFiles: File[] = [];
    const invalidFiles: { file: File; errors: string[] }[] = [];

    // Check number of files
    if (files.length > maxFiles) {
      errors.push(`Maximum ${maxFiles} files allowed`);
    }

    // Validate each file
    files.forEach((file) => {
      const validation = this.validateFile(file, options);
      if (validation.isValid) {
        validFiles.push(file);
      } else {
        invalidFiles.push({ file, errors: validation.errors });
      }
    });

    return {
      isValid: errors.length === 0 && invalidFiles.length === 0,
      errors,
      validFiles,
      invalidFiles,
    };
  }

  /**
   * Format file size for display
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * Get file extension from filename
   */
  static getFileExtension(filename: string): string {
    return filename.slice(((filename.lastIndexOf(".") - 1) >>> 0) + 2);
  }

  /**
   * Check if file is an image
   */
  static isImageFile(file: File): boolean {
    return file.type.startsWith("image/");
  }

  /**
   * Create image preview URL
   */
  static createImagePreview(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!this.isImageFile(file)) {
        reject(new Error("File is not an image"));
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as string);
      };
      reader.onerror = () => {
        reject(new Error("Failed to read file"));
      };
      reader.readAsDataURL(file);
    });
  }

  /**
   * Resize image file (client-side)
   */
  static async resizeImage(
    file: File,
    maxWidth: number,
    maxHeight: number,
    quality: number = 0.8
  ): Promise<File> {
    return new Promise((resolve, reject) => {
      if (!this.isImageFile(file)) {
        reject(new Error("File is not an image"));
        return;
      }

      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;

        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            if (blob) {
              const resizedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              resolve(resizedFile);
            } else {
              reject(new Error("Failed to resize image"));
            }
          },
          file.type,
          quality
        );
      };

      img.onerror = () => {
        reject(new Error("Failed to load image"));
      };

      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Generate thumbnail from image file
   */
  static async generateThumbnail(
    file: File,
    size: number = 150,
    quality: number = 0.7
  ): Promise<File> {
    return this.resizeImage(file, size, size, quality);
  }

  /**
   * Convert file to base64
   */
  static fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result.split(",")[1]); // Remove data:image/jpeg;base64, prefix
      };
      reader.onerror = () => {
        reject(new Error("Failed to convert file to base64"));
      };
      reader.readAsDataURL(file);
    });
  }
}

export default FileUploadService;
