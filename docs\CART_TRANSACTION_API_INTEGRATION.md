# Cart and Transaction API Integration Guide

## Overview

This document provides comprehensive information about the cart and transaction API integration between the SastoBazar e-commerce frontend and backend. The backend is built with NestJS and provides robust APIs for cart management, order processing, and payment handling.

## Backend API Structure

### Base Configuration

- **Base URL**: `https://sasto-api.webstudiomatrix.com/api/v1`
- **Authentication**: Bearer token required for all cart and transaction endpoints
- **Content Type**: `application/json`
- **Error Format**: Standardized error responses with status codes and messages

### Authentication Requirements

All cart and transaction endpoints require authentication via JWT Bearer token:

```
Authorization: Bearer <jwt_token>
```

## Cart API Endpoints

### 1. Get User Cart

- **Endpoint**: `GET /cart`
- **Authentication**: Required
- **Description**: Retrieve the current user's cart with all items

**Response Format**:

```typescript
interface CartResponseDto {
  id: string;
  userId: string;
  items: CartItemResponseDto[];
  totalAmount: number;
  totalItems: number;
  currency: CurrencyType; // 'NPR' | 'USD'
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface CartItemResponseDto {
  id: string;
  advertisementId: string;
  productName: string;
  productImage?: string;
  price: number;
  quantity: number;
  maxQuantity: number;
  subtotal: number;
  sellerId: string;
  sellerName: string;
  isAvailable: boolean;
  addedAt: Date;
}
```

### 2. Add Item to Cart

- **Endpoint**: `POST /cart/items`
- **Authentication**: Required
- **Description**: Add a new item to the cart or update quantity if item exists

**Request Format**:

```typescript
interface AddToCartDto {
  advertisementId: string; // UUID v4
  quantity: number; // Min: 1, Max: 100
}
```

**Response**: Returns updated `CartResponseDto`

### 3. Update Cart Item Quantity

- **Endpoint**: `PUT /cart/items/:itemId`
- **Authentication**: Required
- **Description**: Update the quantity of a specific cart item

**Request Format**:

```typescript
interface UpdateCartItemDto {
  quantity: number; // Min: 1, Max: 100
}
```

**Response**: Returns updated `CartResponseDto`

### 4. Remove Single Item from Cart

- **Endpoint**: `DELETE /cart/items/:itemId`
- **Authentication**: Required
- **Description**: Remove a specific item from the cart

**Response**: Returns updated `CartResponseDto`

### 5. Remove Multiple Items from Cart

- **Endpoint**: `DELETE /cart/items/bulk`
- **Authentication**: Required
- **Description**: Remove multiple items from the cart at once

**Request Format**:

```typescript
interface BulkRemoveDto {
  itemIds: string[]; // Array of cart item UUIDs, Min: 1, Max: 50
}
```

**Response**: Returns updated `CartResponseDto`

### 6. Clear Entire Cart

- **Endpoint**: `DELETE /cart`
- **Authentication**: Required
- **Description**: Remove all items from the cart

**Response**: `204 No Content`

### 7. Sync Cart with Server

- **Endpoint**: `POST /cart/sync`
- **Authentication**: Required
- **Description**: Synchronize cart with server to update item availability and prices

**Response**: Returns updated `CartResponseDto`

## Order API Endpoints

### 1. Get User Addresses

- **Endpoint**: `GET /users/addresses`
- **Authentication**: Required
- **Description**: Retrieve all shipping addresses for the user

**Response Format**:

```typescript
interface AddressResponseDto {
  id: string;
  type: AddressType; // 'HOME' | 'WORK' | 'OTHER'
  firstName: string;
  lastName: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone: string;
  isDefault: boolean;
}
```

### 2. Create Address

- **Endpoint**: `POST /users/addresses`
- **Authentication**: Required
- **Description**: Add a new shipping address

**Request Format**:

```typescript
interface CreateAddressDto {
  type: AddressType;
  firstName: string;
  lastName: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone: string;
  isDefault?: boolean;
}
```

### 3. Update Address

- **Endpoint**: `PUT /users/addresses/:id`
- **Authentication**: Required
- **Description**: Update an existing address

**Request Format**: Same as `CreateAddressDto` but all fields optional

### 4. Delete Address

- **Endpoint**: `DELETE /users/addresses/:id`
- **Authentication**: Required
- **Description**: Delete a shipping address

**Response**: `204 No Content`

### 5. Set Default Address

- **Endpoint**: `PUT /users/addresses/:id/default`
- **Authentication**: Required
- **Description**: Set an address as the default shipping address

**Response**: Returns updated `AddressResponseDto`

## Checkout and Order Processing

### 1. Validate Cart Before Checkout

- **Endpoint**: `POST /checkout/validate`
- **Authentication**: Required
- **Description**: Validate cart items before proceeding to checkout

**Response Format**:

```typescript
interface CartValidationResponse {
  isValid: boolean;
  errors: string[];
}
```

### 2. Process Checkout

- **Endpoint**: `POST /checkout`
- **Authentication**: Required
- **Description**: Process checkout and create order

**Request Format**:

```typescript
interface CheckoutDto {
  shippingAddressId: string; // UUID v4
  billingAddressId?: string; // UUID v4, optional
  paymentMethod: PaymentMethod; // 'esewa' | 'khalti' | 'cod' | 'bank_transfer'
  notes?: string; // Max 500 characters
  couponCode?: string; // Max 50 characters
}
```

**Response**: Returns `OrderResponseDto`

### 3. Get User Orders

- **Endpoint**: `GET /orders`
- **Authentication**: Required
- **Description**: Get user's order history with pagination and filtering

**Query Parameters**:

```typescript
interface OrderFilterDto {
  status?: OrderStatus;
  dateFrom?: string; // ISO date string
  dateTo?: string; // ISO date string
  page?: number; // Default: 1
  limit?: number; // Default: 10
  sortBy?: string; // Default: 'createdAt'
  sortOrder?: "asc" | "desc"; // Default: 'desc'
}
```

**Response Format**:

```typescript
interface PaginatedOrdersResponse {
  orders: OrderResponseDto[];
  total: number;
  page: number;
  limit: number;
}
```

### 4. Get Order by ID

- **Endpoint**: `GET /orders/:orderId`
- **Authentication**: Required
- **Description**: Get detailed information about a specific order

**Response**: Returns `OrderResponseDto`

### 5. Cancel Order

- **Endpoint**: `POST /orders/:orderId/cancel`
- **Authentication**: Required
- **Description**: Cancel an order (only if status allows)

**Response**: Returns updated `OrderResponseDto`

## Order Response Structure

```typescript
interface OrderResponseDto {
  id: string;
  orderNumber: string; // Format: ORD-{timestamp}-{random}
  userId: string;
  status: OrderStatus; // 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded'
  items: OrderItemResponseDto[];
  shippingAddress: AddressResponseDto;
  billingAddress?: AddressResponseDto;
  paymentMethod: PaymentMethod;
  paymentStatus: PaymentStatus; // 'pending' | 'processing' | 'completed' | 'failed' | 'refunded'
  subtotal: number;
  shippingCost: number;
  tax: number;
  discount: number;
  totalAmount: number;
  currency: CurrencyType;
  estimatedDelivery?: Date;
  trackingNumber?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  cancelledAt?: Date;
  deliveredAt?: Date;
}

interface OrderItemResponseDto {
  id: string;
  advertisementId: string;
  productName: string;
  productImage?: string;
  price: number;
  quantity: number;
  subtotal: number;
  sellerId: string;
  sellerName: string;
  status: OrderItemStatus; // 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled'
}
```

## Status Values (Lowercase as per project standards)

### Order Status

- `pending` - Order created, awaiting confirmation
- `confirmed` - Order confirmed, ready for processing
- `processing` - Order being prepared
- `shipped` - Order shipped to customer
- `delivered` - Order delivered successfully
- `cancelled` - Order cancelled
- `refunded` - Order refunded

### Payment Status

- `pending` - Payment not yet processed
- `processing` - Payment being processed
- `completed` - Payment successful
- `failed` - Payment failed
- `refunded` - Payment refunded

### Payment Methods

- `esewa` - eSewa digital wallet
- `khalti` - Khalti digital wallet
- `cod` - Cash on delivery
- `bank_transfer` - Bank transfer

## Error Handling Patterns

### Standard Error Response Format

```typescript
interface ApiErrorResponse {
  message: string;
  error?: string;
  statusCode: number;
  timestamp: string;
  path: string;
}
```

### Common Error Scenarios

#### Cart Operations

- **400 Bad Request**: Invalid data, quantity limits exceeded
- **401 Unauthorized**: Authentication required
- **404 Not Found**: Cart item or advertisement not found
- **409 Conflict**: Item no longer available

#### Order Operations

- **400 Bad Request**: Invalid checkout data, cart validation failed
- **401 Unauthorized**: Authentication required
- **404 Not Found**: Order or address not found
- **409 Conflict**: Order cannot be cancelled (status doesn't allow)

#### Address Operations

- **400 Bad Request**: Invalid address data
- **401 Unauthorized**: Authentication required
- **404 Not Found**: Address not found
- **409 Conflict**: Cannot delete default address when it's the only one

### Error Handling Best Practices

1. Always check authentication before making requests
2. Validate cart before checkout
3. Handle network errors gracefully
4. Provide user-friendly error messages
5. Implement retry logic for transient failures
6. Log errors for debugging purposes

## Integration Notes

### Frontend Implementation Requirements

1. Use Redux Toolkit for state management
2. Implement proper error handling with user feedback
3. Ensure status values are lowercase
4. Handle loading states for better UX
5. Implement optimistic updates where appropriate
6. Cache cart data appropriately
7. Sync cart periodically to handle availability changes

### Security Considerations

1. Always validate user permissions on the frontend
2. Never trust client-side cart calculations
3. Implement proper token refresh handling
4. Sanitize user inputs before sending to API
5. Handle sensitive payment information securely

### Performance Optimization

1. Implement debouncing for cart updates
2. Use pagination for order history
3. Cache frequently accessed data
4. Implement proper loading states
5. Optimize API calls to reduce server load

## Payment and Transaction API Endpoints

### 1. Create Transaction

- **Endpoint**: `POST /payments/transactions`
- **Authentication**: Required
- **Description**: Create a new transaction for various purposes

**Request Format**:

```typescript
interface CreateTransactionDto {
  type: TransactionType; // 'ad_payment' | 'wallet_topup' | 'wallet_transfer' | 'withdrawal' | 'refund' | 'commission' | 'subscription_payment'
  amount: number; // Min: 0.01, Max decimal places: 2
  currency?: CurrencyType; // Default: 'NPR'
  advertisementId?: string; // UUID, for ad-related transactions
  paymentMethod?: string; // Max 50 characters
  paymentGateway?: string; // Max 50 characters
  description?: string;
}
```

**Response**: Returns `TransactionResponseDto`

### 2. Get User Transactions

- **Endpoint**: `GET /payments/transactions`
- **Authentication**: Required
- **Description**: Get user's transaction history with pagination

**Query Parameters**:

- `page?: number` (Default: 1)
- `limit?: number` (Default: 20)

**Response Format**:

```typescript
interface PaginatedTransactionsResponse {
  transactions: TransactionResponseDto[];
  total: number;
  page: number;
  limit: number;
}
```

### 3. Get or Create User Wallet

- **Endpoint**: `GET /payments/wallet`
- **Authentication**: Required
- **Description**: Retrieve user's wallet or create if doesn't exist

**Response**: Returns `WalletResponseDto`

### 4. Wallet Top-up

- **Endpoint**: `POST /payments/wallet/topup`
- **Authentication**: Required
- **Description**: Add money to user's wallet

**Request Format**:

```typescript
interface WalletTopupDto {
  amount: number; // Min: 0.01
  paymentMethod: string;
  paymentGateway?: string;
}
```

**Response**: Returns `TransactionResponseDto`

### 5. Wallet Withdrawal

- **Endpoint**: `POST /payments/wallet/withdraw`
- **Authentication**: Required
- **Description**: Withdraw money from user's wallet

**Request Format**:

```typescript
interface WalletWithdrawDto {
  amount: number; // Min: 0.01
  withdrawalMethod: string;
  accountDetails?: Record<string, any>;
}
```

**Response**: Returns `TransactionResponseDto`

### 6. Wallet Transfer

- **Endpoint**: `POST /payments/wallet/transfer`
- **Authentication**: Required
- **Description**: Transfer money to another user's wallet

**Request Format**:

```typescript
interface WalletTransferDto {
  recipientId: string; // UUID
  amount: number; // Min: 0.01
  description?: string;
}
```

**Response Format**:

```typescript
interface WalletTransferResponse {
  senderTransaction: TransactionResponseDto;
  receiverTransaction: TransactionResponseDto;
}
```

### 7. Add Payment Method

- **Endpoint**: `POST /payments/payment-methods`
- **Authentication**: Required
- **Description**: Add a new payment method for the user

**Request Format**:

```typescript
interface CreatePaymentMethodDto {
  type: PaymentMethodType; // 'card' | 'bank_account' | 'ewallet' | 'qr_code'
  provider?: string;
  details: Record<string, any>; // Encrypted payment details
  isDefault?: boolean;
}
```

**Response**: Returns `PaymentMethodResponseDto`

### 8. Payment Gateway Webhooks

- **Endpoint**: `POST /payments/webhooks/:gateway`
- **Authentication**: Not required (webhook endpoint)
- **Description**: Handle payment gateway callbacks

**Response**: `{ status: 'processed' }`

## Transaction and Wallet Response Structures

### Transaction Response

```typescript
interface TransactionResponseDto {
  id: string;
  userId: string;
  advertisementId?: string;
  type: TransactionType;
  amount: number;
  currency: CurrencyType;
  status: TransactionStatus; // 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded'
  paymentMethod?: string;
  paymentGateway?: string;
  gatewayTransactionId?: string;
  description?: string;
  createdAt: Date;
  processedAt?: Date;
  formattedAmount: string; // e.g., "NPR 1,500"
}
```

### Wallet Response

```typescript
interface WalletResponseDto {
  id: string;
  userId: string;
  balance: number;
  currency: CurrencyType;
  isActive: boolean;
  formattedBalance: string; // e.g., "NPR 2,500"
  createdAt: Date;
  updatedAt: Date;
}
```

### Payment Method Response

```typescript
interface PaymentMethodResponseDto {
  id: string;
  userId: string;
  type: PaymentMethodType;
  provider?: string;
  maskedDetails: Record<string, any>; // Masked/safe details for display
  isDefault: boolean;
  isActive: boolean;
  createdAt: Date;
}
```

### Wallet Transaction Response

```typescript
interface WalletTransactionResponseDto {
  id: string;
  walletId: string;
  type: WalletTransactionType; // 'credit' | 'debit'
  amount: number;
  balanceBefore: number;
  balanceAfter: number;
  description: string;
  referenceTransactionId?: string;
  createdAt: Date;
}
```

## Transaction Types and Status Values

### Transaction Types

- `ad_payment` - Payment for advertisement services
- `wallet_topup` - Adding money to wallet
- `wallet_transfer` - Transfer between users
- `withdrawal` - Withdrawing money from wallet
- `refund` - Refund transaction
- `commission` - Platform commission
- `subscription_payment` - Subscription fee payment

### Transaction Status

- `pending` - Transaction initiated but not processed
- `completed` - Transaction successfully completed
- `failed` - Transaction failed
- `cancelled` - Transaction cancelled
- `refunded` - Transaction refunded

### Payment Gateways

- `khalti` - Khalti payment gateway
- `esewa` - eSewa payment gateway
- `stripe` - Stripe payment gateway
- `paypal` - PayPal payment gateway

## Integration Implementation Guide

### Frontend Service Layer Structure

```
src/services/
├── cart-service.ts (existing, needs updates)
├── order-service.ts (new)
├── payment-service.ts (new)
└── transaction-service.ts (new)
```

### Redux Store Structure

```
src/store/slices/
├── cartSlice.ts (existing, needs updates)
├── orderSlice.ts (new)
├── paymentSlice.ts (new)
└── transactionSlice.ts (new)
```

### Component Structure

```
src/components/
├── cart/ (existing)
├── checkout/ (enhance existing)
├── orders/ (new)
├── payments/ (new)
└── transactions/ (new)
```

### API Endpoints Configuration Updates Needed

The following endpoints need to be added to `src/lib/api.ts`:

```typescript
// Orders & Addresses
ORDERS: {
  LIST: "/orders",
  BY_ID: (id: string) => `/orders/${id}`,
  CANCEL: (id: string) => `/orders/${id}/cancel`,
  UPDATE_STATUS: (id: string) => `/orders/${id}/status`,
},
ADDRESSES: {
  LIST: "/users/addresses",
  CREATE: "/users/addresses",
  BY_ID: (id: string) => `/users/addresses/${id}`,
  SET_DEFAULT: (id: string) => `/users/addresses/${id}/default`,
},
CHECKOUT: {
  VALIDATE: "/checkout/validate",
  PROCESS: "/checkout",
},

// Payments & Transactions
PAYMENTS: {
  TRANSACTIONS: "/payments/transactions",
  CREATE_TRANSACTION: "/payments/transactions",
  WALLET: "/payments/wallet",
  WALLET_TOPUP: "/payments/wallet/topup",
  WALLET_WITHDRAW: "/payments/wallet/withdraw",
  WALLET_TRANSFER: "/payments/wallet/transfer",
  PAYMENT_METHODS: "/payments/payment-methods",
  WEBHOOKS: (gateway: string) => `/payments/webhooks/${gateway}`,
},
```

## Testing Strategy

### Unit Tests

- Service layer methods
- Redux slice reducers and thunks
- Utility functions
- Error handling

### Integration Tests

- API endpoint connectivity
- Authentication flow
- Cart-to-order flow
- Payment processing flow

### E2E Tests

- Complete checkout process
- Order management workflow
- Payment and wallet operations
- Error scenarios and recovery

## Deployment Considerations

### Environment Variables

```
NEXT_PUBLIC_API_BASE_URL=https://sasto-api.webstudiomatrix.com/api/v1
NEXT_PUBLIC_PAYMENT_GATEWAY_ESEWA_URL=...
NEXT_PUBLIC_PAYMENT_GATEWAY_KHALTI_URL=...
```

### Error Monitoring

- Implement error tracking for payment failures
- Monitor cart abandonment rates
- Track API response times
- Set up alerts for critical failures

### Performance Monitoring

- Monitor cart sync frequency
- Track checkout completion rates
- Monitor payment processing times
- Optimize heavy API calls
