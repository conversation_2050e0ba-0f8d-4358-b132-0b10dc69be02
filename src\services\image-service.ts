/**
 * Image Service for providing reliable image URLs
 * Handles fallbacks and ensures images work in both development and production
 */

// Reliable image sources that work in production
const RELIABLE_IMAGE_SOURCES = {
  // Local placeholder
  placeholder: "/assets/images/placeholders/placeholder.jpg",
  // Product images
  products: "/assets/images/products",
};

// Category-specific image mappings
const CATEGORY_IMAGES = {
  "art-crafts": [
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
  ],
  vehicles: [
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
  ],
  property: [
    RELIABLE_IMAGE_SOURCES.placeholder,
    REL<PERSON>BLE_IMAGE_SOURCES.placeholder,
    REL<PERSON><PERSON>E_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
  ],
  electronics: [
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
  ],
  "home-garden": [
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
    RELIABLE_IMAGE_SOURCES.placeholder,
  ],
  jobs: [
    RELIABLE_IMAGE_SOURCES.placeholder, // Jobs use placeholder for company logos
  ],
};

/**
 * Get a reliable image URL for a given category and index
 */
export function getReliableImageUrl(
  category: string,
  index: number = 0,
  fallbackToPlaceholder: boolean = true
): string {
  const categoryImages =
    CATEGORY_IMAGES[category as keyof typeof CATEGORY_IMAGES];

  if (categoryImages && categoryImages.length > 0) {
    const imageIndex = index % categoryImages.length;
    return categoryImages[imageIndex];
  }

  if (fallbackToPlaceholder) {
    return RELIABLE_IMAGE_SOURCES.placeholder;
  }

  // Return placeholder as last resort
  return RELIABLE_IMAGE_SOURCES.placeholder;
}

/**
 * Get multiple reliable image URLs for a product
 */
export function getReliableImageUrls(
  category: string,
  count: number = 1,
  startIndex: number = 0
): string[] {
  const images: string[] = [];

  for (let i = 0; i < count; i++) {
    images.push(getReliableImageUrl(category, startIndex + i));
  }

  return images;
}

/**
 * Validate and fix image URLs
 * Replaces problematic URLs with reliable alternatives
 */
export function validateAndFixImageUrl(
  url: string,
  category: string = "art-crafts",
  index: number = 0
): string {
  // If it's already a local asset, return as-is
  if (url.startsWith("/assets/") || url.startsWith("/images/")) {
    return url;
  }

  // If it's a blob or data URL, return as-is
  if (url.startsWith("blob:") || url.startsWith("data:")) {
    return url;
  }

  // List of problematic URL patterns
  const problematicPatterns = [
    "images.unsplash.com",
    "picsum.photos",
    "photo-1594736797933-d0401ba2fe65",
    "localhost:3000/images/placeholder.jpg",
  ];

  // Check if URL contains problematic patterns
  const isProblematic = problematicPatterns.some((pattern) =>
    url.includes(pattern)
  );

  if (isProblematic) {
    return getReliableImageUrl(category, index);
  }

  return url;
}

/**
 * Get placeholder image URL
 */
export function getPlaceholderImageUrl(): string {
  return RELIABLE_IMAGE_SOURCES.placeholder;
}

/**
 * Generate a data URL for emergency fallback
 */
export function generateFallbackDataUrl(
  width: number = 400,
  height: number = 400,
  text: string = "Image"
): string {
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f3f4f6"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16"
            fill="#6b7280" text-anchor="middle" dy=".3em">${text}</text>
    </svg>
  `;

  return `data:image/svg+xml;base64,${btoa(svg)}`;
}

/**
 * Create a comprehensive image configuration for a product
 */
export function createProductImageConfig(
  category: string,
  productId: string,
  imageCount: number = 1
) {
  const baseIndex = parseInt(productId.replace(/\D/g, "")) || 0;

  return {
    primary: getReliableImageUrl(category, baseIndex),
    gallery: getReliableImageUrls(category, imageCount, baseIndex),
    placeholder: getPlaceholderImageUrl(),
    fallback: generateFallbackDataUrl(400, 400, "Product Image"),
  };
}
