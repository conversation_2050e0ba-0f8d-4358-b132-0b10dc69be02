import { useState, useCallback, useEffect } from "react"
import { validateImageFiles } from "@/utils/imageValidation"
import { type FormData } from "../types"

export interface UseImageHandlingResult {
  uploadedImages: string[]
  handleImageUpload: (event: React.ChangeEvent<HTMLInputElement>) => void
  handleImageUpdate: (index: number, newImageUrl: string) => Promise<void>
  handleImageDelete: (index: number) => void
  setUploadedImages: React.Dispatch<React.SetStateAction<string[]>>
}

export const useImageHandling = (
  formData: FormData,
  updateFormData: (updates: Partial<FormData>) => void,
  setErrors: React.Dispatch<React.SetStateAction<string[]>>
): UseImageHandlingResult => {
  const [uploadedImages, setUploadedImages] = useState<string[]>([])

  const handleImageUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files
      if (files) {
        const newFiles = Array.from(files)
        const validationResult = validateImageFiles(newFiles, formData.photos.length)

        if (!validationResult.isValid) {
          setErrors(validationResult.errors.map((error) => error.message))
          return
        }

        updateFormData({
          photos: [...formData.photos, ...validationResult.validFiles],
        })

        const newPreviewUrls = validationResult.validFiles.map((file) => URL.createObjectURL(file))
        setUploadedImages((prevImages) => [...prevImages, ...newPreviewUrls])
        setErrors([])
      }
    },
    [formData.photos.length, updateFormData, setErrors]
  )

  const handleImageUpdate = useCallback(
    async (index: number, newImageUrl: string) => {
      try {
        const response = await fetch(newImageUrl)
        const blob = await response.blob()
        const file = new File([blob], `cropped-image-${index}.jpg`, {
          type: "image/jpeg",
        })

        const updatedPhotos = [...formData.photos]
        updatedPhotos[index] = file
        updateFormData({ photos: updatedPhotos })

        setUploadedImages((prevImages) => {
          const updatedImages = [...prevImages]
          if (updatedImages[index] && updatedImages[index].startsWith("blob:")) {
            URL.revokeObjectURL(updatedImages[index])
          }
          updatedImages[index] = newImageUrl
          return updatedImages
        })
      } catch (error) {
        console.error("Error converting image URL to File:", error)
        setErrors(["Failed to update image. Please try again."])
      }
    },
    [formData.photos, updateFormData, setErrors]
  )

  const handleImageDelete = useCallback(
    (index: number) => {
      const updatedPhotos = [...formData.photos]
      updatedPhotos.splice(index, 1)
      updateFormData({ photos: updatedPhotos })

      setUploadedImages((prevImages) => {
        const updatedImages = [...prevImages]
        if (updatedImages[index] && updatedImages[index].startsWith("blob:")) {
          URL.revokeObjectURL(updatedImages[index])
        }
        updatedImages.splice(index, 1)
        return updatedImages
      })
    },
    [formData.photos, updateFormData]
  )

  // Cleanup effect
  useEffect(() => {
    return () => {
      uploadedImages.forEach((url) => {
        if (url.startsWith("blob:")) {
          URL.revokeObjectURL(url)
        }
      })
    }
  }, [uploadedImages])

  return {
    uploadedImages,
    handleImageUpload,
    handleImageUpdate,
    handleImageDelete,
    setUploadedImages,
  }
}
