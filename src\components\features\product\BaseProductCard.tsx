"use client";

import { memo } from "react";
import { Icon } from "@iconify/react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import Link from "next/link";
import { SmartImage } from "@/components/common/SmartImage";
import { useImageHandler } from "@/hooks/use-image-handler";
import { useKeyboardNavigation } from "@/hooks/use-keyboard-navigation";
import { validateImageUrl } from "@/utils/image-utils";
import { cn } from "@/lib/utils";
import type { BaseProductCardProps } from "./types";
import {
  transformProductToCardData,
  getDefaultProductCardConfig,
  mergeProductCardConfig,
  formatPrice,
  getConditionBadgeColor,
} from "./utils";

const BaseProductCard = memo(function BaseProductCard({
  product,
  config: configOverride,
  className = "",
  onViewDetails,
  onContactSeller: _onContactSeller,
  onFavorite,
  onAddToCart: _onAddToCart,
}: BaseProductCardProps) {
  const config = mergeProductCardConfig(
    getDefaultProductCardConfig(),
    configOverride
  );
  const cardData = transformProductToCardData(product);
  const { handleImageError, handleImageLoad } = useImageHandler();
  const { handleKeyDown } = useKeyboardNavigation({
    onEnter: () => onViewDetails?.(product),
    onSpace: () => onViewDetails?.(product),
  });

  const handleFavorite = (e: React.MouseEvent) => {
    e.stopPropagation();
    onFavorite?.(product);
  };

  const handleClick = () => {
    onViewDetails?.(product);
  };

  // Render horizontal layout (list view)
  if (config.layout === "horizontal") {
    return (
      <div
        className={cn(
          "bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow cursor-pointer",
          className
        )}
        onClick={handleClick}
      >
        <div className="flex">
          {/* Product Image */}
          <div
            className={cn(
              "relative flex-shrink-0 m-3",
              config.size === "compact"
                ? "w-16 h-16"
                : "w-20 h-20 sm:w-24 sm:h-24"
            )}
          >
            <Image
              src={cardData.image}
              alt={cardData.title}
              fill
              className="object-cover rounded-lg"
              onError={handleImageError}
              onLoad={handleImageLoad}
            />
          </div>

          {/* Content Section */}
          <div className="flex-1 p-3 pr-4 relative">
            {/* Heart Icon */}
            {config.showFavorite && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-1 right-1 h-6 w-6 text-gray-400 hover:text-red-500"
                onClick={handleFavorite}
              >
                <Icon icon="lucide:heart" className="h-3 w-3" />
              </Button>
            )}

            {/* Main Content */}
            <div className="space-y-1.5 pr-8">
              <h3
                className={cn(
                  "font-medium text-gray-900 line-clamp-2 leading-tight",
                  config.size === "compact" ? "text-xs" : "text-sm"
                )}
              >
                {cardData.title}
              </h3>

              <div
                className={cn(
                  "font-bold text-gray-900",
                  config.size === "compact" ? "text-base" : "text-lg"
                )}
              >
                {formatPrice(cardData.price, cardData.currency)}
              </div>

              <div className="flex items-center text-xs text-gray-600">
                <Icon icon="lucide:map-pin" className="h-3 w-3 mr-1" />
                {cardData.location}
              </div>

              {(config.showRating || config.showViewCount) && (
                <div className="flex items-center gap-3 text-xs text-gray-600">
                  {config.showRating && cardData.rating && (
                    <div className="flex items-center">
                      <Icon
                        icon="lucide:star"
                        className="h-3 w-3 mr-1 fill-yellow-400 text-yellow-400"
                      />
                      {cardData.rating}
                    </div>
                  )}
                  {config.showViewCount && cardData.views && (
                    <div className="flex items-center">
                      <Icon icon="lucide:eye" className="h-3 w-3 mr-1" />
                      {cardData.views} views
                    </div>
                  )}
                </div>
              )}

              {config.showSellerInfo && (
                <>
                  <div className="border-t border-gray-200 mt-2"></div>
                  <div className="text-xs text-gray-500 pt-1">
                    {cardData.sellerId ? (
                      <Link
                        href={`/profile/${cardData.sellerId}`}
                        className="text-gray-500 hover:text-gray-700 hover:underline"
                        onClick={(e) => e.stopPropagation()}
                      >
                        {cardData.seller}
                      </Link>
                    ) : (
                      <span>{cardData.seller}</span>
                    )}
                    . {cardData.timeAgo}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render vertical layout (grid view)
  return (
    <article
      className={cn(
        "bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer group flex flex-col h-full",
        config.showAnimations && "hover:-translate-y-1 sm:hover:-translate-y-2",
        className
      )}
      onClick={handleClick}
      role="button"
      tabIndex={0}
      onKeyDown={handleKeyDown}
      aria-label={`View details for ${cardData.title}`}
    >
      {/* Product Image */}
      <div
        className={cn(
          "aspect-[4/3] bg-white relative overflow-hidden flex-shrink-0 rounded-lg",
          config.size === "compact"
            ? "mx-1 mt-1"
            : "mx-1 sm:mx-2 md:mx-3 lg:mx-4 mt-1 sm:mt-2 md:mt-3"
        )}
      >
        {product.images && product.images.length > 0 ? (
          <SmartImage
            src={validateImageUrl(product.images[0])}
            alt={product.title}
            width={300}
            height={225}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            category={product.category}
            productTitle={product.title}
            onLoad={handleImageLoad}
          />
        ) : (
          <div className="w-full h-full bg-gray-100 flex items-center justify-center">
            <Icon icon="lucide:image" className="w-12 h-12 text-gray-400" />
          </div>
        )}

        {/* Favorite Button */}
        {config.showFavorite && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 h-8 w-8 bg-white/80 hover:bg-white text-gray-600 hover:text-red-500 backdrop-blur-sm"
            onClick={handleFavorite}
          >
            <Icon icon="lucide:heart" className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Content */}
      <div
        className={cn(
          "flex-1 flex flex-col",
          config.size === "compact" ? "p-2" : "p-3 sm:p-4"
        )}
      >
        {/* Badges */}
        {config.showBadges && cardData.badges && cardData.badges.length > 0 && (
          <div className="flex items-center gap-1 mb-2 flex-wrap">
            <Badge
              variant="secondary"
              className={cn(
                "text-xs px-2 py-0.5",
                getConditionBadgeColor(cardData.condition || "")
              )}
            >
              {cardData.condition}
            </Badge>
            {cardData.brand && config.size !== "compact" && (
              <Badge
                variant="outline"
                className="text-xs px-2 py-0.5 hidden sm:inline-flex"
              >
                <Icon icon="lucide:package" className="h-3 w-3 mr-1" />
                {cardData.brand}
              </Badge>
            )}
          </div>
        )}

        {/* Title */}
        <h3
          className={cn(
            "font-semibold text-gray-900 line-clamp-2 mb-2",
            config.size === "compact" ? "text-sm" : "text-base sm:text-lg"
          )}
        >
          {cardData.title}
        </h3>

        {/* Price */}
        <div
          className={cn(
            "font-bold text-teal-600 mb-2",
            config.size === "compact" ? "text-lg" : "text-xl sm:text-2xl"
          )}
        >
          {formatPrice(cardData.price, cardData.currency)}
        </div>

        {/* Location */}
        <div className="flex items-center text-gray-600 mb-2">
          <Icon icon="lucide:map-pin" className="h-4 w-4 mr-1 flex-shrink-0" />
          <span className="text-sm truncate">{cardData.location}</span>
        </div>

        {/* Stats */}
        {(config.showRating || config.showViewCount) && (
          <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
            {config.showRating && cardData.rating && (
              <div className="flex items-center">
                <Icon
                  icon="lucide:star"
                  className="h-4 w-4 mr-1 fill-yellow-400 text-yellow-400"
                />
                <span>{cardData.rating}</span>
              </div>
            )}
            {config.showViewCount && cardData.views && (
              <div className="flex items-center">
                <Icon icon="lucide:eye" className="h-4 w-4 mr-1" />
                <span>{cardData.views}</span>
              </div>
            )}
          </div>
        )}

        {/* Seller Info */}
        {config.showSellerInfo && (
          <div className="text-xs text-gray-500 mt-auto pt-2 border-t border-gray-100">
            {cardData.sellerId ? (
              <Link
                href={`/profile/${cardData.sellerId}`}
                className="hover:text-gray-700 hover:underline"
                onClick={(e) => e.stopPropagation()}
              >
                {cardData.seller}
              </Link>
            ) : (
              <span>{cardData.seller}</span>
            )}
            <span className="mx-1">•</span>
            <span>{cardData.timeAgo}</span>
          </div>
        )}

        {/* View Details Button */}
        <Button
          className={cn(
            "w-full bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 text-white font-semibold rounded-lg transition-all duration-200 transform hover:scale-105 mt-auto min-h-[44px] flex items-center justify-center",
            config.size === "compact"
              ? "py-1.5 text-xs"
              : "py-2 sm:py-2.5 text-sm sm:text-base"
          )}
          onClick={(e) => {
            e.stopPropagation();
            onViewDetails?.(product);
          }}
          aria-label={`View details for ${cardData.title}`}
        >
          {config.size === "compact" ? "View" : "View Details"}
        </Button>
      </div>
    </article>
  );
});

export default BaseProductCard;
