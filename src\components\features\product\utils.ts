import type { Product } from "@/types/ecommerce";
import type { ProductCardData, ProductCardConfig } from "./types";
import { formatTimeAgo } from "@/utils/date-utils";

/**
 * Shared utility functions for product components
 */

/**
 * Transform a Product object to ProductCardData for display
 */
export function transformProductToCardData(product: Product): ProductCardData {
  return {
    id: product.id,
    title: product.title,
    price: product.price,
    currency: product.currency || "Rs.",
    location: product.location,
    rating: product.seller?.rating,
    views: product.views,
    seller: product.seller?.name || "Unknown",
    sellerId: product.seller?.id,
    timeAgo: formatTimeAgo(product.postedAt),
    image: product.images?.[0] || "/placeholder.svg?height=80&width=120",
    condition: product.condition,
    brand: product.brand,
    subcategory: product.subcategory,
    badges: generateProductBadges(product),
    features: product.features || [],
  };
}

/**
 * Generate badges for a product based on its properties
 */
export function generateProductBadges(product: Product): string[] {
  const badges: string[] = [];
  
  if (product.condition) {
    badges.push(product.condition);
  }
  
  if (product.featured) {
    badges.push("Featured");
  }
  
  if (product.delivery?.available) {
    badges.push("Delivery Available");
  }
  
  if (product.brand) {
    badges.push(product.brand);
  }
  
  if (product.subcategory) {
    badges.push(product.subcategory);
  }
  
  return badges;
}

/**
 * Format price with currency
 */
export function formatPrice(price: number, currency: string = "Rs."): string {
  return `${currency}${price.toLocaleString()}`;
}

/**
 * Format price range
 */
export function formatPriceRange(
  minPrice: number,
  maxPrice: number,
  currency: string = "Rs."
): string {
  if (minPrice === maxPrice) {
    return formatPrice(minPrice, currency);
  }
  return `${formatPrice(minPrice, currency)} - ${formatPrice(maxPrice, currency)}`;
}

/**
 * Check if a product is a job listing
 */
export function isJobProduct(product: Product): boolean {
  return product.category === "jobs" || product.category === "job";
}

/**
 * Get default product card configuration
 */
export function getDefaultProductCardConfig(): ProductCardConfig {
  return {
    layout: "vertical",
    size: "normal",
    showAnimations: true,
    showFavorite: true,
    showBadges: true,
    showSellerInfo: true,
    showViewCount: true,
    showRating: true,
  };
}

/**
 * Merge product card configurations
 */
export function mergeProductCardConfig(
  base: ProductCardConfig,
  override?: Partial<ProductCardConfig>
): ProductCardConfig {
  return { ...base, ...override };
}

/**
 * Generate animation delay for staggered animations
 */
export function getAnimationDelay(index: number, baseDelay: number = 0.1): number {
  return index * baseDelay;
}

/**
 * Validate product data
 */
export function validateProduct(product: Product): boolean {
  return !!(
    product.id &&
    product.title &&
    product.price >= 0 &&
    product.seller?.name &&
    product.location
  );
}

/**
 * Get product display grid classes based on category
 */
export function getProductGridClasses(category?: string): string {
  const isJobCategory = category === "jobs" || category === "job";
  
  if (isJobCategory) {
    return "grid-cols-1 sm:grid-cols-1 lg:grid-cols-2 xl:grid-cols-2";
  }
  
  return "grid-responsive-1-2-3 xl:grid-cols-4 2xl:grid-cols-4";
}

/**
 * Get responsive grid configuration
 */
export function getResponsiveGridConfig(category?: string) {
  const isJobCategory = category === "jobs" || category === "job";
  
  return {
    mobile: 1,
    tablet: isJobCategory ? 1 : 2,
    desktop: isJobCategory ? 2 : 3,
    xl: isJobCategory ? 2 : 4,
  };
}

/**
 * Generate mock job-specific data from product
 */
export function generateJobData(product: Product) {
  // Simple seeded random for consistent results
  const seededRandom = (seed: number) => {
    const x = Math.sin(seed) * 10000;
    return x - Math.floor(x);
  };

  // Generate seed from product ID for consistency
  const seed = product.id ? parseInt(product.id.replace(/\D/g, "") || "1") : 1;

  const jobType =
    product.subcategory === "full-time"
      ? "Full-time"
      : product.subcategory === "part-time"
      ? "Part-time"
      : product.subcategory === "freelance"
      ? "Freelance"
      : "Internship";

  const workMode = "Remote"; // Could be derived from product features
  const experienceLevel = "Senior Level"; // Could be derived from product features
  const applicantCount = Math.floor(seededRandom(seed) * 50) + 1;

  // Mock skills (could be derived from product features or additional fields)
  const skills = ["React", "TypeScript", "Next.js", "Tailwind CSS", "GraphQL"];
  const requirements = skills.slice(0, 4);

  return {
    jobType,
    workMode,
    experienceLevel,
    applicantCount,
    skills,
    requirements,
  };
}

/**
 * Calculate days ago from a date
 */
export function calculateDaysAgo(date: string): number {
  return Math.floor(
    (Date.now() - new Date(date).getTime()) / (1000 * 60 * 60 * 24)
  );
}

/**
 * Get product status color
 */
export function getProductStatusColor(status: string): string {
  switch (status.toLowerCase()) {
    case "active":
      return "text-green-600";
    case "sold":
      return "text-gray-600";
    case "expired":
      return "text-red-600";
    case "hold":
      return "text-yellow-600";
    default:
      return "text-gray-600";
  }
}

/**
 * Get condition badge color
 */
export function getConditionBadgeColor(condition: string): string {
  switch (condition.toLowerCase()) {
    case "new":
      return "bg-green-100 text-green-800 border-green-200";
    case "used":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "refurbished":
      return "bg-purple-100 text-purple-800 border-purple-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
}
