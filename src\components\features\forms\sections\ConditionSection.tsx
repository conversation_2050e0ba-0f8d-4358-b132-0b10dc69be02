import React from "react"
import { Icon } from "@iconify/react"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { ConditionSelector } from "../ui/ConditionSelector"
import { SectionHeader } from "../components/SectionHeader"
import { type FormData } from "../types"

interface ConditionSectionProps {
  formData: FormData
  updateFormData: (updates: Partial<FormData>) => void
}

export const ConditionSection: React.FC<ConditionSectionProps> = ({
  formData,
  updateFormData,
}) => {
  return (
    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
      <CardHeader className="pb-4">
        <SectionHeader
          icon={<Icon icon="lucide:shield-check" className="w-6 h-6 text-white" />}
          title="Product Condition"
          subtitle="Let buyers know the condition of your item"
          required
          step={5}
          totalSteps={6}
        />
      </CardHeader>
      <CardContent>
        <ConditionSelector
          selectedCondition={formData.condition}
          onConditionChange={(condition) =>
            updateFormData({
              condition: condition === "" ? undefined : condition,
            })
          }
        />
      </CardContent>
    </Card>
  )
}
