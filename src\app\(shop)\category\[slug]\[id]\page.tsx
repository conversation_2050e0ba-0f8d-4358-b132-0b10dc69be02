"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { use } from "react";
import {
  PageLayout,
  ShopPageContent,
  CategoryProductSectionsWrapper,
} from "@/components";
import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react";
import { useEcommerceActions } from "@/store/hooks";
import { useGetCategoryBySlugQuery } from "@/store/api/categoriesApi";
import { AdBanner } from "@/components/features/marketing/banner";

interface CategoryPageProps {
  params: Promise<{
    slug: string;
    id: string;
  }>;
}

export default function CategoryPage({ params }: CategoryPageProps) {
  const router = useRouter();
  const { slug, id } = use(params);

  // Redux actions
  const { selectCategory } = useEcommerceActions();

  // Fetch category by slug using RTK Query
  const { data: category, error, isLoading } = useGetCategoryBySlugQuery(slug);

  useEffect(() => {
    if (category && category.id === id) {
      // Update Redux state to select this category
      selectCategory(category.id);
    } else if (error || (category && category.id !== id)) {
      // If category not found or ID doesn't match, redirect to home
      router.push("/");
    }
  }, [category, id, error, selectCategory, router]);

  const handleBack = () => {
    router.push("/");
  };

  if (isLoading) {
    return (
      <PageLayout>
        <div className="flex flex-col items-center justify-center p-4 min-h-[calc(100vh-80px)]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading category...</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  if (error || !category) {
    return (
      <PageLayout>
        <div className="flex flex-col items-center justify-center p-4 min-h-[calc(100vh-80px)]">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Category not found
            </h1>
            <p className="text-gray-600 mb-6">
              The category you&apos;re looking for doesn&apos;t exist or has
              been removed.
            </p>
            <Button onClick={handleBack} className="flex items-center gap-2">
              <Icon icon="lucide:arrow-left" className="w-4 h-4" />
              Go Back to Home
            </Button>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout showBreadcrumb={false}>
      {/* Ads Banner below navbar */}
      <div className="w-full mb-6">
        <div className=" ">
          <AdBanner
            className="w-full"
            autoPlay={true}
            showControls={true}
            muted={true}
          />
        </div>
      </div>

      <ShopPageContent
        showHero={false}
        showCategoryProductSections={false}
        renderCategoryProductSections={false}
        maxProducts={20}
      />

      {/* Full Width Category Product Sections - Show only current category */}
      <CategoryProductSectionsWrapper
        categoryProductSectionIds={category ? [category.id] : undefined}
      />
    </PageLayout>
  );
}
