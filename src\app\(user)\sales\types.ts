export interface Transaction {
  id: string;
  item: string;
  image: string;
  type: "Sold" | "Bought";
  price: string;
  person: string;
  role: string;
  date: string;
  status: "Completed" | "Pending";
  rating: number;
}

export interface StatCardProps {
  title: string;
  value: string;
  change: string;
  changeType: "increase" | "decrease";
  iconName: string;
  bgColor: string;
  iconColor: string;
}

export interface MetricCardProps {
  title: string;
  value: string;
  subtitle: string;
  iconName: string;
  bgColor: string;
  iconColor: string;
}

export interface TransactionFilters {
  searchTerm: string;
  statusFilter: string;
}

export interface PaginationInfo {
  currentPage: number;
  itemsPerPage: number;
  totalItems: number;
  totalPages: number;
  startIndex: number;
  endIndex: number;
}
