"use client";

import BaseProductDetailView from "../BaseProductDetailView";
import type { Product } from "@/types/ecommerce";

interface JobDetailViewProps {
  product: Product;
  onBack?: () => void;
}

export default function JobDetailView({ product, onBack }: JobDetailViewProps) {
  return (
    <BaseProductDetailView
      product={product}
      onBack={onBack}
      config={{
        showSimilarProducts: true,
        showReviews: false,
        showQA: false,
        showSellerVerification: true,
        showSafetyTips: true,
        showPurchaseOptions: false,
        tabsEnabled: false,
      }}
    />
  );
}
