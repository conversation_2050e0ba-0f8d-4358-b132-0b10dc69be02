"use client";

import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON>, Footer } from "@/components";
import PublicProfile from "@/components/features/profile/PublicProfile";
import ContactFormModal from "@/components/features/profile/ContactFormModal";
import { BackButton } from "@/components/ui/back-button";
import { ToastProvider, useToast } from "@/components/ui/toast";
import { Card, CardContent } from "@/components/ui/card";
import { Icon } from "@iconify/react";
import { PageLoading } from "@/components/ui";
import type { PublicProfileData, ContactFormData } from "@/types/ecommerce";
import { useGetUserByUsernameQuery } from "@/store/api/userApi";
import { getAuthToken } from "@/lib/api";

// Simple seeded random number generator for consistent results
const seededRandom = (seed: number) => {
  const x = Math.sin(seed) * 10000;
  return x - Math.floor(x);
};

// Generate a hash from string for consistent seeding
const hashCode = (str: string) => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash);
};

// Mock data for demonstration - in a real app, this would come from an API
// Mock data generator - creates a profile for any userId
const generateMockProfile = (userId: string): PublicProfileData => {
  const isSeller = userId.startsWith("seller-");
  const seed = hashCode(userId);
  const isVerified = seededRandom(seed) > 0.3; // 70% chance of being verified

  const cities = [
    "New York",
    "Los Angeles",
    "Miami",
    "Kathmandu",
    "Chicago",
    "Boston",
  ];

  return {
    id: userId,
    username: isSeller
      ? `${userId.replace("-", " ").replace("seller", "Seller")}`
      : `User${userId.slice(-3)}`,
    email: `${userId}@example.com`,
    phoneNumber: `+1 (555) ${Math.floor(seededRandom(seed + 1) * 900) + 100}-${
      Math.floor(seededRandom(seed + 2) * 9000) + 1000
    }`,
    profilePicture: undefined,
    address: {
      city: cities[Math.floor(seededRandom(seed + 3) * cities.length)],
      country: "Nepal",
    },
    createdAt: new Date(
      2020 + Math.floor(seededRandom(seed + 4) * 4),
      Math.floor(seededRandom(seed + 5) * 12),
      Math.floor(seededRandom(seed + 6) * 28) + 1
    ).toISOString(),
    lastLogin: new Date(
      Date.now() - Math.floor(seededRandom(seed + 7) * 7) * 24 * 60 * 60 * 1000
    ).toISOString(),
    isVerified,
    totalListings: Math.floor(seededRandom(seed + 8) * 50) + 10,
    totalSold: Math.floor(seededRandom(seed + 9) * 40) + 5,
    totalBought: Math.floor(seededRandom(seed + 10) * 20) + 2,
    responseRate: Math.floor(seededRandom(seed + 11) * 30) + 70,
    overallRating: Math.round((seededRandom(seed + 12) * 1.5 + 3.5) * 10) / 10,
    profileViews: Math.floor(seededRandom(seed + 13) * 1000) + 100,
    ratings: {
      asSeller: {
        average: Math.round((seededRandom(seed + 14) * 1.5 + 3.5) * 10) / 10,
        total: Math.floor(seededRandom(seed + 15) * 30) + 5,
        breakdown: {
          5: Math.floor(seededRandom(seed + 16) * 20) + 10,
          4: Math.floor(seededRandom(seed + 17) * 8) + 2,
          3: Math.floor(seededRandom(seed + 18) * 3) + 1,
          2: Math.floor(seededRandom(seed + 19) * 2),
          1: Math.floor(seededRandom(seed + 20) * 1),
        },
      },
      asBuyer: {
        average: Math.round((seededRandom(seed + 21) * 1.5 + 3.5) * 10) / 10,
        total: Math.floor(seededRandom(seed + 22) * 20) + 3,
        breakdown: {
          5: Math.floor(seededRandom(seed + 23) * 15) + 5,
          4: Math.floor(seededRandom(seed + 24) * 5) + 1,
          3: Math.floor(seededRandom(seed + 25) * 3),
          2: Math.floor(seededRandom(seed + 26) * 2),
          1: Math.floor(seededRandom(seed + 27) * 1),
        },
      },
    },
  };
};

function PublicProfilePageContent() {
  const params = useParams();
  const router = useRouter();
  const { addToast } = useToast();
  const [showContactModal, setShowContactModal] = useState(false);

  const userId = params.userId as string;

  // Check if user is authenticated
  const isAuthenticated = !!getAuthToken();

  // Redirect to login if not authenticated (since backend requires auth for all user endpoints)
  React.useEffect(() => {
    if (!isAuthenticated) {
      router.push(`/login?redirect=/profile/${userId}`);
      return;
    }
  }, [isAuthenticated, router, userId]);

  // Show loading while redirecting if not authenticated
  if (!isAuthenticated) {
    return <PageLoading />;
  }

  // Use real API hooks - only call if authenticated
  const {
    data: userData,
    isLoading: userLoading,
    error: userError,
  } = useGetUserByUsernameQuery(userId, {
    skip: !userId || !isAuthenticated,
  });

  const loading = userLoading;
  const error = userError;

  // Convert API User data to PublicProfileData format
  const profileData: PublicProfileData | null = userData
    ? {
        id: userData.id,
        username: userData.username,
        email: userData.email,
        phoneNumber: userData.phone || "",
        profilePicture: userData.profilePicture || "/images/default-avatar.png",
        address: userData.address,
        city: userData.city,
        state: userData.state,
        country: userData.country,
        createdAt: userData.createdAt, // Keep as string for consistency
        updatedAt: userData.updatedAt, // Keep as string for consistency
        isVerified: userData.isVerified,
        totalListings: 0,
        totalSold: 0,
        profileViews: 0,
        bio:
          userData.firstName && userData.lastName
            ? `${userData.firstName} ${userData.lastName}`
            : userData.username,
      }
    : null;

  const handleContactUser = () => {
    setShowContactModal(true);
  };

  const handleSendMessage = async (data: ContactFormData): Promise<void> => {
    // Simulate API call to send message
    await new Promise((resolve) => setTimeout(resolve, 1500));

    console.log("Sending message:", data);

    addToast({
      type: "success",
      title: "Message sent!",
      description: `Your message has been sent to ${profileData?.username}.`,
    });
  };

  const handleBack = () => {
    router.back();
  };

  if (loading) {
    return <PageLoading message="Loading profile..." />;
  }

  if (error || !profileData) {
    return (
      <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
        <Header />
        <div className="mx-[5%] py-8">
          <div className="mb-6">
            <BackButton onClick={handleBack} size="lg" />
          </div>

          <Card className="max-w-md mx-auto">
            <CardContent className="p-8 text-center">
              <Icon
                icon="lucide:alert-circle"
                className="w-12 h-12 text-red-500 mx-auto mb-4"
              />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                {"status" in (error || {}) && (error as any).status === 404
                  ? "Profile Not Found"
                  : "Error Loading Profile"}
              </h2>
              <p className="text-gray-600 mb-4">
                {"status" in (error || {}) && (error as any).status === 404
                  ? "The user profile you're looking for doesn't exist or has been removed."
                  : "We couldn't load this profile. Please try again later."}
              </p>
              <button
                onClick={handleBack}
                className="text-teal-600 hover:text-teal-700 font-medium"
              >
                Go Back
              </button>
            </CardContent>
          </Card>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
      <Header />

      <div className="mx-[5%] py-8">
        {/* Back Button */}
        <div className="mb-6">
          <BackButton onClick={handleBack} size="lg" />
        </div>

        {/* Public Profile Component */}
        <PublicProfile
          profileData={profileData}
          onContactUser={handleContactUser}
          isOwnProfile={false}
        />

        {/* Contact Form Modal */}
        <ContactFormModal
          isOpen={showContactModal}
          onClose={() => setShowContactModal(false)}
          recipientProfile={profileData}
          onSendMessage={handleSendMessage}
        />
      </div>

      <Footer />
    </div>
  );
}

export default function PublicProfilePage() {
  return (
    <ToastProvider>
      <PublicProfilePageContent />
    </ToastProvider>
  );
}
