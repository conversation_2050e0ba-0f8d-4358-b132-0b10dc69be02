"use client";

import React from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import Image from "next/image";

interface SimpleBannerProps {
  className?: string;
  title: string;
  description: string;
  buttonText: string;
  buttonLink?: string;
  backgroundImage?: string;
  backgroundColor?: string;
  onClose?: () => void;
  showCloseButton?: boolean;
}

export default function SimpleBanner({
  className = "",
  title,
  description,
  buttonText,
  buttonLink,
  backgroundImage,
  backgroundColor = "from-[#356267] to-[#478085]",
  onClose,
  showCloseButton = true,
}: SimpleBannerProps) {
  const handleButtonClick = () => {
    if (buttonLink) {
      window.location.href = buttonLink;
    }
  };

  return (
    <div
      className={`relative w-full overflow-hidden rounded-lg shadow-lg ${className}`}
    >
      {/* Banner Container */}
      <div className="relative w-full h-32 sm:h-32 md:h-43 lg:h-45">
        {/* Background */}
        <div className="absolute inset-0">
          {backgroundImage ? (
            <Image
              src={backgroundImage}
              alt="Banner background"
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div
              className={`w-full h-full bg-gradient-to-r ${backgroundColor}`}
            />
          )}
        </div>

        {/* Content */}
        <div className="absolute inset-0 flex items-center justify-between text-white px-4 sm:px-6 lg:px-8">
          <div className="flex-1">
            <h3 className="text-sm sm:text-base md:text-lg lg:text-xl font-bold mb-1 sm:mb-2">
              {title}
            </h3>
            <p className="text-xs sm:text-sm md:text-base opacity-90 mb-2 sm:mb-3">
              {description}
            </p>
          </div>

          <div className="flex items-center gap-2 sm:gap-3">
            <Button
              variant="secondary"
              onClick={handleButtonClick}
              className="bg-white/20 hover:bg-white/30 text-white border-white/30 hover:border-white/50 text-xs sm:text-sm px-3 py-1 sm:px-4 sm:py-2 backdrop-blur-sm"
            >
              {buttonText}
            </Button>

            {showCloseButton && onClose && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-white hover:bg-white/20 p-1 sm:p-2"
                title="Close Banner"
              >
                <Icon icon="lucide:x" className="h-3 w-3 sm:h-4 sm:w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
