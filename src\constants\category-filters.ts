import { CategoryFilterConfig, FilterOptions } from "@/types/ecommerce";

// Base filter options that are common across categories
const baseFilterOptions: Partial<FilterOptions> = {
  condition: ["new", "used", "refurbished"],
  location: [
    "Kathmandu",
    "Lalitpur",
    "Bhaktapur",
    "Pokhara",
    "Chitwan",
    "Biratnagar",
    "Dharan",
    "Butwal",
    "Nepalgunj",
    "Janakpur",
  ],
  delivery: ["home", "pickup"],
  postedWithin: "7days",
  priceRange: {
    min: 0,
    max: 1000000,
  },
  type: [
    "Electronics",
    "Furniture",
    "Clothing",
    "Books",
    "Toys",
    "Sports Equipment",
    "Tools",
    "Accessories",
  ],
  brand: [
    "Samsung",
    "Apple",
    "Sony",
    "LG",
    "HP",
    "Dell",
    "Nike",
    "Adidas",
    "Local Brand",
    "Generic",
  ],
};

// Default filter configuration for categories without specific filters
const defaultFilterConfig: CategoryFilterConfig = {
  categoryId: "default",
  filters: [
    {
      key: "condition",
      label: "Condition",
      type: "select",
      placeholder: "Any",
    },
    {
      key: "type",
      label: "Type",
      type: "select",
      placeholder: "Any",
    },
    {
      key: "location",
      label: "Location",
      type: "select",
      placeholder: "Any",
    },
    {
      key: "delivery",
      label: "Delivery",
      type: "select",
      placeholder: "Any",
    },
    {
      key: "postedWithin",
      label: "Posted Within",
      type: "select",
      placeholder: "Any time",
    },
    {
      key: "brand",
      label: "Brand",
      type: "select",
      placeholder: "Any",
    },
    {
      key: "priceRange",
      label: "Price Range",
      type: "priceRange",
    },
  ],
  availableOptions: baseFilterOptions,
};

// Category-specific filter configurations
export const categoryFilterConfigs: CategoryFilterConfig[] = [
  // Pet & Animal - Filters: Category, Breed, Gender, Location, Delivery, Posted Within, Brand, Price Range
  {
    categoryId: "pets-animals",
    filters: [
      {
        key: "category",
        label: "Category",
        type: "select",
        placeholder: "Any",
      },
      { key: "breed", label: "Breed", type: "select", placeholder: "Any" },
      { key: "gender", label: "Gender", type: "select", placeholder: "Any" },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "delivery",
        label: "Delivery",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      { key: "brand", label: "Brand", type: "select", placeholder: "Any" },
      { key: "priceRange", label: "Price Range", type: "priceRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      category: [
        "Dogs",
        "Cats",
        "Birds",
        "Fish",
        "Small Animals",
        "Pet Supplies",
      ],
      breed: [
        "Labrador",
        "German Shepherd",
        "Golden Retriever",
        "Bulldog",
        "Poodle",
        "Persian Cat",
        "Siamese Cat",
        "Maine Coon",
        "British Shorthair",
        "Canary",
        "Parrot",
        "Goldfish",
        "Betta Fish",
      ],
      gender: ["male", "female"],
      brand: [
        "Pedigree",
        "Royal Canin",
        "Whiskas",
        "Hill's",
        "Purina",
        "Local Breeder",
      ],
    },
  },

  // Photography - Filters: Condition, Category, Type, Brand, Location, Delivery, Posted Within, Price Range
  {
    categoryId: "photography",
    filters: [
      {
        key: "condition",
        label: "Condition",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "category",
        label: "Category",
        type: "select",
        placeholder: "Any",
      },
      { key: "type", label: "Type", type: "select", placeholder: "Any" },
      { key: "brand", label: "Brand", type: "select", placeholder: "Any" },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "delivery",
        label: "Delivery",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      { key: "priceRange", label: "Price Range", type: "priceRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      category: ["Cameras", "Lenses", "Accessories", "Lighting", "Tripods"],
      type: [
        "DSLR",
        "Mirrorless",
        "Point & Shoot",
        "Film Camera",
        "Action Camera",
        "Drone",
      ],
      brand: [
        "Canon",
        "Nikon",
        "Sony",
        "Fujifilm",
        "Olympus",
        "Panasonic",
        "GoPro",
        "DJI",
      ],
    },
  },

  // Property - Filters: Condition, Category, Purpose, Location, Posted Within, Brand, Price Range
  {
    categoryId: "property",
    filters: [
      {
        key: "condition",
        label: "Condition",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "category",
        label: "Category",
        type: "select",
        placeholder: "Any",
      },
      { key: "purpose", label: "Purpose", type: "select", placeholder: "Any" },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      {
        key: "brand",
        label: "Builder/Developer",
        type: "select",
        placeholder: "Any",
      },
      { key: "priceRange", label: "Price Range", type: "priceRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      category: ["Houses", "Apartments", "Land", "Commercial", "Office Space"],
      purpose: ["rent", "sale"],
      brand: [
        "Local Builder",
        "ABC Construction",
        "XYZ Developers",
        "Independent",
      ],
    },
  },

  // Sports & Recreation - Filters: Condition, Category, Brand, Location, Delivery, Posted Within, Price Range
  {
    categoryId: "sports-recreation",
    filters: [
      {
        key: "condition",
        label: "Condition",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "category",
        label: "Category",
        type: "select",
        placeholder: "Any",
      },
      { key: "brand", label: "Brand", type: "select", placeholder: "Any" },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "delivery",
        label: "Delivery",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      { key: "priceRange", label: "Price Range", type: "priceRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      category: [
        "Fitness Equipment",
        "Outdoor Sports",
        "Indoor Games",
        "Team Sports",
      ],
      brand: [
        "Nike",
        "Adidas",
        "Puma",
        "Under Armour",
        "Reebok",
        "Local Brand",
      ],
    },
  },

  // Travel & Tourism - Filters: Destination Type, Travel Type, Duration, Location, Posted Within, Price Range
  {
    categoryId: "travel-tourism",
    filters: [
      {
        key: "destinationType",
        label: "Destination Type",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "travelType",
        label: "Travel Type",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "duration",
        label: "Duration",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      { key: "priceRange", label: "Price Range", type: "priceRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      destinationType: [
        "Domestic",
        "International",
        "Adventure",
        "Cultural",
        "Religious",
      ],
      travelType: ["Solo", "Group", "Family", "Business", "Honeymoon"],
      duration: ["1-3 days", "4-7 days", "1-2 weeks", "2+ weeks"],
    },
  },

  // Vehicles - Filters: Condition, Category, Brand, Location, Delivery, Posted Within, Price Range, Year Range
  {
    categoryId: "vehicles",
    filters: [
      {
        key: "condition",
        label: "Condition",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "category",
        label: "Category",
        type: "select",
        placeholder: "Any",
      },
      { key: "brand", label: "Brand", type: "select", placeholder: "Any" },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "delivery",
        label: "Delivery",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      { key: "priceRange", label: "Price Range", type: "priceRange" },
      { key: "yearRange", label: "Year Range", type: "yearRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      category: ["Cars", "Motorcycles", "Bicycles", "Trucks", "Buses"],
      brand: [
        "Toyota",
        "Honda",
        "Hyundai",
        "Maruti",
        "Tata",
        "Bajaj",
        "Hero",
        "TVS",
      ],
      yearRange: {
        min: 1990,
        max: new Date().getFullYear(),
      },
    },
  },

  // Home & Garden - Filters: Condition, Category, Location, Delivery, Posted Within, Price Range
  {
    categoryId: "home-garden",
    filters: [
      {
        key: "condition",
        label: "Condition",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "category",
        label: "Category",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "delivery",
        label: "Delivery",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      { key: "priceRange", label: "Price Range", type: "priceRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      category: [
        "Furniture",
        "Garden Tools",
        "Home Decor",
        "Kitchen Items",
        "Appliances",
      ],
    },
  },

  // IT & Computer - Filters: Condition, Category, Brand, Size, Location, Delivery, Posted Within, Price Range
  {
    categoryId: "it-computers",
    filters: [
      {
        key: "condition",
        label: "Condition",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "category",
        label: "Category",
        type: "select",
        placeholder: "Any",
      },
      { key: "brand", label: "Brand", type: "select", placeholder: "Any" },
      { key: "size", label: "Size", type: "select", placeholder: "Any" },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "delivery",
        label: "Delivery",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      { key: "priceRange", label: "Price Range", type: "priceRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      category: [
        "Laptops",
        "Desktops",
        "Accessories",
        "Software",
        "Networking",
      ],
      brand: ["Dell", "HP", "Lenovo", "Asus", "Acer", "Apple", "MSI"],
      size: [
        "11-13 inch",
        "14-15 inch",
        "16-17 inch",
        "18+ inch",
        "Mini",
        "Full Size",
      ],
    },
  },

  // Jewellers - Filters: Condition, Category, Materials, Location, Delivery, Posted Within, Gender, Price Range
  {
    categoryId: "jewellers",
    filters: [
      {
        key: "condition",
        label: "Condition",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "category",
        label: "Category",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "materials",
        label: "Materials",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "delivery",
        label: "Delivery",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      { key: "gender", label: "Gender", type: "select", placeholder: "Any" },
      { key: "priceRange", label: "Price Range", type: "priceRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      category: [
        "Gold Jewelry",
        "Silver Jewelry",
        "Precious Stones",
        "Watches",
        "Accessories",
      ],
      materials: [
        "Gold",
        "Silver",
        "Platinum",
        "Diamond",
        "Ruby",
        "Emerald",
        "Sapphire",
      ],
      gender: ["male", "female"],
    },
  },

  // Jobs - Filters: Category, Type, Location, Delivery, Posted Within, Salary Range
  {
    categoryId: "jobs",
    filters: [
      {
        key: "category",
        label: "Category",
        type: "select",
        placeholder: "Any",
      },
      { key: "type", label: "Type", type: "select", placeholder: "Any" },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "delivery",
        label: "Remote/On-site",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      { key: "salaryRange", label: "Salary Range", type: "salaryRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      category: [
        "Full Time",
        "Part Time",
        "Freelance",
        "Contract",
        "Internship",
      ],
      type: [
        "IT",
        "Marketing",
        "Sales",
        "Finance",
        "HR",
        "Engineering",
        "Healthcare",
      ],
      delivery: ["home", "pickup"],
      salaryRange: {
        min: 10000,
        max: 500000,
      },
    },
  },

  // Mobile Phones - Filters: Condition, Category, Brand, Location, Delivery, Posted Within, Price Range
  {
    categoryId: "mobile-phones-gadgets",
    filters: [
      {
        key: "condition",
        label: "Condition",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "category",
        label: "Category",
        type: "select",
        placeholder: "Any",
      },
      { key: "brand", label: "Brand", type: "select", placeholder: "Any" },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "delivery",
        label: "Delivery",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      { key: "priceRange", label: "Price Range", type: "priceRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      category: ["Smartphones", "Tablets", "Smartwatches", "Phone Accessories"],
      brand: [
        "Apple",
        "Samsung",
        "Xiaomi",
        "OnePlus",
        "Huawei",
        "Oppo",
        "Vivo",
      ],
    },
  },

  // Music - Filters: Condition, Category, Brand, Location, Delivery, Posted Within, Price Range
  {
    categoryId: "music-musical-instruments",
    filters: [
      {
        key: "condition",
        label: "Condition",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "category",
        label: "Category",
        type: "select",
        placeholder: "Any",
      },
      { key: "brand", label: "Brand", type: "select", placeholder: "Any" },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "delivery",
        label: "Delivery",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      { key: "priceRange", label: "Price Range", type: "priceRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      category: [
        "Guitars",
        "Keyboards",
        "Drums",
        "Audio Equipment",
        "Wind Instruments",
      ],
      brand: [
        "Yamaha",
        "Fender",
        "Gibson",
        "Roland",
        "Casio",
        "Pearl",
        "Shure",
      ],
    },
  },

  // Office Supplies - Filters: Condition, Category, Brand, Location, Delivery, Posted Within, Price Range
  {
    categoryId: "office-supplies",
    filters: [
      {
        key: "condition",
        label: "Condition",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "category",
        label: "Category",
        type: "select",
        placeholder: "Any",
      },
      { key: "brand", label: "Brand", type: "select", placeholder: "Any" },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "delivery",
        label: "Delivery",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      { key: "priceRange", label: "Price Range", type: "priceRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      category: [
        "Stationery",
        "Office Furniture",
        "Office Electronics",
        "Supplies",
      ],
      brand: ["HP", "Canon", "Epson", "Brother", "Local Brand"],
    },
  },

  // Electronics - Filters: Condition, Category, Brand, Location, Delivery, Posted Within, Price Range
  {
    categoryId: "electronics",
    filters: [
      {
        key: "condition",
        label: "Condition",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "category",
        label: "Category",
        type: "select",
        placeholder: "Any",
      },
      { key: "brand", label: "Brand", type: "select", placeholder: "Any" },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "delivery",
        label: "Delivery",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      { key: "priceRange", label: "Price Range", type: "priceRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      category: [
        "TVs & Audio",
        "Home Appliances",
        "Gaming",
        "Smart Devices",
        "Accessories",
      ],
      brand: [
        "Samsung",
        "LG",
        "Sony",
        "Panasonic",
        "Philips",
        "Xiaomi",
        "Apple",
        "Local Brand",
      ],
    },
  },

  // Art & Crafts - Filters: Condition, Category, Materials, Location, Delivery, Posted Within, Price Range
  {
    categoryId: "art-crafts",
    filters: [
      {
        key: "condition",
        label: "Condition",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "category",
        label: "Category",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "materials",
        label: "Materials",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "delivery",
        label: "Delivery",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      { key: "priceRange", label: "Price Range", type: "priceRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      category: [
        "Paintings",
        "Sculptures",
        "Handmade Crafts",
        "Art Supplies",
        "Digital Art",
        "Traditional Art",
      ],
      materials: [
        "Canvas",
        "Paper",
        "Wood",
        "Metal",
        "Clay",
        "Fabric",
        "Glass",
        "Stone",
        "Digital",
      ],
    },
  },

  // Fashion & Beauty - Filters: Condition, Category, Brand, Size, Gender, Location, Delivery, Posted Within, Price Range
  {
    categoryId: "fashion-beauty",
    filters: [
      {
        key: "condition",
        label: "Condition",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "category",
        label: "Category",
        type: "select",
        placeholder: "Any",
      },
      { key: "brand", label: "Brand", type: "select", placeholder: "Any" },
      { key: "size", label: "Size", type: "select", placeholder: "Any" },
      { key: "gender", label: "Gender", type: "select", placeholder: "Any" },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "delivery",
        label: "Delivery",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      { key: "priceRange", label: "Price Range", type: "priceRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      category: [
        "Clothing",
        "Shoes",
        "Accessories",
        "Beauty Products",
        "Bags",
        "Watches",
      ],
      brand: [
        "Nike",
        "Adidas",
        "Zara",
        "H&M",
        "Uniqlo",
        "Local Brand",
        "Designer",
      ],
      size: ["XS", "S", "M", "L", "XL", "XXL", "Free Size"],
      gender: ["male", "female", "unisex"],
    },
  },

  // Books & Education - Filters: Condition, Category, Type, Location, Delivery, Posted Within, Price Range
  {
    categoryId: "books-education",
    filters: [
      {
        key: "condition",
        label: "Condition",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "category",
        label: "Category",
        type: "select",
        placeholder: "Any",
      },
      { key: "type", label: "Type", type: "select", placeholder: "Any" },
      {
        key: "location",
        label: "Location",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "delivery",
        label: "Delivery",
        type: "select",
        placeholder: "Any",
      },
      {
        key: "postedWithin",
        label: "Posted Within",
        type: "select",
        placeholder: "Any time",
      },
      { key: "priceRange", label: "Price Range", type: "priceRange" },
    ],
    availableOptions: {
      ...baseFilterOptions,
      category: [
        "Academic Books",
        "Fiction",
        "Non-Fiction",
        "Children's Books",
        "Educational Materials",
        "Reference Books",
      ],
      type: [
        "Hardcover",
        "Paperback",
        "Digital",
        "Audio Book",
        "Magazine",
        "Journal",
      ],
    },
  },
];

// Helper function to get filter configuration for a specific category
export const getCategoryFilterConfig = (
  categoryId: string
): CategoryFilterConfig => {
  return (
    categoryFilterConfigs.find((config) => config.categoryId === categoryId) ||
    defaultFilterConfig
  );
};

// Helper function to get all available filter options for a category
export const getCategoryFilterOptions = (
  categoryId: string
): Partial<FilterOptions> => {
  const config = getCategoryFilterConfig(categoryId);
  return config.availableOptions;
};

// Helper function to get available options for a specific filter field
export const getFilterFieldOptions = (
  categoryId: string,
  filterKey: keyof FilterOptions
): string[] => {
  const config = getCategoryFilterConfig(categoryId);
  const options = (config.availableOptions as Record<string, unknown>)[
    filterKey
  ];
  return Array.isArray(options) ? options : [];
};

// Helper function to check if a category has a specific filter
export const categoryHasFilter = (
  categoryId: string,
  filterKey: keyof FilterOptions
): boolean => {
  const config = getCategoryFilterConfig(categoryId);
  return config.filters.some((filter) => filter.key === filterKey);
};

// Helper function to get all supported categories
export const getSupportedCategories = (): string[] => {
  return categoryFilterConfigs.map((config) => config.categoryId);
};

// Helper function to validate filter values for a category
export const validateFilterValue = (
  categoryId: string,
  filterKey: keyof FilterOptions,
  value: unknown
): boolean => {
  if (!categoryHasFilter(categoryId, filterKey)) return false;

  const options = getFilterFieldOptions(categoryId, filterKey);

  // For array values, check if all items are valid
  if (Array.isArray(value)) {
    return value.every((item) => options.includes(item));
  }

  // For string values, check if it's in options
  if (typeof value === "string") {
    return options.includes(value);
  }

  // For range values (objects), validate structure
  if (typeof value === "object" && value !== null) {
    return "min" in value || "max" in value;
  }

  return true;
};
