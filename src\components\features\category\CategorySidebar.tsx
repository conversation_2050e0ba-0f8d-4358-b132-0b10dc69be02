"use client";

import React from "react";
// Redux imports - replacing context API
import { useAppSelector, useEcommerceActions } from "@/store/hooks";
import {
  selectShowFilters,
  selectCategories,
  selectSelectedCategory,
} from "@/store/selectors";
import { FilterPanel } from "../../common";
import AllCategoriesDropdown from "./AllCategoriesDropdown";
import { SidebarPromoBanner } from "../../features/marketing/banner";
import { Icon } from "@iconify/react";
import type { Category } from "@/types/ecommerce";

interface CategorySidebarProps {
  className?: string;
}

// Category Item Component for Sidebar
function CategoryItem({
  category,
  isExpanded,
  onCategorySelect,
  onToggleExpand,
}: {
  category: Category;
  isExpanded: boolean;
  onCategorySelect: (categoryId: string) => void;
  onToggleExpand: (categoryId: string) => void;
}) {
  const hasSubcategories =
    category.subcategories && category.subcategories.length > 0;

  return (
    <div className="border-b border-gray-100 last:border-b-0">
      {/* Category Header */}
      <div className="flex items-center">
        <button
          onClick={() => onCategorySelect(category.id)}
          className="flex-1 flex items-center gap-2 sm:gap-3 p-2 sm:p-3 hover:bg-gray-50 active:bg-gray-100 transition-colors text-left touch-manipulation"
        >
          <span className="text-lg sm:text-xl">{category.icon}</span>
          <span className="text-base sm:text-lg font-medium text-gray-700">
            {category.name}
          </span>
        </button>
        {hasSubcategories && (
          <button
            onClick={() => onToggleExpand(category.id)}
            className="p-2 sm:p-3 hover:bg-gray-50 active:bg-gray-100 transition-colors touch-manipulation min-w-[44px] min-h-[44px] flex items-center justify-center"
            aria-label={`${isExpanded ? "Collapse" : "Expand"} ${
              category.name
            } subcategories`}
          >
            <Icon
              icon="lucide:chevron-down"
              className={`h-4 w-4 sm:h-5 sm:w-5 text-gray-500 transition-transform ${
                isExpanded ? "rotate-180" : ""
              }`}
            />
          </button>
        )}
      </div>

      {/* Subcategories */}
      {hasSubcategories && isExpanded && (
        <div className="bg-gray-50">
          {category.subcategories?.map((subcategory) => (
            <button
              key={subcategory.id}
              onClick={() => onCategorySelect(subcategory.id)}
              className="w-full flex items-center gap-2 sm:gap-3 py-2 sm:py-2.5 px-2 sm:px-3 pl-8 sm:pl-12 hover:bg-gray-100 active:bg-gray-200 transition-colors text-left touch-manipulation min-h-[44px]"
            >
              <span className="text-sm sm:text-base text-gray-600 flex-1">
                {subcategory.name}
              </span>
              <span className="text-xs sm:text-sm text-gray-400 flex-shrink-0">
                ({subcategory.productCount})
              </span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

export default function CategorySidebar({
  className = "",
}: CategorySidebarProps) {
  // Redux selectors - replace context usage
  const showFilters = useAppSelector(selectShowFilters);
  const _categories = useAppSelector(selectCategories);
  const _selectedCategory = useAppSelector(selectSelectedCategory);

  // Redux actions
  const { selectCategory: _selectCategory, toggleFilters: _toggleFilters } =
    useEcommerceActions();

  // State from AllCategoriesDropdown
  const [dropdownState, setDropdownState] = React.useState<{
    isOpen: boolean;
    categories: Category[];
    expandedCategories: Set<string>;
    handleCategorySelect: (categoryId: string) => void;
    handleToggleExpand: (categoryId: string) => void;
  } | null>(null);

  const isDropdownOpen = dropdownState?.isOpen || false;
  const dropdownCategories = dropdownState?.categories || [];
  const expandedCategories = dropdownState?.expandedCategories || new Set();
  const handleCategorySelect =
    dropdownState?.handleCategorySelect || (() => {});
  const handleToggleExpand = dropdownState?.handleToggleExpand || (() => {});

  return (
    <>
      {/* All Categories Dropdown - Always visible at top */}
      <div className="ml-0 mb-3 sm:mb-4 border-gray-200 flex-shrink-0">
        <AllCategoriesDropdown onStateChange={setDropdownState} />
      </div>

      {/* Header */}
      <div className="flex items-center gap-2 ml-1 pb-2 mb-2 sm:mb-3">
        <Icon
          icon="lucide:filter"
          className="h-5 w-5 sm:h-6 sm:w-6 text-gray-600"
        />
        <h3 className="text-base sm:text-lg md:text-xl font-semibold text-gray-800">
          {isDropdownOpen ? "Categories" : "Filter"}
        </h3>
      </div>

      <div
        className={`w-full max-w-sm lg:max-w-md xl:max-w-lg bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col h-fit relative z-40 ${className}`}
      >
        {/* Show Categories when dropdown is open, otherwise show filters */}
        {isDropdownOpen ? (
          <div className="h-fit max-h-[50vh] sm:max-h-[60vh] md:max-h-[65vh] lg:max-h-[70vh] overflow-y-auto relative z-50 bg-white rounded-lg border-2 border-teal-200 shadow-lg custom-scrollbar">
            {dropdownCategories.length > 0 ? (
              dropdownCategories.map((category) => (
                <CategoryItem
                  key={category.id}
                  category={category}
                  isExpanded={expandedCategories.has(category.id)}
                  onCategorySelect={handleCategorySelect}
                  onToggleExpand={handleToggleExpand}
                />
              ))
            ) : (
              <div className="p-4 sm:p-6 text-center text-gray-500">
                <span className="text-sm sm:text-base">
                  No categories available
                </span>
              </div>
            )}
          </div>
        ) : showFilters ? (
          <div className="p-3 sm:p-4 flex-shrink-0">
            <FilterPanel />
          </div>
        ) : null}
      </div>

      {/* Promotional Banner at the bottom - Hide on mobile in modal */}
      <div className="mt-4 sm:mt-6 hidden sm:block lg:block">
        <SidebarPromoBanner />
      </div>
    </>
  );
}
