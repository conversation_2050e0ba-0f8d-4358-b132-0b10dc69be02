"use client";
import Link from "next/link";
import { Icon } from "@iconify/react";
import { But<PERSON> } from "@/components/ui/button";

export function ChatButton() {
  return (
    <Link href="/profile/messages">
      <Button
        variant="ghost"
        className="text-white hover:text-gray-200 hover:bg-white/10 transition-all duration-300 relative p-0 w-10 h-10"
        title="Messages"
      >
        <div className="w-full h-full bg-[#1F5E64] border border-white rounded-lg flex items-center justify-center hover:bg-[#1a5157] transition-all duration-300">
          <Icon icon="material-symbols:chat" className="w-5 h-5" />
        </div>
        {/* Optional: Add notification badge for unread messages */}
        {/* <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
          3
        </span> */}
      </Button>
    </Link>
  );
}
