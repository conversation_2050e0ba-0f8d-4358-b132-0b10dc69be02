"use client";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FilterFieldConfig, ActiveFilters } from "@/types/ecommerce";

interface FilterFieldProps {
  config: FilterFieldConfig;
  value: ActiveFilters[keyof ActiveFilters];
  onChange: (
    key: keyof ActiveFilters,
    value: ActiveFilters[keyof ActiveFilters]
  ) => void;
  options: (string | { value: string; label: string })[];
}

export default function FilterField({
  config,
  value,
  onChange,
  options,
}: FilterFieldProps) {
  const [rangeMin, setRangeMin] = useState(
    config.type.includes("Range") &&
      value &&
      typeof value === "object" &&
      "min" in value &&
      value.min
      ? value.min.toString()
      : ""
  );
  const [rangeMax, setRangeMax] = useState(
    config.type.includes("Range") &&
      value &&
      typeof value === "object" &&
      "max" in value &&
      value.max
      ? value.max.toString()
      : ""
  );

  const handleRangeSubmit = () => {
    const min = rangeMin ? Number.parseInt(rangeMin) : undefined;
    const max = rangeMax ? Number.parseInt(rangeMax) : undefined;

    // Validate range
    if (min !== undefined && max !== undefined && min > max) {
      onChange(config.key, { min: max, max: min });
    } else {
      onChange(config.key, { min, max });
    }
  };

  const handleRangeKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleRangeSubmit();
    }
  };

  const getSelectedValue = (): string => {
    if (Array.isArray(value)) {
      return value.length > 0 ? value[0] : "all";
    }
    if (config.type.includes("Range")) {
      return "all"; // Range inputs don't use select dropdown
    }
    return (value as string) || "all";
  };

  const renderSelectField = () => (
    <Select
      value={getSelectedValue()}
      onValueChange={(val) => {
        if (config.key === "postedWithin") {
          onChange(
            config.key,
            val === "all"
              ? undefined
              : (val as ActiveFilters[typeof config.key])
          );
        } else if (
          [
            "condition",
            "delivery",
            "location",
            "type",
            "brand",
            "category",
            "breed",
            "gender",
            "materials",
            "size",
            "destinationType",
            "travelType",
            "duration",
            "purpose",
          ].includes(config.key)
        ) {
          onChange(
            config.key,
            val === "all" ? [] : ([val] as ActiveFilters[typeof config.key])
          );
        } else {
          onChange(
            config.key,
            val === "all"
              ? undefined
              : (val as ActiveFilters[typeof config.key])
          );
        }
      }}
    >
      <SelectTrigger className="w-full bg-white">
        <SelectValue placeholder={config.placeholder || "Any"} />
      </SelectTrigger>
      <SelectContent className="bg-white text-black">
        <SelectItem value="all">{config.placeholder || "Any"}</SelectItem>
        {options.map((option) => (
          <SelectItem
            key={typeof option === "string" ? option : option.value}
            value={typeof option === "string" ? option : option.value}
          >
            {typeof option === "string"
              ? option.charAt(0).toUpperCase() + option.slice(1)
              : option.label || option.value}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );

  const renderRangeField = () => (
    <div className="flex items-center gap-2">
      <Input
        type="number"
        placeholder={
          config.type === "priceRange"
            ? "Min Price"
            : config.type === "yearRange"
            ? "Min Year"
            : "Min Salary"
        }
        value={rangeMin}
        onChange={(e) => setRangeMin(e.target.value)}
        onKeyDown={handleRangeKeyDown}
        onBlur={handleRangeSubmit}
        className="flex-1 bg-white"
      />
      <span className="text-md text-gray-500">To</span>
      <Input
        type="number"
        placeholder={
          config.type === "priceRange"
            ? "Max Price"
            : config.type === "yearRange"
            ? "Max Year"
            : "Max Salary"
        }
        value={rangeMax}
        onChange={(e) => setRangeMax(e.target.value)}
        onKeyDown={handleRangeKeyDown}
        onBlur={handleRangeSubmit}
        className="flex-1 bg-white"
      />
    </div>
  );

  const renderPostedWithinField = () => (
    <Select
      value={(typeof value === "string" ? value : undefined) || "all"}
      onValueChange={(val) =>
        onChange(
          config.key,
          val === "all" ? undefined : (val as ActiveFilters[typeof config.key])
        )
      }
    >
      <SelectTrigger className="w-full bg-white">
        <SelectValue placeholder="Any time" />
      </SelectTrigger>
      <SelectContent className="bg-white text-black">
        <SelectItem value="all">Any time</SelectItem>
        {[
          { value: "1day", label: "Last 24 hours" },
          { value: "3days", label: "Last 3 days" },
          { value: "7days", label: "Last 7 days" },
          { value: "30days", label: "Last 30 days" },
        ].map((timeOption) => (
          <SelectItem key={timeOption.value} value={timeOption.value}>
            {timeOption.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );

  return (
    <div className="space-y-2">
      <label className="text-md font-medium text-gray-700">
        {config.label}
      </label>
      {config.key === "postedWithin"
        ? renderPostedWithinField()
        : config.type.includes("Range")
        ? renderRangeField()
        : renderSelectField()}
    </div>
  );
}
