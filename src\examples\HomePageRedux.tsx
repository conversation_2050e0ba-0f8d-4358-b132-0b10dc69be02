"use client";

import React, { useState } from "react";
import {
  CategorySidebar,
  CategoryProductSections,
  ProductGrid,
  DynamicBannerCarousel as BannerCarousel,
  SortDropdown,
  MobileControlBar,
  SimpleErrorBoundary,
} from "@/components";
import { But<PERSON> } from "@/components/ui/button";
import { Icon } from "@iconify/react";

// Redux imports
import { useAppSelector, useEcommerceActions } from "@/store/hooks";
import {
  selectSortBy,
  selectFilteredProducts,
  selectSelectedCategory,
  selectShowFilters,
  selectProductsLoading,
} from "@/store/selectors";
import { updateSortAndProducts } from "@/store/thunks/ecommerceThunks";
import { useAppDispatch } from "@/store/hooks";
import type { SortBy } from "@/types/ecommerce";

export default function HomePageRedux() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Redux selectors - replace context usage
  const dispatch = useAppDispatch();
  const sortBy = useAppSelector(selectSortBy);
  const filteredProducts = useAppSelector(selectFilteredProducts);
  const selectedCategory = useAppSelector(selectSelectedCategory);
  const showFilters = useAppSelector(selectShowFilters);
  const isLoading = useAppSelector(selectProductsLoading);

  // Redux actions - replace context actions
  const { toggleFilters } = useEcommerceActions();

  const handleSortChange = (newSortBy: SortBy) => {
    // Use async thunk for complex operations
    dispatch(updateSortAndProducts(newSortBy));
  };

  const handleToggleFilters = () => {
    toggleFilters(!showFilters);
  };

  return (
    <SimpleErrorBoundary>
      <div className="min-h-screen bg-[#EFEFEF]">
        {/* Mobile Control Bar */}
        <div className="lg:hidden">
          <MobileControlBar
            onFilterClick={() => setIsSidebarOpen(!isSidebarOpen)}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
          />
        </div>

        <div className="flex">
          {/* Left Sidebar */}
          <div
            className={`
              fixed inset-y-0 left-0 z-50 w-80 bg-white shadow-lg transform transition-transform duration-300 ease-in-out
              lg:relative lg:translate-x-0 lg:shadow-none lg:z-auto
              ${isSidebarOpen ? "translate-x-0" : "-translate-x-full"}
            `}
          >
            <CategorySidebar />
          </div>

          {/* Mobile Overlay */}
          {isSidebarOpen && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
              onClick={() => setIsSidebarOpen(false)}
            />
          )}

          {/* Main Content */}
          <div className="flex-1 lg:ml-0">
            {/* Hero Section with Banner Carousel */}
            <div className="bg-white">
              <BannerCarousel />
            </div>

            {/* Sub Header with All Categories and Sort */}
            <div className="bg-white border-b border-gray-200 px-4 py-3">
              <div className="flex items-center justify-between">
                {/* All Categories Button */}
                <div className="flex items-center space-x-4">
                  <Button
                    variant="outline"
                    onClick={handleToggleFilters}
                    className="flex items-center space-x-2"
                  >
                    <Icon
                      icon="material-symbols:filter-list"
                      className="w-4 h-4"
                    />
                    <span>All Categories</span>
                  </Button>

                  {selectedCategory && (
                    <span className="text-sm text-gray-600">
                      Category: {selectedCategory}
                    </span>
                  )}
                </div>

                {/* Sort Dropdown */}
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Icon
                      icon="material-symbols:grid-view"
                      className={`w-5 h-5 cursor-pointer ${
                        viewMode === "grid" ? "text-blue-600" : "text-gray-400"
                      }`}
                      onClick={() => setViewMode("grid")}
                    />
                    <Icon
                      icon="material-symbols:list"
                      className={`w-5 h-5 cursor-pointer ${
                        viewMode === "list" ? "text-blue-600" : "text-gray-400"
                      }`}
                      onClick={() => setViewMode("list")}
                    />
                  </div>

                  <SortDropdown
                    sortBy={sortBy}
                    onSortChange={handleSortChange}
                  />
                </div>
              </div>
            </div>

            {/* Products Grid */}
            <div className="p-4">
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                  <span className="ml-2">Loading products...</span>
                </div>
              ) : (
                <ProductGrid viewMode={viewMode} />
              )}
            </div>

            {/* Category Product Sections */}
            <div className="space-y-8 px-4 pb-8">
              <CategoryProductSections
                categoryIds={undefined} // Use all available categories from API
              />
            </div>
          </div>
        </div>
      </div>
    </SimpleErrorBoundary>
  );
}

// Example of a component that uses multiple Redux features
export function ProductGridRedux({ viewMode }: { viewMode: "grid" | "list" }) {
  const dispatch = useAppDispatch();

  // Multiple selectors
  const products = useAppSelector(selectFilteredProducts);
  const currentPage = useAppSelector((state) => state.product.currentPage);
  const totalPages = useAppSelector((state) => state.product.totalPages);
  const isLoading = useAppSelector(selectProductsLoading);

  // Actions
  const { setCurrentPage, addToCart } = useEcommerceActions();

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleAddToCart = (product: any) => {
    // Use async thunk for optimistic updates
    dispatch({
      type: "ecommerce/addToCartOptimistic",
      payload: { product, quantity: 1 },
    });
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      {/* Product grid implementation */}
      <div
        className={`grid ${
          viewMode === "grid"
            ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
            : "grid-cols-1"
        } gap-4`}
      >
        {products.map((product: any) => (
          <div key={product.id} className="border rounded-lg p-4">
            <h3>{product.title}</h3>
            <p>{product.price}</p>
            <button onClick={() => handleAddToCart(product)}>
              Add to Cart
            </button>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-center mt-8">
        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
          <button
            key={page}
            onClick={() => handlePageChange(page)}
            className={`px-3 py-1 mx-1 rounded ${
              currentPage === page ? "bg-blue-500 text-white" : "bg-gray-200"
            }`}
          >
            {page}
          </button>
        ))}
      </div>
    </div>
  );
}
