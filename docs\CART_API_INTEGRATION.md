# Cart API Integration Guide

## Overview

This document provides comprehensive information about the completed cart API integration in the SastoBazar e-commerce frontend. The cart system now fully integrates with the backend APIs for real-time cart management.

## Architecture

### Frontend Components
- **CartService**: API service layer for cart operations
- **cartSlice**: Redux slice with API-integrated thunks
- **Cart Components**: UI components that use the integrated cart system
- **Hooks**: Convenient hooks for cart operations

### Backend Integration
- **Base URL**: Uses the configured API base URL from `src/lib/api.ts`
- **Authentication**: All cart operations require JWT authentication
- **Real-time Sync**: Cart state is synchronized with the backend

## API Endpoints Used

### Cart Operations
- `GET /cart` - Fetch user's cart
- `POST /cart/items` - Add item to cart
- `PUT /cart/items/:itemId` - Update cart item quantity
- `DELETE /cart/items/:itemId` - Remove item from cart
- `DELETE /cart/items/bulk` - Remove multiple items
- `DELETE /cart` - Clear entire cart
- `POST /cart/sync` - Sync cart with server

## Redux Integration

### New Async Thunks

#### Core Cart Operations
```typescript
// Fetch cart from server
fetchCart()

// Add item to cart (using advertisement ID)
addToCartAsync(data: AddToCartDto)

// Update cart item quantity
updateCartItemAsync({ itemId: string, data: UpdateCartItemDto })

// Remove single item
removeCartItemAsync(itemId: string)

// Remove multiple items
bulkRemoveCartItemsAsync(data: BulkRemoveDto)

// Clear entire cart
clearCartAsync()

// Sync cart with server
syncCartWithServer()
```

#### Convenience Thunks
```typescript
// Add product to cart (converts Product to AddToCartDto)
addProductToCart({ product: Product, quantity: number })
```

### State Management

#### Cart State Interface
```typescript
interface CartState {
  items: CartItem[];           // Cart items (frontend format)
  totalItems: number;          // Total quantity of items
  totalPrice: number;          // Total price
  currency: string;            // Currency (NPR/USD)
  loading: boolean;            // Loading state
  error?: string;              // Error message
  cartId?: string;             // Backend cart ID
  lastSyncAt?: string;         // Last sync timestamp
}
```

#### Data Conversion
- **Backend → Frontend**: `CartItemDto` → `CartItem` with full Product details
- **Frontend → Backend**: `Product` → `AddToCartDto` with advertisement ID

## Usage Examples

### Basic Cart Operations

#### Adding Products to Cart
```typescript
import { useEcommerceActions } from '@/store/hooks';

const { addProductToCart } = useEcommerceActions();

// Add product to cart
await addProductToCart(product, quantity);
```

#### Fetching Cart
```typescript
const { fetchCart } = useEcommerceActions();

// Fetch current cart state
await fetchCart();
```

#### Updating Cart Items
```typescript
const { updateCartItem } = useEcommerceActions();

// Update item quantity
await updateCartItem(cartItemId, newQuantity);
```

#### Removing Items
```typescript
const { removeCartItem, clearCartAsync } = useEcommerceActions();

// Remove single item
await removeCartItem(cartItemId);

// Clear entire cart
await clearCartAsync();
```

### Component Integration

#### Cart Page Example
```typescript
import { useEffect } from 'react';
import { useEcommerce, useEcommerceActions } from '@/store/compatibility';

export default function CartPage() {
  const { state } = useEcommerce();
  const { fetchCart, updateCartItem, removeCartItem } = useEcommerceActions();
  
  useEffect(() => {
    // Fetch cart on component mount
    fetchCart();
  }, [fetchCart]);

  const handleQuantityChange = async (itemId: string, quantity: number) => {
    await updateCartItem(itemId, quantity);
  };

  const handleRemoveItem = async (itemId: string) => {
    await removeCartItem(itemId);
  };

  return (
    <div>
      {state.cart.loading && <div>Loading...</div>}
      {state.cart.error && <div>Error: {state.cart.error}</div>}
      
      {state.cart.items.map(item => (
        <div key={item.id}>
          <h3>{item.product.title}</h3>
          <p>Price: {item.product.currency} {item.product.price}</p>
          <p>Quantity: {item.quantity}</p>
          <button onClick={() => handleQuantityChange(item.id, item.quantity + 1)}>
            Increase
          </button>
          <button onClick={() => handleRemoveItem(item.id)}>
            Remove
          </button>
        </div>
      ))}
    </div>
  );
}
```

### Product Card Integration
```typescript
import { useEcommerceActions } from '@/store/hooks';

export function ProductCard({ product }: { product: Product }) {
  const { addProductToCart } = useEcommerceActions();

  const handleAddToCart = async () => {
    try {
      await addProductToCart(product, 1);
      // Show success message
    } catch (error) {
      // Handle error
    }
  };

  return (
    <div>
      <h3>{product.title}</h3>
      <p>{product.currency} {product.price}</p>
      <button onClick={handleAddToCart}>
        Add to Cart
      </button>
    </div>
  );
}
```

## Error Handling

### Common Error Scenarios
1. **Authentication Required**: User must be logged in
2. **Item Not Available**: Advertisement is no longer active
3. **Quantity Limits**: Exceeding available inventory
4. **Network Errors**: API connectivity issues

### Error Handling Pattern
```typescript
const { addProductToCart } = useEcommerceActions();

try {
  await addProductToCart(product, quantity);
  // Success handling
} catch (error) {
  // Error is automatically stored in cart.error state
  console.error('Failed to add to cart:', error);
}
```

## Initialization

### App Startup
The cart is automatically fetched when the app initializes if the user is authenticated:

```typescript
// In initializeApp thunk
if (userAuthenticated) {
  await dispatch({ type: "cart/fetchCart" });
}
```

### Manual Sync
```typescript
const { syncCart } = useEcommerceActions();

// Manually sync cart with server
await syncCart();
```

## Migration from Mock Data

### Before (Mock Implementation)
```typescript
// Old mock implementation
export const syncCartWithServer = createAsyncThunk(
  "cart/syncCartWithServer",
  async (cartItems: CartItem[]) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    return cartItems;
  }
);
```

### After (API Integration)
```typescript
// New API integration
export const syncCartWithServer = createAsyncThunk(
  "cart/syncCartWithServer",
  async (_, { rejectWithValue }) => {
    try {
      const cartResponse = await CartService.syncCart();
      return cartResponse;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to sync cart");
    }
  }
);
```

## Testing

### Unit Tests
Test the cart service methods:
```typescript
import { CartService } from '@/services/cart-service';

describe('CartService', () => {
  it('should add item to cart', async () => {
    const result = await CartService.addToCart({
      advertisementId: 'ad-123',
      quantity: 1
    });
    expect(result.items).toHaveLength(1);
  });
});
```

### Integration Tests
Test the Redux thunks:
```typescript
import { addProductToCart } from '@/store/slices/cartSlice';

describe('Cart Thunks', () => {
  it('should add product to cart', async () => {
    const result = await store.dispatch(addProductToCart({
      product: mockProduct,
      quantity: 1
    }));
    expect(result.type).toBe('cart/addProductToCart/fulfilled');
  });
});
```

## Performance Considerations

### Optimistic Updates
For better UX, consider implementing optimistic updates:
```typescript
// Add item optimistically, then sync with server
dispatch(addToCart({ product, quantity })); // Local update
await addProductToCart(product, quantity);   // Server sync
```

### Caching
- Cart data is cached in Redux state
- Automatic sync on app initialization
- Manual sync available when needed

## Security

### Authentication
- All cart operations require valid JWT token
- Token is automatically included in API requests
- Automatic logout on authentication errors

### Data Validation
- Server-side validation for all cart operations
- Client-side validation for better UX
- Error handling for invalid requests

## Next Steps

1. **Implement optimistic updates** for better UX
2. **Add cart persistence** for offline scenarios
3. **Implement cart sharing** between devices
4. **Add cart analytics** for business insights
5. **Optimize performance** with selective updates

## Troubleshooting

### Common Issues

1. **Cart not loading**: Check authentication status
2. **Items not updating**: Verify API endpoints configuration
3. **Sync failures**: Check network connectivity and server status
4. **State inconsistencies**: Use manual sync to resolve

### Debug Tools
- Redux DevTools for state inspection
- Network tab for API call monitoring
- Console logs for error tracking
