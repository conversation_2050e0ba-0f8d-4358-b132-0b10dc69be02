import React from "react"
import { Icon } from "@iconify/react"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { SectionHeader } from "../components/SectionHeader"
import { type FormData } from "../types"

interface LocationSectionProps {
  formData: FormData
  updateFormData: (updates: Partial<FormData>) => void
}

export const LocationSection: React.FC<LocationSectionProps> = ({
  formData,
  updateFormData,
}) => {
  return (
    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
      <CardHeader className="pb-4">
        <SectionHeader
          icon={<Icon icon="lucide:map-pin" className="w-6 h-6 text-white" />}
          title="Location Information"
          subtitle="Help buyers find your product (optional)"
        />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="location" className="text-sm font-medium text-gray-700">
              Address/Location
            </Label>
            <Input
              id="location"
              type="text"
              placeholder="Street address or landmark"
              value={formData.location || ""}
              onChange={(e) => updateFormData({ location: e.target.value })}
              className="h-12 border-2 border-gray-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 rounded-xl"
              maxLength={200}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="city" className="text-sm font-medium text-gray-700">
              City
            </Label>
            <Input
              id="city"
              type="text"
              placeholder="Your city"
              value={formData.city || ""}
              onChange={(e) => updateFormData({ city: e.target.value })}
              className="h-12 border-2 border-gray-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 rounded-xl"
              maxLength={100}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="state" className="text-sm font-medium text-gray-700">
              State/Province
            </Label>
            <Input
              id="state"
              type="text"
              placeholder="Your state or province"
              value={formData.state || ""}
              onChange={(e) => updateFormData({ state: e.target.value })}
              className="h-12 border-2 border-gray-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 rounded-xl"
              maxLength={100}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
