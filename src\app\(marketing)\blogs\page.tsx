import { Icon } from "@iconify/react";
import Link from "next/link";

export default function BlogsPage() {
  const blogPosts = [
    {
      id: 1,
      title: "Top 10 Shopping Tips for Nepal Marketplace",
      excerpt: "Discover the best strategies to find great deals and authentic products on our platform.",
      author: "Nepal Marketplace Team",
      date: "2024-01-15",
      category: "Shopping Tips",
      image: "/api/placeholder/400/250",
      readTime: "5 min read"
    },
    {
      id: 2,
      title: "How to Sell Successfully on Our Platform",
      excerpt: "A comprehensive guide for sellers to maximize their sales and build a strong reputation.",
      author: "Seller Success Team",
      date: "2024-01-10",
      category: "Seller Guide",
      image: "/api/placeholder/400/250",
      readTime: "8 min read"
    },
    {
      id: 3,
      title: "Understanding Product Quality Standards",
      excerpt: "Learn about our quality assurance process and what makes a product eligible for listing.",
      author: "Quality Team",
      date: "2024-01-05",
      category: "Quality Assurance",
      image: "/api/placeholder/400/250",
      readTime: "6 min read"
    },
    {
      id: 4,
      title: "Digital Payment Security in Nepal",
      excerpt: "Everything you need to know about safe online transactions and protecting your financial data.",
      author: "Security Team",
      date: "2023-12-28",
      category: "Security",
      image: "/api/placeholder/400/250",
      readTime: "7 min read"
    },
    {
      id: 5,
      title: "Supporting Local Businesses Through E-commerce",
      excerpt: "How our marketplace helps small businesses reach customers across Nepal.",
      author: "Community Team",
      date: "2023-12-20",
      category: "Community",
      image: "/api/placeholder/400/250",
      readTime: "4 min read"
    },
    {
      id: 6,
      title: "Seasonal Shopping Trends in Nepal",
      excerpt: "Insights into shopping patterns during festivals and seasonal changes in Nepal.",
      author: "Market Research Team",
      date: "2023-12-15",
      category: "Market Insights",
      image: "/api/placeholder/400/250",
      readTime: "6 min read"
    }
  ];

  const categories = ["All", "Shopping Tips", "Seller Guide", "Quality Assurance", "Security", "Community", "Market Insights"];

  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <div className="container-responsive spacing-responsive-lg">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Our Blog
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Stay updated with the latest news, tips, and insights from Nepal Marketplace. 
            Learn how to make the most of your shopping and selling experience.
          </p>
        </div>

        {/* Categories Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              className="px-6 py-2 rounded-full bg-white text-gray-600 hover:bg-teal-600 hover:text-white transition-all duration-300 shadow-md hover:shadow-lg"
            >
              {category}
            </button>
          ))}
        </div>

        {/* Blog Posts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {blogPosts.map((post) => (
            <article 
              key={post.id}
              className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden"
            >
              <div className="aspect-video bg-gray-200 relative">
                <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                  <Icon icon="lucide:image" className="h-12 w-12" />
                </div>
              </div>
              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <span className="bg-teal-100 text-teal-600 px-3 py-1 rounded-full text-sm font-medium">
                    {post.category}
                  </span>
                  <span className="text-gray-500 text-sm">{post.readTime}</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-3 line-clamp-2">
                  {post.title}
                </h3>
                <p className="text-gray-600 mb-4 line-clamp-3">
                  {post.excerpt}
                </p>
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-500">
                    <div>{post.author}</div>
                    <div>{new Date(post.date).toLocaleDateString()}</div>
                  </div>
                  <Link 
                    href={`/blogs/${post.id}`}
                    className="text-teal-600 hover:text-teal-700 font-medium flex items-center gap-1"
                  >
                    Read More
                    <Icon icon="lucide:arrow-right" className="h-4 w-4" />
                  </Link>
                </div>
              </div>
            </article>
          ))}
        </div>

        {/* Newsletter Signup */}
        <div className="bg-gradient-to-r from-teal-600 to-teal-700 rounded-xl p-8 text-white text-center">
          <h2 className="text-3xl font-bold mb-4">Stay Updated</h2>
          <p className="text-lg mb-6 opacity-90">
            Subscribe to our newsletter and never miss our latest blog posts and updates.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-white"
            />
            <button className="bg-white text-teal-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300">
              Subscribe
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
