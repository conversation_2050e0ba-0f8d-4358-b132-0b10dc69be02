"use client";
import AccountInformation from "./AccountInformation";
import ChangePassword from "./ChangePassword";
import ProfileVisibility from "./ProfileVisibility";
import DeleteAccount from "./DeleteAccount";

interface AccountTabProps {
  showCurrentPassword: boolean;
  setShowCurrentPassword: (show: boolean) => void;
  showNewPassword: boolean;
  setShowNewPassword: (show: boolean) => void;
  showConfirmPassword: boolean;
  setShowConfirmPassword: (show: boolean) => void;
  profileVisibility: {
    showEmail: boolean;
    showPhone: boolean;
    showLastLogin: boolean;
    showTransactionHistory: boolean;
  };
  setProfileVisibility: React.Dispatch<
    React.SetStateAction<{
      showEmail: boolean;
      showPhone: boolean;
      showLastLogin: boolean;
      showTransactionHistory: boolean;
    }>
  >;
}

export default function AccountTab({
  showCurrentPassword,
  setShowCurrentPassword,
  showNewPassword,
  setShowNewPassword,
  showConfirmPassword,
  setShowConfirmPassword,
  profileVisibility,
  setProfileVisibility,
}: AccountTabProps) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <AccountInformation />
        <ChangePassword
          showCurrentPassword={showCurrentPassword}
          setShowCurrentPassword={setShowCurrentPassword}
          showNewPassword={showNewPassword}
          setShowNewPassword={setShowNewPassword}
          showConfirmPassword={showConfirmPassword}
          setShowConfirmPassword={setShowConfirmPassword}
        />
      </div>

      <ProfileVisibility
        profileVisibility={profileVisibility}
        setProfileVisibility={setProfileVisibility}
      />

      <DeleteAccount />
    </div>
  );
}
