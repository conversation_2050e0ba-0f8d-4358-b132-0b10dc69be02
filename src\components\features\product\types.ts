import type { Product } from "@/types/ecommerce";

/**
 * Shared interfaces and types for product components
 */

// Base product card configuration
export interface ProductCardConfig {
  layout: "horizontal" | "vertical" | "detailed" | "job";
  size: "compact" | "normal" | "large";
  showAnimations?: boolean;
  showFavorite?: boolean;
  showBadges?: boolean;
  showSellerInfo?: boolean;
  showViewCount?: boolean;
  showRating?: boolean;
}

// Common product card props
export interface BaseProductCardProps {
  product: Product;
  config?: Partial<ProductCardConfig>;
  className?: string;
  onViewDetails?: (product: Product) => void;
  onContactSeller?: (product: Product) => void;
  onFavorite?: (product: Product) => void;
  onAddToCart?: (product: Product) => void;
}

// Product display configuration
export interface ProductDisplayConfig {
  viewMode: "grid" | "list";
  itemsPerRow?: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
  showAnimations?: boolean;
  showPagination?: boolean;
  showSorting?: boolean;
  showFilters?: boolean;
}

// Product list/grid props
export interface ProductDisplayProps {
  products: Product[];
  config?: Partial<ProductDisplayConfig>;
  loading?: boolean;
  error?: string | null;
  className?: string;
  emptyStateTitle?: string;
  emptyStateDescription?: string;
  onProductSelect?: (product: Product) => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
}

// Product detail view configuration
export interface ProductDetailConfig {
  showSimilarProducts?: boolean;
  showReviews?: boolean;
  showQA?: boolean;
  showSellerVerification?: boolean;
  showSafetyTips?: boolean;
  showSharing?: boolean;
  showWishlist?: boolean;
  showPurchaseOptions?: boolean;
  tabsEnabled?: boolean;
  defaultTab?: string;
}

// Product detail view props
export interface ProductDetailProps {
  product: Product;
  config?: Partial<ProductDetailConfig>;
  similarProducts?: Product[];
  className?: string;
  onBack?: () => void;
  onContactSeller?: (product: Product) => void;
  onPurchase?: (product: Product, quantity: number) => void;
  onAddToCart?: (product: Product, quantity: number) => void;
}

// Job-specific interfaces
export interface JobApplicationData {
  personalInfo: {
    fullName: FormDataEntryValue | null;
    phone: FormDataEntryValue | null;
    email: FormDataEntryValue | null;
    address: FormDataEntryValue | null;
  };
  experiences: Array<{
    id: string;
    company: string;
    position: string;
    duration: string;
  }>;
  references: Array<{
    id: string;
    name: string;
    position: string;
    contact: string;
  }>;
  documents: {
    cv: File | null;
    coverLetter: File | null;
  };
  jobTitle: string;
  companyName: string;
}

export interface JobCardProps extends BaseProductCardProps {
  onApply?: (product: Product) => void;
  applicationData?: JobApplicationData;
}

// Product transformation utilities types
export interface ProductCardData {
  id: string;
  title: string;
  price: number;
  currency: string;
  location: string;
  rating?: number;
  views?: number;
  seller: string;
  sellerId?: string;
  timeAgo: string;
  image: string;
  condition?: string;
  brand?: string;
  subcategory?: string;
  badges?: string[];
  features?: string[];
}

// Animation and interaction types
export interface AnimationConfig {
  enabled: boolean;
  delay?: number;
  duration?: number;
  type?: "fadeIn" | "slideUp" | "scaleIn";
}

export interface InteractionHandlers {
  onViewDetails?: (product: Product) => void;
  onContactSeller?: (product: Product) => void;
  onFavorite?: (product: Product) => void;
  onShare?: (product: Product) => void;
  onReport?: (product: Product) => void;
  onAddToWishlist?: (product: Product) => void;
  onRemoveFromWishlist?: (product: Product) => void;
}
