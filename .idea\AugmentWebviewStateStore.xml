<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;c080c00b-2f46-4fa8-8f3b-2c41cc8bfc1b&quot;,&quot;conversations&quot;:{&quot;c080c00b-2f46-4fa8-8f3b-2c41cc8bfc1b&quot;:{&quot;id&quot;:&quot;c080c00b-2f46-4fa8-8f3b-2c41cc8bfc1b&quot;,&quot;createdAtIso&quot;:&quot;2025-08-11T06:18:08.580Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-08-11T08:12:37.620Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;5be48996-e567-4bd4-bfba-daef55c31f01&quot;,&quot;request_message&quot;:&quot;for production why img hitting my own website link \r\nhttps://ecom.webstudiomatrix.com/advertisements?page=1&amp;limit=12&amp;status=active&amp;sortBy=createdAt&amp;sortOrder=DESC\r\nRequest Method\r\nGET\r\nStatus Code\r\n404 Not Found\r\nRemote Address\r\n************:443 .  check&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;for production why img hitting my own website link \r&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;https://ecom.webstudiomatrix.com/advertisements?page=1&amp;limit=12&amp;status=active&amp;sortBy=createdAt&amp;sortOrder=DESC\r&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Request Method\r&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;GET\r&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Status Code\r&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;404 Not Found\r&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Remote Address\r&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;************:443 .  check&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;for production why img hitting my own website link \r\nhttps://ecom.webstudiomatrix.com/advertisements?page=1&amp;limit=12&amp;status=active&amp;sortBy=createdAt&amp;sortOrder=DESC\r\nRequest Method\r\nGET\r\nStatus Code\r\n404 Not Found\r\nRemote Address\r\n************:443 .  check&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Desktop/ecom&quot;,&quot;folderRoot&quot;:&quot;C:/Users/<USER>/Desktop/ecom&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-11T06:22:17.315Z&quot;,&quot;response_text&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-feed4871-9d53-47ba-8e0f-d8fd154aa630&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasAgentOnboarded&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;469f0917-f80f-4ff4-a553-82116b4585da&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>