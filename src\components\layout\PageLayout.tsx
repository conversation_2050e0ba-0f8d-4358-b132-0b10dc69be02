"use client";

import React, { ReactNode } from "react";
import { <PERSON><PERSON>, Footer } from "@/components";

interface PageLayoutProps {
  children: ReactNode;
  showBreadcrumb?: boolean;
  breadcrumbContent?: ReactNode;
  className?: string;
}

export default function PageLayout({
  children,
  showBreadcrumb = false,
  breadcrumbContent,
  className = "",
}: PageLayoutProps) {
  return (
    <div className={`min-h-screen bg-[#EFEFEF] ${className}`}>
      <Header />
      
      {/* Breadcrumb */}
      {showBreadcrumb && breadcrumbContent && (
        <div className="bg-white border-b border-gray-200">
          <div className="container-responsive px-4 py-3">
            {breadcrumbContent}
          </div>
        </div>
      )}

      {/* Main Content */}
      {children}

      <Footer />
    </div>
  );
}
