/**
 * Image validation utilities for advertisement uploads
 * Ensures frontend validation matches backend AdImageConfig constraints
 */

// Backend AdImageConfig constraints
export const IMAGE_UPLOAD_CONSTRAINTS = {
  MAX_FILE_SIZE: 8 * 1024 * 1024, // 8MB - matches backend AdImageConfig
  MAX_IMAGES: 8, // Maximum images per advertisement
  ALLOWED_MIME_TYPES: [
    'image/jpeg',
    'image/png', 
    'image/gif',
    'image/webp'
  ] as const,
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.webp'] as const,
} as const;

export interface ImageValidationError {
  type: 'size' | 'type' | 'count' | 'general';
  message: string;
  fileIndex?: number;
  fileName?: string;
}

export interface ImageValidationResult {
  isValid: boolean;
  errors: ImageValidationError[];
  validFiles: File[];
}

/**
 * Validates a single image file against backend constraints
 */
export function validateSingleImage(file: File, index?: number): ImageValidationError[] {
  const errors: ImageValidationError[] = [];
  const fileIndex = index !== undefined ? index + 1 : undefined;
  const fileName = file.name;

  // File size validation
  if (file.size > IMAGE_UPLOAD_CONSTRAINTS.MAX_FILE_SIZE) {
    errors.push({
      type: 'size',
      message: `Image ${fileIndex ? `${fileIndex} ` : ''}is too large (max 8MB)`,
      fileIndex,
      fileName,
    });
  }

  // MIME type validation
  if (!IMAGE_UPLOAD_CONSTRAINTS.ALLOWED_MIME_TYPES.includes(file.type as any)) {
    errors.push({
      type: 'type',
      message: `Image ${fileIndex ? `${fileIndex} ` : ''}must be JPEG, PNG, GIF, or WebP format`,
      fileIndex,
      fileName,
    });
  }

  return errors;
}

/**
 * Validates multiple image files for advertisement upload
 */
export function validateImageFiles(
  newFiles: File[], 
  existingFileCount: number = 0
): ImageValidationResult {
  const errors: ImageValidationError[] = [];
  const validFiles: File[] = [];

  // Check total file count
  const totalFiles = existingFileCount + newFiles.length;
  if (totalFiles > IMAGE_UPLOAD_CONSTRAINTS.MAX_IMAGES) {
    errors.push({
      type: 'count',
      message: `Maximum ${IMAGE_UPLOAD_CONSTRAINTS.MAX_IMAGES} images allowed`,
    });
    return { isValid: false, errors, validFiles: [] };
  }

  // Validate each file
  newFiles.forEach((file, index) => {
    const fileErrors = validateSingleImage(file, index);
    if (fileErrors.length > 0) {
      errors.push(...fileErrors);
    } else {
      validFiles.push(file);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    validFiles,
  };
}

/**
 * Formats file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Gets human-readable file type from MIME type
 */
export function getFileTypeDisplay(mimeType: string): string {
  const typeMap: Record<string, string> = {
    'image/jpeg': 'JPEG',
    'image/png': 'PNG',
    'image/gif': 'GIF',
    'image/webp': 'WebP',
  };
  
  return typeMap[mimeType] || 'Unknown';
}

/**
 * Checks if a file extension is allowed
 */
export function isAllowedExtension(fileName: string): boolean {
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return IMAGE_UPLOAD_CONSTRAINTS.ALLOWED_EXTENSIONS.includes(extension as any);
}

/**
 * Gets the maximum file size in a human-readable format
 */
export function getMaxFileSizeDisplay(): string {
  return formatFileSize(IMAGE_UPLOAD_CONSTRAINTS.MAX_FILE_SIZE);
}

/**
 * Gets allowed file types for display
 */
export function getAllowedTypesDisplay(): string {
  return IMAGE_UPLOAD_CONSTRAINTS.ALLOWED_MIME_TYPES
    .map(type => getFileTypeDisplay(type))
    .join(', ');
}

/**
 * Creates an accept attribute value for file input
 */
export function getFileInputAccept(): string {
  return IMAGE_UPLOAD_CONSTRAINTS.ALLOWED_MIME_TYPES.join(',');
}
