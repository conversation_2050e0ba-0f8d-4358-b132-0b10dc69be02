"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { Icon } from "@iconify/react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/toast";
import type { SavedListing } from "@/types/ecommerce";

// Mock saved listings data
const mockSavedListings: SavedListing[] = [
  {
    id: "saved-1",
    userId: "user-123",
    productId: "prod-1",
    product: {
      id: "prod-1",
      slug: "vintage-camera",
      title: "Vintage Canon AE-1 Camera",
      description: "Classic 35mm film camera in excellent condition",
      price: 25000,
      currency: "₹",
      images: ["/assets/images/placeholders/placeholder.jpg"],
      category: "Photography",
      location: "Mumbai, Maharashtra",
      seller: {
        id: "seller-1",
        name: "PhotoPro",
        rating: 4.8,
      },
      condition: "used",
      postedAt: "2024-01-10",
      delivery: { available: true, type: "both" },
      featured: false,
      status: "active",
      views: 156,
      isSavedByUser: true,
    },
    savedAt: new Date("2024-01-15"),
    notes: "Perfect for my photography collection",
  },
  {
    id: "saved-2",
    userId: "user-123",
    productId: "prod-2",
    product: {
      id: "prod-2",
      slug: "gaming-laptop",
      title: "ASUS ROG Gaming Laptop",
      description: "High-performance gaming laptop with RTX 3070",
      price: 85000,
      currency: "₹",
      images: [
        "https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=400&h=300&fit=crop&crop=center",
      ],
      category: "Electronics",
      location: "Delhi, India",
      seller: {
        id: "seller-2",
        name: "TechDealer",
        rating: 4.5,
      },
      condition: "used",
      postedAt: "2024-01-12",
      delivery: { available: true, type: "home" },
      featured: true,
      status: "active",
      views: 234,
      isSavedByUser: true,
    },
    savedAt: new Date("2024-01-18"),
    notes: "Waiting for price drop",
  },
];

interface SavedListingsProps {
  className?: string;
}

export default function SavedListings({ className }: SavedListingsProps) {
  const [savedListings, setSavedListings] =
    useState<SavedListing[]>(mockSavedListings);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedListing, setSelectedListing] = useState<SavedListing | null>(
    null
  );
  const [notes, setNotes] = useState("");
  const { addToast } = useToast();

  const filteredListings = savedListings.filter(
    (listing) =>
      listing.product.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      listing.product.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleRemoveFromSaved = (listingId: string) => {
    const listing = savedListings.find((l) => l.id === listingId);
    setSavedListings((prev) =>
      prev.filter((listing) => listing.id !== listingId)
    );

    if (listing) {
      addToast({
        type: "success",
        title: "Removed from saved",
        description: `${listing.product.title} has been removed from your saved listings.`,
      });
    }
  };

  const handleUpdateNotes = (listingId: string, newNotes: string) => {
    setSavedListings((prev) =>
      prev.map((listing) =>
        listing.id === listingId ? { ...listing, notes: newNotes } : listing
      )
    );
  };

  const openNotesDialog = (listing: SavedListing) => {
    setSelectedListing(listing);
    setNotes(listing.notes || "");
  };

  const saveNotes = () => {
    if (selectedListing) {
      handleUpdateNotes(selectedListing.id, notes);
      addToast({
        type: "success",
        title: "Notes updated",
        description: `Notes for ${selectedListing.product.title} have been saved.`,
      });
      setSelectedListing(null);
      setNotes("");
    }
  };

  return (
    <div className={`space-y-6 ${className || ""}`}>
      {/* Header with Search */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Icon
            icon="lucide:search"
            className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"
          />
          <Input
            placeholder="Search saved listings..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 w-full"
          />
        </div>
        <Button variant="outline" size="sm" className="whitespace-nowrap">
          <Icon icon="lucide:filter" className="w-4 h-4 mr-2" />
          Filter
        </Button>
      </div>

      {/* Saved Listings Grid */}
      {filteredListings.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredListings.map((listing) => (
            <Card
              key={listing.id}
              className="group hover:shadow-lg transition-all duration-300 border border-gray-200 rounded-lg overflow-hidden"
            >
              <CardContent className="p-0">
                <div className="relative overflow-hidden">
                  <Image
                    src={listing.product.images[0]}
                    alt={listing.product.title}
                    width={300}
                    height={200}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300"></div>

                  {/* Action Buttons */}
                  <div className="absolute top-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-8 w-8 p-0 bg-white/90 hover:bg-white shadow-sm backdrop-blur-sm rounded-full"
                      onClick={() => openNotesDialog(listing)}
                    >
                      <Icon
                        icon="lucide:sticky-note"
                        className="w-4 h-4 text-gray-600"
                      />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-8 w-8 p-0 bg-white/90 hover:bg-red-50 shadow-sm backdrop-blur-sm rounded-full"
                      onClick={() => handleRemoveFromSaved(listing.id)}
                    >
                      <Icon
                        icon="lucide:trash-2"
                        className="w-4 h-4 text-red-500"
                      />
                    </Button>
                  </div>

                  {/* Status Badge */}
                  <div className="absolute top-3 left-3">
                    <Badge
                      variant={
                        listing.product.status === "sold"
                          ? "secondary"
                          : "default"
                      }
                      className={`text-xs font-medium shadow-sm rounded-full px-3 py-1 ${
                        listing.product.status === "sold"
                          ? "bg-red-500 hover:bg-red-600 text-white border-red-500"
                          : "bg-green-500 hover:bg-green-600 text-white border-green-500"
                      }`}
                    >
                      {listing.product.status === "sold" ? "Sold" : "Available"}
                    </Badge>
                  </div>
                </div>

                <div className="p-4 space-y-3">
                  <div className="space-y-2">
                    <h3 className="font-semibold text-gray-900 text-lg group-hover:text-teal-600 transition-colors duration-300 line-clamp-2">
                      {listing.product.title}
                    </h3>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Icon
                        icon="lucide:map-pin"
                        className="w-4 h-4 flex-shrink-0"
                      />
                      <span className="truncate">
                        {listing.product.location}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Icon
                        icon="lucide:calendar"
                        className="w-4 h-4 flex-shrink-0"
                      />
                      <span>
                        Saved {new Date(listing.savedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <p className="text-xl font-bold text-gray-900 group-hover:text-teal-600 transition-colors duration-300">
                      {listing.product.currency}{" "}
                      {listing.product.price.toLocaleString()}
                    </p>
                    <div className="flex items-center gap-1">
                      <Icon
                        icon="lucide:star"
                        className="w-4 h-4 fill-yellow-400 text-yellow-400"
                      />
                      <span className="text-sm font-medium text-gray-600">
                        {listing.product.seller.rating}
                      </span>
                    </div>
                  </div>

                  {listing.notes && (
                    <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                      <p className="text-sm text-blue-800 italic line-clamp-2">
                        {listing.notes}
                      </p>
                    </div>
                  )}

                  <div className="pt-2">
                    <Link
                      href={`/products/${listing.product.slug}`}
                      className="block"
                    >
                      <Button className="w-full bg-teal-600 hover:bg-teal-700 text-white transition-colors duration-200">
                        <Icon
                          icon="lucide:external-link"
                          className="w-4 h-4 mr-2"
                        />
                        View Details
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-16 px-4">
          <div className="max-w-md mx-auto">
            <Icon
              icon="lucide:heart"
              className="w-20 h-20 text-gray-300 mx-auto mb-6"
            />
            <h3 className="text-2xl font-semibold text-gray-900 mb-3">
              {searchQuery
                ? "No matching saved listings"
                : "No saved listings yet"}
            </h3>
            <p className="text-gray-600 mb-8 leading-relaxed">
              {searchQuery
                ? "Try adjusting your search terms to find what you're looking for"
                : "Start saving listings you're interested in to see them here. Click the heart icon on any product to save it."}
            </p>
            {!searchQuery && (
              <Link href="/">
                <Button className="bg-teal-600 hover:bg-teal-700 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                  Browse Products
                </Button>
              </Link>
            )}
          </div>
        </div>
      )}

      {/* Notes Dialog */}
      <Dialog
        open={!!selectedListing}
        onOpenChange={() => setSelectedListing(null)}
      >
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle className="text-lg font-semibold">
              Add Notes
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Notes for:{" "}
                <span className="font-medium text-gray-900">
                  {selectedListing?.product.title}
                </span>
              </p>
              <Textarea
                placeholder="Add your notes about this listing..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={4}
                className="resize-none border-gray-300 focus:border-teal-500 focus:ring-teal-500"
              />
            </div>
            <div className="flex gap-3 justify-end pt-2">
              <Button
                variant="outline"
                onClick={() => setSelectedListing(null)}
                className="px-4 py-2"
              >
                Cancel
              </Button>
              <Button
                onClick={saveNotes}
                className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 transition-colors duration-200"
              >
                Save Notes
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
