{"name": "ecommerce-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "docker:build": "docker build -t ecom-app .", "docker:run": "docker run -p 3000:3000 ecom-app", "docker:dev": "docker-compose up --build", "docker:dev-db": "docker-compose --profile with-db up --build", "docker:stop": "docker-compose down", "docker:clean": "docker-compose down -v --rmi all", "analyze": "ANALYZE=true npm run build", "analyze:server": "ANALYZE=true npm run build && npx serve out", "bundle:analyze": "npx @next/bundle-analyzer", "perf:monitor": "node scripts/performance-monitor.js", "perf:baseline": "node scripts/performance-monitor.js && echo 'Performance baseline established'", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "clean": "rm -rf .next out node_modules/.cache", "clean:install": "npm run clean && npm install"}, "dependencies": {"@iconify/react": "^6.0.0", "@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.5", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.5", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@reduxjs/toolkit": "^2.8.2", "@types/yup": "^0.29.14", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "embla-carousel-react": "^8.6.0", "formik": "^2.4.6", "nest-winston": "^1.10.2", "next": "15.4.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-image-crop": "^11.0.10", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "sonner": "^2.0.7", "sqlite3": "^5.1.7", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "typeorm": "^0.3.25", "winston": "^3.17.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@jest/globals": "^30.0.5", "@nestjs/cli": "^11.0.10", "@nestjs/testing": "^11.1.5", "@next/bundle-analyzer": "^15.4.5", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/pg": "^8.15.5", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-image-crop": "^8.1.6", "@types/react-redux": "^7.1.34", "@types/redux-logger": "^3.0.13", "eslint": "^9", "eslint-config-next": "15.4.1", "redux-logger": "^3.0.6", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}