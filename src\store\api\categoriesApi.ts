import { apiSlice } from "./index";
import { Category, Subcategory } from "@/types/ecommerce";
import {
  CategoryResponseDto,
  SubcategoryResponseDto,
  GetCategoriesParams,
  GetSubcategoriesParams,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CreateSubcategoryRequest,
  UpdateSubcategoryRequest,
} from "@/types/api";
import {
  transformSingleCategoryResponse,
  transformSubcategoriesResponse,
} from "@/utils/api-transformers";

export const categoriesApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all categories
    getCategories: builder.query<Category[], GetCategoriesParams | void>({
      query: (params = {}) => ({
        url: "/categories",
        params: {
          includeInactive: true,
          ...params,
        },
      }),
      transformResponse: (response: CategoryResponseDto[]) => {
        // Handle direct array response from the API
        if (!response) {
          console.warn("getCategories: response is null/undefined");
          return [];
        }

        if (!Array.isArray(response)) {
          console.warn("getCategories: response is not an array", response);
          return [];
        }

        return response.map((category) => ({
          id: category.id,
          name: category.name,
          slug: category.slug,
          description: category.description || "",
          icon: category.iconUrl || "📦",
          productCount: category.advertisementCount || 0,
          subcategories:
            category.subcategories?.map((sub) => ({
              id: sub.id,
              name: sub.name,
              slug: sub.slug,
              description: sub.description || "",
              productCount: sub.advertisementCount || 0,
            })) || [],
        }));
      },
      providesTags: ["Category"],
    }),

    // Get category by ID
    getCategoryById: builder.query<Category, string>({
      query: (id) => `/categories/${id}`,
      transformResponse: (response: CategoryResponseDto) =>
        transformSingleCategoryResponse(response),
      providesTags: (_, __, id) => [{ type: "Category", id }],
    }),

    // Get category by slug
    getCategoryBySlug: builder.query<Category, string>({
      query: (slug) => `/categories/slug/${slug}`,
      transformResponse: (response: CategoryResponseDto) =>
        transformSingleCategoryResponse(response),
      providesTags: (_, __, slug) => [{ type: "Category", id: slug }],
    }),

    // Get subcategories for a category
    getSubcategories: builder.query<Subcategory[], GetSubcategoriesParams>({
      query: ({ categoryId, activeOnly = true }) => ({
        url: `/categories/${categoryId}/subcategories`,
        params: { activeOnly },
      }),
      transformResponse: (response: SubcategoryResponseDto[]) =>
        transformSubcategoriesResponse(response),
      providesTags: (_, __, { categoryId }) => [
        { type: "Category", id: categoryId },
        "Subcategory",
      ],
    }),

    // Create category
    createCategory: builder.mutation<
      CategoryResponseDto,
      CreateCategoryRequest
    >({
      query: (data) => ({
        url: "/categories",
        method: "POST",
        body: data,
        headers: {
          "Content-Type": "application/json",
        },
      }),
      invalidatesTags: ["Category"],
    }),

    // Update category
    updateCategory: builder.mutation<
      CategoryResponseDto,
      { id: string; data: UpdateCategoryRequest }
    >({
      query: ({ id, data }) => ({
        url: `/categories/${id}`,
        method: "PATCH",
        body: data,
        headers: {
          "Content-Type": "application/json",
        },
      }),
      invalidatesTags: (_, __, { id }) => [
        { type: "Category", id },
        "Category",
      ],
    }),

    // Delete category
    deleteCategory: builder.mutation<void, string>({
      query: (id) => ({
        url: `/categories/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: (_, __, id) => [{ type: "Category", id }, "Category"],
    }),

    // Create subcategory
    createSubcategory: builder.mutation<
      SubcategoryResponseDto,
      CreateSubcategoryRequest
    >({
      query: (data) => ({
        url: `/categories/${data.categoryId}/subcategories`,
        method: "POST",
        body: data,
        headers: {
          "Content-Type": "application/json",
        },
      }),
      invalidatesTags: (_, __, { categoryId }) => [
        { type: "Category", id: categoryId },
        "Subcategory",
      ],
    }),

    // Update subcategory
    updateSubcategory: builder.mutation<
      SubcategoryResponseDto,
      { id: string; data: UpdateSubcategoryRequest }
    >({
      query: ({ id, data }) => ({
        url: `/subcategories/${id}`,
        method: "PATCH",
        body: data,
        headers: {
          "Content-Type": "application/json",
        },
      }),
      invalidatesTags: (_, __, { id }) => [
        { type: "Subcategory", id },
        "Category",
        "Subcategory",
      ],
    }),

    // Delete subcategory
    deleteSubcategory: builder.mutation<void, string>({
      query: (id) => ({
        url: `/subcategories/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: (_, __, id) => [
        { type: "Subcategory", id },
        "Category",
        "Subcategory",
      ],
    }),
  }),
});

// Export hooks for use in components
export const {
  useGetCategoriesQuery,
  useGetCategoryByIdQuery,
  useGetCategoryBySlugQuery,
  useGetSubcategoriesQuery,
  useCreateCategoryMutation,
  useUpdateCategoryMutation,
  useDeleteCategoryMutation,
  useCreateSubcategoryMutation,
  useUpdateSubcategoryMutation,
  useDeleteSubcategoryMutation,
} = categoriesApi;

// Export the API slice for store configuration
export default categoriesApi;
