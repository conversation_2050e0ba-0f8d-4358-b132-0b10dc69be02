import React from "react"
import { Icon } from "@iconify/react"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SectionHeader } from "../components/SectionHeader"
import { type FormData, CURRENCY_OPTIONS } from "../types"

interface PricingSectionProps {
  formData: FormData
  updateFormData: (updates: Partial<FormData>) => void
}

export const PricingSection: React.FC<PricingSectionProps> = ({
  formData,
  updateFormData,
}) => {
  return (
    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
      <CardHeader className="pb-4">
        <SectionHeader
          icon={<Icon icon="lucide:dollar-sign" className="w-6 h-6 text-white" />}
          title="Pricing Information"
          subtitle="Set your price or mark as negotiable"
          step={6}
          totalSteps={6}
        />
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="currency" className="text-sm font-medium text-gray-700">
                Currency
              </Label>
              <Select
                value={formData.currency}
                onValueChange={(value) => updateFormData({ currency: value as any })}
              >
                <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 rounded-xl">
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  {CURRENCY_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{option.symbol}</span>
                        <span>{option.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="md:col-span-2 space-y-2">
              <Label htmlFor="price" className="text-sm font-medium text-gray-700">
                Price (optional)
              </Label>
              <div className="relative">
                <div className="absolute mr-8 inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <span className="text-gray-500 text-lg font-medium">
                    {CURRENCY_OPTIONS.find((c) => c.value === formData.currency)?.symbol || "₨"}
                  </span>
                </div>
                <Input
                  id="price"
                  type="text"
                  placeholder="Enter your price (e.g., 50,000)"
                  value={formData.price || ""}
                  onChange={(e) => updateFormData({ price: e.target.value })}
                  className="h-12 text-lg border-2 border-gray-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 rounded-xl pl-16 pr-6 transition-all duration-200"
                />
              </div>
            </div>
          </div>

          <div className="p-4 border-2 border-gray-200 rounded-xl hover:border-blue-300 transition-colors">
            <div className="flex items-center space-x-3">
              <Checkbox
                id="negotiable"
                checked={formData.negotiable}
                onCheckedChange={(checked) => updateFormData({ negotiable: !!checked })}
                className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
              />
              <div className="flex-1">
                <Label htmlFor="negotiable" className="text-sm font-medium text-gray-700 cursor-pointer">
                  Price is negotiable
                </Label>
                <p className="text-xs text-gray-500">Allow buyers to make offers on your product</p>
              </div>
              <Icon icon="lucide:handshake" className="w-5 h-5 text-blue-600" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
