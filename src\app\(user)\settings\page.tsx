"use client";

import { useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import AccountTab from "@/components/features/settings/AccountTab";
import NotificationsTab from "@/components/features/settings/NotificationsTab";
import { useRouter } from "next/navigation";
import { BackButton } from "@/components/ui/back-button";

export default function SettingsPage() {
  const router = useRouter();
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [emailNotifications, setEmailNotifications] = useState({
    offers: true,
    watchlist: true,
    adsActivity: true,
    priceReductions: false,
  });
  const [communicationPrefs, setCommunicationPrefs] = useState({
    directMessages: true,
    onlineStatus: true,
  });
  const [profileVisibility, setProfileVisibility] = useState({
    showEmail: true,
    showPhone: true,
    showLastLogin: true,
    showTransactionHistory: false,
  });

  return (
    <div className="max-w-7xl  mx-auto p-6 space-y-6">
      <div className="mb-0">
        {/* back button */}
        <div className="mb-6">
          <BackButton onClick={() => router.back()} size="lg" />
        </div>
      </div>
      <div className="bg-white rounded-xl shadow-xl  px-8 pt-2 pb-8 ">
        <h1 className="text-4xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600 mt-2">
          Manage your account settings and preferences
        </p>

        <Tabs defaultValue="account" className="w-full">
          <TabsList className="grid w-full grid-cols-2 h-13  bg-gray-200">
            <TabsTrigger
              value="account"
              className="data-[state=active]:bg-white rounded-sm"
            >
              Account
            </TabsTrigger>
            <TabsTrigger
              value="notifications"
              className="data-[state=active]:bg-white"
            >
              Notifications
            </TabsTrigger>
          </TabsList>

          <TabsContent value="account" className="mt-6">
            <AccountTab
              showCurrentPassword={showCurrentPassword}
              setShowCurrentPassword={setShowCurrentPassword}
              showNewPassword={showNewPassword}
              setShowNewPassword={setShowNewPassword}
              showConfirmPassword={showConfirmPassword}
              setShowConfirmPassword={setShowConfirmPassword}
              profileVisibility={profileVisibility}
              setProfileVisibility={setProfileVisibility}
            />
          </TabsContent>

          <TabsContent value="notifications" className="mt-6">
            <NotificationsTab
              emailNotifications={emailNotifications}
              setEmailNotifications={setEmailNotifications}
              communicationPrefs={communicationPrefs}
              setCommunicationPrefs={setCommunicationPrefs}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
