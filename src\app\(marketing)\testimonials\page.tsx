import { Icon } from "@iconify/react";

export default function TestimonialsPage() {
  const testimonials = [
    {
      id: 1,
      name: "<PERSON><PERSON>",
      location: "Kathmandu",
      role: "Regular Customer",
      rating: 5,
      comment:
        "Nepal Marketplace has completely changed how I shop online. The quality of products is excellent and delivery is always on time. I've been using it for over a year now and never had any issues.",
      avatar: "/api/placeholder/60/60",
      purchaseInfo: "50+ orders",
    },
    {
      id: 2,
      name: "<PERSON><PERSON>",
      location: "Pokhara",
      role: "Seller",
      rating: 5,
      comment:
        "As a small business owner, this platform has helped me reach customers across Nepal. The seller support is fantastic and the commission rates are very reasonable. My sales have increased by 300%!",
      avatar: "/api/placeholder/60/60",
      purchaseInfo: "Seller since 2023",
    },
    {
      id: 3,
      name: "<PERSON>",
      location: "Chitwan",
      role: "Regular Customer",
      rating: 5,
      comment:
        "I love the variety of products available here. From electronics to traditional crafts, everything is authentic and reasonably priced. The customer service team is very helpful too.",
      avatar: "/api/placeholder/60/60",
      purchaseInfo: "30+ orders",
    },
    {
      id: 4,
      name: "<PERSON><PERSON><PERSON>",
      location: "Biratnagar",
      role: "Seller",
      rating: 4,
      comment:
        "Great platform for sellers! The verification process is thorough which builds trust with customers. Payment processing is smooth and I get my earnings on time every week.",
      avatar: "/api/placeholder/60/60",
      purchaseInfo: "Seller since 2022",
    },
    {
      id: 5,
      name: "Sunita Magar",
      location: "Lalitpur",
      role: "Regular Customer",
      rating: 5,
      comment:
        "The mobile app is so user-friendly! I can shop anytime, anywhere. The product recommendations are spot-on and I've discovered many great local brands through this platform.",
      avatar: "/api/placeholder/60/60",
      purchaseInfo: "80+ orders",
    },
    {
      id: 6,
      name: "Deepak Shrestha",
      location: "Bhaktapur",
      role: "Seller",
      rating: 5,
      comment:
        "Nepal Marketplace has been instrumental in growing my handicraft business. The platform's focus on promoting local products has helped me connect with customers who appreciate authentic Nepali crafts.",
      avatar: "/api/placeholder/60/60",
      purchaseInfo: "Seller since 2021",
    },
  ];

  const stats = [
    { number: "50,000+", label: "Happy Customers" },
    { number: "4.8/5", label: "Average Rating" },
    { number: "500+", label: "Verified Sellers" },
    { number: "99%", label: "Customer Satisfaction" },
  ];

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Icon
        key={index}
        icon="lucide:star"
        className={`h-5 w-5 ${
          index < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <div className="container-responsive spacing-responsive-lg">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            What Our Customers Say
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Read genuine reviews from our satisfied customers and successful
            sellers. Their experiences speak to our commitment to excellence.
          </p>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="bg-white rounded-xl p-6 text-center shadow-lg"
            >
              <div className="text-3xl font-bold text-teal-600 mb-2">
                {stat.number}
              </div>
              <div className="text-gray-600 font-medium">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial) => (
            <div
              key={testimonial.id}
              className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
            >
              {/* Rating */}
              <div className="flex items-center mb-4">
                {renderStars(testimonial.rating)}
              </div>

              {/* Comment */}
              <p className="text-gray-600 mb-6 leading-relaxed">
                &quot;{testimonial.comment}&quot;
              </p>

              {/* User Info */}
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                  <Icon icon="lucide:user" className="h-6 w-6 text-gray-400" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">
                    {testimonial.name}
                  </h4>
                  <p className="text-sm text-gray-500">
                    {testimonial.location}
                  </p>
                  <p className="text-sm text-teal-600 font-medium">
                    {testimonial.purchaseInfo}
                  </p>
                </div>
              </div>

              {/* Role Badge */}
              <div className="mt-4">
                <span
                  className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                    testimonial.role === "Seller"
                      ? "bg-green-100 text-green-600"
                      : "bg-blue-100 text-blue-600"
                  }`}
                >
                  {testimonial.role}
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-teal-600 to-teal-700 rounded-xl p-8 text-white text-center">
          <h2 className="text-3xl font-bold mb-4">Join Our Happy Community</h2>
          <p className="text-lg mb-6 opacity-90">
            Experience the difference that thousands of customers and sellers
            already enjoy.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white text-teal-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300">
              Start Shopping
            </button>
            <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-teal-600 transition-colors duration-300">
              Become a Seller
            </button>
          </div>
        </div>

        {/* Review Form Teaser */}
        <div className="mt-16 bg-white rounded-xl p-8 shadow-lg text-center">
          <h3 className="text-2xl font-bold text-gray-800 mb-4">
            Share Your Experience
          </h3>
          <p className="text-gray-600 mb-6">
            Have you shopped with us? We&apos;d love to hear about your
            experience!
          </p>
          <button className="bg-teal-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-teal-700 transition-colors duration-300">
            Write a Review
          </button>
        </div>
      </div>
    </div>
  );
}
