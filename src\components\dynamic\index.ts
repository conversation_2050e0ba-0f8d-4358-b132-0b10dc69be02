"use client";

import dynamic from "next/dynamic";
import React from "react";
import { LoadingSpinner, ProductCardSkeleton } from "@/components/ui";

// Heavy components that should be loaded dynamically
export const ImageCropModal = dynamic(
  () => import("@/components/features/forms/ui/ImageCropModal"),
  {
    loading: () =>
      React.createElement(LoadingSpinner, {
        message: "Loading image editor...",
      }),
    ssr: false,
  }
);

export const ProductDetailView = dynamic(
  () => import("@/components/features/product/detail/ProductDetailView"),
  {
    loading: () => React.createElement(ProductCardSkeleton),
  }
);

export const JobDetailView = dynamic(
  () => import("@/components/features/product/detail/JobDetailView"),
  {
    loading: () => React.createElement(ProductCardSkeleton),
  }
);

export const JobApplyForm = dynamic(
  () => import("@/components/features/product/detail/JobApplyForm"),
  {
    loading: () =>
      React.createElement(LoadingSpinner, {
        message: "Loading application form...",
      }),
    ssr: false,
  }
);

// Additional heavy form components
export const PostAdsForm = dynamic(
  () => import("@/components/features/forms/PostAdsForm"),
  {
    loading: () =>
      React.createElement(LoadingSpinner, { message: "Loading form..." }),
    ssr: false,
  }
);

// Enhanced image gallery for product details
export const EnhancedImageGallery = dynamic(
  () => import("@/components/features/product/detail/enhanced-image-gallery"),
  {
    loading: () =>
      React.createElement("div", {
        className: "animate-pulse bg-gray-200 aspect-[3/1] rounded-lg",
      }),
  }
);

// Banner carousel for homepage
export const BannerCarousel = dynamic(
  () => import("@/components/features/marketing/banner/BannerCarousel"),
  {
    loading: () =>
      React.createElement("div", {
        className: "animate-pulse bg-gray-200 h-64 rounded-lg",
      }),
  }
);

// Modal components for better performance
export const ContactFormModal = dynamic(
  () => import("@/components/features/profile/ContactFormModal"),
  {
    loading: () =>
      React.createElement(LoadingSpinner, {
        message: "Loading contact form...",
      }),
    ssr: false,
  }
);

export const EditJobProfileModal = dynamic(
  () =>
    import("@/components/features/profile/EditJobProfileModal").then((mod) => ({
      default: mod.EditJobProfileModal,
    })),
  {
    loading: () =>
      React.createElement(LoadingSpinner, {
        message: "Loading profile editor...",
      }),
    ssr: false,
  }
);

export const EditProfileModal = dynamic(
  () =>
    import("@/components/features/profile/EditProfileModal").then((mod) => ({
      default: mod.EditProfileModal,
    })),
  {
    loading: () =>
      React.createElement(LoadingSpinner, {
        message: "Loading profile editor...",
      }),
    ssr: false,
  }
);
