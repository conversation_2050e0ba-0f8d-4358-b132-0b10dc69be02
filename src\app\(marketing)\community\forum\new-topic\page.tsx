"use client";

import { useRouter } from "next/navigation";
import { BackButton } from "@/components/ui/back-button";

export default function NewTopicPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        {/* Back Button */}
        <div className="mb-6">
          <BackButton onClick={() => router.back()} size="lg" />
        </div>

        {/* Main Content Card */}
        <div className="bg-white rounded-xl shadow-lg px-8 py-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            Create New Topic
          </h1>
          <p className="text-gray-600">
            This page is under construction. Please check back later.
          </p>
        </div>
      </div>
    </div>
  );
}
