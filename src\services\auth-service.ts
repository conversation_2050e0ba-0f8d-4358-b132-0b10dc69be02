import {
  apiClient,
  apiRequest,
  API_ENDPOINTS,
  setAuthToken,
  setRefreshToken,
  removeAuthToken,
} from "@/lib/api";
import {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  User,
  AuthResponse,
  UpdateProfileRequest,
  UpdatePrivacySettingsRequest,
  UpdateNotificationPreferencesRequest,
} from "@/types/auth";

/**
 * Authentication Service
 * Handles all authentication-related API calls
 */
export class AuthService {
  /**
   * Login user with username/email and password
   */
  static async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await apiRequest<AuthResponse>(() =>
      apiClient.post(API_ENDPOINTS.LOGIN, credentials)
    );

    // Store tokens if login is successful
    if (response.accessToken) {
      setAuthToken(response.accessToken);
    }
    if (response.refreshToken) {
      setRefreshToken(response.refreshToken);
    }

    return response;
  }

  /**
   * Register new user with optional role selection
   */
  static async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await apiRequest<AuthResponse>(() =>
      apiClient.post(API_ENDPOINTS.REGISTER, userData)
    );

    // Store tokens if registration is successful
    if (response.accessToken) {
      setAuthToken(response.accessToken);
    }
    if (response.refreshToken) {
      setRefreshToken(response.refreshToken);
    }

    return response;
  }

  /**
   * Logout user
   */
  static async logout(): Promise<void> {
    try {
      await apiRequest<void>(() => apiClient.post(API_ENDPOINTS.LOGOUT));
    } catch (error) {
      // Even if logout fails on server, we should clear local token
      console.warn("Logout request failed:", error);
    } finally {
      // Always remove token from local storage
      removeAuthToken();
    }
  }

  /**
   * Get current user profile from auth endpoint
   */
  static async getCurrentUser(): Promise<User> {
    return await apiRequest<User>(() =>
      apiClient.get(API_ENDPOINTS.AUTH_PROFILE)
    );
  }

  /**
   * Get user profile from users endpoint
   */
  static async getUserProfile(): Promise<User> {
    return await apiRequest<User>(() =>
      apiClient.get(API_ENDPOINTS.USER_PROFILE)
    );
  }

  /**
   * Get user profile by username
   */
  static async getUserProfileByUsername(username: string): Promise<User> {
    return await apiRequest<User>(() =>
      apiClient.get(API_ENDPOINTS.USER_PROFILE_BY_USERNAME(username))
    );
  }

  /**
   * Refresh authentication token
   */
  static async refreshToken(refreshToken: string): Promise<AuthResponse> {
    const response = await apiRequest<AuthResponse>(() =>
      apiClient.post(API_ENDPOINTS.REFRESH_TOKEN, { refreshToken })
    );

    // Store new tokens
    if (response.accessToken) {
      setAuthToken(response.accessToken);
    }
    if (response.refreshToken) {
      setRefreshToken(response.refreshToken);
    }

    return response;
  }

  /**
   * Request password reset
   */
  static async forgotPassword(email: string): Promise<{ message: string }> {
    return await apiRequest<{ message: string }>(() =>
      apiClient.post(API_ENDPOINTS.FORGOT_PASSWORD, { email })
    );
  }

  /**
   * Reset password with token
   */
  static async resetPassword(
    token: string,
    password: string
  ): Promise<{ message: string }> {
    return await apiRequest<{ message: string }>(() =>
      apiClient.post(API_ENDPOINTS.RESET_PASSWORD, {
        token,
        password,
      })
    );
  }

  /**
   * Change password for authenticated user
   */
  static async changePassword(
    currentPassword: string,
    newPassword: string
  ): Promise<{ message: string }> {
    return await apiRequest<{ message: string }>(() =>
      apiClient.post(API_ENDPOINTS.CHANGE_PASSWORD, {
        currentPassword,
        newPassword,
      })
    );
  }

  /**
   * Update user profile
   */
  static async updateProfile(profileData: UpdateProfileRequest): Promise<User> {
    return await apiRequest<User>(() =>
      apiClient.put(API_ENDPOINTS.UPDATE_PROFILE, profileData)
    );
  }

  /**
   * Update privacy settings
   */
  static async updatePrivacySettings(
    settings: UpdatePrivacySettingsRequest
  ): Promise<User> {
    return await apiRequest<User>(() =>
      apiClient.put(API_ENDPOINTS.UPDATE_PRIVACY_SETTINGS, settings)
    );
  }

  /**
   * Update notification preferences
   */
  static async updateNotificationPreferences(
    preferences: UpdateNotificationPreferencesRequest
  ): Promise<User> {
    return await apiRequest<User>(() =>
      apiClient.put(API_ENDPOINTS.UPDATE_NOTIFICATION_PREFERENCES, preferences)
    );
  }

  /**
   * Follow a user
   */
  static async followUser(userId: string): Promise<{ message: string }> {
    return await apiRequest<{ message: string }>(() =>
      apiClient.post(API_ENDPOINTS.FOLLOW_USER(userId))
    );
  }

  /**
   * Unfollow a user
   */
  static async unfollowUser(userId: string): Promise<{ message: string }> {
    return await apiRequest<{ message: string }>(() =>
      apiClient.delete(API_ENDPOINTS.UNFOLLOW_USER(userId))
    );
  }

  /**
   * Get user's followers
   */
  static async getFollowers(): Promise<User[]> {
    return await apiRequest<User[]>(() =>
      apiClient.get(API_ENDPOINTS.GET_FOLLOWERS)
    );
  }

  /**
   * Get users that current user is following
   */
  static async getFollowing(): Promise<User[]> {
    return await apiRequest<User[]>(() =>
      apiClient.get(API_ENDPOINTS.GET_FOLLOWING)
    );
  }

  /**
   * Check if user is authenticated (has valid token)
   */
  static isAuthenticated(): boolean {
    if (typeof window === "undefined") return false;
    const token = localStorage.getItem("auth_token");
    return !!token;
  }

  /**
   * Validate token by making a request to get current user
   */
  static async validateToken(): Promise<boolean> {
    try {
      await this.getCurrentUser();
      return true;
    } catch (_error) {
      removeAuthToken();
      return false;
    }
  }
}

export default AuthService;
