import { apiClient, apiRequest, API_ENDPOINTS } from "@/lib/api";
import { PAGINATION_CONFIG } from "@/constants/api-constants";

/**
 * Advertisement-related interfaces matching backend DTOs
 */
export interface LocationDto {
  location?: string;              // Max 200 chars
  city?: string;                  // Max 100 chars
  state?: string;                 // Max 100 chars
  latitude?: number;              // -90 to 90
  longitude?: number;             // -180 to 180
}

export interface CreateAdvertisementDto {
  title: string;                  // 5-200 chars
  description: string;            // 10-5000 chars
  categoryId: string;             // UUID
  subcategoryId?: string;         // UUID
  price?: number;                 // Min 0, max 2 decimal places
  currency?: 'NPR' | 'USD';      // Default: NPR
  condition?: 'new' | 'used' | 'refurbished';
  location?: LocationDto;
  negotiable?: boolean;           // Default: true
  inventoryQuantity?: number;     // Min 1, default: 1
  youtubeVideoUrl?: string;       // Valid URL
  isFeatured?: boolean;           // Default: false
  isUrgent?: boolean;             // Default: false
}

export interface UpdateAdvertisementDto {
  title?: string;
  description?: string;
  categoryId?: string;
  subcategoryId?: string;
  price?: number;
  currency?: 'NPR' | 'USD';
  condition?: 'new' | 'used' | 'refurbished';
  location?: LocationDto;
  negotiable?: boolean;
  inventoryQuantity?: number;
  youtubeVideoUrl?: string;
  isFeatured?: boolean;
  isUrgent?: boolean;
}

export interface QueryAdvertisementDto {
  page?: number;                  // Default: 1
  limit?: number;                 // Default: 20, max: 100
  search?: string;
  categoryId?: string;
  subcategoryId?: string;
  minPrice?: number;
  maxPrice?: number;
  condition?: 'new' | 'used' | 'refurbished';
  location?: string;
  city?: string;
  state?: string;
  sortBy?: 'createdAt' | 'price' | 'title' | 'views';
  sortOrder?: 'ASC' | 'DESC';
  status?: 'draft' | 'pending_approval' | 'active' | 'sold' | 'expired' | 'rejected' | 'suspended';
  userId?: string;
  isFeatured?: boolean;
  isUrgent?: boolean;
}

export interface AdvertisementImageDto {
  id: string;
  url: string;
  thumbnailUrl?: string;
  altText?: string;
  sortOrder: number;
  isPrimary: boolean;
  createdAt: string;
}

export interface AdvertisementResponseDto {
  id: string;
  title: string;
  description: string;
  slug: string;
  price?: number;
  currency: 'NPR' | 'USD';
  condition?: 'new' | 'used' | 'refurbished';
  negotiable: boolean;
  inventoryQuantity: number;
  status: 'draft' | 'pending_approval' | 'active' | 'sold' | 'expired' | 'rejected' | 'suspended';
  isFeatured: boolean;
  isUrgent: boolean;
  youtubeVideoUrl?: string;
  location?: LocationDto;
  images: AdvertisementImageDto[];
  category: any; // CategoryResponseDto
  subcategory?: any; // SubcategoryResponseDto
  user: any; // UserProfileResponseDto
  viewsCount: number;
  favoritesCount: number;
  isFavorited?: boolean;
  createdAt: string;
  updatedAt: string;
  approvedAt?: string;
  rejectedAt?: string;
  suspendedAt?: string;
  soldAt?: string;
  expiresAt?: string;
}

export interface PaginatedAdvertisementResponseDto {
  data: AdvertisementResponseDto[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ReportAdvertisementDto {
  reason: string;
  description?: string;
}

export interface ReportResponseDto {
  id: string;
  reason: string;
  description?: string;
  status: string;
  createdAt: string;
}

/**
 * Advertisements Service
 * Handles all advertisement-related API calls
 */
export class AdvertisementsService {
  /**
   * Get all advertisements with filtering and pagination
   */
  static async getAdvertisements(
    query: QueryAdvertisementDto = {}
  ): Promise<PaginatedAdvertisementResponseDto> {
    const params = {
      page: PAGINATION_CONFIG.DEFAULT_PAGE,
      limit: PAGINATION_CONFIG.DEFAULT_LIMIT,
      ...query,
    };

    return await apiRequest<PaginatedAdvertisementResponseDto>(() =>
      apiClient.get(API_ENDPOINTS.ADVERTISEMENTS, { params })
    );
  }

  /**
   * Get advertisement by ID
   */
  static async getAdvertisementById(id: string): Promise<AdvertisementResponseDto> {
    return await apiRequest<AdvertisementResponseDto>(() =>
      apiClient.get(API_ENDPOINTS.ADVERTISEMENT_BY_ID(id))
    );
  }

  /**
   * Create new advertisement
   */
  static async createAdvertisement(
    data: CreateAdvertisementDto
  ): Promise<AdvertisementResponseDto> {
    return await apiRequest<AdvertisementResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.ADVERTISEMENTS, data)
    );
  }

  /**
   * Update advertisement
   */
  static async updateAdvertisement(
    id: string,
    data: UpdateAdvertisementDto
  ): Promise<AdvertisementResponseDto> {
    return await apiRequest<AdvertisementResponseDto>(() =>
      apiClient.patch(API_ENDPOINTS.ADVERTISEMENT_BY_ID(id), data)
    );
  }

  /**
   * Delete advertisement
   */
  static async deleteAdvertisement(id: string): Promise<void> {
    return await apiRequest<void>(() =>
      apiClient.delete(API_ENDPOINTS.ADVERTISEMENT_BY_ID(id))
    );
  }

  /**
   * Get current user's advertisements
   */
  static async getMyAdvertisements(
    query: QueryAdvertisementDto = {}
  ): Promise<PaginatedAdvertisementResponseDto> {
    const params = {
      page: PAGINATION_CONFIG.DEFAULT_PAGE,
      limit: PAGINATION_CONFIG.DEFAULT_LIMIT,
      ...query,
    };

    return await apiRequest<PaginatedAdvertisementResponseDto>(() =>
      apiClient.get(API_ENDPOINTS.MY_ADVERTISEMENTS, { params })
    );
  }

  /**
   * Get user's favorite advertisements
   */
  static async getFavoriteAdvertisements(
    query: QueryAdvertisementDto = {}
  ): Promise<PaginatedAdvertisementResponseDto> {
    const params = {
      page: PAGINATION_CONFIG.DEFAULT_PAGE,
      limit: PAGINATION_CONFIG.DEFAULT_LIMIT,
      ...query,
    };

    return await apiRequest<PaginatedAdvertisementResponseDto>(() =>
      apiClient.get(API_ENDPOINTS.FAVORITE_ADVERTISEMENTS, { params })
    );
  }

  /**
   * Add advertisement to favorites
   */
  static async addToFavorites(id: string): Promise<{ message: string }> {
    return await apiRequest<{ message: string }>(() =>
      apiClient.post(API_ENDPOINTS.ADD_TO_FAVORITES(id))
    );
  }

  /**
   * Remove advertisement from favorites
   */
  static async removeFromFavorites(id: string): Promise<{ message: string }> {
    return await apiRequest<{ message: string }>(() =>
      apiClient.delete(API_ENDPOINTS.REMOVE_FROM_FAVORITES(id))
    );
  }

  /**
   * Upload images for advertisement
   */
  static async uploadImages(id: string, files: File[]): Promise<any> {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('images', file);
    });

    return await apiRequest<any>(() =>
      apiClient.post(API_ENDPOINTS.UPLOAD_AD_IMAGES(id), formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
    );
  }

  /**
   * Submit advertisement for approval
   */
  static async submitForApproval(id: string): Promise<AdvertisementResponseDto> {
    return await apiRequest<AdvertisementResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.SUBMIT_FOR_APPROVAL(id))
    );
  }

  /**
   * Mark advertisement as sold
   */
  static async markAsSold(id: string): Promise<AdvertisementResponseDto> {
    return await apiRequest<AdvertisementResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.MARK_AS_SOLD(id))
    );
  }

  /**
   * Get advertisement statistics
   */
  static async getAdvertisementStats(id: string): Promise<any> {
    return await apiRequest<any>(() =>
      apiClient.get(API_ENDPOINTS.AD_STATISTICS(id))
    );
  }

  /**
   * Report advertisement
   */
  static async reportAdvertisement(
    id: string,
    data: ReportAdvertisementDto
  ): Promise<ReportResponseDto> {
    return await apiRequest<ReportResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.REPORT_ADVERTISEMENT(id), data)
    );
  }

  /**
   * Get featured advertisements
   */
  static async getFeaturedAdvertisements(limit?: number): Promise<AdvertisementResponseDto[]> {
    const params = limit ? { limit } : {};
    return await apiRequest<AdvertisementResponseDto[]>(() =>
      apiClient.get(API_ENDPOINTS.FEATURED_ADVERTISEMENTS, { params })
    );
  }

  /**
   * Get popular advertisements
   */
  static async getPopularAdvertisements(limit?: number): Promise<AdvertisementResponseDto[]> {
    const params = limit ? { limit } : {};
    return await apiRequest<AdvertisementResponseDto[]>(() =>
      apiClient.get(API_ENDPOINTS.POPULAR_ADVERTISEMENTS, { params })
    );
  }

  /**
   * Get recent advertisements
   */
  static async getRecentAdvertisements(limit?: number): Promise<AdvertisementResponseDto[]> {
    const params = limit ? { limit } : {};
    return await apiRequest<AdvertisementResponseDto[]>(() =>
      apiClient.get(API_ENDPOINTS.RECENT_ADVERTISEMENTS, { params })
    );
  }

  /**
   * Get similar advertisements
   */
  static async getSimilarAdvertisements(
    id: string,
    limit?: number
  ): Promise<AdvertisementResponseDto[]> {
    const params = limit ? { limit } : {};
    return await apiRequest<AdvertisementResponseDto[]>(() =>
      apiClient.get(API_ENDPOINTS.SIMILAR_ADVERTISEMENTS(id), { params })
    );
  }

  /**
   * Search advertisements by location
   */
  static async searchByLocation(
    latitude: number,
    longitude: number,
    radius: number,
    query: QueryAdvertisementDto = {}
  ): Promise<PaginatedAdvertisementResponseDto> {
    const params = {
      latitude,
      longitude,
      radius,
      page: PAGINATION_CONFIG.DEFAULT_PAGE,
      limit: PAGINATION_CONFIG.DEFAULT_LIMIT,
      ...query,
    };

    return await apiRequest<PaginatedAdvertisementResponseDto>(() =>
      apiClient.get(API_ENDPOINTS.LOCATION_SEARCH, { params })
    );
  }
}

export default AdvertisementsService;
