"use client";

import { useState } from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Product } from "@/types/ecommerce";
import {
  useGetProductQuestionsQuery,
  useCreateQuestionMutation,
  useMarkQuestionHelpfulMutation
} from "@/store/api/productDetailsApi";
import type { ProductQuestion } from "@/types/product-details";

interface ProductQAProps {
  product: Product;
}

export function ProductQA({ product }: ProductQAProps) {
  const [newQuestion, setNewQuestion] = useState("");
  const [showQuestionForm, setShowQuestionForm] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'helpful'>('newest');

  // API hooks
  const {
    data: questionsData,
    isLoading,
    error,
    refetch
  } = useGetProductQuestionsQuery({
    productId: product.id,
    page: currentPage,
    limit: 10,
    sortBy,
    status: 'all'
  });

  const [createQuestion, { isLoading: isSubmitting }] = useCreateQuestionMutation();
  const [markHelpful, { isLoading: isMarkingHelpful }] = useMarkQuestionHelpfulMutation();

  const questions = questionsData?.data || [];
  const INITIAL_QUESTIONS_COUNT = 3;
  const [showAllQuestions, setShowAllQuestions] = useState(false);
  const displayedQuestions = showAllQuestions
    ? questions
    : questions.slice(0, INITIAL_QUESTIONS_COUNT);

  const handleSubmitQuestion = async () => {
    if (!newQuestion.trim()) return;

    try {
      await createQuestion({
        productId: product.id,
        question: newQuestion.trim()
      }).unwrap();

      setNewQuestion("");
      setShowQuestionForm(false);
      refetch(); // Refresh the questions list
    } catch (error) {
      console.error('Failed to submit question:', error);
    }
  };

  const handleHelpful = async (questionId: string) => {
    try {
      await markHelpful({ questionId, helpful: true }).unwrap();
    } catch (error) {
      console.error('Failed to mark question as helpful:', error);
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;

    return date.toLocaleDateString();
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="bg-white rounded-lg p-6 shadow-sm h-[813px] flex flex-col">
        <div className="flex items-center justify-between mb-6">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="space-y-6 flex-1">
          {[1, 2, 3].map((i) => (
            <div key={i} className="space-y-4">
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-20 w-full" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-white rounded-lg p-6 shadow-sm h-[813px] flex flex-col">
        <Alert>
          <Icon icon="lucide:alert-circle" className="h-4 w-4" />
          <AlertDescription>
            Failed to load questions. Please try again later.
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              className="ml-2"
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm h-[813px] flex flex-col">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-2xl font-semibold text-gray-900 flex items-center gap-2">
          <Icon
            icon="mdi:message-text-outline"
            className="h-6 w-6 text-blue-600"
          />
          Questions & Answers ({questions.length})
        </h3>
        <div className="flex items-center gap-4">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as typeof sortBy)}
            className="border border-gray-300 rounded-md px-3 py-1 text-sm"
          >
            <option value="newest">Newest</option>
            <option value="oldest">Oldest</option>
            <option value="helpful">Most Helpful</option>
          </select>
          <Button
            onClick={() => setShowQuestionForm(!showQuestionForm)}
            variant="outline"
            size="sm"
          >
            Ask a Question
          </Button>
        </div>
      </div>

      {/* Question Form */}
      {showQuestionForm && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="text-lg font-medium text-gray-900 mb-3">
            Ask a Question
          </h4>
          <Textarea
            value={newQuestion}
            onChange={(e) => setNewQuestion(e.target.value)}
            placeholder="Ask anything about this product..."
            className="mb-3"
            rows={3}
          />
          <div className="flex gap-2 justify-end">
            <Button
              variant="outline"
              onClick={() => {
                setShowQuestionForm(false);
                setNewQuestion("");
              }}
              size="sm"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmitQuestion}
              disabled={!newQuestion.trim() || isSubmitting}
              size="sm"
              className="bg-teal-600 hover:bg-teal-700"
            >
              {isSubmitting ? (
                "Submitting..."
              ) : (
                <>
                  <Icon icon="mdi:send" className="h-4 w-4 mr-1" />
                  Submit Question
                </>
              )}
            </Button>
          </div>
        </div>
      )}

      {/* Questions List */}
      <div className="flex-1 overflow-hidden flex flex-col">
        <div className="flex-1 overflow-y-auto space-y-6 pr-2">
          {questions.length === 0 ? (
            <div className="text-center py-8">
              <Icon
                icon="mdi:message-text-outline"
                className="h-12 w-12 text-gray-300 mx-auto mb-4"
              />
              <h4 className="text-xl font-medium text-gray-900 mb-2">
                No questions yet
              </h4>
              <p className="text-gray-600 text-base">
                Be the first to ask a question about this product!
              </p>
            </div>
          ) : (
            displayedQuestions.map((qa) => (
              <div
                key={qa.id}
                className="border-b border-gray-100 pb-6 last:border-b-0"
              >
                {/* Question */}
                <div className="mb-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      {qa.askedByAvatar ? (
                        <img
                          src={qa.askedByAvatar}
                          alt={qa.askedBy}
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        <Icon
                          icon="mdi:account"
                          className="h-4 w-4 text-blue-600"
                        />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-gray-900 text-base">
                          {qa.askedBy}
                        </span>
                        <span className="text-base text-gray-500 flex items-center gap-1">
                          <Icon icon="mdi:clock-outline" className="h-4 w-4" />
                          {formatTimeAgo(qa.askedAt)}
                        </span>
                      </div>
                      <p className="text-gray-700 text-base">{qa.question}</p>
                    </div>
                  </div>
                </div>

                {/* Answer */}
                {qa.answer ? (
                  <div className="ml-11 bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-medium text-gray-900 text-base">
                        {qa.answeredBy}
                      </span>
                      {qa.isSellerAnswer && (
                        <Badge
                          variant="secondary"
                          className="bg-green-100 text-green-800 text-sm"
                        >
                          Seller
                        </Badge>
                      )}
                      <span className="text-base text-gray-500 flex items-center gap-1">
                        <Icon icon="mdi:clock-outline" className="h-4 w-4" />
                        {qa.answeredAt && formatTimeAgo(qa.answeredAt)}
                      </span>
                    </div>
                    <p className="text-gray-700 mb-3 text-base">{qa.answer}</p>

                    {/* Helpful Button */}
                    <button
                      onClick={() => handleHelpful(qa.id)}
                      disabled={isMarkingHelpful}
                      className="flex items-center gap-1 text-base text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50"
                    >
                      <Icon icon="mdi:thumb-up-outline" className="h-4 w-4" />
                      <span>Helpful ({qa.helpfulCount})</span>
                    </button>
                  </div>
                ) : (
                  <div className="ml-11 text-base text-gray-500 italic">
                    Waiting for seller&apos;s response...
                  </div>
                )}
              </div>
            ))
          )}
        </div>

        {/* Show More Button */}
        {questions.length > INITIAL_QUESTIONS_COUNT && (
          <div className="mt-4 text-center border-t pt-4">
            <Button
              onClick={() => setShowAllQuestions(!showAllQuestions)}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Icon
                icon="mdi:chevron-down"
                className={`h-4 w-4 transition-transform ${
                  showAllQuestions ? "rotate-180" : ""
                }`}
              />
              {showAllQuestions
                ? `Show Less`
                : `Show ${
                    questions.length - INITIAL_QUESTIONS_COUNT
                  } More Questions`}
            </Button>
          </div>
        )}
      </div>

      {/* Guidelines */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg flex-shrink-0">
        <h4 className="text-lg font-medium text-blue-900 mb-2">
          Question Guidelines
        </h4>
        <ul className="text-base text-blue-800 space-y-1">
          <li>• Ask specific questions about the product</li>
          <li>• Be respectful and courteous</li>
          <li>• Avoid sharing personal contact information</li>
          <li>• Check existing questions before asking</li>
        </ul>
      </div>
    </div>
  );
}
