"use client";

import React, { useMemo } from "react";
import { useRouter } from "next/navigation";
import { useEcommerce } from "@/store/compatibility";
import { useGetAdvertisementsQuery } from "@/store/api/advertisementApi";
import { convertAdvertisementsToProducts } from "@/utils/slug-utils";
import { ProductCardDetailed } from "../product";
import { SimpleBanner } from "../marketing/banner";
import { ProductCardSkeleton } from "../../ui";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import type { Category, Product } from "@/types/ecommerce";
import type { QueryAdvertisementParams } from "@/types/advertisement";

interface CategoryProductSectionProps {
  category: Category;
  maxProducts?: number;
}

function CategoryProductSection({
  category,
  maxProducts = 20,
}: CategoryProductSectionProps) {
  const router = useRouter();

  // Build query parameters for this category
  const queryParams = useMemo((): QueryAdvertisementParams => {
    return {
      page: 1,
      limit: maxProducts,
      categoryId: category.id,
      sortBy: "createdAt",
      sortOrder: "DESC",
      // Only show active advertisements
      status: "active" as any,
    };
  }, [category.id, maxProducts]);

  // Fetch advertisements for this category using RTK Query
  const {
    data: advertisementsResponse,
    isLoading,
    error,
  } = useGetAdvertisementsQuery(queryParams);

  // Convert advertisements to products
  const categoryProducts = useMemo(() => {
    if (!advertisementsResponse?.data) return [];
    return convertAdvertisementsToProducts(advertisementsResponse.data);
  }, [advertisementsResponse]);

  // Debug logging
  console.log(`CategoryProductSection [${category.name}]:`, {
    categoryId: category.id,
    queryParams,
    advertisementsCount: advertisementsResponse?.data?.length || 0,
    productsCount: categoryProducts.length,
    isLoading,
    error,
  });

  // Show loading state
  if (isLoading) {
    return (
      <div className="w-full py-12">
        <div className="w-full mx-auto">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <h2 className="text-2xl font-bold text-gray-900">
                {category.name}
              </h2>
            </div>
          </div>
          <div className="relative px-12">
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {Array.from({ length: 5 }).map((_, index) => (
                <ProductCardSkeleton key={index} />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    console.error(
      `Error loading products for category ${category.name}:`,
      error
    );
    return (
      <div className="w-full py-12">
        <div className="w-full mx-auto">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <h2 className="text-2xl font-bold text-gray-900">
                {category.name}
              </h2>
            </div>
          </div>
          <div className="text-center py-8 text-gray-500">
            Failed to load products for this category
          </div>
        </div>
      </div>
    );
  }

  // Don't render if no products
  if (categoryProducts.length === 0) {
    return null;
  }

  return (
    <div className="w-full py-12">
      <div className="w-full mx-auto">
        {/* Category Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-3">
            <h2 className="text-2xl font-bold text-gray-900">
              {category.name}
            </h2>
          </div>
        </div>

        {/* Products Carousel using shadcn carousel */}
        <div className="relative px-12">
          <Carousel
            opts={{
              align: "start",
              loop: false,
            }}
            className="w-full"
          >
            <CarouselContent className="-ml-2 md:-ml-4">
              {categoryProducts.map((product: Product) => (
                <CarouselItem
                  key={product.id}
                  className="pl-2 md:pl-4 basis-1/2 sm:basis-1/3 lg:basis-1/4 xl:basis-1/5"
                >
                  <ProductCardDetailed
                    product={product}
                    onViewDetails={() => {
                      router.push(`/product/${product.slug}`);
                    }}
                  />
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="left-0 bg-white hover:bg-white border-white/50 shadow-lg hover:shadow-xl" />
            <CarouselNext className="right-0 bg-white hover:bg-white border-white/50 shadow-lg hover:shadow-xl" />
          </Carousel>
        </div>
      </div>
    </div>
  );
}

interface CategoryProductSectionsProps {
  categoryIds?: string[];
}

export default function CategoryProductSections({
  categoryIds,
}: CategoryProductSectionsProps) {
  const { state } = useEcommerce();
  const { categories } = state.category;

  // If no specific categoryIds provided, use all available categories (up to 5)
  const selectedCategories = categoryIds
    ? (categories || []).filter((category: Category) =>
        categoryIds.includes(category.id)
      )
    : (categories || []).slice(0, 5); // Show first 5 categories if no specific IDs provided

  // Debug logging
  console.log("CategoryProductSections Debug:", {
    categoryIds: categoryIds || "all categories (first 5)",
    categoriesFromRedux: categories?.length || 0,
    selectedCategoriesCount: selectedCategories.length,
    selectedCategoryIds: selectedCategories.map((c: Category) => c.id),
    selectedCategoryNames: selectedCategories.map((c: Category) => c.name),
  });

  return (
    <div className="w-full space-y-0">
      {selectedCategories.map((category: Category, index: number) => (
        <React.Fragment key={category.id}>
          <div className={index > 0 ? " " : ""}>
            <CategoryProductSection category={category} />
          </div>

          {/* Insert SimpleBanner after every 2 category sections */}
          {(index + 1) % 2 === 0 && index < selectedCategories.length - 1 && (
            <div className="mb-6 mt-6">
              <SimpleBanner
                title="Special Promotion"
                description="Don't miss out on our exclusive deals and limited-time offers"
                buttonText="View Deals"
                buttonLink="/deals"
                backgroundColor="from-[#356267] to-[#478085]"
                showCloseButton={false}
              />
            </div>
          )}
        </React.Fragment>
      ))}
    </div>
  );
}
