/**
 * API Constants for consistent frontend-backend communication
 * These constants ensure type safety and prevent API parameter mismatches
 */

// API Configuration Constants matching SastoBazar backend
export const API_CONFIG = {
  BASE_URL:
    process.env.NEXT_PUBLIC_API_BASE_URL ||
    "https://sasto-api.webstudiomatrix.com/api/v1",
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// Authentication Configuration
export const AUTH_CONFIG = {
  TOKEN_KEY: "auth_token",
  REFRESH_TOKEN_KEY: "refresh_token",
  TOKEN_EXPIRY_BUFFER: 300, // 5 minutes before expiry
} as const;

// File Upload Configuration
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_FILES_PER_UPLOAD: 8,
  ALLOWED_IMAGE_TYPES: ["image/jpeg", "image/png", "image/webp", "image/gif"],
  MAX_IMAGE_DIMENSIONS: { width: 4096, height: 4096 },
} as const;

// Pagination Configuration
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100,
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// User Status Values
export const USER_STATUS = {
  ACTIVE: "active",
  SUSPENDED: "suspended",
  BANNED: "banned",
  PENDING_VERIFICATION: "pending_verification",
} as const;

// User Roles
export const USER_ROLES = {
  USER: "USER",
  VENDOR: "VENDOR",
  ADMIN: "ADMIN",
  SUPER_ADMIN: "SUPER_ADMIN",
} as const;

// Error Messages
export const API_ERROR_MESSAGES = {
  NETWORK_ERROR: "Network error. Please check your connection.",
  TIMEOUT_ERROR: "Request timeout. Please try again.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  FORBIDDEN: "Access denied.",
  NOT_FOUND: "Resource not found.",
  VALIDATION_ERROR: "Please check your input and try again.",
  SERVER_ERROR: "Server error. Please try again later.",
  UNKNOWN_ERROR: "An unexpected error occurred.",
  TOKEN_EXPIRED: "Your session has expired. Please login again.",
  RATE_LIMITED: "Too many requests. Please try again later.",
} as const;

// Validation Constants
export const VALIDATION_LIMITS = {
  USERNAME: { MIN: 3, MAX: 50 },
  PASSWORD: { MIN: 8 },
  TITLE: { MIN: 5, MAX: 200 },
  DESCRIPTION: { MIN: 10, MAX: 5000 },
  LOCATION: { MAX: 200 },
  CITY_STATE: { MAX: 100 },
  NAME: { MAX: 100 },
  PRICE: { MIN: 0, DECIMAL_PLACES: 2 },
  QUANTITY: { MIN: 1 },
  LATITUDE: { MIN: -90, MAX: 90 },
  LONGITUDE: { MIN: -180, MAX: 180 },
} as const;

// Advertisement Status Constants
export const AD_STATUS = {
  DRAFT: "draft",
  PENDING_APPROVAL: "pending_approval",
  ACTIVE: "active",
  SOLD: "sold",
  EXPIRED: "expired",
  REJECTED: "rejected",
  SUSPENDED: "suspended",
} as const;

// Type for advertisement status
export type AdStatus = (typeof AD_STATUS)[keyof typeof AD_STATUS];

// Condition Type Constants
export const CONDITION_TYPE = {
  NEW: "new",
  USED: "used",
  REFURBISHED: "refurbished",
} as const;

// Type for condition
export type ConditionType =
  (typeof CONDITION_TYPE)[keyof typeof CONDITION_TYPE];

// Currency Type Constants (matching backend)
export const CURRENCY_TYPE = {
  NPR: "NPR",
  USD: "USD",
} as const;

// Type for currency
export type CurrencyType = (typeof CURRENCY_TYPE)[keyof typeof CURRENCY_TYPE];

// Sort Order Constants
export const SORT_ORDER = {
  ASC: "ASC",
  DESC: "DESC",
} as const;

// Type for sort order
export type SortOrder = (typeof SORT_ORDER)[keyof typeof SORT_ORDER];

// Sort By Constants for Advertisements
export const SORT_BY = {
  CREATED_AT: "createdAt",
  UPDATED_AT: "updatedAt",
  PRICE: "price",
  TITLE: "title",
  VIEWS: "views",
} as const;

// Type for sort by
export type SortBy = (typeof SORT_BY)[keyof typeof SORT_BY];

// API Parameter Validation Helpers
export const isValidAdStatus = (status: string): status is AdStatus => {
  return Object.values(AD_STATUS).includes(status as AdStatus);
};

export const isValidConditionType = (
  condition: string
): condition is ConditionType => {
  return Object.values(CONDITION_TYPE).includes(condition as ConditionType);
};

export const isValidCurrencyType = (
  currency: string
): currency is CurrencyType => {
  return Object.values(CURRENCY_TYPE).includes(currency as CurrencyType);
};

// Default API Parameters
export const DEFAULT_API_PARAMS = {
  page: 1,
  limit: 12,
  status: AD_STATUS.ACTIVE,
  sortBy: SORT_BY.CREATED_AT,
  sortOrder: SORT_ORDER.DESC,
} as const;

// Helper function to build query parameters safely
export const buildQueryParams = (params: Record<string, any>) => {
  const cleanParams: Record<string, any> = {};

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      // Validate status parameter
      if (key === "status" && typeof value === "string") {
        if (isValidAdStatus(value)) {
          cleanParams[key] = value;
        } else {
          console.warn(
            `Invalid status value: ${value}. Using default: ${AD_STATUS.ACTIVE}`
          );
          cleanParams[key] = AD_STATUS.ACTIVE;
        }
      }
      // Validate condition parameter
      else if (key === "condition" && typeof value === "string") {
        if (isValidConditionType(value)) {
          cleanParams[key] = value;
        } else {
          console.warn(`Invalid condition value: ${value}`);
        }
      }
      // Validate currency parameter
      else if (key === "currency" && typeof value === "string") {
        if (isValidCurrencyType(value)) {
          cleanParams[key] = value;
        } else {
          console.warn(`Invalid currency value: ${value}`);
        }
      } else {
        cleanParams[key] = value;
      }
    }
  });

  return cleanParams;
};
