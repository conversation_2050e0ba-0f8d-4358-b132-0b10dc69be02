"use client";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { Category } from "@/types/ecommerce";

interface CategoryDropdownProps {
  categories: Category[];
  selectedCategory: string | null;
  onCategorySelect: (categoryId: string | null) => void;
}

export default function CategoryDropdown({
  categories,
  selectedCategory,
  onCategorySelect,
}: CategoryDropdownProps) {
  const selectedCategoryData = selectedCategory
    ? categories.find((cat) => cat.id === selectedCategory)
    : null;

  return (
    <Select
      value={selectedCategory || "all"}
      onValueChange={(value) =>
        onCategorySelect(value === "all" ? null : value)
      }
    >
      <SelectTrigger className="w-full bg-white">
        <div className="flex items-center gap-2">
          {selectedCategoryData && (
            <span className="text-lg">{selectedCategoryData.icon}</span>
          )}
          <SelectValue placeholder="All Categories" />
        </div>
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">
          <div className="flex items-center gap-2">
            <span>All Categories</span>
          </div>
        </SelectItem>
        {categories.map((category) => (
          <SelectItem key={category.id} value={category.id}>
            <div className="flex items-center gap-2">
              <span className="text-lg">{category.icon}</span>
              <span>{category.name}</span>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
