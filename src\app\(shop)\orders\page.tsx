"use client";
import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Image from "next/image";
import { Icon } from "@iconify/react";
import { RootState } from "@/store";
import { fetchOrders, cancelOrder } from "@/store/slices/orderSlice";
import { OrderStatus, OrderFilterDto } from "@/types/orders";
import { OrderService } from "@/services/order-service";
import { toast } from "sonner";

export default function OrdersPage() {
  const router = useRouter();
  const dispatch = useDispatch();

  // Redux state
  const { orders, ordersLoading, ordersError, ordersPagination } = useSelector(
    (state: RootState) => state.order
  );

  // Local state
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<OrderStatus | "all">("all");
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // Load orders on component mount
  useEffect(() => {
    loadOrders();
  }, [statusFilter, sortBy, sortOrder]);

  const loadOrders = () => {
    const filters: OrderFilterDto = {
      page: 1,
      limit: 20,
      sortBy,
      sortOrder,
    };

    if (statusFilter !== "all") {
      filters.status = statusFilter;
    }

    dispatch(fetchOrders(filters) as any);
  };

  const handleCancelOrder = async (orderId: string) => {
    try {
      await dispatch(cancelOrder(orderId) as any).unwrap();
      toast.success("Order cancelled successfully");
    } catch (error: any) {
      toast.error(error.message || "Failed to cancel order");
    }
  };

  const handleLoadMore = () => {
    if (ordersPagination.hasMore && !ordersLoading) {
      const filters: OrderFilterDto = {
        page: ordersPagination.page + 1,
        limit: ordersPagination.limit,
        sortBy,
        sortOrder,
      };

      if (statusFilter !== "all") {
        filters.status = statusFilter;
      }

      dispatch(fetchOrders(filters) as any);
    }
  };

  const getStatusColor = (status: OrderStatus) => {
    const colors = {
      pending: "bg-yellow-100 text-yellow-800",
      confirmed: "bg-blue-100 text-blue-800",
      processing: "bg-purple-100 text-purple-800",
      shipped: "bg-indigo-100 text-indigo-800",
      delivered: "bg-green-100 text-green-800",
      cancelled: "bg-red-100 text-red-800",
      refunded: "bg-gray-100 text-gray-800",
    };
    return colors[status] || "bg-gray-100 text-gray-800";
  };

  const getPaymentStatusColor = (status: string) => {
    const colors = {
      pending: "bg-yellow-100 text-yellow-800",
      processing: "bg-blue-100 text-blue-800",
      completed: "bg-green-100 text-green-800",
      failed: "bg-red-100 text-red-800",
      refunded: "bg-gray-100 text-gray-800",
    };
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  const filteredOrders = orders.filter(
    (order: any) =>
      order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.items.some((item: any) =>
        item.productName.toLowerCase().includes(searchQuery.toLowerCase())
      )
  );

  const ordersByStatus = {
    all: filteredOrders,
    pending: filteredOrders.filter((order: any) =>
      ["pending", "confirmed"].includes(order.status)
    ),
    processing: filteredOrders.filter((order: any) =>
      ["processing", "shipped"].includes(order.status)
    ),
    completed: filteredOrders.filter(
      (order: any) => order.status === "delivered"
    ),
    cancelled: filteredOrders.filter((order: any) =>
      ["cancelled", "refunded"].includes(order.status)
    ),
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">My Orders</h1>
          <p className="text-gray-600">Track and manage your orders</p>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search orders by order number or product name..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="flex gap-2">
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="createdAt">Date</SelectItem>
                    <SelectItem value="totalAmount">Amount</SelectItem>
                    <SelectItem value="status">Status</SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={sortOrder}
                  onValueChange={(value: "asc" | "desc") => setSortOrder(value)}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Order" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="desc">Newest</SelectItem>
                    <SelectItem value="asc">Oldest</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error Alert */}
        {ordersError && (
          <Alert variant="destructive" className="mb-6">
            <Icon icon="mdi:alert-circle" className="h-4 w-4" />
            <AlertDescription>{ordersError}</AlertDescription>
          </Alert>
        )}

        {/* Order Tabs */}
        <Tabs
          value={statusFilter}
          onValueChange={(value) => setStatusFilter(value as any)}
        >
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="all">
              All ({ordersByStatus.all.length})
            </TabsTrigger>
            <TabsTrigger value="pending">
              Pending ({ordersByStatus.pending.length})
            </TabsTrigger>
            <TabsTrigger value="processing">
              Processing ({ordersByStatus.processing.length})
            </TabsTrigger>
            <TabsTrigger value="completed">
              Completed ({ordersByStatus.completed.length})
            </TabsTrigger>
            <TabsTrigger value="cancelled">
              Cancelled ({ordersByStatus.cancelled.length})
            </TabsTrigger>
          </TabsList>

          {Object.entries(ordersByStatus).map(([status, statusOrders]) => (
            <TabsContent key={status} value={status} className="mt-6">
              {ordersLoading && orders.length === 0 ? (
                <div className="flex justify-center py-12">
                  <Icon
                    icon="mdi:loading"
                    className="animate-spin text-3xl text-gray-400"
                  />
                </div>
              ) : statusOrders.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-12">
                    <Icon
                      icon="mdi:package-variant"
                      className="text-6xl text-gray-300 mx-auto mb-4"
                    />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No {status === "all" ? "" : status} orders found
                    </h3>
                    <p className="text-gray-500 mb-6">
                      {status === "all"
                        ? "You haven't placed any orders yet."
                        : `You don't have any ${status} orders.`}
                    </p>
                    <Button onClick={() => router.push("/")}>
                      Start Shopping
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {statusOrders.map((order: any) => (
                    <Card
                      key={order.id}
                      className="hover:shadow-md transition-shadow"
                    >
                      <CardContent className="p-6">
                        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                          {/* Order Info */}
                          <div className="flex-1">
                            <div className="flex items-center gap-4 mb-3">
                              <h3 className="font-semibold text-lg">
                                Order #{order.orderNumber}
                              </h3>
                              <Badge className={getStatusColor(order.status)}>
                                {OrderService.formatOrderStatus(order.status)}
                              </Badge>
                              <Badge
                                className={getPaymentStatusColor(
                                  order.paymentStatus
                                )}
                              >
                                {OrderService.formatPaymentStatus(
                                  order.paymentStatus
                                )}
                              </Badge>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                              <div>
                                <span className="font-medium">Date:</span>{" "}
                                {new Date(order.createdAt).toLocaleDateString()}
                              </div>
                              <div>
                                <span className="font-medium">Total:</span>{" "}
                                {order.currency}{" "}
                                {order.totalAmount.toLocaleString()}
                              </div>
                              <div>
                                <span className="font-medium">Items:</span>{" "}
                                {order.items.length} item
                                {order.items.length > 1 ? "s" : ""}
                              </div>
                            </div>

                            {/* Order Items Preview */}
                            <div className="mt-4">
                              <div className="flex gap-2 overflow-x-auto">
                                {order.items.slice(0, 3).map((item: any) => (
                                  <div
                                    key={item.id}
                                    className="flex items-center gap-2 bg-gray-50 rounded-lg p-2 min-w-0"
                                  >
                                    {item.productImage && (
                                      <div className="w-8 h-8 bg-gray-200 rounded flex-shrink-0">
                                        <Image
                                          src={item.productImage}
                                          alt={item.productName}
                                          width={32}
                                          height={32}
                                          className="w-full h-full object-cover rounded"
                                        />
                                      </div>
                                    )}
                                    <span className="text-xs text-gray-600 truncate">
                                      {item.productName}
                                    </span>
                                  </div>
                                ))}
                                {order.items.length > 3 && (
                                  <div className="flex items-center justify-center bg-gray-100 rounded-lg p-2 min-w-0">
                                    <span className="text-xs text-gray-500">
                                      +{order.items.length - 3} more
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Actions */}
                          <div className="flex flex-col sm:flex-row gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => router.push(`/orders/${order.id}`)}
                            >
                              View Details
                            </Button>

                            {OrderService.canCancelOrder(order) && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleCancelOrder(order.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                Cancel Order
                              </Button>
                            )}

                            {OrderService.canTrackOrder(order) && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  router.push(`/orders/${order.id}/track`)
                                }
                              >
                                Track Order
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                  {/* Load More Button */}
                  {ordersPagination.hasMore && (
                    <div className="text-center pt-6">
                      <Button
                        variant="outline"
                        onClick={handleLoadMore}
                        disabled={ordersLoading}
                      >
                        {ordersLoading ? (
                          <>
                            <Icon
                              icon="mdi:loading"
                              className="animate-spin mr-2"
                            />
                            Loading...
                          </>
                        ) : (
                          "Load More Orders"
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  );
}
