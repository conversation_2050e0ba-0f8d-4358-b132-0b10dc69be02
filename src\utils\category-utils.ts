/**
 * Category utility functions for consistent category handling
 */

/**
 * Get category-specific feature text for product cards
 * @param category - The product category
 * @returns Feature text specific to the category
 */
export function getCategoryFeature(category: string): string {
  switch (category.toLowerCase()) {
    case "property":
      return "5000sq.ft Land";
    case "vehicles":
    case "vehicle":
      return "12345km";
    case "electronics":
      return "256GB";
    case "art-crafts":
    case "art":
    case "crafts":
      return "Handmade";
    case "construction":
      return "Premium Quality";
    case "motorbikes":
    case "motorcycle":
      return "150cc";
    case "pets":
    case "pet":
    case "animals":
      return "Vaccinated";
    case "photography":
      return "Professional";
    case "fashion":
    case "clothing":
      return "Brand New";
    case "books":
      return "Latest Edition";
    case "sports":
      return "Professional Grade";
    case "home-garden":
    case "home":
    case "garden":
      return "Premium Quality";
    default:
      return "Premium";
  }
}

/**
 * Get category display name with proper formatting
 * @param category - The category identifier
 * @returns Formatted category name
 */
export function formatCategoryName(category: string): string {
  return category
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

/**
 * Get category icon name for UI display
 * @param category - The category identifier
 * @returns Icon name string
 */
export function getCategoryIcon(category: string): string {
  switch (category.toLowerCase()) {
    case "property":
      return "home";
    case "vehicles":
    case "vehicle":
      return "car";
    case "electronics":
      return "smartphone";
    case "art-crafts":
    case "art":
    case "crafts":
      return "palette";
    case "construction":
      return "hammer";
    case "motorbikes":
    case "motorcycle":
      return "bike";
    case "pets":
    case "pet":
    case "animals":
      return "heart";
    case "photography":
      return "camera";
    case "fashion":
    case "clothing":
      return "shirt";
    case "books":
      return "book";
    case "sports":
      return "trophy";
    case "home-garden":
    case "home":
    case "garden":
      return "flower";
    default:
      return "package";
  }
}

/**
 * Check if a category is a main category (not a subcategory)
 * @param category - The category identifier
 * @returns True if it's a main category
 */
export function isMainCategory(category: string): boolean {
  const mainCategories = [
    "property",
    "vehicles",
    "electronics",
    "art-crafts",
    "construction",
    "motorbikes",
    "pets",
    "photography",
    "fashion",
    "books",
    "sports",
    "home-garden",
  ];

  return mainCategories.includes(category.toLowerCase());
}

/**
 * Generate category URL with the format /category/{slug}/{id}
 * @param slug - The category slug
 * @param id - The category ID
 * @returns Formatted category URL
 */
export function generateCategoryUrl(slug: string, id: string): string {
  return `/category/${slug}/${id}`;
}

/**
 * Generate category URL from category object
 * @param category - The category object with slug and id
 * @returns Formatted category URL
 */
export function getCategoryUrl(category: { slug: string; id: string }): string {
  return generateCategoryUrl(category.slug, category.id);
}

/**
 * Get category color for UI theming
 * @param category - The category identifier
 * @returns CSS color class or hex color
 */
export function getCategoryColor(category: string): string {
  switch (category.toLowerCase()) {
    case "property":
      return "bg-blue-100 text-blue-800";
    case "vehicles":
    case "vehicle":
      return "bg-red-100 text-red-800";
    case "electronics":
      return "bg-purple-100 text-purple-800";
    case "art-crafts":
    case "art":
    case "crafts":
      return "bg-pink-100 text-pink-800";
    case "construction":
      return "bg-orange-100 text-orange-800";
    case "motorbikes":
    case "motorcycle":
      return "bg-gray-100 text-gray-800";
    case "pets":
    case "pet":
    case "animals":
      return "bg-green-100 text-green-800";
    case "photography":
      return "bg-indigo-100 text-indigo-800";
    case "fashion":
    case "clothing":
      return "bg-yellow-100 text-yellow-800";
    case "books":
      return "bg-teal-100 text-teal-800";
    case "sports":
      return "bg-emerald-100 text-emerald-800";
    case "home-garden":
    case "home":
    case "garden":
      return "bg-lime-100 text-lime-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}
