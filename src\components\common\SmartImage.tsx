"use client";

import React from "react";
import Image from "next/image";
import { useImageHandler } from "@/hooks";
import { handleImageError } from "@/services/api-image-service";

interface SmartImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  onError?: (e: React.SyntheticEvent<HTMLImageElement>) => void;
  onLoad?: () => void;
  loading?: "lazy" | "eager";
  priority?: boolean;
  fill?: boolean;
  style?: React.CSSProperties;
  sizes?: string;
  category?: string; // For fallback images
  productTitle?: string; // For fallback images
}

/**
 * SmartImage component that automatically chooses between Next.js Image and regular img
 * based on the source URL type. Uses regular img for blob URLs and data URLs,
 * and Next.js Image for other URLs to get optimization benefits.
 */
export function SmartImage({
  src,
  alt,
  width,
  height,
  className = "",
  onError,
  onLoad,
  loading = "eager",
  priority = false,
  fill = false,
  style,
  sizes,
  category,
  productTitle,
}: SmartImageProps) {
  // Enhanced error handler that uses API image service
  const enhancedErrorHandler = (e: React.SyntheticEvent<HTMLImageElement>) => {
    handleImageError(e, category, productTitle);
    onError?.(e);
  };

  const { handleImageLoad } = useImageHandler({
    onError: enhancedErrorHandler,
    onLoad,
  });

  // Use regular img tag for blob URLs and data URLs since Next.js Image doesn't support them
  const shouldUseRegularImg =
    src.startsWith("blob:") || src.startsWith("data:");

  if (shouldUseRegularImg) {
    return (
      // eslint-disable-next-line @next/next/no-img-element
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={className}
        onError={enhancedErrorHandler}
        onLoad={handleImageLoad}
        loading={loading}
        style={style}
      />
    );
  }

  // Use Next.js Image for all other URLs to get optimization benefits
  if (fill) {
    return (
      <Image
        src={src}
        alt={alt}
        fill
        className={className}
        onError={enhancedErrorHandler}
        onLoad={handleImageLoad}
        loading={loading}
        priority={priority}
        style={style}
        sizes={sizes}
      />
    );
  }

  return (
    <Image
      src={src}
      alt={alt}
      width={width || 300}
      height={height || 225}
      className={className}
      onError={enhancedErrorHandler}
      onLoad={handleImageLoad}
      loading={loading}
      priority={priority}
      style={style}
      sizes={sizes}
    />
  );
}
