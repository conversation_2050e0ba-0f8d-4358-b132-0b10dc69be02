import { Icon } from "@iconify/react";

export function SafetyTips() {
  const tips = [
    {
      icon: <Icon icon="lucide:shield" className="h-5 w-5 text-yellow-600" />,
      text: "Pay securely through our platform",
    },
    {
      icon: (
        <Icon icon="lucide:check-circle" className="h-5 w-5 text-yellow-600" />
      ),
      text: "Check product details and reviews",
    },
    {
      icon: <Icon icon="lucide:eye" className="h-5 w-5 text-yellow-600" />,
      text: "Verify seller identity",
    },
    {
      icon: (
        <Icon icon="lucide:file-text" className="h-5 w-5 text-yellow-600" />
      ),
      text: "Keep proof of transactions",
    },
  ];

  return (
    <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
      <h3 className="text-xl font-medium text-gray-900 mb-3 flex items-center gap-2">
        <Icon icon="lucide:shield" className="h-5 w-5 text-yellow-600" />
        Safety Tips
      </h3>

      <div className="space-y-2 text-2xl">
        {tips.map((tip, index) => {
          return (
            <div key={index} className="flex items-start gap-3">
              {tip.icon}
              <span className="text-lg text-gray-700 leading-relaxed">
                {tip.text}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}
