"use client";
// Redux imports - replacing context API
import { useAppSelector, useEcommerceActions } from "@/store/hooks";
import {
  selectActiveFilters,
  selectAvailableFilters,
  selectSelectedCategory,
  selectGlobalLoading,
} from "@/store/selectors";
import { LoadingButton, LoadingOverlay } from "@/components/ui";
import { FilterField } from "./filters";
import {
  getCategoryFilterConfig,
  getCategoryFilterOptions,
} from "@/constants/category-filters";
import type {
  ActiveFilters,
  FilterFieldConfig,
  FilterOptions,
} from "@/types/ecommerce";

export default function FilterPanel() {
  // Redux selectors - replace context usage
  const activeFilters = useAppSelector(selectActiveFilters);

  const selectedCategory = useAppSelector(selectSelectedCategory);
  const isLoading = useAppSelector(selectGlobalLoading);

  // Redux actions
  const { setActiveFilters, clearFilters } = useEcommerceActions();

  // Get filter configuration for the selected category (or default if no category)
  const filterConfig = selectedCategory
    ? getCategoryFilterConfig(selectedCategory)
    : getCategoryFilterConfig("default");

  // Get available filter options for the selected category (or default)
  const availableOptions = selectedCategory
    ? getCategoryFilterOptions(selectedCategory)
    : getCategoryFilterOptions("default");

  const handleFilterChange = (
    filterType: keyof ActiveFilters,
    value: ActiveFilters[keyof ActiveFilters]
  ) => {
    const newFilters = { ...activeFilters };
    (newFilters as Record<string, unknown>)[filterType] = value;
    setActiveFilters(newFilters);
  };

  const handleClearFilters = () => {
    clearFilters();
  };

  const getFilterValue = (filterKey: keyof ActiveFilters) => {
    return activeFilters[filterKey];
  };

  const getFilterOptions = (
    filterKey: keyof ActiveFilters
  ): (string | { value: string; label: string })[] => {
    const options = (availableOptions as Partial<FilterOptions>)[filterKey];
    return Array.isArray(options) ? options : [];
  };

  // Always show filters (either category-specific or default)

  return (
    <div className="bg-gray-50 p-3 sm:p-4 space-y-3 sm:space-y-4 rounded-lg relative">
      {/* Loading Overlay */}
      <LoadingOverlay isLoading={isLoading} message="Updating filters..." />

      {/* Dynamic Filter Fields */}
      {filterConfig.filters.map((fieldConfig: FilterFieldConfig) => (
        <FilterField
          key={fieldConfig.key}
          config={fieldConfig}
          value={getFilterValue(fieldConfig.key)}
          onChange={handleFilterChange}
          options={getFilterOptions(fieldConfig.key)}
        />
      ))}

      {/* Filter Actions */}
      <div className="space-y-2 pt-3 sm:pt-4">
        <LoadingButton
          isLoading={isLoading}
          loadingText="Applying..."
          className="w-full bg-teal-600 hover:bg-teal-700 text-white font-medium py-2.5 sm:py-3 rounded-lg text-sm sm:text-base min-h-[44px]"
          onClick={() => {
            // Apply filters - this could trigger a search or filter update
            // For now, the filters are already applied via handleFilterChange
          }}
        >
          Apply Filters
        </LoadingButton>
        <LoadingButton
          isLoading={false}
          className="w-full border border-gray-300 text-gray-700 hover:bg-gray-50 font-medium py-2.5 sm:py-3 rounded-lg text-sm sm:text-base min-h-[44px] bg-white"
          onClick={handleClearFilters}
        >
          Clear All Filters
        </LoadingButton>
      </div>
    </div>
  );
}
