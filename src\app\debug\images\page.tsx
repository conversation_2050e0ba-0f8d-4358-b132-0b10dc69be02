"use client";

import { SmartImage } from "@/components/common/SmartImage";
import { useGetAdvertisementsQuery } from "@/store/api/advertisementApi";
import { convertAdvertisementToProduct } from "@/utils/slug-utils";
import { createProductImageConfig } from "@/services/api-image-service";

export default function DebugImagesPage() {
  const { data: adsData, isLoading, error } = useGetAdvertisementsQuery({ 
    page: 1, 
    limit: 6 
  });

  if (isLoading) return <div className="p-8">Loading advertisements...</div>;
  if (error) return <div className="p-8 text-red-600">Error loading advertisements</div>;

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Image Integration Test</h1>
      
      {/* Test No Images Behavior */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">No Images Test</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="border rounded-lg p-4">
            <h3 className="font-medium mb-2">Invalid Image URL</h3>
            <div className="w-full h-48 border-2 border-dashed border-gray-300 rounded flex items-center justify-center">
              <SmartImage
                src="https://invalid-url.com/nonexistent.jpg"
                alt="Invalid Image Test"
                width={300}
                height={200}
                category="electronics"
                productTitle="Test Electronics Product"
                className="w-full h-48 object-cover rounded"
              />
            </div>
            <p className="text-sm text-gray-600 mt-2">Should hide when image fails to load</p>
          </div>

          <div className="border rounded-lg p-4">
            <h3 className="font-medium mb-2">Empty Image Array</h3>
            <div className="w-full h-48 bg-gray-50 rounded flex items-center justify-center">
              <div className="text-center text-gray-500">
                <div className="w-16 h-16 mx-auto mb-2 bg-gray-200 rounded"></div>
                <p>No images available</p>
              </div>
            </div>
            <p className="text-sm text-gray-600 mt-2">Product with no images</p>
          </div>

          <div className="border rounded-lg p-4">
            <h3 className="font-medium mb-2">Null Image</h3>
            <div className="w-full h-48 bg-gray-50 rounded flex items-center justify-center">
              <div className="text-center text-gray-500">
                <div className="w-16 h-16 mx-auto mb-2 bg-gray-200 rounded"></div>
                <p>No images available</p>
              </div>
            </div>
            <p className="text-sm text-gray-600 mt-2">Product with null images</p>
          </div>
        </div>
      </section>

      {/* Test Real API Data */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">Real API Data Test</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {adsData?.data?.slice(0, 6).map((ad) => {
            const product = convertAdvertisementToProduct(ad);
            const imageConfig = createProductImageConfig(
              ad.images, 
              ad.category?.name, 
              ad.title
            );
            
            return (
              <div key={ad.id} className="border rounded-lg p-4">
                <h3 className="font-medium mb-2 truncate">{ad.title}</h3>
                <div className="mb-2">
                  {imageConfig.primary ? (
                    <SmartImage
                      src={imageConfig.primary}
                      alt={ad.title}
                      width={300}
                      height={200}
                      category={ad.category?.name}
                      productTitle={ad.title}
                      className="w-full h-48 object-cover rounded"
                    />
                  ) : (
                    <div className="w-full h-48 bg-gray-50 rounded flex items-center justify-center">
                      <div className="text-center text-gray-500">
                        <div className="w-16 h-16 mx-auto mb-2 bg-gray-200 rounded"></div>
                        <p className="text-sm">No images</p>
                      </div>
                    </div>
                  )}
                </div>
                <div className="text-sm text-gray-600 space-y-1">
                  <p><strong>Category:</strong> {ad.category?.name || 'N/A'}</p>
                  <p><strong>API Images:</strong> {ad.images?.length || 0}</p>
                  <p><strong>Has API Images:</strong> {imageConfig.hasApiImages ? 'Yes' : 'No'}</p>
                  <p><strong>Gallery Count:</strong> {imageConfig.count}</p>
                </div>
                
                {/* Show image URLs */}
                <details className="mt-2">
                  <summary className="text-sm cursor-pointer text-blue-600">Show Image URLs</summary>
                  <div className="mt-2 text-xs bg-gray-100 p-2 rounded">
                    <p><strong>Primary:</strong> {imageConfig.primary || 'None'}</p>
                    <p><strong>Gallery:</strong></p>
                    <ul className="ml-4">
                      {imageConfig.gallery.map((url, index) => (
                        <li key={index} className="truncate">• {url}</li>
                      ))}
                    </ul>
                  </div>
                </details>
              </div>
            );
          })}
        </div>
      </section>

      {/* Test Valid Images */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">Valid Images Test</h2>
        <p className="text-gray-600 mb-4">Testing with some reliable image URLs to ensure the image loading works correctly:</p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[
            "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=600&fit=crop&crop=center",
            "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=800&h=600&fit=crop&crop=center",
            "https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=800&h=600&fit=crop&crop=center"
          ].map((url, index) => (
            <div key={index} className="border rounded-lg p-4">
              <h3 className="font-medium mb-2">Valid Image {index + 1}</h3>
              <SmartImage
                src={url}
                alt={`Test image ${index + 1}`}
                width={300}
                height={200}
                className="w-full h-48 object-cover rounded"
              />
              <p className="text-xs text-gray-600 mt-2 truncate">{url}</p>
            </div>
          ))}
        </div>
      </section>

      {/* Debug Information */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">Debug Information</h2>
        <div className="bg-gray-100 p-4 rounded-lg">
          <p><strong>Total Advertisements:</strong> {adsData?.meta?.total || 'N/A'}</p>
          <p><strong>API Base URL:</strong> {process.env.NEXT_PUBLIC_API_BASE_URL}</p>
          <p><strong>Current Page:</strong> /debug/images</p>
          <p><strong>Image Service:</strong> API Image Service Integrated ✅</p>
        </div>
      </section>
    </div>
  );
}
