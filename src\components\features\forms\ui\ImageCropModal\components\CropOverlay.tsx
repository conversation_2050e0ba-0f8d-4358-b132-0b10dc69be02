"use client";
import React from "react";
import { useImageCropModal } from "../ImageCropModalProvider";
import { ResizeHandle } from "./ResizeHandle";

export const CropOverlay: React.FC = () => {
  const { crop, handleMouseDown } = useImageCropModal();

  return (
    <>
      {/* Dark overlay with crop area cut out */}
      <div
        className="absolute inset-0 bg-black bg-opacity-40 pointer-events-none"
        style={{
          zIndex: 1,
          clipPath: `polygon(0% 0%, 0% 100%, ${crop.x}px 100%, ${crop.x}px ${crop.y}px, ${
            crop.x + crop.width
          }px ${crop.y}px, ${crop.x + crop.width}px ${
            crop.y + crop.height
          }px, ${crop.x}px ${crop.y + crop.height}px, ${crop.x}px 100%, 100% 100%, 100% 0%)`,
        }}
      />

      {/* Interactive crop border */}
      <div
        className="absolute border-2 border-blue-500 pointer-events-auto"
        style={{
          left: crop.x,
          top: crop.y,
          width: crop.width,
          height: crop.height,
          cursor: "move",
          zIndex: 3,
        }}
        onMouseDown={(e) => {
          e.stopPropagation();
          handleMouseDown(e, "move-crop");
        }}
      >
        {/* Corner resize handles */}
        <ResizeHandle
          position="se"
          style={{ right: -6, bottom: -6 }}
          cursor="se-resize"
        />
        <ResizeHandle
          position="nw"
          style={{ left: -6, top: -6 }}
          cursor="nw-resize"
        />
        <ResizeHandle
          position="ne"
          style={{ right: -6, top: -6 }}
          cursor="ne-resize"
        />
        <ResizeHandle
          position="sw"
          style={{ left: -6, bottom: -6 }}
          cursor="sw-resize"
        />

        {/* Edge resize handles */}
        <ResizeHandle
          position="n"
          style={{
            top: -4,
            left: "50%",
            transform: "translateX(-50%)",
            width: "24px",
            height: "8px",
          }}
          cursor="n-resize"
        />
        <ResizeHandle
          position="s"
          style={{
            bottom: -4,
            left: "50%",
            transform: "translateX(-50%)",
            width: "24px",
            height: "8px",
          }}
          cursor="s-resize"
        />
        <ResizeHandle
          position="w"
          style={{
            left: -4,
            top: "50%",
            transform: "translateY(-50%)",
            width: "8px",
            height: "24px",
          }}
          cursor="w-resize"
        />
        <ResizeHandle
          position="e"
          style={{
            right: -4,
            top: "50%",
            transform: "translateY(-50%)",
            width: "8px",
            height: "24px",
          }}
          cursor="e-resize"
        />

        {/* Crop lines for rule of thirds */}
        <div className="absolute inset-0 pointer-events-none">
          {/* Vertical lines */}
          <div
            className="absolute border-l border-white opacity-50"
            style={{ left: "33.33%", height: "100%" }}
          />
          <div
            className="absolute border-l border-white opacity-50"
            style={{ left: "66.66%", height: "100%" }}
          />
          {/* Horizontal lines */}
          <div
            className="absolute border-t border-white opacity-50"
            style={{ top: "33.33%", width: "100%" }}
          />
          <div
            className="absolute border-t border-white opacity-50"
            style={{ top: "66.66%", width: "100%" }}
          />
        </div>
      </div>
    </>
  );
};
