import { useState, useCallback, useEffect } from "react"
import {
  useCreateAdvertisementMutation,
  useUpdateAdvertisementMutation,
} from "@/store/api/advertisementApi"
import { transformFormDataToApiRequest, sanitizeFormData } from "@/utils/form-transformer"
import { type FormData } from "../types"

export type AutoSaveStatus = "idle" | "saving" | "saved" | "error"

export interface UseAutoSaveResult {
  autoSaveStatus: AutoSaveStatus
  advertisementId: string | null
  saveDraft: () => Promise<void>
}

export const useAutoSave = (formData: FormData): UseAutoSaveResult => {
  const [autoSaveStatus, setAutoSaveStatus] = useState<AutoSaveStatus>("idle")
  const [advertisementId, setAdvertisementId] = useState<string | null>(null)

  const [createAdvertisement] = useCreateAdvertisementMutation()
  const [updateAdvertisement] = useUpdateAdvertisementMutation()

  const saveDraft = useCallback(async () => {
    // Only save if minimum required fields are filled
    if (!formData.adTitle.trim() || !formData.categoryId || !formData.description.trim()) {
      return
    }

    try {
      setAutoSaveStatus("saving")
      const sanitizedData = sanitizeFormData(formData)
      const apiRequest = transformFormDataToApiRequest(sanitizedData)

      if (!advertisementId) {
        const result = await createAdvertisement(apiRequest).unwrap()
        setAdvertisementId(result.id)
      } else {
        await updateAdvertisement({
          id: advertisementId,
          data: apiRequest,
        }).unwrap()
      }

      setAutoSaveStatus("saved")
      setTimeout(() => setAutoSaveStatus("idle"), 2000)
    } catch (error) {
      setAutoSaveStatus("error")
      setTimeout(() => setAutoSaveStatus("idle"), 3000)
      if (process.env.NODE_ENV === "development") {
        console.error("Error saving draft:", error)
      }
    }
  }, [formData, advertisementId, createAdvertisement, updateAdvertisement])

  // Auto-save effect disabled - no automatic saving
  // useEffect(() => {
  //   const timeoutId = setTimeout(() => {
  //     if (formData.adTitle.trim() && formData.categoryId && formData.description.trim()) {
  //       saveDraft()
  //     }
  //   }, 3000) // 3 second debounce

  //   return () => clearTimeout(timeoutId)
  // }, [formData, saveDraft])

  return {
    autoSaveStatus,
    advertisementId,
    saveDraft,
  }
}
