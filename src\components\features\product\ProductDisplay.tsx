"use client";

import { memo, useMemo } from "react";
import { ErrorBoundary } from "@/components/common";
import { EmptyState, LoadingSpinner, ProductCardSkeleton } from "@/components/ui";
import BaseProductCard from "./BaseProductCard";
import BaseJobCard from "./BaseJobCard";
import { cn } from "@/lib/utils";
import type { ProductDisplayProps, ProductDisplayConfig } from "./types";
import {
  isJobProduct,
  getProductGridClasses,
  getAnimationDelay,
  validateProduct,
} from "./utils";

const defaultConfig: ProductDisplayConfig = {
  viewMode: "grid",
  itemsPerRow: {
    mobile: 1,
    tablet: 2,
    desktop: 3,
  },
  showAnimations: true,
  showPagination: false,
  showSorting: false,
  showFilters: false,
};

const ProductDisplay = memo(function ProductDisplay({
  products = [],
  config: configOverride,
  loading = false,
  error = null,
  className = "",
  emptyStateTitle = "No products found",
  emptyStateDescription = "Try adjusting your search or filter criteria",
  onProductSelect,
  onLoadMore,
  hasMore = false,
}: ProductDisplayProps) {
  const config = { ...defaultConfig, ...configOverride };

  // Filter valid products
  const validProducts = useMemo(() => {
    return products.filter(validateProduct);
  }, [products]);

  // Determine if we have job products
  const hasJobProducts = useMemo(() => {
    return validProducts.some(isJobProduct);
  }, [validProducts]);

  // Get grid classes based on product types
  const gridClasses = useMemo(() => {
    if (hasJobProducts) {
      return "grid-cols-1 sm:grid-cols-1 lg:grid-cols-2 xl:grid-cols-2";
    }
    return "grid-responsive-1-2-3 xl:grid-cols-4 2xl:grid-cols-4";
  }, [hasJobProducts]);

  // Loading state
  if (loading && validProducts.length === 0) {
    return (
      <div className={cn("w-full", className)}>
        <LoadingSpinner message="Loading products..." />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn("w-full", className)}>
        <EmptyState
          title="Error loading products"
          description={error}
          icon="alert-circle"
        />
      </div>
    );
  }

  // Empty state
  if (validProducts.length === 0) {
    return (
      <div className={cn("w-full", className)}>
        <EmptyState
          title={emptyStateTitle}
          description={emptyStateDescription}
          icon="search"
        />
      </div>
    );
  }

  // List view (mobile-friendly horizontal cards)
  if (config.viewMode === "list") {
    return (
      <ErrorBoundary>
        <div className={cn("w-full space-y-4", className)}>
          {validProducts.map((product, index) => {
            const isJob = isJobProduct(product);
            const animationDelay = config.showAnimations 
              ? getAnimationDelay(index) 
              : 0;

            return (
              <div
                key={product.id}
                className={cn(
                  "product-card-item",
                  config.showAnimations && "animate-fadeIn"
                )}
                style={
                  config.showAnimations 
                    ? { animationDelay: `${animationDelay}s` } 
                    : undefined
                }
              >
                {isJob ? (
                  <BaseJobCard
                    product={product}
                    config={{ layout: "horizontal", size: "normal" }}
                    onViewDetails={onProductSelect}
                  />
                ) : (
                  <BaseProductCard
                    product={product}
                    config={{ layout: "horizontal", size: "normal" }}
                    onViewDetails={onProductSelect}
                  />
                )}
              </div>
            );
          })}

          {/* Load More Button */}
          {hasMore && onLoadMore && (
            <div className="flex justify-center pt-6">
              <button
                onClick={onLoadMore}
                className="px-6 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
              >
                Load More
              </button>
            </div>
          )}
        </div>
      </ErrorBoundary>
    );
  }

  // Grid view
  return (
    <ErrorBoundary>
      <div
        className={cn(
          "grid gap-responsive-md mb-4 sm:mb-6 mt-0 sm:pt-2 md:pt-4 pb-4 sm:pb-6 md:pb-8 product-grid-equal-height",
          gridClasses,
          className
        )}
      >
        {validProducts.map((product, index) => {
          const isJob = isJobProduct(product);
          const animationDelay = config.showAnimations 
            ? getAnimationDelay(index) 
            : 0;

          return (
            <div
              key={product.id}
              className={cn(
                "product-card-flex",
                config.showAnimations && "animate-fadeIn"
              )}
              style={
                config.showAnimations 
                  ? { animationDelay: `${animationDelay}s` } 
                  : undefined
              }
            >
              {isJob ? (
                <BaseJobCard
                  product={product}
                  config={{ layout: "vertical", size: "normal" }}
                  onViewDetails={onProductSelect}
                />
              ) : (
                <BaseProductCard
                  product={product}
                  config={{ layout: "detailed", size: "normal" }}
                  onViewDetails={onProductSelect}
                />
              )}
            </div>
          );
        })}

        {/* Loading skeleton for additional items */}
        {loading && validProducts.length > 0 && (
          <>
            {Array.from({ length: 4 }).map((_, index) => (
              <ProductCardSkeleton key={`skeleton-${index}`} />
            ))}
          </>
        )}
      </div>

      {/* Load More Button */}
      {hasMore && onLoadMore && (
        <div className="flex justify-center pt-6">
          <button
            onClick={onLoadMore}
            className="px-6 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
            disabled={loading}
          >
            {loading ? "Loading..." : "Load More"}
          </button>
        </div>
      )}
    </ErrorBoundary>
  );
});

export default ProductDisplay;
