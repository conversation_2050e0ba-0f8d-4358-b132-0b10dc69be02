"use client";
import type React from "react";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Icon } from "@iconify/react";
import type { Product } from "@/types/ecommerce";
import { BackButton } from "@/components/ui";
import { useContactSellerMutation } from "@/store/api/productDetailsApi";
import { useGetUserByUsernameQuery } from "@/store/api/userApi";

interface ContactSellerProps {
  product: Product;
  onBack?: () => void;
  onClose?: () => void;
}

export function ContactSeller({
  product,
  onBack,
  onClose,
}: ContactSellerProps) {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    message: "",
  });
  const [sendMessage, setSendMessage] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // API hooks
  const [contactSeller] = useContactSellerMutation();
  const { data: sellerData, isLoading: sellerLoading } =
    useGetUserByUsernameQuery(product.seller.username || product.seller.name, {
      skip: !product.seller.username && !product.seller.name,
    });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Create conversation with seller via API
      const messageContent = `Hi ${product.seller.name},

I'm interested in your listing: ${product.title}

${formData.message}

Contact Details:
Name: ${formData.name}
Email: ${formData.email}
${formData.phone ? `Phone: ${formData.phone}` : ""}

Best regards`;

      const result = await contactSeller({
        productId: product.id,
        sellerId: product.seller.id,
        message: messageContent,
        contactMethod: "message",
        buyerInfo: {
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
        },
      }).unwrap();

      // Success - show confirmation and close
      alert(
        "Message sent successfully! The seller will be notified and can respond to you."
      );
      onClose?.();
    } catch (error: any) {
      console.error("Failed to send message:", error);

      // Fallback to external contact methods if API fails
      const subject = `Inquiry about: ${product.title}`;
      const body = `Hi ${product.seller.name},\n\nI'm interested in your listing: ${product.title}\n\nMessage: ${formData.message}\n\nContact Details:\nName: ${formData.name}\nEmail: ${formData.email}\nPhone: ${formData.phone}\n\nBest regards`;

      const useWhatsApp = confirm(
        "Failed to send message through the platform. Would you like to contact via WhatsApp? (Cancel for Email)"
      );

      if (useWhatsApp) {
        const whatsappUrl = `https://wa.me/9779800000000?text=${encodeURIComponent(
          body
        )}`;
        window.open(whatsappUrl, "_blank");
      } else {
        const emailUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(
          subject
        )}&body=${encodeURIComponent(body)}`;
        window.open(emailUrl, "_blank");
      }

      alert("External contact method opened as fallback.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const safetyTips = [
    "Pay securely through our platform",
    "Check product details and reviews",
    "Verify seller identity",
    "Keep proof of transactions",
  ];

  return (
    <>
      {/* Header with Back Button */}
      <div className=" left-0  ">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={onBack || onClose}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <BackButton size="lg" asChild />
            </Button>
            <h1 className="text-3xl font-semibold uppercase text-gray-900">
              Contact Seller
            </h1>
          </div>
        </div>
      </div>
      <div className="min-h-screen  ">
        <div className="max-w-4xl mx-auto p-4 space-y-6">
          {/* Seller Information Card */}
          <Card className="shadow-lg border-0">
            <CardHeader className="pb-4">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <div className="w-16 h-16 bg-gradient-to-br from-teal-400 to-teal-600 rounded-full flex items-center justify-center shadow-lg">
                    <Icon icon="mdi:account" className="h-8 w-8 text-white" />
                  </div>
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <Icon
                      icon="mdi:check-circle"
                      className="h-4 w-4 text-white"
                    />
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h2 className="text-2xl font-bold text-gray-900">
                      {sellerData?.fullName || product.seller.name}
                    </h2>
                    <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                      {sellerData?.emailVerified ? "Verified Seller" : "Seller"}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Icon
                          key={i}
                          icon="mdi:star"
                          className={`h-4 w-4 ${
                            i < Math.floor(sellerData?.averageRating || 4)
                              ? "text-yellow-400"
                              : "text-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-600">
                      ({sellerData?.averageRating?.toFixed(1) || "4.8"} rating)
                    </span>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                  <Icon icon="mdi:calendar" className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      Member since
                    </p>
                    <p className="text-sm text-gray-600">
                      {sellerData?.createdAt
                        ? new Date(sellerData.createdAt).getFullYear()
                        : "2020"}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <Icon
                    icon="mdi:shield-check"
                    className="h-5 w-5 text-green-600"
                  />
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      Total ratings
                    </p>
                    <p className="text-sm text-gray-600">
                      {sellerData?.totalRatings || "0"}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                  <Icon
                    icon="mdi:clock-outline"
                    className="h-5 w-5 text-purple-600"
                  />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Status</p>
                    <p className="text-sm text-gray-600">
                      {sellerData?.status === "active" ? "Active" : "Inactive"}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Contact Actions */}
          <Card className="shadow-lg border-0">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Quick Contact
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button className="bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 text-white py-4 shadow-lg">
                  <Icon icon="mdi:phone" className="h-5 w-5 mr-2" />
                  Call Now
                </Button>
                <Button
                  variant="outline"
                  className="border-2 border-teal-600 text-teal-600 hover:bg-teal-50 py-4 shadow-lg bg-transparent"
                >
                  <Icon icon="mdi:email-outline" className="h-5 w-5 mr-2" />
                  Email Directly
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Send Message Section */}
          <Card className="shadow-lg border-0">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3 mb-6">
                <Checkbox
                  id="send-message"
                  checked={sendMessage}
                  onCheckedChange={(checked) =>
                    setSendMessage(checked as boolean)
                  }
                  className="data-[state=checked]:bg-teal-600 data-[state=checked]:border-teal-600"
                />
                <label
                  htmlFor="send-message"
                  className="text-lg font-semibold text-gray-900 cursor-pointer"
                >
                  Send a Message
                </label>
              </div>

              {sendMessage && (
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Your Name */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Your Name *
                    </label>
                    <Input
                      type="text"
                      placeholder="Enter your full name"
                      value={formData.name}
                      onChange={(e) =>
                        handleInputChange("name", e.target.value)
                      }
                      className="h-12 border-2 border-gray-200 focus:border-teal-500 focus:ring-teal-500"
                      required
                    />
                  </div>

                  {/* Email and Phone Row */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Your Email *
                      </label>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={(e) =>
                          handleInputChange("email", e.target.value)
                        }
                        className="h-12 border-2 border-gray-200 focus:border-teal-500 focus:ring-teal-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Your Phone Number
                      </label>
                      <Input
                        type="tel"
                        placeholder="+****************"
                        value={formData.phone}
                        onChange={(e) =>
                          handleInputChange("phone", e.target.value)
                        }
                        className="h-12 border-2 border-gray-200 focus:border-teal-500 focus:ring-teal-500"
                      />
                    </div>
                  </div>

                  {/* Message */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Message *
                    </label>
                    <Textarea
                      placeholder="Hi, I'm interested in your Toyota Corolla. Is it still available? Can we schedule a viewing?"
                      value={formData.message}
                      onChange={(e) =>
                        handleInputChange("message", e.target.value)
                      }
                      className="h-32 border-2 border-gray-200 focus:border-teal-500 focus:ring-teal-500 resize-none"
                      required
                    />
                  </div>

                  {/* Submit Button */}
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 text-white py-4 text-lg font-semibold shadow-lg disabled:opacity-50"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Icon
                          icon="mdi:message-outline"
                          className="h-5 w-5 mr-2"
                        />
                        Send Message
                      </>
                    )}
                  </Button>
                </form>
              )}
            </CardContent>
          </Card>

          {/* Safety Tips */}
          <Card className="shadow-lg border-0 bg-gradient-to-r from-amber-50 to-orange-50">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Icon
                  icon="mdi:shield-check"
                  className="h-5 w-5 text-amber-600"
                />
                Safety Tips
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {safetyTips.map((tip, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-3 p-3 bg-white rounded-lg shadow-sm"
                  >
                    <Icon
                      icon="mdi:check-circle"
                      className="h-5 w-5 text-green-500 flex-shrink-0"
                    />
                    <span className="text-sm text-gray-700">{tip}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}
