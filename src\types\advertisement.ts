// Advertisement types matching backend DTOs and entities

// Enums matching backend
export enum CurrencyType {
  NPR = "NPR",
  USD = "USD",
  EUR = "EUR",
  INR = "INR",
}

export enum ConditionType {
  NEW = "new",
  USED = "used",
  REFURBISHED = "refurbished",
}

export enum AdStatus {
  DRAFT = "draft",
  PENDING_APPROVAL = "pending_approval",
  ACTIVE = "active",
  SOLD = "sold",
  EXPIRED = "expired",
  SUSPENDED = "suspended",
  REJECTED = "rejected",
}

// Location interface matching backend LocationDto
export interface LocationDto {
  location?: string; // Location description (max 200 chars)
  city?: string; // City name (max 100 chars)
  state?: string; // State name (max 100 chars)
  latitude?: number; // Latitude coordinate (-90 to 90)
  longitude?: number; // Longitude coordinate (-180 to 180)
}

// User summary for advertisement response
export interface UserSummaryDto {
  id: string;
  username: string;
  firstName?: string;
  lastName?: string;
  profilePictureUrl?: string;
  isVerified: boolean;
}

// Category summary for advertisement response
export interface CategorySummaryDto {
  id: string;
  name: string;
  slug: string;
  iconUrl?: string;
}

// Subcategory summary for advertisement response
export interface SubcategorySummaryDto {
  id: string;
  name: string;
  slug: string;
}

// Ad image interface
export interface AdImageDto {
  id: string;
  imageUrl: string;
  thumbnailUrl?: string;
  altText?: string;
  sortOrder: number;
  isPrimary: boolean;
}

// Create Advertisement Request matching backend CreateAdvertisementDto
export interface CreateAdvertisementRequest {
  title: string; // 5-200 chars, required
  description: string; // 10-5000 chars, required
  categoryId: string; // UUID, required
  subcategoryId?: string; // UUID, optional
  price?: number; // >= 0, max 2 decimal places, optional
  currency?: CurrencyType; // default: NPR
  condition?: ConditionType; // optional
  location?: LocationDto; // optional
  negotiable?: boolean; // default: true
  inventoryQuantity?: number; // >= 1, default: 1
  youtubeVideoUrl?: string; // max 500 chars, optional
  isFeatured?: boolean; // default: false
  isUrgent?: boolean; // default: false
}

// Update Advertisement Request (partial of create request)
export interface UpdateAdvertisementRequest
  extends Partial<CreateAdvertisementRequest> {}

// Advertisement Response matching backend AdvertisementResponseDto
export interface AdvertisementResponse {
  id: string;
  title: string;
  description: string;
  price?: number;
  currency: CurrencyType;
  condition?: ConditionType;
  status: AdStatus;
  location?: string;
  city?: string;
  state?: string;
  latitude?: number;
  longitude?: number;
  viewCount: number;
  favoriteCount: number;
  inquiryCount: number;
  isFeatured: boolean;
  isUrgent: boolean;
  negotiable: boolean;
  inventoryQuantity: number;
  youtubeVideoUrl?: string;
  expiresAt: string; // ISO date string
  approvedAt?: string; // ISO date string
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  user: UserSummaryDto;
  category: CategorySummaryDto;
  subcategory?: SubcategorySummaryDto;
  images: AdImageDto[];
}

// Paginated response for advertisement lists
export interface PaginatedAdvertisementResponse {
  data: AdvertisementResponse[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Query parameters for fetching advertisements
export interface QueryAdvertisementParams {
  page?: number;
  limit?: number;
  categoryId?: string;
  subcategoryId?: string;
  city?: string;
  state?: string;
  minPrice?: number;
  maxPrice?: number;
  condition?: ConditionType;
  status?: AdStatus;
  isFeatured?: boolean;
  isUrgent?: boolean;
  search?: string;
  sortBy?: "createdAt" | "price" | "viewCount" | "favoriteCount";
  sortOrder?: "ASC" | "DESC";
}

// Image upload response
export interface ImageUploadResponse {
  message: string;
  images: AdImageDto[];
}

// Advertisement analytics/stats
export interface AdvertisementAnalytics {
  viewCount: number;
  favoriteCount: number;
  inquiryCount: number;
  dailyViews: Array<{
    date: string;
    views: number;
  }>;
  topLocations: Array<{
    location: string;
    count: number;
  }>;
}

// Error response structure
export interface ApiErrorResponse {
  message: string;
  error?: string;
  statusCode: number;
  timestamp?: string;
  path?: string;
}

// Form validation constraints
export const ADVERTISEMENT_CONSTRAINTS = {
  title: { min: 5, max: 200 },
  description: { min: 10, max: 5000 },
  price: { min: 0 },
  inventoryQuantity: { min: 1 },
  youtubeVideoUrl: { max: 500 },
  location: { max: 200 },
  city: { max: 100 },
  state: { max: 100 },
  latitude: { min: -90, max: 90 },
  longitude: { min: -180, max: 180 },
  maxImages: 8,
} as const;

// Default values
export const ADVERTISEMENT_DEFAULTS = {
  currency: CurrencyType.NPR,
  negotiable: true,
  inventoryQuantity: 1,
  isFeatured: false,
  isUrgent: false,
} as const;
