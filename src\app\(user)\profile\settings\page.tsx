"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>, Foot<PERSON> } from "@/components";
import { ProfileSettingsForm } from "@/components/features/profile";
import { BackButton } from "@/components/ui/back-button";
import { PageLoading } from "@/components/ui";
import { AuthGuard } from "@/components/features/auth";
import type { UserProfile, ProfileFormData } from "@/types/ecommerce";
import {
  useGetUserProfileQuery,
  useUpdateProfileMutation,
} from "@/store/api/userApi";
import { UpdateProfileRequest } from "@/types/auth";
import { getAuthToken } from "@/lib/api";

function ProfileSettingsPageContent() {
  const router = useRouter();
  const [saveMessage, setSaveMessage] = useState<string | null>(null);

  // Check if user is authenticated before making API calls
  const isAuthenticated = !!getAuthToken();

  // Redirect to login if not authenticated
  React.useEffect(() => {
    if (!isAuthenticated) {
      router.push("/login");
      return;
    }
  }, [isAuthenticated, router]);

  // Show loading while redirecting if not authenticated
  if (!isAuthenticated) {
    return <PageLoading />;
  }

  // Use real API hooks - only call if authenticated
  const {
    data: userData,
    isLoading: userLoading,
    error: userError,
  } = useGetUserProfileQuery(undefined, {
    skip: !isAuthenticated, // Skip the query if not authenticated
  });
  const [updateProfile, { isLoading: isUpdating }] = useUpdateProfileMutation();

  // Convert API User data to UserProfile format
  const userProfile: UserProfile | null = userData
    ? {
        id: userData.id,
        username: userData.username,
        email: userData.email || "",
        phoneNumber: userData.phone || "",
        profilePicture: userData.profilePicture,
        address: {
          city: userData.address?.city || "",
          country: userData.address?.country || "Nepal",
        },
        createdAt: userData.createdAt, // Keep as string for Redux serialization
        updatedAt: userData.updatedAt || new Date().toISOString(), // Keep as string for Redux serialization
        isVerified: userData.isVerified,
        preferences: {
          notifications: userData.preferences?.notifications || {
            email: true,
            sms: false,
            push: true,
          },
          privacy: userData.preferences?.privacy || {
            showEmail: false,
            showPhone: false,
          },
        },
      }
    : null;

  const handleSave = async (formData: ProfileFormData) => {
    setSaveMessage(null);

    try {
      // Convert form data to API format
      const updateData: UpdateProfileRequest = {
        firstName: formData.username, // Using username as firstName for now
        lastName: "", // You might want to add lastName to the form
        phone: formData.phoneNumber,
        profilePictureUrl:
          typeof formData.profilePicture === "string"
            ? formData.profilePicture
            : undefined,
        address: {
          city: formData.city,
          country: userProfile?.address.country || "Nepal",
        },
      };

      await updateProfile(updateData).unwrap();
      setSaveMessage("Profile updated successfully!");

      // Clear success message after 3 seconds
      setTimeout(() => setSaveMessage(null), 3000);
    } catch (error) {
      console.error("Failed to update profile:", error);
      setSaveMessage("Failed to update profile. Please try again.");
    }
  };

  // Loading state
  if (userLoading) {
    return <PageLoading message="Loading profile..." />;
  }

  // Error state
  if (userError || !userProfile) {
    return (
      <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
        <Header />
        <div className="mx-[5%] py-8">
          <div className="mb-6">
            <BackButton onClick={() => router.push("/")} />
          </div>
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Error Loading Profile
            </h2>
            <p className="text-gray-600 mb-4">
              We couldn't load your profile. Please try again later.
            </p>
            <button
              onClick={() => router.push("/")}
              className="px-4 py-2 bg-[#1F5E64] text-white rounded-lg hover:bg-[#1a5157] transition-colors"
            >
              Go Back
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
      <Header />

      <div className="mx-[5%] py-8">
        {/* Back Button */}
        <div className="mb-6">
          <BackButton onClick={() => router.push("/")} />
        </div>

        {/* Page Title */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Profile Settings
          </h1>
          <p className="text-gray-600">
            Manage your profile information and preferences
          </p>
        </div>

        {/* Success/Error Message */}
        {saveMessage && (
          <div
            className={`mb-6 p-4 rounded-lg ${
              saveMessage.includes("successfully")
                ? "bg-green-50 text-green-800 border border-green-200"
                : "bg-red-50 text-red-800 border border-red-200"
            }`}
          >
            {saveMessage}
          </div>
        )}

        {/* Profile Settings Form */}
        <div className="flex justify-center">
          <ProfileSettingsForm
            userProfile={userProfile}
            onSave={handleSave}
            isLoading={isUpdating}
          />
        </div>
      </div>

      <Footer />
    </div>
  );
}

export default function ProfileSettingsPage() {
  return (
    <AuthGuard>
      <ProfileSettingsPageContent />
    </AuthGuard>
  );
}
