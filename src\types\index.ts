// Re-export all types for easy importing with explicit exports to avoid conflicts

// Auth types
export type {
  PrivacySettings,
  NotificationPreferences,
  UserStatus,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  AuthState,
  AuthContextType,
  FormErrors,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  SocialLoginRequest,
  TokenPayload,
  UseAuthReturn,
  LoginFormFields,
  RegisterFormFields,
  UpdateProfileRequest,
  UpdatePrivacySettingsRequest,
  UpdateNotificationPreferencesRequest,
  AuthResponse,
  AuthStatus,
} from "./auth";

// Export User interface and default export from auth
export type { User } from "./auth";
export type { default as UserDefault } from "./auth";

// Re-export ApiResponse from auth as AuthApiResponse to avoid conflict
export type { ApiResponse as AuthApiResponse } from "./auth";
// Re-export ApiErrorResponse from auth as AuthApiErrorResponse to avoid conflict
export type { ApiErrorResponse as AuthApiErrorResponse } from "./auth";

// Ecommerce types
export type {
  UserProfile,
  ProfileFormData,
  ProfileUpdateRequest,
  PublicProfileData,
  ContactFormData,
  Message,
  SavedListing,
  SavedListingsState,
  UserListing,
  ListingAction,
  Subcategory,
  Category,
  Product,
  CartItem,
  CartState,
  BaseFilterOptions,
  CategorySpecificFilters,
  FilterOptions,
  ActiveFilters,
  FilterFieldConfig,
  CategoryFilterConfig,
  SortOption,
  SortBy,
  SearchState,
  CategoryState,
  FilterState,
  ProductState,
  EcommerceContextState,
  EcommerceAction,
  PaginationInfo,
  EcommerceApiResponse,
} from "./ecommerce";

// API types
export type {
  SubcategoryResponseDto,
  CategoryResponseDto,
  CategoriesListResponseDto,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CreateSubcategoryRequest,
  UpdateSubcategoryRequest,
  ApiError,
  GetCategoriesParams,
  GetSubcategoriesParams,
} from "./api";

// Re-export ApiResponse from api as ApiApiResponse to avoid conflict
export type { ApiResponse as ApiApiResponse } from "./api";

// Advertisement types
export type {
  ConditionType,
  AdStatus,
  LocationDto,
  UserSummaryDto,
  CategorySummaryDto,
  SubcategorySummaryDto,
  AdImageDto,
  CreateAdvertisementRequest,
  UpdateAdvertisementRequest,
  AdvertisementResponse,
  PaginatedAdvertisementResponse,
  QueryAdvertisementParams,
  ImageUploadResponse,
  AdvertisementAnalytics,
} from "./advertisement";

// Re-export CurrencyType from advertisement as AdCurrencyType to avoid conflict
export type { CurrencyType as AdCurrencyType } from "./advertisement";
// Re-export ApiErrorResponse from advertisement as AdApiErrorResponse to avoid conflict
export type { ApiErrorResponse as AdApiErrorResponse } from "./advertisement";

// Product details types
export type {
  Review,
  ReviewStats,
  CreateReviewRequest,
  UpdateReviewRequest,
  ReviewHelpfulRequest,
  PaginatedReviewsResponse,
  ProductQuestion,
  CreateQuestionRequest,
  CreateAnswerRequest,
  QuestionHelpfulRequest,
  PaginatedQuestionsResponse,
  SellerStats,
  SellerReview,
  SellerReviewsResponse,
  SimilarProductsRequest,
  SimilarProductsResponse,
  ContactSellerRequest,
  ContactSellerResponse,
  ProductViewRequest,
  ProductViewResponse,
  WishlistRequest,
  WishlistResponse,
  ReportProductRequest,
  ReportProductResponse,
  GetReviewsParams,
  GetQuestionsParams,
  GetSellerReviewsParams,
  ProductDetailsApiError,
} from "./product-details";

// Orders types
export type {
  AddressType,
  OrderStatus,
  PaymentStatus,
  PaymentMethod,
  OrderItemStatus,
  TransactionType,
  TransactionStatus,
  WalletTransactionType,
  PaymentMethodType,
  PaymentGateway,
  CreateAddressDto,
  UpdateAddressDto,
  AddressResponseDto,
  CartItemResponseDto,
  CartResponseDto,
  AddToCartDto,
  UpdateCartItemDto,
  BulkRemoveDto,
  CheckoutDto,
  CartValidationResponse,
  OrderItemResponseDto,
  OrderResponseDto,
  OrderFilterDto,
  PaginatedOrdersResponse,
  OrderStatusUpdateDto,
  CreateTransactionDto,
  TransactionResponseDto,
  PaginatedTransactionsResponse,
  WalletResponseDto,
  WalletTopupDto,
  WalletWithdrawDto,
  WalletTransferDto,
  WalletTransferResponse,
  WalletTransactionResponseDto,
  CreatePaymentMethodDto,
  PaymentMethodResponseDto,
  PaginationParams,
  BaseResponse,
  CartSummary,
  OrderSummary,
  TransactionSummary,
  CheckoutState,
} from "./orders";

// Re-export CurrencyType from orders as OrderCurrencyType to avoid conflict
export type { CurrencyType as OrderCurrencyType } from "./orders";
// Re-export ApiErrorResponse from orders as OrderApiErrorResponse to avoid conflict
export type { ApiErrorResponse as OrderApiErrorResponse } from "./orders";

// Export constants from advertisement
export {
  ADVERTISEMENT_CONSTRAINTS,
  ADVERTISEMENT_DEFAULTS,
} from "./advertisement";
