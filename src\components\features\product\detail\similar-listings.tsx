"use client";

import { useRouter } from "next/navigation";
import type { Product } from "@/types/ecommerce";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Icon } from "@iconify/react";
import { ProductCardDetailed } from "../";
import { useGetSimilarProductsQuery } from "@/store/api/productDetailsApi";

interface SimilarListingsProps {
  productId: string;
  similarProducts?: Product[]; // Fallback products if API fails
}

// Helper function to transform API response to Product type
const transformSimilarProduct = (apiProduct: any): Product => ({
  id: apiProduct.id,
  slug: apiProduct.slug,
  title: apiProduct.title,
  description: "", // Not provided in similar products API
  price: apiProduct.price,
  currency: apiProduct.currency,
  images: apiProduct.images,
  category: "", // Not provided in similar products API
  location: apiProduct.location,
  seller: apiProduct.seller,
  condition: apiProduct.condition as "new" | "used" | "refurbished",
  postedAt: apiProduct.createdAt,
  delivery: { available: false }, // Default value
  featured: false, // Default value
  status: "active" as const,
});

export function SimilarListings({ productId, similarProducts = [] }: SimilarListingsProps) {
  const router = useRouter();

  // Fetch similar products from API
  const {
    data: similarData,
    isLoading,
    error
  } = useGetSimilarProductsQuery({
    productId,
    limit: 8,
    excludeOwn: true
  });

  // Use API data if available, otherwise fallback to provided products
  const displayProducts = similarData?.data
    ? similarData.data.map(transformSimilarProduct)
    : similarProducts;

  // Loading state
  if (isLoading) {
    return (
      <div className="mt-12">
        <Skeleton className="h-8 w-48 mb-6" />
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="space-y-4">
              <Skeleton className="h-48 w-full rounded-lg" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
              <Skeleton className="h-6 w-1/3" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state with fallback
  if (error && displayProducts.length === 0) {
    return (
      <div className="mt-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Similar Listings
        </h2>
        <Alert>
          <Icon icon="lucide:alert-circle" className="h-4 w-4" />
          <AlertDescription>
            Unable to load similar products at the moment. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // No similar products found
  if (displayProducts.length === 0) {
    return (
      <div className="mt-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Similar Listings
        </h2>
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <Icon icon="lucide:search" className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No similar products found
          </h3>
          <p className="text-gray-600">
            Try browsing other products in the same category.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-12">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">
          Similar Listings
        </h2>
        {error && (
          <div className="text-sm text-amber-600 flex items-center gap-1">
            <Icon icon="lucide:wifi-off" className="h-4 w-4" />
            <span>Showing cached results</span>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {displayProducts.slice(0, 4).map((similarProduct) => (
          <ProductCardDetailed
            key={similarProduct.id}
            product={similarProduct}
            onViewDetails={() => {
              router.push(`/product/${similarProduct.slug}`);
            }}
            className="h-full"
          />
        ))}
      </div>

      <div className="text-center mt-8">
        <Button
          variant="outline"
          size="lg"
          onClick={() => router.push('/search')}
        >
          View More Ads
        </Button>
      </div>
    </div>
  );
}
