import { useEffect } from "react";
import { useImageCropModal } from "../ImageCropModalProvider";

export const useKeyboardShortcuts = () => {
  const { handleUndo, handleRedo, handleZoom } = useImageCropModal();

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case "z":
            e.preventDefault();
            if (e.shiftKey) {
              handleRedo();
            } else {
              handleUndo();
            }
            break;
          case "=":
          case "+":
            e.preventDefault();
            handleZoom(0.1);
            break;
          case "-":
            e.preventDefault();
            handleZoom(-0.1);
            break;
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [handleUndo, handleRedo, handleZoom]);
};
