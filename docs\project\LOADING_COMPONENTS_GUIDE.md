# Loading Components Guide

This guide documents the comprehensive loading system implemented across the e-commerce project. All loading components are centralized in `src/components/ui/loading.tsx` and follow consistent design patterns.

## Overview

The loading system provides:
- **Consistent UX**: Unified loading states across all components
- **Accessibility**: Proper ARIA labels and screen reader support
- **Performance**: Optimized components with proper memoization
- **Flexibility**: Multiple loading patterns for different use cases
- **Design System**: Follows project's Tailwind CSS and Iconify standards

## Available Components

### 1. LoadingSpinner

Basic spinner component for general loading states.

```tsx
import { LoadingSpinner } from "@/components/ui";

// Basic usage
<LoadingSpinner />

// With size and message
<LoadingSpinner 
  size="lg" 
  message="Loading data..." 
  centered={true} 
/>
```

**Props:**
- `size?: "sm" | "md" | "lg"` - Spinner size (default: "md")
- `message?: string` - Optional loading message
- `centered?: boolean` - Centers the spinner with padding
- `className?: string` - Additional CSS classes

### 2. LoadingButton

Button component with integrated loading state.

```tsx
import { LoadingButton } from "@/components/ui";

<LoadingButton
  isLoading={isSubmitting}
  loadingText="Saving..."
  onClick={handleSubmit}
  className="bg-teal-600 text-white"
>
  Save Changes
</LoadingButton>
```

**Props:**
- `isLoading: boolean` - Controls loading state
- `loadingText?: string` - Text shown during loading
- `children: React.ReactNode` - Button content
- Extends all standard button HTML attributes

### 3. LoadingOverlay

Full overlay for forms and containers.

```tsx
import { LoadingOverlay } from "@/components/ui";

<div className="relative">
  <LoadingOverlay 
    isLoading={isProcessing} 
    message="Processing your request..." 
  />
  {/* Your content */}
</div>
```

**Props:**
- `isLoading: boolean` - Controls overlay visibility
- `message?: string` - Loading message (default: "Loading...")
- `className?: string` - Additional CSS classes

### 4. ProductCardSkeleton

Skeleton placeholder for product cards.

```tsx
import { ProductCardSkeleton } from "@/components/ui";

// Loading grid
<div className="grid grid-cols-4 gap-6">
  {Array.from({ length: 8 }).map((_, index) => (
    <ProductCardSkeleton key={index} />
  ))}
</div>
```

### 5. PageLoading

Full-page loading component.

```tsx
import { PageLoading } from "@/components/ui";

// In page components or route loading
<PageLoading message="Loading profile..." />
```

### 6. SearchLoading

Specialized loading for search operations.

```tsx
import { SearchLoading } from "@/components/ui";

<SearchLoading className="py-12" />
```

### 7. LoadingSkeleton

Generic skeleton for text content.

```tsx
import { LoadingSkeleton } from "@/components/ui";

<LoadingSkeleton lines={3} className="mb-4" />
```

### 8. ProgressBar

Progress indicator with percentage.

```tsx
import { ProgressBar } from "@/components/ui";

<ProgressBar 
  progress={uploadProgress} 
  showPercentage={true} 
/>
```

## Implementation Patterns

### Form Loading States

```tsx
// Login/Signup Forms
<LoadingOverlay isLoading={isSubmitting} message="Signing you in..." />
<LoadingButton isLoading={isSubmitting} loadingText="Signing in...">
  Login
</LoadingButton>

// Multi-step forms
<LoadingButton 
  isLoading={isSubmitting} 
  loadingText="Publishing Ad..."
  disabled={!isValid}
>
  Post Ad
</LoadingButton>
```

### Data Loading

```tsx
// Product grids
if (loading) {
  return (
    <div className="grid grid-cols-4 gap-6">
      {Array.from({ length: 8 }).map((_, index) => (
        <ProductCardSkeleton key={index} />
      ))}
    </div>
  );
}

// Page-level loading
if (loading) {
  return <PageLoading message="Loading profile..." />;
}
```

### Search and Filters

```tsx
// Search input
{isSearching && (
  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
    <LoadingSpinner size="sm" />
  </div>
)}

// Filter panel
<LoadingOverlay isLoading={isUpdatingFilters} message="Updating filters..." />
```

### Image Loading

```tsx
// SafeImage component
<SafeImage 
  src={imageSrc}
  alt="Product image"
  showLoadingState={true}
/>

// Photo upload
<PhotoUpload 
  uploadedImages={images}
  onImageUpload={handleUpload}
  isUploading={isUploading}
/>
```

## Best Practices

### 1. Consistent Messaging
- Use descriptive loading messages
- Keep messages concise and user-friendly
- Match message tone with action context

### 2. Appropriate Component Selection
- **LoadingSpinner**: Quick operations, inline loading
- **LoadingOverlay**: Form submissions, data updates
- **ProductCardSkeleton**: Content placeholders
- **PageLoading**: Route transitions, initial page loads

### 3. Accessibility
- All components include proper ARIA labels
- Loading states are announced to screen readers
- Keyboard navigation remains functional during loading

### 4. Performance
- Use skeleton components for better perceived performance
- Implement loading states for operations > 200ms
- Avoid loading states for very quick operations

### 5. Error Handling
- Always pair loading states with error handling
- Provide clear error messages when loading fails
- Allow users to retry failed operations

## Redux Integration

Loading states are managed through Redux slices:

```tsx
// In components
const isLoading = useAppSelector(selectProductsLoading);
const isFilterLoading = useAppSelector(selectFilterLoading);

// In Redux slices
.addCase(fetchProducts.pending, (state) => {
  state.loading = true;
})
.addCase(fetchProducts.fulfilled, (state, action) => {
  state.loading = false;
  state.products = action.payload;
})
```

## Styling Guidelines

- Use `text-teal-600` for spinner colors (brand consistency)
- Background overlays use `bg-white/80 backdrop-blur-sm`
- Skeleton components use `bg-gray-200` with `animate-pulse`
- Maintain consistent spacing and sizing across components

## Migration Notes

When updating existing components:

1. Replace inline loading spinners with `LoadingSpinner`
2. Convert button loading states to `LoadingButton`
3. Add `LoadingOverlay` to forms and containers
4. Use skeleton components for content placeholders
5. Implement proper error boundaries with loading states

## Testing

Test loading states by:
- Simulating slow network conditions
- Testing with screen readers
- Verifying keyboard navigation during loading
- Checking loading state transitions
- Testing error scenarios

---

For questions or improvements to the loading system, please refer to the component source code in `src/components/ui/loading.tsx` or create an issue in the project repository.
