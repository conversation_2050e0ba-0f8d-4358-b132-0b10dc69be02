"use client";
import React from "react";
import { useImageCropModal } from "../ImageCropModalProvider";

interface ResizeHandleProps {
  position: string;
  style: React.CSSProperties;
  cursor: string;
}

export const ResizeHandle: React.FC<ResizeHandleProps> = ({
  position,
  style,
  cursor,
}) => {
  const { handleMouseDown } = useImageCropModal();

  const handleStyle = {
    ...style,
    cursor,
  };

  // Determine if this is an edge handle (for different sizing)
  const isEdgeHandle = ["n", "s", "e", "w"].includes(position);
  const baseClasses = "absolute bg-blue-500 border border-white rounded-sm hover:bg-blue-600 transition-colors";
  const sizeClasses = isEdgeHandle ? "" : "w-3 h-3";

  return (
    <div
      className={`${baseClasses} ${sizeClasses}`}
      style={handleStyle}
      onMouseDown={(e) => {
        e.stopPropagation();
        handleMouseDown(e, `resize-${position}`);
      }}
    />
  );
};
