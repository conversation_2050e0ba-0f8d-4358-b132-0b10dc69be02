// Form data transformation utilities for advertisement API integration

import type { FormData } from "@/components/features/forms/types";
import type { CreateAdvertisementRequest } from "@/types/advertisement";
import {
  CurrencyType,
  ConditionType,
  ADVERTISEMENT_CONSTRAINTS,
} from "@/types/advertisement";

/**
 * Transforms form data to API request format
 * @param formData - The form data from PostAdsForm
 * @returns API request object matching CreateAdvertisementRequest
 */
export const transformFormDataToApiRequest = (
  formData: FormData
): CreateAdvertisementRequest => {
  // Build location object only if location data exists
  const location =
    formData.location?.trim() || formData.city?.trim() || formData.state?.trim()
      ? {
          location: formData.location?.trim() || undefined,
          city: formData.city?.trim() || undefined,
          state: formData.state?.trim() || undefined,
          latitude: formData.latitude || undefined,
          longitude: formData.longitude || undefined,
        }
      : undefined;

  // Ensure subcategoryId is properly handled - convert empty string to undefined
  const subcategoryId = formData.subcategoryId?.trim()
    ? formData.subcategoryId.trim()
    : undefined;

  // Debug logging in development
  if (process.env.NODE_ENV === "development") {
    console.log("Form transformer - subcategory handling:", {
      originalSubcategoryId: formData.subcategoryId,
      processedSubcategoryId: subcategoryId,
      categoryId: formData.categoryId,
    });
  }

  return {
    title: formData.adTitle?.trim() || "",
    description: formData.description?.trim() || "",
    categoryId: formData.categoryId || "",
    subcategoryId,
    price: formData.price ? parseFloat(formData.price) : undefined,
    currency: formData.currency || CurrencyType.NPR,
    condition: formData.condition || undefined,
    location,
    negotiable: formData.negotiable,
    inventoryQuantity: formData.inventoryQuantity || 1,
    youtubeVideoUrl: formData.youtubeVideoUrl?.trim() || undefined,
    isFeatured: formData.isFeatured || false,
    isUrgent: formData.isUrgent || false,
  };
};

/**
 * Validates form data for a specific step
 * @param formData - The form data to validate
 * @param step - The step number to validate (1, 2, or 3)
 * @returns Array of validation error messages
 */
export const validateFormData = (
  formData: FormData,
  step: number
): string[] => {
  const errors: string[] = [];

  switch (step) {
    case 1:
      // Validate title
      if (!formData.adTitle.trim()) {
        errors.push("Title is required");
      } else if (
        formData.adTitle.length < ADVERTISEMENT_CONSTRAINTS.title.min
      ) {
        errors.push(
          `Title must be at least ${ADVERTISEMENT_CONSTRAINTS.title.min} characters`
        );
      } else if (
        formData.adTitle.length > ADVERTISEMENT_CONSTRAINTS.title.max
      ) {
        errors.push(
          `Title must be less than ${ADVERTISEMENT_CONSTRAINTS.title.max} characters`
        );
      }

      // Validate photos (optional but limited)
      if (formData.photos.length > ADVERTISEMENT_CONSTRAINTS.maxImages) {
        errors.push(
          `Maximum ${ADVERTISEMENT_CONSTRAINTS.maxImages} photos allowed`
        );
      }

      // Validate photo file sizes and types
      formData.photos.forEach((photo, index) => {
        if (photo.size > 10 * 1024 * 1024) {
          // 10MB limit
          errors.push(`Photo ${index + 1} is too large (max 10MB)`);
        }

        if (!photo.type.startsWith("image/")) {
          errors.push(`Photo ${index + 1} must be an image file`);
        }
      });
      break;

    case 2:
      // Validate category (required)
      if (!formData.categoryId || formData.categoryId.trim() === "") {
        errors.push("Category is required");
      }

      // Validate subcategory format if provided (should be UUID or undefined)
      if (formData.subcategoryId && formData.subcategoryId.trim() === "") {
        // Convert empty string to undefined to prevent validation issues
        formData.subcategoryId = undefined;
      }

      // Validate description (required)
      if (!formData.description.trim()) {
        errors.push("Description is required");
      } else if (
        formData.description.length < ADVERTISEMENT_CONSTRAINTS.description.min
      ) {
        errors.push(
          `Description must be at least ${ADVERTISEMENT_CONSTRAINTS.description.min} characters`
        );
      } else if (
        formData.description.length > ADVERTISEMENT_CONSTRAINTS.description.max
      ) {
        errors.push(
          `Description must be less than ${ADVERTISEMENT_CONSTRAINTS.description.max} characters`
        );
      }

      // Validate location (optional)
      if (
        formData.location &&
        formData.location.length > ADVERTISEMENT_CONSTRAINTS.location.max
      ) {
        errors.push(
          `Location must be less than ${ADVERTISEMENT_CONSTRAINTS.location.max} characters`
        );
      }

      // Validate city if provided
      if (
        formData.city &&
        formData.city.length > ADVERTISEMENT_CONSTRAINTS.city.max
      ) {
        errors.push(
          `City must be less than ${ADVERTISEMENT_CONSTRAINTS.city.max} characters`
        );
      }

      // Validate state if provided
      if (
        formData.state &&
        formData.state.length > ADVERTISEMENT_CONSTRAINTS.state.max
      ) {
        errors.push(
          `State must be less than ${ADVERTISEMENT_CONSTRAINTS.state.max} characters`
        );
      }

      // Validate coordinates if provided
      if (formData.latitude !== undefined) {
        if (
          formData.latitude < ADVERTISEMENT_CONSTRAINTS.latitude.min ||
          formData.latitude > ADVERTISEMENT_CONSTRAINTS.latitude.max
        ) {
          errors.push("Invalid latitude coordinate");
        }
      }

      if (formData.longitude !== undefined) {
        if (
          formData.longitude < ADVERTISEMENT_CONSTRAINTS.longitude.min ||
          formData.longitude > ADVERTISEMENT_CONSTRAINTS.longitude.max
        ) {
          errors.push("Invalid longitude coordinate");
        }
      }

      // Validate condition (optional)
      if (
        formData.condition &&
        !Object.values(ConditionType).includes(formData.condition)
      ) {
        errors.push("Invalid condition selected");
      }
      break;

    case 3:
      // Validate price (optional)
      if (formData.price && formData.price.trim()) {
        const price = parseFloat(formData.price);
        if (isNaN(price) || price < ADVERTISEMENT_CONSTRAINTS.price.min) {
          errors.push("Price must be a valid positive number");
        }

        // Check for reasonable decimal places (max 2)
        if (formData.price.includes(".")) {
          const decimalPlaces = formData.price.split(".")[1]?.length || 0;
          if (decimalPlaces > 2) {
            errors.push("Price can have maximum 2 decimal places");
          }
        }
      }

      // Validate inventory quantity
      if (
        formData.inventoryQuantity <
        ADVERTISEMENT_CONSTRAINTS.inventoryQuantity.min
      ) {
        errors.push(
          `Quantity must be at least ${ADVERTISEMENT_CONSTRAINTS.inventoryQuantity.min}`
        );
      }

      // Validate YouTube URL if provided
      if (formData.youtubeVideoUrl) {
        if (
          formData.youtubeVideoUrl.length >
          ADVERTISEMENT_CONSTRAINTS.youtubeVideoUrl.max
        ) {
          errors.push(
            `YouTube URL must be less than ${ADVERTISEMENT_CONSTRAINTS.youtubeVideoUrl.max} characters`
          );
        }

        // Basic YouTube URL validation
        const youtubeRegex =
          /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+/;
        if (!youtubeRegex.test(formData.youtubeVideoUrl)) {
          errors.push("Please enter a valid YouTube URL");
        }
      }

      // Validate currency
      if (!Object.values(CurrencyType).includes(formData.currency)) {
        errors.push("Invalid currency selected");
      }
      break;
  }

  return errors;
};

/**
 * Validates all form data at once
 * @param formData - The form data to validate
 * @returns Object with validation results for each step
 */
export const validateAllFormData = (formData: FormData) => {
  return {
    step1: validateFormData(formData, 1),
    step2: validateFormData(formData, 2),
    step3: validateFormData(formData, 3),
    isValid: function () {
      return (
        this.step1.length === 0 &&
        this.step2.length === 0 &&
        this.step3.length === 0
      );
    },
    getAllErrors: function () {
      return [...this.step1, ...this.step2, ...this.step3];
    },
  };
};

/**
 * Checks if a step is valid
 * @param formData - The form data to validate
 * @param step - The step number to check
 * @returns True if the step is valid
 */
export const isStepValid = (formData: FormData, step: number): boolean => {
  return validateFormData(formData, step).length === 0;
};

/**
 * Gets the next invalid step number
 * @param formData - The form data to validate
 * @param currentStep - The current step number
 * @returns The next invalid step number or null if all are valid
 */
export const getNextInvalidStep = (
  formData: FormData,
  currentStep: number
): number | null => {
  for (let step = currentStep; step <= 3; step++) {
    if (!isStepValid(formData, step)) {
      return step;
    }
  }
  return null;
};

/**
 * Sanitizes form data before submission
 * @param formData - The form data to sanitize
 * @returns Sanitized form data
 */
export const sanitizeFormData = (formData: FormData): FormData => {
  // Ensure subcategoryId is properly handled - convert empty string to undefined
  const subcategoryId = formData.subcategoryId?.trim()
    ? formData.subcategoryId.trim()
    : undefined;

  return {
    ...formData,
    adTitle: formData.adTitle.trim(),
    categoryId: formData.categoryId.trim(),
    subcategoryId,
    location: formData.location?.trim(),
    city: formData.city?.trim(),
    state: formData.state?.trim(),
    description: formData.description.trim(),
    price: formData.price?.trim(),
    youtubeVideoUrl: formData.youtubeVideoUrl?.trim(),
  };
};

/**
 * Creates default form data with proper types
 * @returns Default FormData object
 */
export const createDefaultFormData = (): FormData => {
  return {
    photos: [],
    adTitle: "",
    categoryId: "",
    subcategoryId: undefined,
    description: "",
    condition: undefined,
    location: undefined,
    city: undefined,
    state: undefined,
    latitude: undefined,
    longitude: undefined,
    price: undefined,
    currency: CurrencyType.NPR,
    negotiable: true,
    inventoryQuantity: 1,
    youtubeVideoUrl: undefined,
    isFeatured: false,
    isUrgent: false,
  };
};
