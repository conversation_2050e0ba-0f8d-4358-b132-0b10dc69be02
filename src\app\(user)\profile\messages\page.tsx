"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/components";
import { InboxMessages } from "@/components/features/profile";
import { BackButton } from "@/components/ui/back-button";
import { ToastProvider } from "@/components/ui/toast";
import { useRouter } from "next/navigation";

export default function MessagesPage() {
  const router = useRouter();

  const handleBack = () => {
    router.push("/profile");
  };

  return (
    <ToastProvider>
      <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
        <Header />

        <div className="mx-[5%] py-8">
          {/* Back Button */}
          <div className="mb-6">
            <BackButton onClick={handleBack} size="lg" />
          </div>

          {/* Page Title */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Messages</h1>
            <p className="text-gray-600">
              Manage your conversations with other users
            </p>
          </div>

          {/* Inbox Messages Component */}
          <InboxMessages />
        </div>

        <Footer />
      </div>
    </ToastProvider>
  );
}
