"use client";
import React from "react";
import { Icon } from "@iconify/react";
import { useImageCropModal } from "../ImageCropModalProvider";

export const PreviewArea: React.FC = () => {
  const {
    imageSrc,
    imageError,
    imageLoaded,
    crop,
    aspect,
    transforms,
    previewCanvasRef,
  } = useImageCropModal();

  return (
    <div className="bg-gray-50 rounded-lg p-4 h-full">
      <h3 className="text-sm font-medium text-gray-700 mb-3">Live Preview</h3>
      <div className="aspect-square bg-white rounded border-2 border-dashed border-gray-300 flex items-center justify-center mb-4">
        {imageError || !imageSrc ? (
          <div className="text-center text-red-400">
            <Icon
              icon="lucide:alert-circle"
              className="w-12 h-12 mx-auto mb-2 opacity-50"
            />
            <span className="text-sm">
              {!imageSrc ? "No image" : "Failed to load"}
            </span>
          </div>
        ) : imageLoaded ? (
          <canvas
            ref={previewCanvasRef}
            style={{
              maxWidth: "100%",
              maxHeight: "100%",
              objectFit: "contain",
              borderRadius: "4px",
            }}
          />
        ) : (
          <div className="text-center text-gray-400">
            <Icon
              icon="lucide:square"
              className="w-12 h-12 mx-auto mb-2 opacity-50"
            />
            <span className="text-sm">Loading image...</span>
          </div>
        )}
      </div>

      {imageLoaded && (
        <div className="text-xs text-gray-500 space-y-1">
          <div>
            Size: {Math.round(crop.width)} × {Math.round(crop.height)} px
          </div>
          <div>Ratio: {aspect ? `${aspect.toFixed(2)}:1` : "Free"}</div>
          <div>Scale: {Math.round(transforms.scale * 100)}%</div>
          <div>Rotation: {transforms.rotate}°</div>
        </div>
      )}
    </div>
  );
};
