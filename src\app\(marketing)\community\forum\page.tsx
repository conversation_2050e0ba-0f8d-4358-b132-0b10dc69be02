"use client";

import { useRouter } from "next/navigation";
import { BackButton } from "@/components/ui/back-button";
import { Icon } from "@iconify/react";

export default function ForumPage() {
  const router = useRouter();

  const forumCategories = [
    {
      id: "general",
      title: "General Discussion",
      icon: "material-symbols:forum",
      description: "General topics and marketplace discussions",
      posts: 1234,
      topics: 156,
      lastPost: {
        title: "Welcome to the marketplace!",
        author: "Admin",
        time: "2 hours ago"
      }
    },
    {
      id: "buying-selling",
      title: "Buying & Selling Tips",
      icon: "material-symbols:tips-and-updates",
      description: "Share tips and best practices for trading",
      posts: 856,
      topics: 98,
      lastPost: {
        title: "How to negotiate better prices",
        author: "SellerPro",
        time: "1 hour ago"
      }
    },
    {
      id: "reviews",
      title: "Product Reviews",
      icon: "material-symbols:rate-review",
      description: "Share your experience with products and sellers",
      posts: 642,
      topics: 87,
      lastPost: {
        title: "Amazing camera quality!",
        author: "PhotoLover",
        time: "30 minutes ago"
      }
    },
    {
      id: "support",
      title: "Technical Support",
      icon: "material-symbols:support-agent",
      description: "Get help with technical issues",
      posts: 423,
      topics: 65,
      lastPost: {
        title: "Payment not processing",
        author: "UserHelp",
        time: "45 minutes ago"
      }
    }
  ];

  const featuredTopics = [
    {
      id: 1,
      title: "Best practices for product photography",
      author: "PhotoPro123",
      category: "Tips",
      replies: 23,
      views: 156,
      lastReply: "2 hours ago",
      isPinned: true,
      isHot: true
    },
    {
      id: 2,
      title: "How to handle difficult buyers?",
      author: "SellerExpert",
      category: "Discussion",
      replies: 45,
      views: 289,
      lastReply: "1 hour ago",
      isPinned: false,
      isHot: true
    },
    {
      id: 3,
      title: "New payment method suggestions",
      author: "TechUser99",
      category: "Suggestions",
      replies: 12,
      views: 78,
      lastReply: "3 hours ago",
      isPinned: false,
      isHot: false
    }
  ];

  const handleCategoryClick = (categoryId: string) => {
    router.push(`/community/forum/${categoryId}`);
  };

  const handleTopicClick = (topicId: number) => {
    router.push(`/community/forum/topic/${topicId}`);
  };

  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Back Button */}
        <div className="mb-6">
          <BackButton onClick={() => router.back()} size="lg" />
        </div>

        {/* Main Content Card */}
        <div className="bg-white rounded-xl shadow-lg px-8 py-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Community Forum
              </h1>
              <p className="text-gray-600 text-lg">
                Engage in discussions with our community members
              </p>
            </div>
            <button 
              onClick={() => router.push('/community/forum/new-topic')}
              className="bg-[#478085] text-white px-6 py-3 rounded-lg hover:bg-[#356267] transition-colors font-medium flex items-center space-x-2"
            >
              <Icon icon="material-symbols:add" className="w-5 h-5" />
              <span>New Topic</span>
            </button>
          </div>

          {/* Forum Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">2,847</div>
              <div className="text-gray-600 text-sm">Members</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">3,155</div>
              <div className="text-gray-600 text-sm">Posts</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">406</div>
              <div className="text-gray-600 text-sm">Topics</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">156</div>
              <div className="text-gray-600 text-sm">Online Now</div>
            </div>
          </div>

          {/* Forum Categories */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Forum Categories
            </h2>
            <div className="space-y-4">
              {forumCategories.map((category) => (
                <div
                  key={category.id}
                  onClick={() => handleCategoryClick(category.id)}
                  className="border border-gray-200 rounded-lg p-6 hover:bg-gray-50 transition-colors cursor-pointer group"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      <Icon
                        icon={category.icon}
                        className="w-8 h-8 text-[#478085] mt-1 group-hover:text-[#356267]"
                      />
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 text-lg group-hover:text-[#478085] mb-1">
                          {category.title}
                        </h3>
                        <p className="text-gray-600 mb-2">
                          {category.description}
                        </p>
                        <div className="text-sm text-gray-500">
                          {category.topics} topics • {category.posts} posts
                        </div>
                      </div>
                    </div>
                    <div className="text-right text-sm text-gray-500 min-w-[200px]">
                      <div className="font-medium text-gray-700">{category.lastPost.title}</div>
                      <div>by {category.lastPost.author}</div>
                      <div>{category.lastPost.time}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Featured Topics */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Featured Topics
            </h2>
            <div className="space-y-3">
              {featuredTopics.map((topic) => (
                <div
                  key={topic.id}
                  onClick={() => handleTopicClick(topic.id)}
                  className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        {topic.isPinned && (
                          <Icon icon="material-symbols:push-pin" className="w-4 h-4 text-red-500" />
                        )}
                        {topic.isHot && (
                          <Icon icon="material-symbols:local-fire-department" className="w-4 h-4 text-orange-500" />
                        )}
                        <h3 className="font-semibold text-gray-900 hover:text-blue-700">
                          {topic.title}
                        </h3>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>by {topic.author}</span>
                        <span className="bg-gray-200 px-2 py-1 rounded text-xs">
                          {topic.category}
                        </span>
                      </div>
                    </div>
                    <div className="text-right text-sm text-gray-500 min-w-[120px]">
                      <div>{topic.replies} replies</div>
                      <div>{topic.views} views</div>
                      <div>Last: {topic.lastReply}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="border-t border-gray-200 pt-6">
            <div className="flex flex-wrap gap-4">
              <button className="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">
                <Icon icon="material-symbols:search" className="w-4 h-4" />
                <span>Search Forum</span>
              </button>
              <button className="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">
                <Icon icon="material-symbols:trending-up" className="w-4 h-4" />
                <span>Trending Topics</span>
              </button>
              <button className="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">
                <Icon icon="material-symbols:bookmark" className="w-4 h-4" />
                <span>My Bookmarks</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
