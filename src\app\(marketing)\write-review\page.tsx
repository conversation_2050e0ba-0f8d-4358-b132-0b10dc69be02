"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { BackButton } from "@/components/ui/back-button";
import { <PERSON><PERSON>, Footer } from "@/components/layout";
import {
  ReviewFormSection,
  StarRatingSection,
} from "@/components/features/product/review";
import { AuthGuard } from "@/components/features/auth/AuthGuard";

interface ReviewFormData {
  rating: number;
  comment: string;
}

export default function WriteReviewPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<ReviewFormData>({
    rating: 0,
    comment: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleRatingChange = (rating: number) => {
    setFormData((prev) => ({ ...prev, rating }));
  };

  const handleCommentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData((prev) => ({ ...prev, comment: e.target.value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (formData.rating === 0) {
      alert("Please select a rating");
      return;
    }

    if (formData.comment.trim().length < 10) {
      alert("Please write at least 10 characters in your review");
      return;
    }

    setIsSubmitting(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // Show success message and redirect
    alert("Review submitted successfully!");
    router.back();
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <AuthGuard
      promptTitle="Write a Review"
      promptMessage="Please log in to share your review and help other customers"
    >
      <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
        <Header />
        <div className="mx-[5%]">
          {/* Back Button */}
          <div className="py-4">
            <BackButton onClick={() => window.history.back()} size="lg" />
          </div>

          {/* Main Content */}
          <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-sm p-8 mb-8">
            <h1 className="text-2xl font-semibold text-gray-900 mb-8">
              Write a Review
            </h1>

            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Rating Section */}
              <StarRatingSection
                rating={formData.rating}
                onRatingChange={handleRatingChange}
              />

              {/* Review Form Section */}
              <ReviewFormSection
                formData={formData}
                isSubmitting={isSubmitting}
                onCommentChange={handleCommentChange}
                onCancel={handleCancel}
              />
            </form>
          </div>
        </div>
        <Footer />
      </div>
    </AuthGuard>
  );
}
