# SastoBazar API Data Models

## User Models

### UserProfileResponseDto
```typescript
interface UserProfileResponseDto {
  id: string;
  username: string;
  email?: string;                    // Optional based on privacy settings
  phone?: string;                    // Optional based on privacy settings
  firstName?: string;
  lastName?: string;
  fullName?: string;
  bio?: string;
  profilePictureUrl?: string;
  status: 'active' | 'suspended' | 'banned' | 'pending_verification';
  emailVerified: boolean;
  phoneVerified: boolean;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  fullAddress?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt?: string;
  roles: string[];
  privacySettings?: PrivacySettings;
  notificationPreferences?: NotificationPreferences;
  isOwnProfile: boolean;
  followersCount: number;
  followingCount: number;
  averageRating: number;
  totalRatings: number;
}
```

### PrivacySettings
```typescript
interface PrivacySettings {
  showEmail: boolean;
  showPhone: boolean;
  showFullName: boolean;
  showProfilePicture: boolean;
  showBio: boolean;
  showLocation: boolean;
  showLastSeen: boolean;
  allowDirectMessages: boolean;
  searchableByEmail: boolean;
  searchableByPhone: boolean;
}
```

### NotificationPreferences
```typescript
interface NotificationPreferences {
  email: boolean;
  sms: boolean;
  push: boolean;
  marketing: boolean;
  orderUpdates: boolean;
  messages: boolean;
  advertisements: boolean;
  security: boolean;
}
```

## Authentication Models

### AuthResponseDto
```typescript
interface AuthResponseDto {
  accessToken: string;
  refreshToken: string;
  tokenType: string;              // "Bearer"
  expiresIn: number;              // Seconds
  user: UserProfileResponseDto;
}
```

### RegisterDto
```typescript
interface RegisterDto {
  username: string;               // 3-50 chars, alphanumeric + underscore
  email: string;                  // Valid email
  password: string;               // Min 8 chars with complexity requirements
  firstName?: string;             // Max 100 chars
  lastName?: string;              // Max 100 chars
  phone?: string;                 // Valid phone format
  role?: 'USER' | 'VENDOR';      // Default: USER
}
```

### LoginDto
```typescript
interface LoginDto {
  usernameOrEmail: string;
  password: string;
}
```

## Category Models

### CategoryResponseDto
```typescript
interface CategoryResponseDto {
  id: string;
  name: string;
  slug: string;
  description?: string;
  iconUrl?: string;
  imageUrl?: string;
  isActive: boolean;
  sortOrder: number;
  subcategories: SubcategoryResponseDto[];
  createdAt: string;
  updatedAt: string;
}
```

### SubcategoryResponseDto
```typescript
interface SubcategoryResponseDto {
  id: string;
  name: string;
  slug: string;
  description?: string;
  iconUrl?: string;
  imageUrl?: string;
  isActive: boolean;
  sortOrder: number;
  categoryId: string;
  createdAt: string;
  updatedAt: string;
}
```

## Advertisement Models

### AdvertisementResponseDto
```typescript
interface AdvertisementResponseDto {
  id: string;
  title: string;
  description: string;
  slug: string;
  price?: number;
  currency: 'NPR' | 'USD';
  condition?: 'new' | 'used' | 'refurbished';
  negotiable: boolean;
  inventoryQuantity: number;
  status: 'draft' | 'pending_approval' | 'active' | 'sold' | 'expired' | 'rejected' | 'suspended';
  isFeatured: boolean;
  isUrgent: boolean;
  youtubeVideoUrl?: string;
  location?: LocationDto;
  images: AdvertisementImageDto[];
  category: CategoryResponseDto;
  subcategory?: SubcategoryResponseDto;
  user: UserProfileResponseDto;
  viewsCount: number;
  favoritesCount: number;
  isFavorited?: boolean;          // Only when user is authenticated
  createdAt: string;
  updatedAt: string;
  approvedAt?: string;
  rejectedAt?: string;
  suspendedAt?: string;
  soldAt?: string;
  expiresAt?: string;
}
```

### CreateAdvertisementDto
```typescript
interface CreateAdvertisementDto {
  title: string;                  // 5-200 chars
  description: string;            // 10-5000 chars
  categoryId: string;             // UUID
  subcategoryId?: string;         // UUID
  price?: number;                 // Min 0, max 2 decimal places
  currency?: 'NPR' | 'USD';      // Default: NPR
  condition?: 'new' | 'used' | 'refurbished';
  location?: LocationDto;
  negotiable?: boolean;           // Default: true
  inventoryQuantity?: number;     // Min 1, default: 1
  youtubeVideoUrl?: string;       // Valid URL
  isFeatured?: boolean;           // Default: false
  isUrgent?: boolean;             // Default: false
}
```

### LocationDto
```typescript
interface LocationDto {
  location?: string;              // Max 200 chars
  city?: string;                  // Max 100 chars
  state?: string;                 // Max 100 chars
  latitude?: number;              // -90 to 90
  longitude?: number;             // -180 to 180
}
```

### AdvertisementImageDto
```typescript
interface AdvertisementImageDto {
  id: string;
  url: string;
  thumbnailUrl?: string;
  altText?: string;
  sortOrder: number;
  isPrimary: boolean;
  createdAt: string;
}
```

### QueryAdvertisementDto
```typescript
interface QueryAdvertisementDto {
  page?: number;                  // Default: 1
  limit?: number;                 // Default: 20, max: 100
  search?: string;
  categoryId?: string;
  subcategoryId?: string;
  minPrice?: number;
  maxPrice?: number;
  condition?: 'new' | 'used' | 'refurbished';
  location?: string;
  city?: string;
  state?: string;
  sortBy?: 'createdAt' | 'price' | 'title' | 'views';
  sortOrder?: 'ASC' | 'DESC';
  status?: 'draft' | 'pending_approval' | 'active' | 'sold' | 'expired' | 'rejected' | 'suspended';
  userId?: string;
  isFeatured?: boolean;
  isUrgent?: boolean;
}
```

### PaginatedAdvertisementResponseDto
```typescript
interface PaginatedAdvertisementResponseDto {
  data: AdvertisementResponseDto[];
  pagination: PaginationDto;
}
```

## Cart Models

### CartResponseDto
```typescript
interface CartResponseDto {
  id: string;
  userId: string;
  items: CartItemDto[];
  totalItems: number;
  totalAmount: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
}
```

### CartItemDto
```typescript
interface CartItemDto {
  id: string;
  advertisementId: string;
  advertisement: AdvertisementResponseDto;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  isAvailable: boolean;
  addedAt: string;
}
```

### AddToCartDto
```typescript
interface AddToCartDto {
  advertisementId: string;
  quantity: number;               // Min 1
}
```

### UpdateCartItemDto
```typescript
interface UpdateCartItemDto {
  quantity: number;               // Min 1
}
```

### BulkRemoveDto
```typescript
interface BulkRemoveDto {
  itemIds: string[];
}
```

## File Upload Models

### FileUploadResponseDto
```typescript
interface FileUploadResponseDto {
  successful: UploadedFileDto[];
  failed: FailedUploadDto[];
}
```

### UploadedFileDto
```typescript
interface UploadedFileDto {
  originalName: string;
  filename: string;
  url: string;
  size: number;
  mimeType: string;
  width?: number;                 // For images
  height?: number;                // For images
}
```

### FailedUploadDto
```typescript
interface FailedUploadDto {
  originalName: string;
  error: string;
}
```

## Common Models

### PaginationDto
```typescript
interface PaginationDto {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}
```

### ApiErrorResponseDto
```typescript
interface ApiErrorResponseDto {
  statusCode: number;
  message: string | string[];
  error?: string;
  timestamp: string;
  path: string;
}
```

## Enums

### UserRole
```typescript
enum UserRole {
  USER = 'USER',
  VENDOR = 'VENDOR',
  ADMIN = 'ADMIN',
  SUPER_ADMIN = 'SUPER_ADMIN'
}
```

### UserStatus
```typescript
enum UserStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  BANNED = 'banned',
  PENDING_VERIFICATION = 'pending_verification'
}
```

### AdvertisementStatus
```typescript
enum AdvertisementStatus {
  DRAFT = 'draft',
  PENDING_APPROVAL = 'pending_approval',
  ACTIVE = 'active',
  SOLD = 'sold',
  EXPIRED = 'expired',
  REJECTED = 'rejected',
  SUSPENDED = 'suspended'
}
```

### CurrencyType
```typescript
enum CurrencyType {
  NPR = 'NPR',
  USD = 'USD'
}
```

### ConditionType
```typescript
enum ConditionType {
  NEW = 'new',
  USED = 'used',
  REFURBISHED = 'refurbished'
}
```

## Validation Rules

### Common Validations
- UUIDs must be valid UUID v4 format
- Dates are in ISO 8601 format
- Prices have maximum 2 decimal places
- Phone numbers follow international format (+country_code)
- URLs must be valid HTTP/HTTPS URLs

### String Length Limits
- Username: 3-50 characters
- Email: Standard email validation
- Password: Minimum 8 characters with complexity
- Title: 5-200 characters
- Description: 10-5000 characters
- Location: 1-200 characters
- City/State: 1-100 characters
- First/Last Name: 1-100 characters

### Numeric Limits
- Price: Minimum 0, maximum 2 decimal places
- Quantity: Minimum 1
- Latitude: -90 to 90
- Longitude: -180 to 180
- Page: Minimum 1
- Limit: 1-100

### File Upload Limits
- Maximum file size: 10MB per file
- Maximum files per upload: 8 files
- Supported image formats: JPEG, PNG, WebP, GIF
- Image dimensions: Maximum 4096x4096 pixels
