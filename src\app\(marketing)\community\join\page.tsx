"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { BackButton } from "@/components/ui/back-button";
import { Icon } from "@iconify/react";

export default function JoinCommunityPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    // Basic Information
    username: "",
    email: "",
    password: "",
    confirmPassword: "",

    // Personal Information
    firstName: "",
    lastName: "",
    displayName: "",

    // Profile Information
    bio: "",
    location: "",
    website: "",

    // Community Preferences
    interests: [] as string[],
    notifications: {
      email: true,
      forum: true,
      mentions: true,
      messages: false,
    },

    // Privacy Settings
    profileVisibility: "public", // public, members, private
    showEmail: false,
    showLocation: true,

    // Agreement
    agreeToTerms: false,
    agreeToPrivacy: false,
    subscribeNewsletter: false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const interestOptions = [
    "General Discussion",
    "Buying & Selling",
    "Product Reviews",
    "Technical Support",
    "Photography",
    "Electronics",
    "Fashion",
    "Home & Garden",
    "Automotive",
    "Sports & Recreation",
    "Art & Crafts",
    "Books & Literature",
    "Travel",
    "Food & Cooking",
    "Technology",
    "Business & Finance",
  ];

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value, type } = e.target;

    if (type === "checkbox") {
      const checked = (e.target as HTMLInputElement).checked;
      if (name.includes(".")) {
        const [parent, child] = name.split(".");
        setFormData((prev) => ({
          ...prev,
          [parent]: {
            ...(prev[parent as keyof typeof prev] as Record<string, boolean>),
            [child]: checked,
          },
        }));
      } else {
        setFormData((prev) => ({ ...prev, [name]: checked }));
      }
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleInterestToggle = (interest: string) => {
    setFormData((prev) => ({
      ...prev,
      interests: prev.interests.includes(interest)
        ? prev.interests.filter((i) => i !== interest)
        : [...prev.interests, interest],
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.username.trim()) newErrors.username = "Username is required";
    if (!formData.email.trim()) newErrors.email = "Email is required";
    if (!formData.password) newErrors.password = "Password is required";
    if (!formData.firstName.trim())
      newErrors.firstName = "First name is required";
    if (!formData.lastName.trim()) newErrors.lastName = "Last name is required";

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    // Password validation
    if (formData.password && formData.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters";
    }

    // Confirm password
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    // Username validation
    if (formData.username && formData.username.length < 3) {
      newErrors.username = "Username must be at least 3 characters";
    }

    // Terms agreement
    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = "You must agree to the terms and conditions";
    }

    if (!formData.agreeToPrivacy) {
      newErrors.agreeToPrivacy = "You must agree to the privacy policy";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Success - redirect to community forum
      router.push("/community/forum");
    } catch (error) {
      console.error("Registration failed:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        {/* Back Button */}
        <div className="mb-6">
          <BackButton onClick={() => router.back()} size="lg" />
        </div>

        {/* Main Content Card */}
        <div className="bg-white rounded-xl shadow-lg px-8 py-8">
          {/* Header */}
          <div className="mb-8 text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Join Our Community
            </h1>
            <p className="text-gray-600 text-lg">
              Create your account and become part of our vibrant marketplace
              community
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Basic Information */}
            <div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                Basic Information
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Username *
                  </label>
                  <input
                    type="text"
                    name="username"
                    value={formData.username}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#478085] focus:border-transparent ${
                      errors.username ? "border-red-500" : "border-gray-300"
                    }`}
                    placeholder="Choose a unique username"
                  />
                  {errors.username && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.username}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#478085] focus:border-transparent ${
                      errors.email ? "border-red-500" : "border-gray-300"
                    }`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Password *
                  </label>
                  <input
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#478085] focus:border-transparent ${
                      errors.password ? "border-red-500" : "border-gray-300"
                    }`}
                    placeholder="Minimum 8 characters"
                  />
                  {errors.password && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.password}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Confirm Password *
                  </label>
                  <input
                    type="password"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#478085] focus:border-transparent ${
                      errors.confirmPassword
                        ? "border-red-500"
                        : "border-gray-300"
                    }`}
                    placeholder="Re-enter your password"
                  />
                  {errors.confirmPassword && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.confirmPassword}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Personal Information */}
            <div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                Personal Information
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    First Name *
                  </label>
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#478085] focus:border-transparent ${
                      errors.firstName ? "border-red-500" : "border-gray-300"
                    }`}
                    placeholder="Your first name"
                  />
                  {errors.firstName && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.firstName}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name *
                  </label>
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#478085] focus:border-transparent ${
                      errors.lastName ? "border-red-500" : "border-gray-300"
                    }`}
                    placeholder="Your last name"
                  />
                  {errors.lastName && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.lastName}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Display Name
                  </label>
                  <input
                    type="text"
                    name="displayName"
                    value={formData.displayName}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#478085] focus:border-transparent"
                    placeholder="How others will see your name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Location
                  </label>
                  <input
                    type="text"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#478085] focus:border-transparent"
                    placeholder="City, Country"
                  />
                </div>
              </div>

              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Website
                </label>
                <input
                  type="url"
                  name="website"
                  value={formData.website}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#478085] focus:border-transparent"
                  placeholder="https://yourwebsite.com"
                />
              </div>

              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Bio
                </label>
                <textarea
                  name="bio"
                  value={formData.bio}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#478085] focus:border-transparent"
                  placeholder="Tell us a bit about yourself..."
                />
              </div>
            </div>

            {/* Interests */}
            <div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                Interests
              </h2>
              <p className="text-gray-600 mb-4">
                Select topics you&apos;re interested in to personalize your
                experience
              </p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {interestOptions.map((interest) => (
                  <label
                    key={interest}
                    className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                      formData.interests.includes(interest)
                        ? "border-[#478085] bg-[#478085]/10"
                        : "border-gray-300 hover:border-gray-400"
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={formData.interests.includes(interest)}
                      onChange={() => handleInterestToggle(interest)}
                      className="sr-only"
                    />
                    <span
                      className={`text-sm ${
                        formData.interests.includes(interest)
                          ? "text-[#478085] font-medium"
                          : "text-gray-700"
                      }`}
                    >
                      {interest}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Notification Preferences */}
            <div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                Notification Preferences
              </h2>
              <div className="space-y-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="notifications.email"
                    checked={formData.notifications.email}
                    onChange={handleInputChange}
                    className="w-4 h-4 text-[#478085] border-gray-300 rounded focus:ring-[#478085]"
                  />
                  <span className="ml-3 text-gray-700">
                    Email notifications for new posts and replies
                  </span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="notifications.forum"
                    checked={formData.notifications.forum}
                    onChange={handleInputChange}
                    className="w-4 h-4 text-[#478085] border-gray-300 rounded focus:ring-[#478085]"
                  />
                  <span className="ml-3 text-gray-700">
                    In-forum notifications
                  </span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="notifications.mentions"
                    checked={formData.notifications.mentions}
                    onChange={handleInputChange}
                    className="w-4 h-4 text-[#478085] border-gray-300 rounded focus:ring-[#478085]"
                  />
                  <span className="ml-3 text-gray-700">
                    Notify when someone mentions me
                  </span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="notifications.messages"
                    checked={formData.notifications.messages}
                    onChange={handleInputChange}
                    className="w-4 h-4 text-[#478085] border-gray-300 rounded focus:ring-[#478085]"
                  />
                  <span className="ml-3 text-gray-700">
                    Direct message notifications
                  </span>
                </label>
              </div>
            </div>

            {/* Privacy Settings */}
            <div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                Privacy Settings
              </h2>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Profile Visibility
                  </label>
                  <select
                    name="profileVisibility"
                    value={formData.profileVisibility}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#478085] focus:border-transparent"
                  >
                    <option value="public">
                      Public - Anyone can view my profile
                    </option>
                    <option value="members">
                      Members Only - Only community members can view
                    </option>
                    <option value="private">
                      Private - Only I can view my profile
                    </option>
                  </select>
                </div>

                <div className="space-y-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="showEmail"
                      checked={formData.showEmail}
                      onChange={handleInputChange}
                      className="w-4 h-4 text-[#478085] border-gray-300 rounded focus:ring-[#478085]"
                    />
                    <span className="ml-3 text-gray-700">
                      Show my email address on my profile
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="showLocation"
                      checked={formData.showLocation}
                      onChange={handleInputChange}
                      className="w-4 h-4 text-[#478085] border-gray-300 rounded focus:ring-[#478085]"
                    />
                    <span className="ml-3 text-gray-700">
                      Show my location on my profile
                    </span>
                  </label>
                </div>
              </div>
            </div>

            {/* Terms and Agreements */}
            <div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                Terms and Agreements
              </h2>
              <div className="space-y-4">
                <label className="flex items-start">
                  <input
                    type="checkbox"
                    name="agreeToTerms"
                    checked={formData.agreeToTerms}
                    onChange={handleInputChange}
                    className={`w-4 h-4 text-[#478085] border-gray-300 rounded focus:ring-[#478085] mt-1 ${
                      errors.agreeToTerms ? "border-red-500" : ""
                    }`}
                  />
                  <span className="ml-3 text-gray-700">
                    I agree to the{" "}
                    <a href="/terms" className="text-[#478085] hover:underline">
                      Terms and Conditions
                    </a>{" "}
                    *
                  </span>
                </label>
                {errors.agreeToTerms && (
                  <p className="text-red-500 text-sm ml-7">
                    {errors.agreeToTerms}
                  </p>
                )}

                <label className="flex items-start">
                  <input
                    type="checkbox"
                    name="agreeToPrivacy"
                    checked={formData.agreeToPrivacy}
                    onChange={handleInputChange}
                    className={`w-4 h-4 text-[#478085] border-gray-300 rounded focus:ring-[#478085] mt-1 ${
                      errors.agreeToPrivacy ? "border-red-500" : ""
                    }`}
                  />
                  <span className="ml-3 text-gray-700">
                    I agree to the{" "}
                    <a
                      href="/privacy"
                      className="text-[#478085] hover:underline"
                    >
                      Privacy Policy
                    </a>{" "}
                    *
                  </span>
                </label>
                {errors.agreeToPrivacy && (
                  <p className="text-red-500 text-sm ml-7">
                    {errors.agreeToPrivacy}
                  </p>
                )}

                <label className="flex items-start">
                  <input
                    type="checkbox"
                    name="subscribeNewsletter"
                    checked={formData.subscribeNewsletter}
                    onChange={handleInputChange}
                    className="w-4 h-4 text-[#478085] border-gray-300 rounded focus:ring-[#478085] mt-1"
                  />
                  <span className="ml-3 text-gray-700">
                    Subscribe to our newsletter for updates and community
                    highlights
                  </span>
                </label>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-center pt-6">
              <button
                type="submit"
                disabled={isSubmitting}
                className="bg-[#478085] text-white px-8 py-3 rounded-lg hover:bg-[#356267] transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {isSubmitting ? (
                  <>
                    <Icon
                      icon="material-symbols:hourglass-empty"
                      className="w-5 h-5 animate-spin"
                    />
                    <span>Creating Account...</span>
                  </>
                ) : (
                  <>
                    <Icon
                      icon="material-symbols:person-add"
                      className="w-5 h-5"
                    />
                    <span>Join Community</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
