import { Icon } from "@iconify/react";

interface PasswordStrengthIndicatorProps {
  password: string;
  requirements?: {
    minLength?: number;
    requireUppercase?: boolean;
    requireLowercase?: boolean;
    requireNumbers?: boolean;
    requireSpecialChars?: boolean;
  };
}

export default function PasswordStrengthIndicator({
  password,
  requirements = {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
  },
}: PasswordStrengthIndicatorProps) {
  const checks = [
    {
      label: `At least ${requirements.minLength} characters`,
      passed: password.length >= (requirements.minLength || 8),
      required: true,
    },
    {
      label: "One uppercase letter",
      passed: /[A-Z]/.test(password),
      required: requirements.requireUppercase,
    },
    {
      label: "One lowercase letter",
      passed: /[a-z]/.test(password),
      required: requirements.requireLowercase,
    },
    {
      label: "One number",
      passed: /\d/.test(password),
      required: requirements.requireNumbers,
    },
    {
      label: "One special character",
      passed: /[!@#$%^&*(),.?":{}|<>]/.test(password),
      required: requirements.requireSpecialChars,
    },
  ].filter((check) => check.required);

  const passedCount = checks.filter((check) => check.passed).length;
  const totalRequired = checks.length;

  const getStrengthColor = () => {
    const percentage = passedCount / totalRequired;
    if (percentage === 1) return "text-green-600";
    if (percentage >= 0.75) return "text-yellow-600";
    return "text-red-600";
  };

  const getStrengthText = () => {
    const percentage = passedCount / totalRequired;
    if (percentage === 1) return "Strong";
    if (percentage >= 0.75) return "Medium";
    return "Weak";
  };

  if (!password) return null;

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-gray-700">
          Password Strength
        </span>
        <span className={`text-sm font-medium ${getStrengthColor()}`}>
          {getStrengthText()}
        </span>
      </div>

      <div className="space-y-2">
        {checks.map((check, index) => (
          <div key={index} className="flex items-center space-x-2">
            {check.passed ? (
              <Icon icon="lucide:check" className="h-4 w-4 text-green-600" />
            ) : (
              <Icon icon="lucide:x" className="h-4 w-4 text-red-400" />
            )}
            <span
              className={`text-sm ${
                check.passed ? "text-green-600" : "text-gray-600"
              }`}
            >
              {check.label}
            </span>
          </div>
        ))}
      </div>

      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${
            passedCount === totalRequired
              ? "bg-green-600"
              : passedCount >= totalRequired * 0.75
              ? "bg-yellow-500"
              : "bg-red-500"
          }`}
          style={{ width: `${(passedCount / totalRequired) * 100}%` }}
        />
      </div>
    </div>
  );
}
