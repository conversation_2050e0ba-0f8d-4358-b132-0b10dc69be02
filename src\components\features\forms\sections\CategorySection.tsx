import React from "react"
import { Icon } from "@iconify/react"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { CategorySelector } from "../ui/CategorySelector"
import { SectionHeader } from "../components/SectionHeader"
import { type FormData } from "../types"

interface CategorySectionProps {
  formData: FormData
  updateFormData: (updates: Partial<FormData>) => void
}

export const CategorySection: React.FC<CategorySectionProps> = ({
  formData,
  updateFormData,
}) => {
  return (
    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
      <CardHeader className="pb-4">
        <SectionHeader
          icon={<Icon icon="lucide:tag" className="w-6 h-6 text-white" />}
          title="Category & Subcategory"
          subtitle="Help buyers find your product easily"
          required
          step={3}
          totalSteps={6}
        />
      </CardHeader>
      <CardContent>
        <CategorySelector
          selectedCategoryId={formData.categoryId}
          selectedSubcategoryId={formData.subcategoryId}
          onCategoryChange={(categoryId) => updateFormData({ categoryId })}
          onSubcategoryChange={(subcategoryId) => updateFormData({ subcategoryId })}
        />
      </CardContent>
    </Card>
  )
}
