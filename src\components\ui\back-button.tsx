"use client";

import type React from "react";

interface BackButtonProps {
  onClick?: () => void;
  className?: string;
  size?: "sm" | "md" | "lg";
  disabled?: boolean;
  asChild?: boolean;
}

const BackButton: React.FC<BackButtonProps> = ({
  onClick,
  className = "",
  size = "md",
  disabled = false,
  asChild = false,
}) => {
  const sizeClasses = {
    sm: "w-10 h-10",
    md: "w-12 h-12",
    lg: "w-16 h-16",
  };

  const iconSizes = {
    sm: { width: 40, height: 40 },
    md: { width: 50, height: 50 },
    lg: { width: 60, height: 60 },
  };

  const iconSize = iconSizes[size];

  const iconElement = (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={iconSize.width}
      height={iconSize.height}
      viewBox="0 0 60 60"
      fill="none"
      className={sizeClasses[size]}
    >
      <g filter="url(#filter0_d_558_8869)">
        <rect x="10" y="8" width="40" height="40" rx="10" fill="white" />
        <path
          d="M17.2648 28.3762L25.0404 36.1499C25.2165 36.326 25.3154 36.5647 25.3154 36.8137C25.3154 37.0626 25.2165 37.3014 25.0404 37.4774C24.8644 37.6535 24.6256 37.7524 24.3767 37.7524C24.1277 37.7524 23.889 37.6535 23.7129 37.4774L15.2754 29.0399C15.1881 28.9528 15.1189 28.8494 15.0716 28.7355C15.0243 28.6216 15 28.4995 15 28.3762C15 28.2529 15.0243 28.1308 15.0716 28.0169C15.1189 27.903 15.1881 27.7995 15.2754 27.7124L23.7129 19.2749C23.889 19.0989 24.1277 19 24.3767 19C24.6256 19 24.8644 19.0989 25.0404 19.2749C25.2165 19.451 25.3154 19.6897 25.3154 19.9387C25.3154 20.1876 25.2165 20.4264 25.0404 20.6024L17.2648 28.3762Z"
          fill="black"
        />
        <path
          d="M26.2529 27.4395H45.0029C45.2516 27.4395 45.49 27.5382 45.6658 27.714C45.8417 27.8899 45.9404 28.1283 45.9404 28.377C45.9404 28.6256 45.8417 28.8641 45.6658 29.0399C45.49 29.2157 45.2516 29.3145 45.0029 29.3145H26.2529C26.0043 29.3145 25.7658 29.2157 25.59 29.0399C25.4142 28.8641 25.3154 28.6256 25.3154 28.377C25.3154 28.1283 25.4142 27.8899 25.59 27.714C25.7658 27.5382 26.0043 27.4395 26.2529 27.4395Z"
          fill="black"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_558_8869"
          x="0"
          y="0"
          width="60"
          height="60"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2" />
          <feGaussianBlur stdDeviation="5" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_558_8869"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_558_8869"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );

  // If asChild is true, return just the icon element (for use inside other buttons)
  if (asChild) {
    return iconElement;
  }

  // Otherwise, return the full button element
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`flex items-center justify-center text-sm text-gray-600 hover:text-gray-900 transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
      aria-label="Go back"
    >
      {iconElement}
    </button>
  );
};

export { BackButton };
export type { BackButtonProps };
