import { Icon } from "@iconify/react";

export default function ContactPage() {
  const contactInfo = [
    {
      icon: "lucide:map-pin",
      title: "Address",
      details: ["Kathmandu, Nepal", "Thamel, Ward No. 26"],
    },
    {
      icon: "lucide:phone",
      title: "Phone",
      details: ["+977-1-4567890", "+977-9876543210"],
    },
    {
      icon: "lucide:mail",
      title: "Email",
      details: ["<EMAIL>", "<EMAIL>"],
    },
    {
      icon: "lucide:clock",
      title: "Business Hours",
      details: ["Mon - Fri: 9:00 AM - 6:00 PM", "Sat: 10:00 AM - 4:00 PM"],
    },
  ];

  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <div className="container-responsive spacing-responsive-lg">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Contact Us
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            We&apos;d love to hear from you. Get in touch with our team for any
            questions, support, or feedback about Nepal Marketplace.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div className="bg-white rounded-xl p-8 shadow-lg">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">
              Send us a Message
            </h2>
            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    First Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    placeholder="Enter your first name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    placeholder="Enter your last name"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  placeholder="Enter your email"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  placeholder="Enter your phone number"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Subject
                </label>
                <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent">
                  <option>General Inquiry</option>
                  <option>Technical Support</option>
                  <option>Seller Support</option>
                  <option>Payment Issues</option>
                  <option>Product Quality</option>
                  <option>Other</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Message
                </label>
                <textarea
                  rows={5}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  placeholder="Tell us how we can help you..."
                ></textarea>
              </div>

              <button
                type="submit"
                className="w-full bg-teal-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-teal-700 transition-colors duration-300"
              >
                Send Message
              </button>
            </form>
          </div>

          {/* Contact Information */}
          <div className="space-y-8">
            <div className="bg-white rounded-xl p-8 shadow-lg">
              <h2 className="text-2xl font-bold text-gray-800 mb-6">
                Get in Touch
              </h2>
              <div className="space-y-6">
                {contactInfo.map((info, index) => (
                  <div key={index} className="flex items-start">
                    <div className="bg-teal-100 p-3 rounded-lg mr-4 flex-shrink-0">
                      <Icon
                        icon={info.icon}
                        className="h-6 w-6 text-teal-600"
                      />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-800 mb-1">
                        {info.title}
                      </h3>
                      {info.details.map((detail, idx) => (
                        <p key={idx} className="text-gray-600">
                          {detail}
                        </p>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* FAQ Quick Links */}
            <div className="bg-white rounded-xl p-8 shadow-lg">
              <h3 className="text-xl font-bold text-gray-800 mb-4">
                Quick Help
              </h3>
              <div className="space-y-3">
                <a
                  href="/faqs"
                  className="block text-teal-600 hover:text-teal-700 hover:underline"
                >
                  → Frequently Asked Questions
                </a>
                <a
                  href="/help-center"
                  className="block text-teal-600 hover:text-teal-700 hover:underline"
                >
                  → Help Center
                </a>
                <a
                  href="/support"
                  className="block text-teal-600 hover:text-teal-700 hover:underline"
                >
                  → Support Portal
                </a>
              </div>
            </div>

            {/* Social Media */}
            <div className="bg-white rounded-xl p-8 shadow-lg">
              <h3 className="text-xl font-bold text-gray-800 mb-4">
                Follow Us
              </h3>
              <div className="flex gap-4">
                <a
                  href="#"
                  className="bg-teal-100 p-3 rounded-lg hover:bg-teal-200 transition-colors"
                >
                  <Icon
                    icon="lucide:facebook"
                    className="h-6 w-6 text-teal-600"
                  />
                </a>
                <a
                  href="#"
                  className="bg-teal-100 p-3 rounded-lg hover:bg-teal-200 transition-colors"
                >
                  <Icon
                    icon="lucide:instagram"
                    className="h-6 w-6 text-teal-600"
                  />
                </a>
                <a
                  href="#"
                  className="bg-teal-100 p-3 rounded-lg hover:bg-teal-200 transition-colors"
                >
                  <Icon
                    icon="lucide:twitter"
                    className="h-6 w-6 text-teal-600"
                  />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
