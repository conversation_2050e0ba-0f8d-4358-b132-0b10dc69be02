"use client";

import React from "react";
import { Icon } from "@iconify/react";

interface ProfileNavigationProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const navigationTabs = [
  {
    id: "general",
    label: "General Profile",
    icon: "lucide:user",
    description: "Personal information and account details",
  },
  {
    id: "job",
    label: "Job Profile",
    icon: "lucide:briefcase",
    description: "Professional information and career details",
  },
  {
    id: "posts",
    label: "My Listings",
    icon: "lucide:package",
    description: "Manage your product listings and ads",
  },
  {
    id: "save-lists",
    label: "Saved Items",
    icon: "lucide:heart",
    description: "Your saved and favorite listings",
  },
  {
    id: "reviews",
    label: "Reviews",
    icon: "lucide:star",
    description: "Customer reviews and ratings",
  },
  {
    id: "privacy",
    label: "Privacy Settings",
    icon: "lucide:shield",
    description: "Control your privacy and visibility settings",
  },
  {
    id: "notifications",
    label: "Notifications",
    icon: "lucide:bell",
    description: "Manage your notification preferences",
  },
];

export default function ProfileNavigation({
  activeTab,
  onTabChange,
}: ProfileNavigationProps) {
  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-2">
      <div className="flex flex-wrap gap-2">
        {navigationTabs.map((tab) => {
          const isActive = activeTab === tab.id;

          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`
                flex items-center gap-3 px-6 py-4 rounded-xl transition-all duration-300
                min-w-fit flex-1 group relative overflow-hidden
                ${
                  isActive
                    ? "bg-teal-50 text-teal-700 shadow-sm border border-teal-200"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                }
              `}
            >
              {/* Background gradient for active state */}
              {isActive && (
                <div className="absolute inset-0 bg-gradient-to-r from-teal-50 to-blue-50 opacity-50"></div>
              )}

              {/* Icon */}
              <Icon
                icon={tab.icon}
                className={`
                  w-5 h-5 transition-all duration-300 relative z-10
                  ${
                    isActive
                      ? "text-teal-600"
                      : "text-gray-500 group-hover:text-gray-700"
                  }
                `}
              />

              {/* Label */}
              <div className="text-left relative z-10">
                <div
                  className={`
                    font-semibold text-sm transition-all duration-300
                    ${
                      isActive
                        ? "text-teal-700"
                        : "text-gray-700 group-hover:text-gray-900"
                    }
                  `}
                >
                  {tab.label}
                </div>
                <div
                  className={`
                    text-xs mt-0.5 transition-all duration-300 hidden sm:block
                    ${
                      isActive
                        ? "text-teal-600"
                        : "text-gray-500 group-hover:text-gray-600"
                    }
                  `}
                >
                  {tab.description}
                </div>
              </div>

              {/* Active indicator */}
              {isActive && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-teal-500 rounded-full"></div>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
}
