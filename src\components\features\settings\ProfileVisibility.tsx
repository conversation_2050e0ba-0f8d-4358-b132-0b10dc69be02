"use client";

import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

interface ProfileVisibilityProps {
  profileVisibility: {
    showEmail: boolean;
    showPhone: boolean;
    showLastLogin: boolean;
    showTransactionHistory: boolean;
  };
  setProfileVisibility: React.Dispatch<
    React.SetStateAction<{
      showEmail: boolean;
      showPhone: boolean;
      showLastLogin: boolean;
      showTransactionHistory: boolean;
    }>
  >;
}

export default function ProfileVisibility({
  profileVisibility,
  setProfileVisibility,
}: ProfileVisibilityProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl font-semibold">
          Profile Visibility
        </CardTitle>
        <CardDescription className="text-sm text-gray-600">
          Control what information is visible to other users
        </CardDescription>
        <div className="w-full h-px bg-gray-300" />
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label className="text-sm font-medium">Show Email Address</Label>
            <p className="text-sm text-gray-600">
              Display your email on your public profile
            </p>
          </div>

          <Switch
            checked={profileVisibility.showEmail}
            onCheckedChange={(checked) =>
              setProfileVisibility((prev) => ({ ...prev, showEmail: checked }))
            }
            className={`${
              profileVisibility.showEmail
                ? "bg-[#478085] data-[state=checked]:bg-[#478085]"
                : "bg-gray-300 data-[state=unchecked]:bg-gray-300"
            } transition-colors duration-200`}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label className="text-sm font-medium">Show Phone Number</Label>
            <p className="text-sm text-gray-600">
              Display your phone number on your public profile
            </p>
          </div>
          <Switch
            checked={profileVisibility.showPhone}
            onCheckedChange={(checked) =>
              setProfileVisibility((prev) => ({ ...prev, showPhone: checked }))
            }
            className={`${
              profileVisibility.showPhone
                ? "bg-[#478085] data-[state=checked]:bg-[#478085]"
                : "bg-gray-300 data-[state=unchecked]:bg-gray-300"
            } transition-colors duration-200`}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label className="text-sm font-medium">Show Last Login</Label>
            <p className="text-sm text-gray-600">
              Display when you were last active
            </p>
          </div>
          <Switch
            checked={profileVisibility.showLastLogin}
            onCheckedChange={(checked) =>
              setProfileVisibility((prev) => ({
                ...prev,
                showLastLogin: checked,
              }))
            }
            className={`${
              profileVisibility.showLastLogin
                ? "bg-[#478085] data-[state=checked]:bg-[#478085]"
                : "bg-gray-300 data-[state=unchecked]:bg-gray-300"
            } transition-colors duration-200`}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label className="text-sm font-medium">
              Show Transaction History
            </Label>
            <p className="text-sm text-gray-600">
              Display your buying and selling history
            </p>
          </div>
          <Switch
            checked={profileVisibility.showTransactionHistory}
            onCheckedChange={(checked) =>
              setProfileVisibility((prev) => ({
                ...prev,
                showTransactionHistory: checked,
              }))
            }
            className={`${
              profileVisibility.showTransactionHistory
                ? "bg-[#478085] data-[state=checked]:bg-[#478085]"
                : "bg-gray-300 data-[state=unchecked]:bg-gray-300"
            } transition-colors duration-200`}
          />
        </div>
      </CardContent>
    </Card>
  );
}
