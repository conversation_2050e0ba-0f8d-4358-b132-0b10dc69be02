"use client";

import EmailNotifications from "./EmailNotifications";
import CommunicationPreferences from "./CommunicationPreferences";

interface NotificationsTabProps {
  emailNotifications: {
    offers: boolean;
    watchlist: boolean;
    adsActivity: boolean;
    priceReductions: boolean;
  };
  setEmailNotifications: React.Dispatch<React.SetStateAction<{
    offers: boolean;
    watchlist: boolean;
    adsActivity: boolean;
    priceReductions: boolean;
  }>>;
  communicationPrefs: {
    directMessages: boolean;
    onlineStatus: boolean;
  };
  setCommunicationPrefs: React.Dispatch<React.SetStateAction<{
    directMessages: boolean;
    onlineStatus: boolean;
  }>>;
}

export default function NotificationsTab({
  emailNotifications,
  setEmailNotifications,
  communicationPrefs,
  setCommunicationPrefs,
}: NotificationsTabProps) {
  return (
    <div className="space-y-6">
      <EmailNotifications
        emailNotifications={emailNotifications}
        setEmailNotifications={setEmailNotifications}
      />
      <CommunicationPreferences
        communicationPrefs={communicationPrefs}
        setCommunicationPrefs={setCommunicationPrefs}
      />
    </div>
  );
}
