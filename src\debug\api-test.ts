// Debug script to test API directly
// Run this in browser console or create a test component

export const testAdvertisementAPI = async () => {
  const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://sasto-api.webstudiomatrix.com/api/v1';
  
  console.log('Testing Advertisement API...');
  console.log('Base URL:', baseUrl);
  
  try {
    // Test 1: Get all advertisements without any filters
    console.log('\n=== Test 1: Get all advertisements ===');
    const response1 = await fetch(`${baseUrl}/advertisements`);
    const data1 = await response1.json();
    console.log('Status:', response1.status);
    console.log('Response:', data1);
    
    // Test 2: Get advertisements with pagination
    console.log('\n=== Test 2: Get advertisements with pagination ===');
    const response2 = await fetch(`${baseUrl}/advertisements?page=1&limit=5`);
    const data2 = await response2.json();
    console.log('Status:', response2.status);
    console.log('Response:', data2);
    
    // Test 3: Get advertisements with active status
    console.log('\n=== Test 3: Get active advertisements ===');
    const response3 = await fetch(`${baseUrl}/advertisements?status=active&page=1&limit=5`);
    const data3 = await response3.json();
    console.log('Status:', response3.status);
    console.log('Response:', data3);
    
    // Test 4: Get categories to see if API is working
    console.log('\n=== Test 4: Get categories ===');
    const response4 = await fetch(`${baseUrl}/categories`);
    const data4 = await response4.json();
    console.log('Status:', response4.status);
    console.log('Response:', data4);
    
  } catch (error) {
    console.error('API Test Error:', error);
  }
};

// Usage: Run this in browser console
// testAdvertisementAPI();
