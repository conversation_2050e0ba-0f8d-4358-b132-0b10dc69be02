import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

import { getAuthToken } from "@/lib/api";

// Base query configuration
const baseQuery = fetchBaseQuery({
  baseUrl:
    process.env.NEXT_PUBLIC_API_BASE_URL,
  prepareHeaders: (
    headers,
    { getState: _getState, endpoint: _endpoint, type: _type, arg }
  ) => {
    // Add auth token if available
    const token = getAuthToken();
    if (token) {
      headers.set("authorization", `Bearer ${token}`);
    }

    // Check if this is a FormData request by examining the request body
    const isFormDataRequest =
      arg &&
      typeof arg === "object" &&
      "body" in arg &&
      arg.body instanceof FormData;

    // Only set content-type for non-FormData requests
    // FormData requests should not have content-type set (browser will set it with boundary)
    if (!isFormDataRequest && !headers.get("content-type")) {
      headers.set("content-type", "application/json");
    }

    headers.set("accept", "application/json");

    return headers;
  },
});

// Base query with re-authentication
const baseQueryWithReauth = async (args: any, api: any, extraOptions: any) => {
  let result = await baseQuery(args, api, extraOptions);

  if (result.error && result.error.status === 401) {
    // Token expired, try to refresh or redirect to login
    // For now, we'll just clear the token and let the user re-authenticate
    if (typeof window !== "undefined") {
      localStorage.removeItem("auth_token");
      localStorage.removeItem("refresh_token");
      // You might want to redirect to login page here
      // window.location.href = '/login';
    }
  }

  return result;
};

// Create the API slice
export const apiSlice = createApi({
  reducerPath: "api",
  baseQuery: baseQueryWithReauth,
  tagTypes: ["Category", "Subcategory", "Product", "User", "Advertisement", "Review", "Question", "Wishlist"],
  endpoints: () => ({}),
});

export default apiSlice;
