export interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ImagePosition {
  x: number;
  y: number;
}

export interface ImageDimensions {
  width: number;
  height: number;
}

export interface HistoryState {
  crop: CropArea;
  scale: number;
  rotate: number;
  flipX: boolean;
  flipY: boolean;
  imagePosition: ImagePosition;
}

export interface ImageTransforms {
  scale: number;
  rotate: number;
  flipX: boolean;
  flipY: boolean;
  imagePosition: ImagePosition;
}

export interface ColorAdjustments {
  brightness: number;
  contrast: number;
  saturation: number;
}

export interface AspectRatio {
  label: string;
  value: number | undefined;
}

export interface DragState {
  isDragging: string | null;
  dragStart: { x: number; y: number };
}

export interface ImageCropModalContextType {
  // Image state
  imageSrc: string;
  imageLoaded: boolean;
  imageError: boolean;
  imageDimensions: ImageDimensions;

  // Crop state
  crop: CropArea;
  aspect: number | undefined;

  // Transform state
  transforms: ImageTransforms;

  // Color adjustments
  colorAdjustments: ColorAdjustments;

  // History
  history: HistoryState[];
  historyIndex: number;

  // Drag state
  dragState: DragState;

  // Refs
  imgRef: React.RefObject<HTMLImageElement>;
  previewCanvasRef: React.RefObject<HTMLCanvasElement>;
  hiddenCanvasRef: React.RefObject<HTMLCanvasElement>;
  containerRef: React.RefObject<HTMLDivElement>;

  // Actions
  setCrop: (crop: CropArea | ((prev: CropArea) => CropArea)) => void;
  setAspect: (aspect: number | undefined) => void;
  updateTransforms: (updates: Partial<ImageTransforms>) => void;
  updateColorAdjustments: (updates: Partial<ColorAdjustments>) => void;
  handleUndo: () => void;
  handleRedo: () => void;
  handleZoom: (delta: number) => void;
  handleRotate: (degrees: number) => void;
  handleFlipHorizontal: () => void;
  handleFlipVertical: () => void;
  handleAspectChange: (newAspect: number | undefined) => void;
  resetAll: () => void;
  handleCropComplete: () => void;
  handleMouseDown: (e: React.MouseEvent, action: string) => void;

  // Image handlers
  onImageLoad: (e: React.SyntheticEvent<HTMLImageElement>) => void;
  onImageError: (e: React.SyntheticEvent<HTMLImageElement>) => void;

  // Callbacks
  onCancel: () => void;
}

// Common aspect ratios
export const ASPECT_RATIOS: AspectRatio[] = [
  { label: "Free", value: undefined },
  { label: "1:1", value: 1 },
  { label: "4:3", value: 4 / 3 },
  { label: "3:4", value: 3 / 4 },
  { label: "16:9", value: 16 / 9 },
  { label: "9:16", value: 9 / 16 },
  { label: "3:2", value: 3 / 2 },
  { label: "2:3", value: 2 / 3 },
];
