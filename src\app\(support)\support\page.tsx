import { Icon } from "@iconify/react";
import Link from "next/link";

export default function SupportPage() {
  const supportOptions = [
    {
      icon: "lucide:message-circle",
      title: "Live Chat",
      description: "Get instant help from our support team",
      availability: "24/7 Available",
      action: "Start Chat",
      href: "#",
      color: "bg-green-100 text-green-600",
    },
    {
      icon: "lucide:mail",
      title: "Email Support",
      description: "Send us a detailed message about your issue",
      availability: "Response within 24 hours",
      action: "Send Email",
      href: "/contact",
      color: "bg-blue-100 text-blue-600",
    },
    {
      icon: "lucide:phone",
      title: "Phone Support",
      description: "Speak directly with our support team",
      availability: "Mon-Fri 9AM-6PM",
      action: "Call Now",
      href: "tel:+977-1-4567890",
      color: "bg-purple-100 text-purple-600",
    },
    {
      icon: "lucide:book-open",
      title: "Help Center",
      description: "Browse our comprehensive knowledge base",
      availability: "Self-service 24/7",
      action: "Browse Articles",
      href: "/help-center",
      color: "bg-orange-100 text-orange-600",
    },
  ];

  const quickHelp = [
    {
      category: "Account Issues",
      links: [
        { title: "Reset Password", href: "/forgot-password" },
        { title: "Update Profile", href: "/profile" },
        { title: "Account Verification", href: "/faqs#account" },
        { title: "Delete Account", href: "/settings" },
      ],
    },
    {
      category: "Orders & Payments",
      links: [
        { title: "Track My Order", href: "/profile/orders" },
        { title: "Payment Issues", href: "/faqs#payment" },
        { title: "Return Policy", href: "/faqs#returns" },
        { title: "Refund Status", href: "/profile/orders" },
      ],
    },
    {
      category: "Selling",
      links: [
        { title: "Become a Seller", href: "/postad" },
        { title: "Seller Dashboard", href: "/sales" },
        { title: "Product Guidelines", href: "/faqs#selling" },
        { title: "Fee Structure", href: "/terms" },
      ],
    },
    {
      category: "Technical",
      links: [
        { title: "Website Issues", href: "/faqs#technical" },
        { title: "Mobile App", href: "/faqs#app" },
        { title: "Browser Support", href: "/faqs#browser" },
        { title: "Report a Bug", href: "/contact" },
      ],
    },
  ];

  const emergencyContacts = [
    {
      title: "Report Fraud",
      description: "Suspicious activity or fraudulent listings",
      contact: "<EMAIL>",
      urgent: true,
    },
    {
      title: "Safety Concerns",
      description: "Report unsafe products or seller behavior",
      contact: "<EMAIL>",
      urgent: true,
    },
    {
      title: "Legal Issues",
      description: "Copyright, trademark, or legal matters",
      contact: "<EMAIL>",
      urgent: false,
    },
  ];

  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <div className="container-responsive spacing-responsive-lg">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            How Can We Help You?
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Our support team is here to assist you with any questions or issues.
            Choose the best way to get in touch with us.
          </p>
        </div>

        {/* Search Bar */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="relative">
            <Icon
              icon="lucide:search"
              className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"
            />
            <input
              type="text"
              placeholder="Search for help articles, FAQs, or topics..."
              className="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-transparent text-lg"
            />
          </div>
        </div>

        {/* Support Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {supportOptions.map((option, index) => (
            <div
              key={index}
              className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
            >
              <div
                className={`w-12 h-12 rounded-lg ${option.color} flex items-center justify-center mb-4`}
              >
                <Icon icon={option.icon} className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">
                {option.title}
              </h3>
              <p className="text-gray-600 mb-3 text-sm">{option.description}</p>
              <p className="text-xs text-gray-500 mb-4">
                {option.availability}
              </p>
              <Link
                href={option.href}
                className="block w-full bg-teal-600 text-white text-center py-2 rounded-lg hover:bg-teal-700 transition-colors font-medium"
              >
                {option.action}
              </Link>
            </div>
          ))}
        </div>

        {/* Quick Help Links */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {quickHelp.map((category, index) => (
            <div key={index} className="bg-white rounded-xl p-6 shadow-lg">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">
                {category.category}
              </h3>
              <ul className="space-y-2">
                {category.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      href={link.href}
                      className="text-teal-600 hover:text-teal-700 hover:underline text-sm"
                    >
                      → {link.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Emergency Contacts */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-16">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
            Emergency & Priority Support
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {emergencyContacts.map((contact, index) => (
              <div
                key={index}
                className={`p-6 rounded-lg border-2 ${
                  contact.urgent
                    ? "border-red-200 bg-red-50"
                    : "border-gray-200 bg-gray-50"
                }`}
              >
                <div className="flex items-center mb-3">
                  <h3 className="text-lg font-semibold text-gray-800">
                    {contact.title}
                  </h3>
                  {contact.urgent && (
                    <span className="ml-2 bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">
                      Urgent
                    </span>
                  )}
                </div>
                <p className="text-gray-600 text-sm mb-3">
                  {contact.description}
                </p>
                <a
                  href={`mailto:${contact.contact}`}
                  className="text-teal-600 hover:text-teal-700 font-medium text-sm"
                >
                  {contact.contact}
                </a>
              </div>
            ))}
          </div>
        </div>

        {/* Status & Updates */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">
              System Status
            </h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Website</span>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
                  <span className="text-green-600 font-medium">
                    Operational
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Payment System</span>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
                  <span className="text-green-600 font-medium">
                    Operational
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Mobile App</span>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-yellow-400 rounded-full mr-2"></div>
                  <span className="text-yellow-600 font-medium">
                    Maintenance
                  </span>
                </div>
              </div>
            </div>
            <Link
              href="/status"
              className="block mt-4 text-teal-600 hover:text-teal-700 hover:underline text-sm"
            >
              View detailed status →
            </Link>
          </div>

          <div className="bg-gradient-to-br from-teal-600 to-teal-700 rounded-xl p-8 text-white">
            <h2 className="text-2xl font-bold mb-4">Still Need Help?</h2>
            <p className="opacity-90 mb-6">
              Can&apos;t find what you&apos;re looking for? Our support team is
              ready to help you with any questions or concerns.
            </p>
            <div className="space-y-3">
              <Link
                href="/contact"
                className="block w-full bg-white text-teal-600 text-center py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                Contact Support Team
              </Link>
              <Link
                href="/community"
                className="block w-full border-2 border-white text-white text-center py-3 rounded-lg font-semibold hover:bg-white hover:text-teal-600 transition-colors"
              >
                Join Community Forum
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
