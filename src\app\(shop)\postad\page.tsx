import { PostAdsForm } from "@/components/dynamic";
import { Head<PERSON> } from "@/components";
import { AuthGuard } from "@/components/features/auth/AuthGuard";
import { ClientOnlyWrapper } from "@/components/layout/ClientOnlyWrapper";

export default function PostAdPage() {
  return (
    <ClientOnlyWrapper
      fallback={
        <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
          <div className="flex items-center justify-center min-h-screen">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#478085]"></div>
          </div>
        </div>
      }
    >
      <AuthGuard
        promptTitle="Post Your Ad"
        promptMessage="Please log in to post an advertisement"
      >
        <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
          <Header />
          <PostAdsForm />
        </div>
      </AuthGuard>
    </ClientOnlyWrapper>
  );
}
