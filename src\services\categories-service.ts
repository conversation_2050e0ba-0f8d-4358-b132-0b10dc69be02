import { apiClient, apiRequest, API_ENDPOINTS } from "@/lib/api";

/**
 * Category-related interfaces matching backend DTOs
 */
export interface SubcategoryResponseDto {
  id: string;
  name: string;
  slug: string;
  description?: string;
  iconUrl?: string;
  imageUrl?: string;
  isActive: boolean;
  sortOrder: number;
  categoryId: string;
  createdAt: string;
  updatedAt: string;
}

export interface CategoryResponseDto {
  id: string;
  name: string;
  slug: string;
  description?: string;
  iconUrl?: string;
  imageUrl?: string;
  isActive: boolean;
  sortOrder: number;
  subcategories: SubcategoryResponseDto[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateCategoryDto {
  name: string;
  slug?: string;
  description?: string;
  iconUrl?: string;
  imageUrl?: string;
  isActive?: boolean;
  sortOrder?: number;
}

export interface UpdateCategoryDto {
  name?: string;
  slug?: string;
  description?: string;
  iconUrl?: string;
  imageUrl?: string;
  isActive?: boolean;
  sortOrder?: number;
}

export interface CreateSubcategoryDto {
  name: string;
  slug?: string;
  description?: string;
  iconUrl?: string;
  imageUrl?: string;
  isActive?: boolean;
  sortOrder?: number;
  categoryId: string;
}

export interface UpdateSubcategoryDto {
  name?: string;
  slug?: string;
  description?: string;
  iconUrl?: string;
  imageUrl?: string;
  isActive?: boolean;
  sortOrder?: number;
  categoryId?: string;
}

/**
 * Categories Service
 * Handles all category-related API calls
 */
export class CategoriesService {
  /**
   * Get all categories with subcategories
   */
  static async getCategories(
    includeInactive?: boolean
  ): Promise<CategoryResponseDto[]> {
    const params = includeInactive ? { includeInactive } : {};

    return await apiRequest<CategoryResponseDto[]>(() =>
      apiClient.get(API_ENDPOINTS.CATEGORIES.LIST, { params })
    );
  }

  /**
   * Get category by ID
   */
  static async getCategoryById(id: string): Promise<CategoryResponseDto> {
    return await apiRequest<CategoryResponseDto>(() =>
      apiClient.get(API_ENDPOINTS.CATEGORIES.BY_ID(id))
    );
  }

  /**
   * Get category by slug
   */
  static async getCategoryBySlug(slug: string): Promise<CategoryResponseDto> {
    return await apiRequest<CategoryResponseDto>(() =>
      apiClient.get(API_ENDPOINTS.CATEGORIES.BY_SLUG(slug))
    );
  }

  /**
   * Get subcategories by category ID
   */
  static async getSubcategoriesByCategory(
    categoryId: string
  ): Promise<SubcategoryResponseDto[]> {
    return await apiRequest<SubcategoryResponseDto[]>(() =>
      apiClient.get(API_ENDPOINTS.CATEGORIES.SUBCATEGORIES(categoryId))
    );
  }

  /**
   * Get subcategory by ID
   */
  static async getSubcategoryById(id: string): Promise<SubcategoryResponseDto> {
    return await apiRequest<SubcategoryResponseDto>(() =>
      apiClient.get(API_ENDPOINTS.CATEGORIES.SUBCATEGORY_BY_ID(id))
    );
  }

  /**
   * Create new category (Admin only)
   */
  static async createCategory(
    data: CreateCategoryDto
  ): Promise<CategoryResponseDto> {
    return await apiRequest<CategoryResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.CATEGORIES.LIST, data)
    );
  }

  /**
   * Update category (Admin only)
   */
  static async updateCategory(
    id: string,
    data: UpdateCategoryDto
  ): Promise<CategoryResponseDto> {
    return await apiRequest<CategoryResponseDto>(() =>
      apiClient.patch(API_ENDPOINTS.CATEGORIES.BY_ID(id), data)
    );
  }

  /**
   * Delete category (Admin only)
   */
  static async deleteCategory(id: string): Promise<void> {
    return await apiRequest<void>(() =>
      apiClient.delete(API_ENDPOINTS.CATEGORIES.BY_ID(id))
    );
  }

  /**
   * Create new subcategory (Admin only)
   */
  static async createSubcategory(
    data: CreateSubcategoryDto
  ): Promise<SubcategoryResponseDto> {
    return await apiRequest<SubcategoryResponseDto>(() =>
      apiClient.post("/categories/subcategories", data)
    );
  }

  /**
   * Update subcategory (Admin only)
   */
  static async updateSubcategory(
    id: string,
    data: UpdateSubcategoryDto
  ): Promise<SubcategoryResponseDto> {
    return await apiRequest<SubcategoryResponseDto>(() =>
      apiClient.patch(API_ENDPOINTS.CATEGORIES.SUBCATEGORY_BY_ID(id), data)
    );
  }

  /**
   * Delete subcategory (Admin only)
   */
  static async deleteSubcategory(id: string): Promise<void> {
    return await apiRequest<void>(() =>
      apiClient.delete(API_ENDPOINTS.CATEGORIES.SUBCATEGORY_BY_ID(id))
    );
  }

  /**
   * Helper method to get category hierarchy for breadcrumbs
   */
  static async getCategoryHierarchy(
    categoryId: string,
    subcategoryId?: string
  ): Promise<{
    category: CategoryResponseDto;
    subcategory?: SubcategoryResponseDto;
  }> {
    const category = await this.getCategoryById(categoryId);

    if (subcategoryId) {
      const subcategory = await this.getSubcategoryById(subcategoryId);
      return { category, subcategory };
    }

    return { category };
  }

  /**
   * Helper method to get all categories in a flat structure for dropdowns
   */
  static async getFlatCategoriesForDropdown(): Promise<
    Array<{
      id: string;
      name: string;
      slug: string;
      type: "category" | "subcategory";
      parentId?: string;
      parentName?: string;
    }>
  > {
    const categories = await this.getCategories();
    const flatCategories: Array<{
      id: string;
      name: string;
      slug: string;
      type: "category" | "subcategory";
      parentId?: string;
      parentName?: string;
    }> = [];

    categories.forEach((category) => {
      // Add category
      flatCategories.push({
        id: category.id,
        name: category.name,
        slug: category.slug,
        type: "category",
      });

      // Add subcategories
      category.subcategories.forEach((subcategory) => {
        flatCategories.push({
          id: subcategory.id,
          name: subcategory.name,
          slug: subcategory.slug,
          type: "subcategory",
          parentId: category.id,
          parentName: category.name,
        });
      });
    });

    return flatCategories;
  }

  /**
   * Helper method to search categories and subcategories by name
   */
  static async searchCategories(searchTerm: string): Promise<{
    categories: CategoryResponseDto[];
    subcategories: SubcategoryResponseDto[];
  }> {
    const allCategories = await this.getCategories();
    const searchLower = searchTerm.toLowerCase();

    const matchingCategories = allCategories.filter(
      (category) =>
        category.name.toLowerCase().includes(searchLower) ||
        category.description?.toLowerCase().includes(searchLower)
    );

    const matchingSubcategories: SubcategoryResponseDto[] = [];
    allCategories.forEach((category) => {
      const subMatches = category.subcategories.filter(
        (subcategory) =>
          subcategory.name.toLowerCase().includes(searchLower) ||
          subcategory.description?.toLowerCase().includes(searchLower)
      );
      matchingSubcategories.push(...subMatches);
    });

    return {
      categories: matchingCategories,
      subcategories: matchingSubcategories,
    };
  }

  /**
   * Helper method to validate category/subcategory relationship
   */
  static async validateCategorySubcategoryRelation(
    categoryId: string,
    subcategoryId: string
  ): Promise<boolean> {
    try {
      const category = await this.getCategoryById(categoryId);
      return category.subcategories.some((sub) => sub.id === subcategoryId);
    } catch (error) {
      return false;
    }
  }

  /**
   * Helper method to get category statistics (if needed for admin)
   */
  static async getCategoryStatistics(): Promise<{
    totalCategories: number;
    totalSubcategories: number;
    activeCategories: number;
    inactiveCategories: number;
  }> {
    const categories = await this.getCategories(true); // Include inactive

    const totalCategories = categories.length;
    const activeCategories = categories.filter((cat) => cat.isActive).length;
    const inactiveCategories = totalCategories - activeCategories;
    const totalSubcategories = categories.reduce(
      (sum, cat) => sum + cat.subcategories.length,
      0
    );

    return {
      totalCategories,
      totalSubcategories,
      activeCategories,
      inactiveCategories,
    };
  }
}

export default CategoriesService;
