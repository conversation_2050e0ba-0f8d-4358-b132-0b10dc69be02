import { useState, useCallback } from "react";
import { getFallbackDataUrl } from "@/utils/image-utils";

interface UseImageHandlerOptions {
  fallbackSrc?: string;
  onError?: (error: React.SyntheticEvent<HTMLImageElement>) => void;
  onLoad?: () => void;
  enableLogging?: boolean;
}

interface UseImageHandlerReturn {
  handleImageError: (e: React.SyntheticEvent<HTMLImageElement>) => void;
  handleImageLoad: () => void;
  isLoading: boolean;
  hasError: boolean;
  resetState: () => void;
}

/**
 * Custom hook for handling image loading, error states, and fallbacks
 * Provides consistent image handling across all components
 */
export function useImageHandler(
  options: UseImageHandlerOptions = {}
): UseImageHandlerReturn {
  const {
    fallbackSrc = "/assets/images/placeholders/placeholder.jpg",
    onError,
    onLoad,
    enableLogging = process.env.NODE_ENV === "development",
  } = options;

  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleImageError = useCallback(
    (e: React.SyntheticEvent<HTMLImageElement>) => {
      const currentSrc = e.currentTarget.src;

      if (enableLogging) {
        // Provide more context for blob URL errors
        if (currentSrc.startsWith("blob:")) {
          console.warn(
            "Blob URL failed to load (this is expected behavior):",
            currentSrc
          );
        } else {
          console.warn("Image failed to load:", currentSrc);
          // Try to set a working fallback image
          if (e.currentTarget instanceof HTMLImageElement) {
            e.currentTarget.src = getFallbackDataUrl();
          }
        }
      }

      setIsLoading(false);
      setHasError(true);

      // Try fallback image, but if that also fails, use a data URL
      if (currentSrc !== fallbackSrc) {
        e.currentTarget.src = fallbackSrc;
      } else {
        // If even the fallback fails, use the utility function
        e.currentTarget.src = getFallbackDataUrl();
      }

      onError?.(e);
    },
    [fallbackSrc, onError, enableLogging]
  );

  const handleImageLoad = useCallback(() => {
    if (enableLogging) {
      console.log("Image loaded successfully");
    }

    setIsLoading(false);
    setHasError(false);

    onLoad?.();
  }, [onLoad, enableLogging]);

  const resetState = useCallback(() => {
    setIsLoading(true);
    setHasError(false);
  }, []);

  return {
    handleImageError,
    handleImageLoad,
    isLoading,
    hasError,
    resetState,
  };
}
