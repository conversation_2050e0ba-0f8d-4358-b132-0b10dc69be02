"use client";
import Link from "next/link";
import Image from "next/image";
import { useMobileNavigation } from "@/context/MobileNavigationContext";

// Logo Image Component
function SastoBazarLogo({
  className = "",
  width = 100,
  height = 20,
}: {
  className?: string;
  width?: number;
  height?: number;
}) {
  return (
    <Image
      src="/logo.png"
      alt=" Logo"
      width={width}
      height={height}
      className={className}
      priority
    />
  );
}

export function Logo() {
  const { toggleMobileMenu } = useMobileNavigation();

  return (
    <>
      {/* Desktop Logo - Regular Link */}
      <Link
        href="/"
        className="hidden md:flex text-3xl md:text-4xl lg:text-4xl xl:text-5xl font-bold text-white hover:text-gray-200 transition-all duration-200 flex-shrink-0 mr-2 md:mr-3 lg:mr-4 xl:mr-6 items-center gap-2 md:gap-2 lg:gap-3"
      >
        <SastoBazarLogo
          className="h-8 md:h-10 lg:h-12 xl:h-14 w-auto"
          width={200}
          height={32}
        />
      </Link>

      {/* Tablet Logo - Regular Link (for tablet breakpoint) */}
      <Link
        href="/"
        className="hidden sm:flex md:hidden text-xl font-bold text-white hover:text-gray-200 transition-all duration-200 flex-shrink-0 mr-3 items-center gap-2"
      >
        <SastoBazarLogo className="h-8 w-auto" width={160} height={32} />
      </Link>

      {/* Mobile Logo - Hamburger Menu Trigger */}
      <button
        onClick={toggleMobileMenu}
        className="sm:hidden flex items-center justify-center p-2 text-white hover:text-gray-200 hover:bg-white/10 transition-all duration-200 flex-shrink-0 rounded-xl min-w-[44px] min-h-[44px]"
        aria-label="Open navigation menu"
      >
        <SastoBazarLogo className="h-6 w-auto" width={120} height={24} />
      </button>
    </>
  );
}
