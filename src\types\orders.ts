/**
 * Order and Transaction Type Definitions
 * Matching backend DTOs for cart, order, payment, and transaction operations
 */

// ===== ENUMS =====

export enum CurrencyType {
  NPR = "NPR",
  USD = "USD",
}

export enum AddressType {
  HOME = "HOME",
  WORK = "WORK",
  OTHER = "OTHER",
}

export enum OrderStatus {
  PENDING = "pending",
  CONFIRMED = "confirmed",
  PROCESSING = "processing",
  SHIPPED = "shipped",
  DELIVERED = "delivered",
  CANCELLED = "cancelled",
  REFUNDED = "refunded",
}

export enum PaymentStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed",
  REFUNDED = "refunded",
}

export enum PaymentMethod {
  ESEWA = "esewa",
  KHALTI = "khalti",
  COD = "cod",
  BANK_TRANSFER = "bank_transfer",
}

export enum OrderItemStatus {
  PENDING = "pending",
  CONFIRMED = "confirmed",
  SHIPPED = "shipped",
  DELIVERED = "delivered",
  CANCELLED = "cancelled",
}

export enum TransactionType {
  AD_PAYMENT = "ad_payment",
  WALLET_TOPUP = "wallet_topup",
  WALLET_TRANSFER = "wallet_transfer",
  WITHDRAWAL = "withdrawal",
  REFUND = "refund",
  COMMISSION = "commission",
  SUBSCRIPTION_PAYMENT = "subscription_payment",
}

export enum TransactionStatus {
  PENDING = "pending",
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled",
  REFUNDED = "refunded",
}

export enum WalletTransactionType {
  CREDIT = "credit",
  DEBIT = "debit",
}

export enum PaymentMethodType {
  CARD = "card",
  BANK_ACCOUNT = "bank_account",
  EWALLET = "ewallet",
  QR_CODE = "qr_code",
}

export enum PaymentGateway {
  KHALTI = "khalti",
  ESEWA = "esewa",
  STRIPE = "stripe",
  PAYPAL = "paypal",
}

// ===== ADDRESS INTERFACES =====

export interface CreateAddressDto {
  type: AddressType;
  firstName: string;
  lastName: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone: string;
  isDefault?: boolean;
}

export interface UpdateAddressDto {
  type?: AddressType;
  firstName?: string;
  lastName?: string;
  company?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  phone?: string;
  isDefault?: boolean;
}

export interface AddressResponseDto {
  id: string;
  type: AddressType;
  firstName: string;
  lastName: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone: string;
  isDefault: boolean;
}

// ===== CART INTERFACES (Updated to match backend) =====

export interface CartItemResponseDto {
  id: string;
  advertisementId: string;
  productName: string;
  productImage?: string;
  price: number;
  quantity: number;
  maxQuantity: number;
  subtotal: number;
  sellerId: string;
  sellerName: string;
  isAvailable: boolean;
  addedAt: Date;
}

export interface CartResponseDto {
  id: string;
  userId: string;
  items: CartItemResponseDto[];
  totalAmount: number;
  totalItems: number;
  currency: CurrencyType;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface AddToCartDto {
  advertisementId: string;
  quantity: number; // Min: 1, Max: 100
}

export interface UpdateCartItemDto {
  quantity: number; // Min: 1, Max: 100
}

export interface BulkRemoveDto {
  itemIds: string[]; // Min: 1, Max: 50 items
}

// ===== CHECKOUT INTERFACES =====

export interface CheckoutDto {
  shippingAddressId: string;
  billingAddressId?: string;
  paymentMethod: PaymentMethod;
  notes?: string; // Max 500 characters
  couponCode?: string; // Max 50 characters
}

export interface CartValidationResponse {
  isValid: boolean;
  errors: string[];
}

// ===== ORDER INTERFACES =====

export interface OrderItemResponseDto {
  id: string;
  advertisementId: string;
  productName: string;
  productImage?: string;
  price: number;
  quantity: number;
  subtotal: number;
  sellerId: string;
  sellerName: string;
  status: OrderItemStatus;
}

export interface OrderResponseDto {
  id: string;
  orderNumber: string;
  userId: string;
  status: OrderStatus;
  items: OrderItemResponseDto[];
  shippingAddress: AddressResponseDto;
  billingAddress?: AddressResponseDto;
  paymentMethod: PaymentMethod;
  paymentStatus: PaymentStatus;
  subtotal: number;
  shippingCost: number;
  tax: number;
  discount: number;
  totalAmount: number;
  currency: CurrencyType;
  estimatedDelivery?: Date;
  trackingNumber?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  cancelledAt?: Date;
  deliveredAt?: Date;
}

export interface OrderFilterDto {
  status?: OrderStatus;
  dateFrom?: string; // ISO date string
  dateTo?: string; // ISO date string
  page?: number; // Default: 1
  limit?: number; // Default: 10
  sortBy?: string; // Default: 'createdAt'
  sortOrder?: "asc" | "desc"; // Default: 'desc'
}

export interface PaginatedOrdersResponse {
  orders: OrderResponseDto[];
  total: number;
  page: number;
  limit: number;
}

export interface OrderStatusUpdateDto {
  status: OrderStatus;
  trackingNumber?: string;
  notes?: string;
}

// ===== TRANSACTION INTERFACES =====

export interface CreateTransactionDto {
  type: TransactionType;
  amount: number; // Min: 0.01, Max decimal places: 2
  currency?: CurrencyType; // Default: NPR
  advertisementId?: string; // UUID, for ad-related transactions
  paymentMethod?: string; // Max 50 characters
  paymentGateway?: string; // Max 50 characters
  description?: string;
}

export interface TransactionResponseDto {
  id: string;
  userId: string;
  advertisementId?: string;
  type: TransactionType;
  amount: number;
  currency: CurrencyType;
  status: TransactionStatus;
  paymentMethod?: string;
  paymentGateway?: string;
  gatewayTransactionId?: string;
  description?: string;
  createdAt: Date;
  processedAt?: Date;
  formattedAmount: string; // e.g., "NPR 1,500"
}

export interface PaginatedTransactionsResponse {
  transactions: TransactionResponseDto[];
  total: number;
  page: number;
  limit: number;
}

// ===== WALLET INTERFACES =====

export interface WalletResponseDto {
  id: string;
  userId: string;
  balance: number;
  currency: CurrencyType;
  isActive: boolean;
  formattedBalance: string; // e.g., "NPR 2,500"
  createdAt: Date;
  updatedAt: Date;
}

export interface WalletTopupDto {
  amount: number; // Min: 0.01
  paymentMethod: string;
  paymentGateway?: string;
}

export interface WalletWithdrawDto {
  amount: number; // Min: 0.01
  withdrawalMethod: string;
  accountDetails?: Record<string, any>;
}

export interface WalletTransferDto {
  recipientId: string; // UUID
  amount: number; // Min: 0.01
  description?: string;
}

export interface WalletTransferResponse {
  senderTransaction: TransactionResponseDto;
  receiverTransaction: TransactionResponseDto;
}

export interface WalletTransactionResponseDto {
  id: string;
  walletId: string;
  type: WalletTransactionType;
  amount: number;
  balanceBefore: number;
  balanceAfter: number;
  description: string;
  referenceTransactionId?: string;
  createdAt: Date;
}

// ===== PAYMENT METHOD INTERFACES =====

export interface CreatePaymentMethodDto {
  type: PaymentMethodType;
  provider?: string;
  details: Record<string, any>; // Encrypted payment details
  isDefault?: boolean;
}

export interface PaymentMethodResponseDto {
  id: string;
  userId: string;
  type: PaymentMethodType;
  provider?: string;
  maskedDetails: Record<string, any>; // Masked/safe details for display
  isDefault: boolean;
  isActive: boolean;
  createdAt: Date;
}

// ===== UTILITY TYPES =====

export interface ApiErrorResponse {
  message: string;
  error?: string;
  statusCode: number;
  timestamp: string;
  path: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface BaseResponse {
  success: boolean;
  message?: string;
  timestamp: string;
}

// ===== FRONTEND-SPECIFIC INTERFACES =====

export interface CartSummary {
  totalItems: number;
  totalAmount: number;
  currency: string;
  availableItems: number;
  unavailableItems: number;
}

export interface OrderSummary {
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  totalSpent: number;
  currency: string;
}

export interface TransactionSummary {
  totalTransactions: number;
  totalAmount: number;
  pendingAmount: number;
  completedAmount: number;
  currency: string;
}

export interface CheckoutState {
  step: "address" | "payment" | "review" | "processing" | "complete";
  selectedAddress?: AddressResponseDto;
  selectedPaymentMethod?: PaymentMethod;
  orderData?: OrderResponseDto;
  loading: boolean;
  error?: string;
}
