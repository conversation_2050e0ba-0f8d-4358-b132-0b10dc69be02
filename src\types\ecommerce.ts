// Core data types for the e-commerce application

// User Profile interfaces
export interface UserProfile {
  id: string;
  username: string;
  email: string;
  phoneNumber?: string;
  profilePicture?: string;
  address: {
    city: string;
    country: string;
  };
  createdAt: string; // Store as ISO string for Redux serialization
  updatedAt: string; // Store as ISO string for Redux serialization
  isVerified: boolean;
  preferences?: {
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
    privacy: {
      showEmail: boolean;
      showPhone: boolean;
    };
  };
  // New properties for enhanced functionality
  savedListings?: string[]; // Array of product IDs
  totalListings?: number;
  totalSold?: number;
  avatar?: string;
  bio?: string;
}

export interface ProfileFormData {
  username: string;
  email: string;
  phoneNumber: string;
  city: string;
  profilePicture?: File | string;
}

export interface ProfileUpdateRequest {
  username?: string;
  email?: string;
  phoneNumber?: string;
  city?: string;
  profilePicture?: string;
}

export interface PublicProfileData {
  id: string;
  username: string;
  email?: string;
  phoneNumber?: string;
  profilePicture?: string;
  address?: {
    city: string;
    country: string;
  };
  city?: string; // City name (legacy support)
  state?: string; // State/Province (missing in your interface)
  country?: string; // Country (legacy support)
  createdAt: string; // Store as ISO string for Redux serialization
  updatedAt?: string; // Store as ISO string for Redux serialization
  lastLogin?: string; // Store as ISO string for Redux serialization
  isVerified: boolean;
  totalListings: number;
  totalSold: number;
  totalBought?: number;
  rating?: number;
  responseRate?: number;
  overallRating?: number;
  profileViews?: number;
  lastActive?: string;
  joinedDate?: string;
  bio?: string;
  ratings?: {
    asSeller: {
      average: number;
      total: number;
      breakdown: {
        5: number;
        4: number;
        3: number;
        2: number;
        1: number;
      };
    };
    asBuyer: {
      average: number;
      total: number;
      breakdown: {
        5: number;
        4: number;
        3: number;
        2: number;
        1: number;
      };
    };
  };
  recentActivity?: Array<{
    type: string;
    description: string;
    date: Date;
  }>;
  preferences?: {
    privacy: {
      showEmail: boolean;
      showPhone: boolean;
    };
  };
}

export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  message: string;
  productId?: string;
}

export interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  productId?: string;
  subject: string;
  content: string;
  createdAt: Date;
  isRead: boolean;
  messageType: "inquiry" | "offer" | "general" | "complaint";
}

// Saved listings interfaces
export interface SavedListing {
  id: string;
  userId: string;
  productId: string;
  product: Product;
  savedAt: Date;
  notes?: string;
}

export interface SavedListingsState {
  listings: SavedListing[];
  loading: boolean;
  error?: string;
}

// User listing management interfaces
export interface UserListing {
  id: string;
  title: string;
  condition: string;
  price: string;
  status: "active" | "sold" | "inactive" | "hold" | "expired";
  image: string;
  postedAt: string;
  views?: number;
  inquiries?: number;
  soldAt?: string;
  soldPrice?: string;
}

export interface ListingAction {
  type:
    | "mark_sold"
    | "mark_active"
    | "mark_inactive"
    | "delete"
    | "edit"
    | "promote";
  productId: string;
  data?: {
    soldPrice?: string;
    soldAt?: string;
    reason?: string;
    price?: string;
  };
}

export interface Subcategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  productCount: number;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  subcategories?: Subcategory[];
  productCount: number;
}

export interface Product {
  id: string;
  slug: string;
  title: string;
  description: string;
  price: number;
  currency: string;
  images: string[];
  category: string;
  subcategory?: string;
  location: string;
  seller: {
    id: string;
    name: string;
    avatar?: string;
    rating?: number;
  };
  condition: "new" | "used" | "refurbished" | "brand new";
  brand?: string;
  postedAt: string;
  delivery: {
    available: boolean;
    type?: "home" | "pickup" | "both";
    cost?: number;
  };
  featured: boolean;
  status: "active" | "sold" | "inactive" | "hold" | "expired";
  // Additional properties for enhanced UI
  originalPrice?: number;
  postedDate?: string;
  views?: number;
  features?: string[];
  additionalInfo?: string;
  // New properties for enhanced functionality
  soldAt?: string;
  soldPrice?: number;
  isSavedByUser?: boolean;
  saveCount?: number;
}

// Cart related interfaces
export interface CartItem {
  id: string;
  product: Product;
  quantity: number;
  addedAt: string;
}

export interface CartState {
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
  currency: string;
  loading: boolean;
  error?: string;
}

// Base filter types that are common across categories
export interface BaseFilterOptions {
  condition: Array<"new" | "used" | "refurbished">;
  location: string[];
  delivery: Array<"home" | "pickup">;
  postedWithin: "1day" | "3days" | "7days" | "30days" | "all";
  priceRange: {
    min: number;
    max: number;
  };
}

// Category-specific filter types
export interface CategorySpecificFilters {
  // Common filters
  category?: string[];
  type?: string[];
  brand?: string[];

  // Pet & Animal specific
  breed?: string[];
  gender?: Array<"male" | "female" | "unisex">;

  // Jewelry & Art & Crafts & Fashion specific
  materials?: string[];

  // IT & Computer & Fashion specific
  size?: string[];

  // Jobs specific
  salaryRange?: {
    min?: number;
    max?: number;
  };

  // Vehicles specific
  yearRange?: {
    min?: number;
    max?: number;
  };

  // Travel specific
  destinationType?: string[];
  travelType?: string[];
  duration?: string[];

  // Property specific
  purpose?: Array<"rent" | "sale">;
}

export interface FilterOptions
  extends BaseFilterOptions,
    CategorySpecificFilters {}

export interface ActiveFilters
  extends Partial<BaseFilterOptions>,
    Partial<CategorySpecificFilters> {}

// Filter field configuration for dynamic rendering
export interface FilterFieldConfig {
  key: keyof FilterOptions;
  label: string;
  type:
    | "select"
    | "multiselect"
    | "range"
    | "priceRange"
    | "yearRange"
    | "salaryRange";
  options?: string[] | { value: string; label: string }[];
  placeholder?: string;
  required?: boolean;
}

// Category-specific filter configuration
export interface CategoryFilterConfig {
  categoryId: string;
  filters: FilterFieldConfig[];
  availableOptions: Partial<FilterOptions>;
}

export interface SortOption {
  value: string;
  label: string;
}

export type SortBy =
  | "newest"
  | "oldest"
  | "price-low"
  | "price-high"
  | "relevance"
  | "top-review";

export interface SearchState {
  query: string;
  category: string;
  location: string;
  results: Product[];
  loading: boolean;
  error?: string;
}

export interface CategoryState {
  selectedCategory: string | null;
  showFilters: boolean;
  categories: Category[];
  loading: boolean;
  error?: string;
}

export interface FilterState {
  activeFilters: ActiveFilters;
  availableFilters: FilterOptions;
  loading: boolean;
  error?: string;
}

export interface ProductState {
  products: Product[];
  filteredProducts: Product[];
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  sortBy: SortBy;
  loading: boolean;
  error?: string;
}

// Context state interface
export interface EcommerceContextState {
  search: SearchState;
  category: CategoryState;
  filter: FilterState;
  product: ProductState;
  cart: CartState;
}

// Action types for context reducer
export type EcommerceAction =
  | { type: "SET_SEARCH_QUERY"; payload: string }
  | { type: "SET_SEARCH_CATEGORY"; payload: string }
  | { type: "SET_SEARCH_LOCATION"; payload: string }
  | { type: "SET_SEARCH_RESULTS"; payload: Product[] }
  | { type: "SET_SEARCH_LOADING"; payload: boolean }
  | { type: "SET_SEARCH_ERROR"; payload: string | undefined }
  | { type: "SELECT_CATEGORY"; payload: string | null }
  | { type: "TOGGLE_FILTERS"; payload: boolean }
  | { type: "SET_CATEGORIES"; payload: Category[] }
  | { type: "SET_CATEGORY_LOADING"; payload: boolean }
  | { type: "SET_CATEGORY_ERROR"; payload: string | undefined }
  | { type: "SET_ACTIVE_FILTERS"; payload: ActiveFilters }
  | { type: "CLEAR_FILTERS" }
  | { type: "SET_AVAILABLE_FILTERS"; payload: FilterOptions }
  | { type: "SET_FILTER_LOADING"; payload: boolean }
  | { type: "SET_FILTER_ERROR"; payload: string | undefined }
  | { type: "SET_PRODUCTS"; payload: Product[] }
  | { type: "SET_FILTERED_PRODUCTS"; payload: Product[] }
  | { type: "SET_CURRENT_PAGE"; payload: number }
  | { type: "SET_SORT_BY"; payload: SortBy }
  | { type: "SET_PRODUCT_LOADING"; payload: boolean }
  | { type: "SET_PRODUCT_ERROR"; payload: string | undefined }
  | { type: "ADD_TO_CART"; payload: { product: Product; quantity: number } }
  | { type: "REMOVE_FROM_CART"; payload: string }
  | { type: "UPDATE_CART_QUANTITY"; payload: { id: string; quantity: number } }
  | { type: "CLEAR_CART" }
  | { type: "SET_CART_LOADING"; payload: boolean }
  | { type: "SET_CART_ERROR"; payload: string | undefined };

// Utility types
export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface EcommerceApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  pagination?: PaginationInfo;
}
