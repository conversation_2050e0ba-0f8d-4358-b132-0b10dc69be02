"use client";

import React, { useState } from "react";
import Image from "next/image";
import { Icon } from "@iconify/react";
import { StatusBadge } from "./shared/StatusBadge";
import { ListingActionDropdown } from "./shared/ListingActionDropdown";
import { MarkAsSoldDialog } from "./shared/MarkAsSoldDialog";
import { ChangePriceDialog } from "./shared/ChangePriceDialog";
import type { UserListing, ListingAction } from "@/types/ecommerce";

interface AdPostCardProps {
  listing: UserListing;
  onAction: (action: ListingAction) => void;
}

export default function AdPostCard({ listing, onAction }: AdPostCardProps) {
  const [showMarkSoldDialog, setShowMarkSoldDialog] = useState(false);
  const [showChangePriceDialog, setShowChangePriceDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-300 h-96 flex flex-col">
        {/* Image Section */}
        <div className="relative flex-shrink-0">
          <Image
            src={listing.image}
            alt={listing.title}
            width={400}
            height={250}
            className="w-full h-48 object-cover"
          />

          {/* Status Badge */}
          <div className="absolute top-3 left-3">
            <StatusBadge status={listing.status} />
          </div>

          {/* Three Dot Menu */}
          <div className="absolute top-3 right-3">
            <ListingActionDropdown
              listing={listing}
              onAction={onAction}
              onMarkSoldClick={() => setShowMarkSoldDialog(true)}
              onChangePriceClick={() => setShowChangePriceDialog(true)}
            />
          </div>
        </div>

        {/* Content Section */}
        <div className="p-4 flex-1 flex flex-col justify-between">
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 mb-2 text-lg line-clamp-2">
              {listing.title}
            </h3>

            <div className="flex items-center gap-2 mb-2">
              <span className="text-green-600 font-medium">Like New</span>
            </div>

            <div className="flex items-center gap-2 text-md text-gray-600 mb-3">
              <Icon icon="lucide:calendar" className="w-4 h-4" />
              <span>
                Posted {new Date(listing.postedAt).toLocaleDateString()}
              </span>
            </div>
          </div>

          <div className="flex items-center justify-between mt-auto">
            <span className="text-xl font-bold text-gray-900">
              {listing.price}
            </span>

            <div className="flex items-center gap-4 text-md text-gray-600">
              <div className="flex items-center gap-1">
                <Icon icon="lucide:eye" className="w-4 h-4" />
                <span>{listing.views || 0} views</span>
              </div>
              <div className="flex items-center gap-1">
                <Icon icon="lucide:message-circle" className="w-4 h-4" />
                <span>{listing.inquiries || 0} inquiries</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mark as Sold Dialog */}
      <MarkAsSoldDialog
        open={showMarkSoldDialog}
        onOpenChange={setShowMarkSoldDialog}
        listing={listing}
        onAction={onAction}
        isLoading={isLoading}
        onLoadingChange={setIsLoading}
      />

      {/* Change Price Dialog */}
      <ChangePriceDialog
        open={showChangePriceDialog}
        onOpenChange={setShowChangePriceDialog}
        listing={listing}
        onAction={onAction}
        isLoading={isLoading}
        onLoadingChange={setIsLoading}
      />
    </>
  );
}
