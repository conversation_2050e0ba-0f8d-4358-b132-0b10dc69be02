"use client";

import React from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface ProductDetailErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ProductDetailErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

class ProductDetailErrorBoundary extends React.Component<
  ProductDetailErrorBoundaryProps,
  ProductDetailErrorBoundaryState
> {
  constructor(props: ProductDetailErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ProductDetailErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("ProductDetailErrorBoundary caught an error:", error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return (
          <FallbackComponent 
            error={this.state.error!} 
            retry={this.handleRetry} 
          />
        );
      }

      return (
        <div className="min-h-[400px] flex items-center justify-center p-8">
          <div className="text-center max-w-md">
            <Icon 
              icon="lucide:alert-triangle" 
              className="h-16 w-16 text-red-500 mx-auto mb-4" 
            />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Something went wrong
            </h2>
            <p className="text-gray-600 mb-6">
              We encountered an error while loading the product details. 
              Please try refreshing the page or contact support if the problem persists.
            </p>
            <div className="space-y-3">
              <Button 
                onClick={this.handleRetry}
                className="w-full"
              >
                <Icon icon="lucide:refresh-cw" className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              <Button 
                variant="outline"
                onClick={() => window.location.reload()}
                className="w-full"
              >
                <Icon icon="lucide:rotate-ccw" className="h-4 w-4 mr-2" />
                Refresh Page
              </Button>
            </div>
            
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <Alert className="mt-6 text-left">
                <Icon icon="lucide:bug" className="h-4 w-4" />
                <AlertDescription>
                  <details className="mt-2">
                    <summary className="cursor-pointer font-medium">
                      Error Details (Development Only)
                    </summary>
                    <pre className="mt-2 text-xs overflow-auto bg-gray-100 p-2 rounded">
                      {this.state.error.message}
                      {this.state.error.stack}
                    </pre>
                  </details>
                </AlertDescription>
              </Alert>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook for handling API errors in functional components
export const useProductDetailErrorHandler = () => {
  const handleError = (error: any, context: string) => {
    console.error(`Product Detail Error in ${context}:`, error);
    
    // You can add error reporting service here
    // Example: reportError(error, context);
    
    return {
      message: getErrorMessage(error),
      canRetry: isRetryableError(error),
    };
  };

  return { handleError };
};

// Helper functions for error handling
const getErrorMessage = (error: any): string => {
  if (error?.data?.message) {
    return error.data.message;
  }
  
  if (error?.message) {
    return error.message;
  }
  
  if (error?.status) {
    switch (error.status) {
      case 404:
        return "Product not found";
      case 403:
        return "Access denied";
      case 500:
        return "Server error occurred";
      case 'FETCH_ERROR':
        return "Network connection error";
      case 'TIMEOUT_ERROR':
        return "Request timed out";
      default:
        return "An unexpected error occurred";
    }
  }
  
  return "An unexpected error occurred";
};

const isRetryableError = (error: any): boolean => {
  if (error?.status) {
    // Retry on server errors and network errors
    return [500, 502, 503, 504, 'FETCH_ERROR', 'TIMEOUT_ERROR'].includes(error.status);
  }
  
  return true; // Default to retryable
};

// Specific error fallback components
export const ReviewsErrorFallback: React.FC<{ error: Error; retry: () => void }> = ({ 
  error, 
  retry 
}) => (
  <Alert>
    <Icon icon="lucide:alert-circle" className="h-4 w-4" />
    <AlertDescription>
      <div className="flex items-center justify-between">
        <span>Failed to load reviews: {getErrorMessage(error)}</span>
        <Button variant="outline" size="sm" onClick={retry}>
          Retry
        </Button>
      </div>
    </AlertDescription>
  </Alert>
);

export const QAErrorFallback: React.FC<{ error: Error; retry: () => void }> = ({ 
  error, 
  retry 
}) => (
  <Alert>
    <Icon icon="lucide:alert-circle" className="h-4 w-4" />
    <AlertDescription>
      <div className="flex items-center justify-between">
        <span>Failed to load Q&A: {getErrorMessage(error)}</span>
        <Button variant="outline" size="sm" onClick={retry}>
          Retry
        </Button>
      </div>
    </AlertDescription>
  </Alert>
);

export const SellerInfoErrorFallback: React.FC<{ error: Error; retry: () => void }> = ({ 
  error, 
  retry 
}) => (
  <Alert>
    <Icon icon="lucide:alert-circle" className="h-4 w-4" />
    <AlertDescription>
      <div className="flex items-center justify-between">
        <span>Failed to load seller info: {getErrorMessage(error)}</span>
        <Button variant="outline" size="sm" onClick={retry}>
          Retry
        </Button>
      </div>
    </AlertDescription>
  </Alert>
);

export default ProductDetailErrorBoundary;
