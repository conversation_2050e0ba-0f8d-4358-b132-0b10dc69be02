import React from "react"
import { Icon } from "@iconify/react"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { SectionHeader } from "../components/SectionHeader"
import { type FormData } from "../types"

interface AdditionalSettingsSectionProps {
  formData: FormData
  updateFormData: (updates: Partial<FormData>) => void
}

export const AdditionalSettingsSection: React.FC<AdditionalSettingsSectionProps> = ({
  formData,
  updateFormData,
}) => {
  return (
    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
      <CardHeader className="pb-4">
        <SectionHeader
          icon={<Icon icon="lucide:settings" className="w-6 h-6 text-white" />}
          title="Additional Settings"
          subtitle="Optional features to enhance your listing"
        />
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="quantity" className="text-sm font-medium text-gray-700">
                Quantity Available
              </Label>
              <Input
                id="quantity"
                type="number"
                min="1"
                placeholder="1"
                value={formData.inventoryQuantity}
                onChange={(e) =>
                  updateFormData({
                    inventoryQuantity: Number.parseInt(e.target.value) || 1,
                  })
                }
                className="h-12 border-2 border-gray-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 rounded-xl"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="youtubeUrl" className="text-sm font-medium text-gray-700">
                YouTube Video URL (optional)
              </Label>
              <Input
                id="youtubeUrl"
                type="url"
                placeholder="https://youtube.com/watch?v=..."
                value={formData.youtubeVideoUrl || ""}
                onChange={(e) => updateFormData({ youtubeVideoUrl: e.target.value })}
                className="h-12 border-2 border-gray-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 rounded-xl"
              />
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
              <Icon icon="lucide:star" className="w-5 h-5 text-yellow-500" />
              Promotion Options
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border-2 border-gray-200 rounded-xl hover:border-yellow-300 transition-colors">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    id="featured"
                    checked={formData.isFeatured}
                    onCheckedChange={(checked) => updateFormData({ isFeatured: !!checked })}
                    className="data-[state=checked]:bg-yellow-500 data-[state=checked]:border-yellow-500"
                  />
                  <div className="flex-1">
                    <Label htmlFor="featured" className="text-sm font-medium text-gray-700 cursor-pointer">
                      Featured Ad
                    </Label>
                    <p className="text-xs text-gray-500">Get more visibility with premium placement</p>
                  </div>
                  <Icon icon="lucide:star" className="w-5 h-5 text-yellow-500" />
                </div>
              </div>
              <div className="p-4 border-2 border-gray-200 rounded-xl hover:border-red-300 transition-colors">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    id="urgent"
                    checked={formData.isUrgent}
                    onCheckedChange={(checked) => updateFormData({ isUrgent: !!checked })}
                    className="data-[state=checked]:bg-red-500 data-[state=checked]:border-red-500"
                  />
                  <div className="flex-1">
                    <Label htmlFor="urgent" className="text-sm font-medium text-gray-700 cursor-pointer">
                      Urgent Ad
                    </Label>
                    <p className="text-xs text-gray-500">Mark as urgent for faster responses</p>
                  </div>
                  <Icon icon="lucide:zap" className="w-5 h-5 text-red-500" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
