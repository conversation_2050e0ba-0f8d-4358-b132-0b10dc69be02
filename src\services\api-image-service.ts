/**
 * API Image Service - Handles images from the backend API
 * Provides fallbacks and ensures reliable image loading
 */

import type { AdImageDto } from "@/types/advertisement";
import { getFallbackDataUrl } from "@/utils/image-utils";

// Reliable fallback images from Unsplash (always available)
const FALLBACK_IMAGES = {
  general: [
    "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=600&fit=crop&crop=center",
    "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=800&h=600&fit=crop&crop=center",
    "https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=800&h=600&fit=crop&crop=center"
  ],
  electronics: [
    "https://images.unsplash.com/photo-1498049794561-7780e7231661?w=800&h=600&fit=crop&crop=center",
    "https://images.unsplash.com/photo-1593642632823-8f785ba67e45?w=800&h=600&fit=crop&crop=center",
    "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=800&h=600&fit=crop&crop=center"
  ],
  vehicles: [
    "https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=800&h=600&fit=crop&crop=center",
    "https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=800&h=600&fit=crop&crop=center",
    "https://images.unsplash.com/photo-1494976388531-d1058494cdd8?w=800&h=600&fit=crop&crop=center"
  ],
  fashion: [
    "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop&crop=center",
    "https://images.unsplash.com/photo-1445205170230-053b83016050?w=800&h=600&fit=crop&crop=center",
    "https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=800&h=600&fit=crop&crop=center"
  ],
  home: [
    "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop&crop=center",
    "https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800&h=600&fit=crop&crop=center",
    "https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800&h=600&fit=crop&crop=center"
  ]
};

/**
 * Get fallback images for a category
 */
export function getCategoryFallbackImages(category?: string): string[] {
  const categoryKey = category?.toLowerCase();
  
  if (categoryKey && categoryKey in FALLBACK_IMAGES) {
    return FALLBACK_IMAGES[categoryKey as keyof typeof FALLBACK_IMAGES];
  }
  
  return FALLBACK_IMAGES.general;
}

/**
 * Process API images - only return real images, no fallbacks
 */
export function processApiImages(
  apiImages: AdImageDto[] | undefined,
  category?: string,
  productTitle?: string
): string[] {
  // Only return real API images if they exist
  if (apiImages && apiImages.length > 0) {
    const validImages = apiImages
      .filter(img => img.imageUrl && img.imageUrl.trim() !== '')
      .map(img => img.imageUrl);

    return validImages;
  }

  // Return empty array if no real images
  return [];
}

/**
 * Get primary image from API images - return null if no real image
 */
export function getPrimaryImage(
  apiImages: AdImageDto[] | undefined,
  category?: string
): string | null {
  if (apiImages && apiImages.length > 0) {
    // Try to find primary image
    const primaryImage = apiImages.find(img => img.isPrimary);
    if (primaryImage?.imageUrl) {
      return primaryImage.imageUrl;
    }

    // Fallback to first image
    const firstImage = apiImages[0];
    if (firstImage?.imageUrl) {
      return firstImage.imageUrl;
    }
  }

  // Return null if no real images
  return null;
}

/**
 * Validate if an image URL is accessible
 */
export function validateImageUrl(url: string): Promise<boolean> {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
    
    // Timeout after 5 seconds
    setTimeout(() => resolve(false), 5000);
  });
}

/**
 * Get a working image URL - return null if invalid
 */
export async function getWorkingImageUrl(
  url: string,
  category?: string
): Promise<string | null> {
  const isValid = await validateImageUrl(url);

  if (isValid) {
    return url;
  }

  // Return null if invalid
  return null;
}

/**
 * Create image configuration for a product - no fallbacks
 */
export function createProductImageConfig(
  apiImages: AdImageDto[] | undefined,
  category?: string,
  productTitle?: string
) {
  const processedImages = processApiImages(apiImages, category, productTitle);
  const primaryImage = getPrimaryImage(apiImages, category);

  return {
    primary: primaryImage, // Can be null
    gallery: processedImages, // Can be empty array
    thumbnail: primaryImage, // Can be null
    count: processedImages.length,
    hasApiImages: apiImages && apiImages.length > 0,
    hasValidImages: processedImages.length > 0,
  };
}

/**
 * Get thumbnail URL from API image
 */
export function getThumbnailUrl(apiImage: AdImageDto): string {
  return apiImage.thumbnailUrl || apiImage.imageUrl;
}

/**
 * Sort API images by sort order and primary status
 */
export function sortApiImages(apiImages: AdImageDto[]): AdImageDto[] {
  return [...apiImages].sort((a, b) => {
    // Primary images first
    if (a.isPrimary && !b.isPrimary) return -1;
    if (!a.isPrimary && b.isPrimary) return 1;
    
    // Then by sort order
    return a.sortOrder - b.sortOrder;
  });
}

/**
 * Handle image loading errors - hide image instead of fallback
 */
export function handleImageError(
  event: React.SyntheticEvent<HTMLImageElement>,
  category?: string,
  productTitle?: string
) {
  const target = event.target as HTMLImageElement;
  const currentSrc = target.src;

  console.warn('Image failed to load:', currentSrc);

  // Hide the image by setting display none
  target.style.display = 'none';

  // Also hide the parent container if it has a specific class
  const parent = target.parentElement;
  if (parent && parent.classList.contains('image-container')) {
    parent.style.display = 'none';
  }
}

/**
 * Preload images for better performance
 */
export function preloadImages(urls: string[]): Promise<void[]> {
  const promises = urls.map(url => {
    return new Promise<void>((resolve) => {
      const img = new Image();
      img.onload = () => resolve();
      img.onerror = () => resolve(); // Resolve even on error
      img.src = url;
    });
  });
  
  return Promise.all(promises);
}

export default {
  processApiImages,
  getPrimaryImage,
  createProductImageConfig,
  handleImageError,
  preloadImages,
  validateImageUrl,
  getWorkingImageUrl,
};
