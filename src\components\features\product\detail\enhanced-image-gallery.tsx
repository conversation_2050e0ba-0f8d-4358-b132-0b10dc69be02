"use client";

import { useState } from "react";
import { SmartImage } from "@/components/common/SmartImage";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Product } from "@/types/ecommerce";
import { validateImageUrl } from "@/utils/image-utils";

interface EnhancedImageGalleryProps {
  product: Product;
  className?: string;
}

const generatePlaceholderUrl = (
  width: number,
  height: number,
  text: string
) => {
  return `https://via.placeholder.com/${width}x${height}?text=${encodeURIComponent(
    text
  )}`;
};

const isPlaceholderImage = (url: string) => {
  return url.startsWith("https://via.placeholder.com/");
};

export function EnhancedImageGallery({
  product,
  className = "",
}: EnhancedImageGalleryProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isZoomed, setIsZoomed] = useState(false);
  const [zoomPosition, setZoomPosition] = useState({ x: 0, y: 0 });

  const images = (product.images || []).map(validateImageUrl);
  const productTitle = product.title;
  const currentImage =
    images[selectedImageIndex] ||
    generatePlaceholderUrl(800, 600, productTitle);
  const isPlaceholder = isPlaceholderImage(currentImage);

  const handlePrevious = () => {
    const newIndex =
      selectedImageIndex > 0 ? selectedImageIndex - 1 : images.length - 1;
    setSelectedImageIndex(newIndex);
  };

  const handleNext = () => {
    const newIndex =
      selectedImageIndex < images.length - 1 ? selectedImageIndex + 1 : 0;
    setSelectedImageIndex(newIndex);
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isZoomed) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;
    setZoomPosition({ x, y });
  };

  const toggleZoom = () => {
    setIsZoomed(!isZoomed);
  };

  const openFullscreen = () => {
    setIsFullscreen(true);
  };

  const closeFullscreen = () => {
    setIsFullscreen(false);
    setIsZoomed(false);
  };

  return (
    <>
      {/* Main Image Gallery */}
      <div className={`space-y-4 ${className}`}>
        {/* Main Image with Navigation */}
        <div className="relative bg-gray-100 rounded-lg overflow-hidden aspect-[3/1] group">
          <div
            className={`relative w-full h-full cursor-${
              isZoomed ? "zoom-out" : "zoom-in"
            }`}
            onMouseMove={handleMouseMove}
            onClick={toggleZoom}
          >
            <SmartImage
              src={
                isPlaceholder
                  ? generatePlaceholderUrl(800, 600, productTitle)
                  : currentImage
              }
              alt={productTitle}
              fill
              className={`object-cover transition-transform duration-300 ${
                isZoomed ? "scale-150" : "scale-100"
              }`}
              style={
                isZoomed
                  ? {
                      transformOrigin: `${zoomPosition.x}% ${zoomPosition.y}%`,
                    }
                  : {}
              }
            />
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={handlePrevious}
            className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 hover:bg-white rounded-full p-2 shadow-lg transition-all opacity-0 group-hover:opacity-100"
          >
            <Icon icon="lucide:chevron-left" className="w-5 h-5" />
          </button>

          <button
            onClick={handleNext}
            className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 hover:bg-white rounded-full p-2 shadow-lg transition-all opacity-0 group-hover:opacity-100"
          >
            <Icon icon="lucide:chevron-right" className="w-5 h-5" />
          </button>

          {/* Action Buttons */}
          <div className="absolute top-4 right-4 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              size="sm"
              variant="secondary"
              onClick={toggleZoom}
              className="bg-white/90 hover:bg-white"
            >
              <Icon icon="lucide:zoom-in" className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="secondary"
              onClick={openFullscreen}
              className="bg-white/90 hover:bg-white"
            >
              <Icon icon="lucide:maximize-2" className="w-4 h-4" />
            </Button>
          </div>

          {/* Image Counter */}
          <div className="absolute bottom-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium">
            {selectedImageIndex + 1}/{images.length}
          </div>
        </div>

        {/* Thumbnail Gallery */}
        <div className="flex gap-3 justify-center overflow-x-auto">
          {images.map((imagePath, index) => {
            const isPlaceholder = isPlaceholderImage(imagePath);
            return (
              <button
                key={index}
                onClick={() => setSelectedImageIndex(index)}
                className={`flex-shrink-0 aspect-[4/3] w-20 bg-gray-100 rounded-lg overflow-hidden border-2 transition-all ${
                  selectedImageIndex === index
                    ? "border-blue-500 ring-2 ring-blue-200"
                    : "border-gray-200 hover:border-gray-300"
                }`}
              >
                <SmartImage
                  src={
                    isPlaceholder
                      ? generatePlaceholderUrl(
                          80,
                          60,
                          `${productTitle} ${index + 1}`
                        )
                      : imagePath
                  }
                  alt={`${productTitle} ${index + 1}`}
                  width={80}
                  height={60}
                  className="w-full h-full object-cover"
                />
              </button>
            );
          })}
        </div>
      </div>

      {/* Fullscreen Modal */}
      {isFullscreen && (
        <div className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center">
          <div className="relative w-full h-full flex items-center justify-center p-4">
            {/* Close Button */}
            <button
              onClick={closeFullscreen}
              className="absolute top-4 right-4 z-10 bg-white/20 hover:bg-white/30 rounded-full p-2 text-white transition-colors"
            >
              <Icon icon="lucide:x" className="w-6 h-6" />
            </button>

            {/* Navigation Arrows */}
            <button
              onClick={handlePrevious}
              className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 rounded-full p-3 text-white transition-colors"
            >
              <Icon icon="lucide:chevron-left" className="w-6 h-6" />
            </button>

            <button
              onClick={handleNext}
              className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 rounded-full p-3 text-white transition-colors"
            >
              <Icon icon="lucide:chevron-right" className="w-6 h-6" />
            </button>

            {/* Main Image */}
            <div className="relative max-w-4xl max-h-full">
              <SmartImage
                src={
                  isPlaceholder
                    ? generatePlaceholderUrl(1200, 800, productTitle)
                    : currentImage
                }
                alt={productTitle}
                width={1200}
                height={800}
                className="max-w-full max-h-full object-contain"
              />
            </div>

            {/* Image Counter */}
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-white/20 text-white px-4 py-2 rounded-full text-sm font-medium">
              {selectedImageIndex + 1} of {images.length}
            </div>
          </div>
        </div>
      )}
    </>
  );
}

// Default export for dynamic imports
export default EnhancedImageGallery;
