"use client";

import { useState } from "react";

import { Icon } from "@iconify/react";

interface StarRatingSectionProps {
  rating: number;
  onRatingChange: (rating: number) => void;
}

export default function StarRatingSection({
  rating,
  onRatingChange,
}: StarRatingSectionProps) {
  const [hoveredRating, setHoveredRating] = useState(0);

  const handleRatingClick = (newRating: number) => {
    onRatingChange(newRating);
  };

  const handleRatingHover = (newRating: number) => {
    setHoveredRating(newRating);
  };

  const handleRatingLeave = () => {
    setHoveredRating(0);
  };

  const displayRating = hoveredRating || rating;

  return (
    <div>
      <h2 className="text-lg font-medium text-gray-900 mb-4">Your Rating</h2>
      <div className="flex items-center gap-2">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => handleRatingClick(star)}
            onMouseEnter={() => handleRatingHover(star)}
            onMouseLeave={handleRatingLeave}
            className="p-1 transition-transform hover:scale-110"
          >
            <Icon
              icon="tabler:star"
              className={`w-8 h-8 transition-colors ${
                star <= displayRating
                  ? "fill-yellow-400 text-yellow-400"
                  : "fill-gray-200 text-gray-200 hover:fill-yellow-200 hover:text-yellow-200"
              }`}
            />
          </button>
        ))}
      </div>
    </div>
  );
}
