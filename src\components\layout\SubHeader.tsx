"use client";

import React, { memo } from "react";
import type { SortBy } from "@/types/ecommerce";

interface SubHeaderProps {
  sortBy: SortBy;
  onSortChange: (sortBy: SortBy) => void;
  isProductDetailActive: boolean;
}

const SubHeader = memo(function SubHeader({
  sortBy: _sortBy,
  onSortChange: _onSortChange,
  isProductDetailActive,
}: SubHeaderProps) {
  if (isProductDetailActive) return null;

  return (
    <div className="w-full mx-auto mt-2 sm:mt-4 -mb-2 sm:-mb-4">
      <div className="mx-4 sm:mx-6 lg:mx-[2%] xl:mx-[3%] 2xl:mx-[5%] flex items-center justify-end">
        {/* Right: Sort Dropdown */}
      </div>
    </div>
  );
});

export default SubHeader;
