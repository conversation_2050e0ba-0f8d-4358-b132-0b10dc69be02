"use client";

import { memo } from "react";
import { Icon } from "@iconify/react";

interface EmptyStateProps {
  title?: string;
  description?: string;
  icon?: "search" | "filter" | "none" | "alert-circle";
  className?: string;
  children?: React.ReactNode;
}

const EmptyState = memo(function EmptyState({
  title = "No items found",
  description = "Try adjusting your search or filter criteria",
  icon = "search",
  className = "",
  children,
}: EmptyStateProps) {
  const renderIcon = () => {
    if (icon === "none") return null;

    return (
      <div className="mb-4">
        {icon === "search" ? (
          <Icon
            icon="lucide:search"
            className="h-12 w-12 text-gray-300 mx-auto"
            aria-hidden="true"
          />
        ) : icon === "filter" ? (
          <Icon
            icon="lucide:filter"
            className="h-12 w-12 text-gray-300 mx-auto"
            aria-hidden="true"
          />
        ) : icon === "alert-circle" ? (
          <Icon
            icon="lucide:alert-circle"
            className="h-12 w-12 text-gray-300 mx-auto"
            aria-hidden="true"
          />
        ) : null}
      </div>
    );
  };

  return (
    <div
      className={`text-center py-12 px-6 md:px-8 pt-2 md:pt-4 pb-6 md:pb-8 ${className}`}
    >
      {renderIcon()}
      <div className="text-gray-500 mb-4 text-lg font-medium">{title}</div>
      <p className="text-sm text-gray-400 max-w-md mx-auto">{description}</p>
      {children && <div className="mt-6">{children}</div>}
    </div>
  );
});

export default EmptyState;
