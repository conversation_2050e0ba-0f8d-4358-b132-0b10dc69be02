"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";

import { Label } from "@/components/ui/label";

export default function AccountInformation() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl font-semibold">
          Account Informations
        </CardTitle>
        <CardDescription className="text-md text-gray-600">
          View your account details
        </CardDescription>
        <div className="w-full h-px bg-gray-300" />
      </CardHeader>
      <CardContent className="p-8">
        <div className="space-y-8">
          {/* Account Timeline */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Label className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                  Account Created
                </Label>
              </div>
              <p className="text-xl font-medium text-gray-900 ml-4">
                Jan 15, 2020
              </p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Label className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                  Last Login
                </Label>
              </div>
              <p className="text-xl font-medium text-gray-900 ml-4">
                2 hours ago
              </p>
            </div>
          </div>

          {/* Divider */}
          <div className="border-t border-gray-100"></div>

          {/* Status Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <Label className="text-xs font-medium  text-gray-700 uppercase tracking-wide">
                Account Status
              </Label>
              <div className="flex items-center gap-5">
                <div className="w-4 h-4 bg-[#478085] rounded-full animate-pulse"></div>
                <span className="text-md  font-medium text-[#478085]">
                  Active
                </span>
              </div>
            </div>
            <div className="space-y-3">
              <Label className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                Verification Status
              </Label>
              <div className="flex items-center gap-5">
                <svg
                  className="w-5 h-5 text-[#478085]"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-md font-medium text-[#478085]">
                  Verified
                </span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
