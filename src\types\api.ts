// Backend API Response Types
// These types match the DTOs from the sastobazar backend

export interface SubcategoryResponseDto {
  id: string;
  categoryId: string;
  name: string;
  slug: string;
  description?: string;
  sortOrder: number;
  isActive: boolean;
  advertisementCount?: number;
}

export interface CategoryResponseDto {
  id: string;
  name: string;
  slug: string;
  description?: string;
  iconUrl?: string;
  sortOrder: number;
  isActive: boolean;
  subcategories?: SubcategoryResponseDto[];
  advertisementCount?: number;
}

export interface CategoriesListResponseDto {
  success: boolean;
  data: CategoryResponseDto[];
  message?: string;
}

// Category Management Request Types
export interface CreateCategoryRequest {
  name: string;
  slug?: string; // Optional, can be auto-generated from name
  description?: string;
  iconUrl?: string;
  sortOrder?: number;
  isActive?: boolean;
}

export interface UpdateCategoryRequest {
  name?: string;
  slug?: string;
  description?: string;
  iconUrl?: string;
  sortOrder?: number;
  isActive?: boolean;
}

export interface CreateSubcategoryRequest {
  categoryId: string;
  name: string;
  slug?: string; // Optional, can be auto-generated from name
  description?: string;
  sortOrder?: number;
  isActive?: boolean;
}

export interface UpdateSubcategoryRequest {
  name?: string;
  slug?: string;
  description?: string;
  sortOrder?: number;
  isActive?: boolean;
}

// Standard API Error Response
export interface ApiError {
  statusCode: number;
  message: string | string[];
  error: string;
  timestamp: string;
  path: string;
}

// Generic API Response wrapper
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// Query parameters for categories
export interface GetCategoriesParams {
  includeSubcategories?: boolean;
  activeOnly?: boolean;
}

export interface GetSubcategoriesParams {
  categoryId: string;
  activeOnly?: boolean;
}
