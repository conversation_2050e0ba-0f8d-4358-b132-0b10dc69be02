"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";

import { useCommentAuth } from "@/hooks/use-auth-action";
import {
  useGetProductReviewsQuery,
  useGetReviewStatsQuery,
  useMarkReviewHelpfulMutation
} from "@/store/api/productDetailsApi";
import type { Review } from "@/types/product-details";

interface ReviewsSectionProps {
  productId: string;
}

// Helper function to calculate rating distribution percentages
const calculateRatingDistribution = (distribution: Record<number, number>, total: number) => {
  return [5, 4, 3, 2, 1].map(stars => ({
    stars,
    count: distribution[stars] || 0,
    percentage: total > 0 ? Math.round(((distribution[stars] || 0) / total) * 100) : 0,
  }));
};

function StarRating({
  rating,
  size = "w-5 h-5",
}: {
  rating: number;
  size?: string;
}) {
  return (
    <div className="flex items-center gap-1">
      {[1, 2, 3, 4, 5].map((star) => {
        if (star <= Math.floor(rating)) {
          // Full star
          return (
            <Icon
              icon="lucide:star"
              key={star}
              className={`${size} fill-yellow-400 text-yellow-400`}
            />
          );
        } else if (star === Math.floor(rating) + 1 && rating % 1 !== 0) {
          // Half star (when there's a decimal part)
          return (
            <div key={star} className="relative">
              <Icon
                icon="lucide:star"
                className={`${size} fill-gray-200 text-gray-200`}
              />
              <div
                className="absolute inset-0 overflow-hidden"
                style={{ width: "50%" }}
              >
                <Icon
                  icon="lucide:star"
                  className={`${size} fill-yellow-400 text-yellow-400`}
                />
              </div>
            </div>
          );
        } else {
          // Empty star
          return (
            <Icon
              icon="lucide:star"
              key={star}
              className={`${size} fill-gray-200 text-gray-200`}
            />
          );
        }
      })}
    </div>
  );
}

function RatingBar({
  stars,
  percentage,
}: {
  stars: number;
  percentage: number;
}) {
  return (
    <div className="flex items-center gap-3">
      <div className="flex items-center gap-1 min-w-[100px]">
        {[1, 2, 3, 4, 5].map((star) => (
          <Icon
            icon="lucide:star"
            key={star}
            className={`w-4 h-4 ${
              star <= stars
                ? "fill-yellow-400 text-yellow-400"
                : "fill-gray-200 text-gray-200"
            }`}
          />
        ))}
      </div>
      <div className="flex-1 bg-gray-200 rounded-full h-2">
        <div
          className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
}

function ReviewCard({ review }: { review: Review }) {
  const [markHelpful, { isLoading: isMarkingHelpful }] = useMarkReviewHelpfulMutation();
  const [userHelpfulVote, setUserHelpfulVote] = useState<boolean | null>(null);

  const handleHelpfulClick = async (helpful: boolean) => {
    try {
      const newVote = userHelpfulVote === helpful ? null : helpful;
      await markHelpful({ reviewId: review.id, helpful }).unwrap();
      setUserHelpfulVote(newVote);
    } catch (error) {
      console.error('Failed to mark review as helpful:', error);
    }
  };

  return (
    <div className="border w-full border-gray-200 rounded-lg p-6 bg-white">
      <div className="flex items-start gap-4">
        {/* Avatar */}
        <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
          {review.userAvatar ? (
            <img
              src={review.userAvatar}
              alt={review.userName}
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            <Icon icon="lucide:user" className="w-6 h-6 text-gray-500" />
          )}
        </div>

        <div className="flex-1">
          {/* User Info */}
          <div className="flex items-center gap-3 mb-2">
            <h4 className="font-medium text-gray-900">{review.userName}</h4>
            {review.isVerifiedPurchase && (
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-800 text-xs"
              >
                Verified Purchase
              </Badge>
            )}
          </div>

          {/* Rating and Date */}
          <div className="flex items-center gap-3 mb-3">
            <StarRating rating={review.rating} size="w-4 h-4" />
            <span className="text-sm text-gray-500">
              {new Date(review.createdAt).toLocaleDateString()}
            </span>
          </div>

          {/* Title */}
          {review.title && (
            <h5 className="font-medium text-gray-900 mb-2">{review.title}</h5>
          )}

          {/* Comment */}
          <p className="text-gray-700 mb-4">{review.comment}</p>

          {/* Review Images */}
          {review.images && review.images.length > 0 && (
            <div className="flex gap-2 mb-4">
              {review.images.slice(0, 3).map((image, index) => (
                <img
                  key={index}
                  src={image}
                  alt={`Review image ${index + 1}`}
                  className="w-16 h-16 rounded-lg object-cover"
                />
              ))}
              {review.images.length > 3 && (
                <div className="w-16 h-16 rounded-lg bg-gray-100 flex items-center justify-center text-sm text-gray-600">
                  +{review.images.length - 3}
                </div>
              )}
            </div>
          )}

          {/* Helpful Buttons */}
          <div className="flex items-center gap-4">
            <button
              onClick={() => handleHelpfulClick(true)}
              disabled={isMarkingHelpful}
              className={`flex items-center gap-2 text-sm transition-colors disabled:opacity-50 ${
                userHelpfulVote === true
                  ? "text-green-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              <Icon icon="lucide:thumbs-up" className="w-4 h-4" />
              <span>{review.helpfulCount}</span>
            </button>
            <button
              onClick={() => handleHelpfulClick(false)}
              disabled={isMarkingHelpful}
              className={`flex items-center gap-2 text-sm transition-colors disabled:opacity-50 ${
                userHelpfulVote === false
                  ? "text-red-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              <Icon icon="lucide:thumbs-down" className="w-4 h-4" />
              <span>{review.notHelpfulCount}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ReviewsSection({ productId }: ReviewsSectionProps) {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'rating_high' | 'rating_low' | 'helpful'>('newest');
  const { requireCommentAuth } = useCommentAuth();

  // Fetch reviews and stats
  const {
    data: reviewsData,
    isLoading: reviewsLoading,
    error: reviewsError,
    refetch: refetchReviews
  } = useGetProductReviewsQuery({
    productId,
    page: currentPage,
    limit: 5,
    sortBy
  });

  const {
    data: reviewStats,
    isLoading: statsLoading
  } = useGetReviewStatsQuery(productId);

  const reviews = reviewsData?.data || [];
  const stats = reviewStats || { totalReviews: 0, averageRating: 0, ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 } };
  const ratingDistribution = calculateRatingDistribution(stats.ratingDistribution, stats.totalReviews);

  const loadMoreReviews = () => {
    if (reviewsData?.meta.hasNextPage) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const handleWriteReview = () => {
    requireCommentAuth(() => {
      router.push(`/write-review?productId=${productId}`);
    });
  };

  const handleSortChange = (newSortBy: typeof sortBy) => {
    setSortBy(newSortBy);
    setCurrentPage(1);
  };

  // Loading state
  if (reviewsLoading || statsLoading) {
    return (
      <div className="bg-white rounded-lg p-8 shadow-sm">
        <div className="flex items-start justify-between mb-8">
          <Skeleton className="h-8 w-32" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="text-center lg:text-left">
            <Skeleton className="h-16 w-24 mx-auto lg:mx-0 mb-4" />
            <Skeleton className="h-6 w-32 mx-auto lg:mx-0 mb-2" />
            <Skeleton className="h-4 w-40 mx-auto lg:mx-0" />
          </div>
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <Skeleton key={i} className="h-6 w-full" />
            ))}
          </div>
        </div>
        <div className="space-y-6">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (reviewsError) {
    return (
      <div className="bg-white rounded-lg p-8 shadow-sm">
        <Alert>
          <Icon icon="lucide:alert-circle" className="h-4 w-4" />
          <AlertDescription>
            Failed to load reviews. Please try again later.
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetchReviews()}
              className="ml-2"
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <div className="flex items-start justify-between mb-8">
        <h2 className="text-2xl font-semibold text-gray-900">Reviews</h2>
      </div>
      <div className="bg-white rounded-lg p-8 shadow-sm">
        {/* Rating Overview */}
        <div className="grid grid-cols-1 mt-6 lg:grid-cols-2 gap-8 mb-8">
          {/* Overall Rating */}
          <div className="text-center ml-40 lg:text-left">
            <div className="text-5xl font-bold text-gray-900 mb-2">
              {stats.averageRating.toFixed(1)}
            </div>
            <div className="mb-2">
              <StarRating rating={stats.averageRating} size="w-6 h-6" />
            </div>
            <p className="text-gray-600">Based on {stats.totalReviews} Reviews</p>
          </div>

          {/* Rating Distribution */}
          <div className="space-y-5">
            {ratingDistribution.map((item) => (
              <RatingBar
                key={item.stars}
                stars={item.stars}
                percentage={item.percentage}
              />
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium text-gray-700">Sort by:</label>
            <select
              value={sortBy}
              onChange={(e) => handleSortChange(e.target.value as typeof sortBy)}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm"
            >
              <option value="newest">Newest</option>
              <option value="oldest">Oldest</option>
              <option value="rating_high">Highest Rating</option>
              <option value="rating_low">Lowest Rating</option>
              <option value="helpful">Most Helpful</option>
            </select>
          </div>
          <Button
            variant="outline"
            onClick={handleWriteReview}
            className="bg-transparent text-lg font-medium border-gray-300 hover:bg-gray-50"
          >
            Write a Review
          </Button>
        </div>

        {/* Customer Reviews */}
        <div className="pt-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">
            Customer Reviews
          </h3>

          {reviews.length === 0 ? (
            <div className="text-center py-12">
              <Icon icon="lucide:star" className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h4 className="text-xl font-medium text-gray-900 mb-2">No reviews yet</h4>
              <p className="text-gray-600 mb-4">Be the first to review this product!</p>
              <Button onClick={handleWriteReview}>Write a Review</Button>
            </div>
          ) : (
            <>
              <div className="space-y-6">
                {reviews.map((review) => (
                  <ReviewCard key={review.id} review={review} />
                ))}
              </div>

              {/* Load More Button */}
              {reviewsData?.meta.hasNextPage && (
                <div className="text-center mt-8">
                  <Button
                    variant="outline"
                    onClick={loadMoreReviews}
                    className="bg-transparent text-xl font-semibold text-black border-gray-300 hover:bg-gray-50"
                  >
                    Load More Reviews
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
}
