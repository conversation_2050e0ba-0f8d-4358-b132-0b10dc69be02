import { Header } from "@/components";
import { AuthGuard } from "@/components/features/auth/AuthGuard";
import { ClientOnlyWrapper } from "@/components/layout/ClientOnlyWrapper";

export default function TestHydrationPage() {
  return (
    <ClientOnlyWrapper
      fallback={
        <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
          <div className="flex items-center justify-center min-h-screen">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#478085]"></div>
          </div>
        </div>
      }
    >
      <AuthGuard
        promptTitle="Test Hydration"
        promptMessage="Please log in to test hydration"
      >
        <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
          <Header />
          <div className="p-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Hydration Test Page
            </h1>
            <p className="text-gray-600">
              This page tests the hydration fix for the Next.js application.
              If you can see this content without hydration errors, the fix is working!
            </p>
          </div>
        </div>
      </AuthGuard>
    </ClientOnlyWrapper>
  );
}
