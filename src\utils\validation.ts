export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password: string): string[] => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push("At least 8 characters");
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push("One uppercase letter");
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push("One lowercase letter");
  }
  
  if (!/\d/.test(password)) {
    errors.push("One number");
  }
  
  return errors;
};

export const getPasswordStrength = (password: string): "weak" | "medium" | "strong" => {
  let score = 0;
  
  if (password.length >= 8) score += 1;
  if (/[A-Z]/.test(password)) score += 1;
  if (/[a-z]/.test(password)) score += 1;
  if (/\d/.test(password)) score += 1;
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;
  
  if (score >= 4) return "strong";
  if (score >= 3) return "medium";
  return "weak";
};
