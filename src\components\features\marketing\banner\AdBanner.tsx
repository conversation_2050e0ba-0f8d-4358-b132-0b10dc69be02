"use client";

import React, { useState, useRef, useEffect } from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import Image from "next/image";

interface AdBannerProps {
  className?: string;
  autoPlay?: boolean;
  showControls?: boolean;
  muted?: boolean;
  onClose?: () => void;
}

export default function AdBanner({
  className = "",
  autoPlay = true,
  showControls = true,
  muted = true,
  onClose: _onClose,
}: AdBannerProps) {
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [isMuted, setIsMuted] = useState(muted);
  const [showVideo, setShowVideo] = useState(true);
  const [isVisible] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Sample video URL - replace with your actual video
  const videoUrl =
    "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4";

  // Sample image URL - replace with your actual image
  const imageUrl =
    "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80";

  useEffect(() => {
    if (videoRef.current && autoPlay) {
      videoRef.current.play().catch(() => {
        // If autoplay fails, show image instead
        setShowVideo(false);
      });
    }
  }, [autoPlay]);

  if (!isVisible) return null;

  const togglePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const toggleMediaType = () => {
    setShowVideo(!showVideo);
    if (!showVideo && videoRef.current) {
      videoRef.current.currentTime = 0;
      if (autoPlay) {
        videoRef.current.play();
      }
    }
  };

  return (
    <div
      className={`relative w-full overflow-hidden rounded-lg shadow-lg ${className}`}
    >
      {/* Media Container */}
      <div className="relative w-full h-32 sm:h-40 md:h-48 lg:h-56 xl:h-64">
        {showVideo ? (
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            muted={isMuted}
            loop
            playsInline
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            onError={() => setShowVideo(false)}
          >
            <source src={videoUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        ) : (
          <Image
            src={imageUrl}
            alt="Advertisement Banner"
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        )}

        {/* Overlay Content */}
        <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
          <div className="text-center text-white px-4">
            <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold mb-2">
              Special Offers & Deals
            </h2>
            <p className="text-sm sm:text-base md:text-lg mb-4 opacity-90">
              Discover amazing products at unbeatable prices
            </p>
            <Button
              variant="default"
              className="bg-[#356267] hover:bg-[#478085] text-white px-4 py-2 sm:px-6 sm:py-3 text-sm sm:text-base"
            >
              Shop Now
            </Button>
          </div>
        </div>

        {/* Media Controls */}
        {showControls && (
          <div className="absolute bottom-2 right-2 flex gap-2">
            {/* Toggle Media Type */}
            <Button
              variant="secondary"
              size="sm"
              onClick={toggleMediaType}
              className="bg-black/50 hover:bg-black/70 text-white border-none p-2"
              title={showVideo ? "Switch to Image" : "Switch to Video"}
            >
              <Icon
                icon={showVideo ? "lucide:image" : "lucide:video"}
                className="h-4 w-4"
              />
            </Button>

            {/* Video Controls */}
            {showVideo && (
              <>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={togglePlayPause}
                  className="bg-black/50 hover:bg-black/70 text-white border-none p-2"
                  title={isPlaying ? "Pause" : "Play"}
                >
                  <Icon
                    icon={isPlaying ? "lucide:pause" : "lucide:play"}
                    className="h-4 w-4"
                  />
                </Button>

                <Button
                  variant="secondary"
                  size="sm"
                  onClick={toggleMute}
                  className="bg-black/50 hover:bg-black/70 text-white border-none p-2"
                  title={isMuted ? "Unmute" : "Mute"}
                >
                  <Icon
                    icon={isMuted ? "lucide:volume-x" : "lucide:volume-2"}
                    className="h-4 w-4"
                  />
                </Button>
              </>
            )}
          </div>
        )}
      </div>

      {/* Bottom Info Bar */}
      <div className="bg-gradient-to-r from-[#356267] to-[#478085] text-white px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Icon icon="lucide:megaphone" className="h-4 w-4" />
            <span className="text-sm font-medium">Featured Advertisement</span>
          </div>
          <div className="flex items-center gap-2 text-xs opacity-80">
            <Icon icon="lucide:clock" className="h-3 w-3" />
            <span>Limited Time Offer</span>
          </div>
        </div>
      </div>
    </div>
  );
}
