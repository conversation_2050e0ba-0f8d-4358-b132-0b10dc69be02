"use client";
import React from "react";
import { Icon } from "@iconify/react";
import { useImageCropModal } from "../ImageCropModalProvider";
import { ASPECT_RATIOS } from "../types";

export const CropControls: React.FC = () => {
  const {
    aspect,
    transforms,
    colorAdjustments,
    history,
    historyIndex,
    handleAspectChange,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
    handleZoom,
    handleUndo,
    handleRedo,
    resetAll,
    updateColorAdjustments,
  } = useImageCropModal();

  return (
    <div className="p-4 border-b bg-gray-50 space-y-4">
      {/* Row 1: Aspect Ratios */}
      <div className="flex flex-wrap items-center gap-2">
        <span className="text-sm font-medium text-gray-700 min-w-max">
          Aspect:
        </span>
        {ASPECT_RATIOS.map((ratio) => (
          <button
            key={ratio.label}
            onClick={() => handleAspectChange(ratio.value)}
            className={`px-3 py-1 text-xs rounded transition-colors ${
              aspect === ratio.value
                ? "bg-blue-500 text-white"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            }`}
          >
            {ratio.label}
          </button>
        ))}
      </div>

      {/* Row 2: Transform Controls */}
      <div className="flex flex-wrap items-center gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Transform:</span>
          <button
            onClick={() => handleRotate(-90)}
            className="p-2 bg-gray-200 hover:bg-gray-300 rounded transition-colors"
            title="Rotate -90°"
          >
            <Icon icon="lucide:rotate-ccw" className="w-4 h-4" />
          </button>
          <button
            onClick={() => handleRotate(90)}
            className="p-2 bg-gray-200 hover:bg-gray-300 rounded transition-colors"
            title="Rotate 90°"
          >
            <Icon icon="lucide:rotate-cw" className="w-4 h-4" />
          </button>
          <button
            onClick={handleFlipHorizontal}
            className={`p-2 rounded transition-colors ${
              transforms.flipX
                ? "bg-blue-500 text-white"
                : "bg-gray-200 hover:bg-gray-300"
            }`}
            title="Flip Horizontal"
          >
            <Icon icon="lucide:flip-horizontal" className="w-4 h-4" />
          </button>
          <button
            onClick={handleFlipVertical}
            className={`p-2 rounded transition-colors ${
              transforms.flipY
                ? "bg-blue-500 text-white"
                : "bg-gray-200 hover:bg-gray-300"
            }`}
            title="Flip Vertical"
          >
            <Icon icon="lucide:flip-vertical" className="w-4 h-4" />
          </button>
        </div>

        {/* Zoom Controls */}
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Zoom:</span>
          <button
            onClick={() => handleZoom(-0.1)}
            className="p-2 bg-gray-200 hover:bg-gray-300 rounded transition-colors"
            title="Zoom Out"
          >
            <Icon icon="lucide:zoom-out" className="w-4 h-4" />
          </button>
          <span className="text-xs text-gray-600 min-w-max">
            {Math.round(transforms.scale * 100)}%
          </span>
          <button
            onClick={() => handleZoom(0.1)}
            className="p-2 bg-gray-200 hover:bg-gray-300 rounded transition-colors"
            title="Zoom In"
          >
            <Icon icon="lucide:zoom-in" className="w-4 h-4" />
          </button>
        </div>

        {/* History Controls */}
        <div className="flex items-center gap-2">
          <button
            onClick={handleUndo}
            disabled={historyIndex <= 0}
            className="p-2 bg-gray-200 hover:bg-gray-300 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Undo (Ctrl+Z)"
          >
            <Icon icon="lucide:undo-2" className="w-4 h-4" />
          </button>
          <button
            onClick={handleRedo}
            disabled={historyIndex >= history.length - 1}
            className="p-2 bg-gray-200 hover:bg-gray-300 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Redo (Ctrl+Shift+Z)"
          >
            <Icon icon="lucide:redo-2" className="w-4 h-4" />
          </button>
        </div>

        <button
          onClick={resetAll}
          className="px-3 py-2 text-sm bg-red-100 text-red-700 hover:bg-red-200 rounded transition-colors"
        >
          Reset All
        </button>
      </div>

      {/* Row 3: Color Adjustments */}
      <div className="flex flex-wrap items-center gap-4">
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium text-gray-700 min-w-max">
            Brightness:
          </label>
          <input
            type="range"
            min="0"
            max="200"
            value={colorAdjustments.brightness}
            onChange={(e) =>
              updateColorAdjustments({ brightness: Number(e.target.value) })
            }
            className="w-20"
          />
          <span className="text-xs text-gray-600 min-w-max">
            {colorAdjustments.brightness}%
          </span>
        </div>
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium text-gray-700 min-w-max">
            Contrast:
          </label>
          <input
            type="range"
            min="0"
            max="200"
            value={colorAdjustments.contrast}
            onChange={(e) =>
              updateColorAdjustments({ contrast: Number(e.target.value) })
            }
            className="w-20"
          />
          <span className="text-xs text-gray-600 min-w-max">
            {colorAdjustments.contrast}%
          </span>
        </div>
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium text-gray-700 min-w-max">
            Saturation:
          </label>
          <input
            type="range"
            min="0"
            max="200"
            value={colorAdjustments.saturation}
            onChange={(e) =>
              updateColorAdjustments({ saturation: Number(e.target.value) })
            }
            className="w-20"
          />
          <span className="text-xs text-gray-600 min-w-max">
            {colorAdjustments.saturation}%
          </span>
        </div>
      </div>
    </div>
  );
};
