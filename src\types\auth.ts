/**
 * Authentication related TypeScript interfaces and types
 */

// Privacy Settings interface matching backend
export interface PrivacySettings {
  showEmail: boolean;
  showPhone: boolean;
  showFullName: boolean;
  showProfilePicture: boolean;
  showBio: boolean;
  showLocation: boolean;
  showLastSeen: boolean;
  allowDirectMessages: boolean;
  searchableByEmail: boolean;
  searchableByPhone: boolean;
}

// Notification Preferences interface matching backend
export interface NotificationPreferences {
  email: boolean;
  sms: boolean;
  push: boolean;
  marketing: boolean;
  orderUpdates: boolean;
  messages: boolean;
  advertisements: boolean;
  security: boolean;
}

// User Status enum matching backend
export enum UserStatus {
  ACTIVE = "active",
  SUSPENDED = "suspended",
  BANNED = "banned",
  PENDING_VERIFICATION = "pending_verification",
}

// Base User interface matching backend UserProfileResponseDto
export interface User {
  id: string;
  username: string;
  email?: string; // Optional based on privacy settings
  phone?: string; // Optional based on privacy settings
  firstName?: string;
  lastName?: string;
  fullName?: string;
  bio?: string;
  profilePictureUrl?: string;
  profilePicture?: string; // Legacy support
  status: UserStatus;
  emailVerified: boolean;
  phoneVerified: boolean;
  isVerified: boolean; // Legacy support
  address?: {
    city: string;
    country: string;
  };
  city?: string; // Legacy support
  state?: string;
  country?: string; // Legacy support
  fullAddress?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt?: string;
  roles: string[];
  privacySettings?: PrivacySettings;
  notificationPreferences?: NotificationPreferences;
  isOwnProfile: boolean;
  followersCount: number;
  followingCount: number;
  averageRating: number;
  totalRatings: number;
  // Legacy support for existing code
  preferences?: {
    notifications?: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
    privacy?: {
      showEmail: boolean;
      showPhone: boolean;
    };
  };
}
// {
//   "id": "123e4567-e89b-12d3-a456-426614174000",
//   "username": "john_doe",
//   "email": "<EMAIL>",
//   "phone": "+9779841234567",
//   "firstName": "John",
//   "lastName": "Doe",
//   "fullName": "John Doe",
//   "bio": "I love buying and selling on SastoBazar!",
//   "profilePictureUrl": "https://example.com/profile.jpg",
//   "status": "active",
//   "emailVerified": true,
//   "phoneVerified": false,
//   "address": "Kathmandu, Nepal",
//   "city": "Kathmandu",
//   "state": "Bagmati",
//   "country": "Nepal",
//   "fullAddress": "Kathmandu, Bagmati, Nepal",
//   "lastLoginAt": "2024-01-15T10:30:00Z",
//   "createdAt": "2024-01-01T00:00:00Z",
//   "roles": [
//     "USER"
//   ],
//   "privacySettings": {},
//   "notificationPreferences": {},
//   "isOwnProfile": false,
//   "followersCount": 42,
//   "followingCount": 15,
//   "averageRating": 4.5,
//   "totalRatings": 23
// }

// Login request payload
export interface LoginRequest {
  usernameOrEmail: string;
  password: string;
}

// Login response from API (legacy support)
export interface LoginResponse {
  success?: boolean;
  message?: string;
  token?: string; // Legacy support
  accessToken?: string; // Backend returns this
  refreshToken?: string;
  tokenType?: string;
  expiresIn?: number;
  user: User;
}

// Register request payload matching backend RegisterDto
export interface RegisterRequest {
  username: string; // 3-50 chars, alphanumeric + underscore
  email: string; // Valid email
  password: string; // Min 8 chars with complexity requirements
  firstName?: string; // Optional, max 100 chars
  lastName?: string; // Optional, max 100 chars
  phone?: string; // Optional, valid phone format
  role?: "USER" | "VENDOR"; // Optional, defaults to USER
  agreeToTerms?: boolean; // Frontend only
}

// Register response from API
export interface RegisterResponse {
  success?: boolean;
  message?: string;
  token?: string; // Legacy support
  accessToken?: string; // Backend returns this
  refreshToken?: string;
  tokenType?: string;
  expiresIn?: number;
  user: User;
}

// Authentication state for context
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  token: string | null;
}

// Authentication context type
export interface AuthContextType {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  clearError: () => void;
  refreshUser: () => Promise<void>;
  validateSession: () => Promise<boolean>;
}

// API Error response structure
export interface ApiErrorResponse {
  success: false;
  message: string;
  errors?: Record<string, string[]>;
  statusCode: number;
}

// Form validation errors
export interface FormErrors {
  usernameOrEmail?: string;
  password?: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  agreeToTerms?: string;
  general?: string;
}

// Password reset types
export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
  confirmPassword: string;
}

// Social login types
export interface SocialLoginRequest {
  provider: "google" | "facebook" | "apple";
  token: string;
}

// Token payload (for JWT decoding)
export interface TokenPayload {
  sub: string; // user id
  email: string;
  username: string;
  iat: number; // issued at
  exp: number; // expires at
  role?: string;
}

// Authentication hook return type
export interface UseAuthReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  clearError: () => void;
  refreshUser: () => Promise<void>;
}

// Form field types for better type safety
export type LoginFormFields = {
  usernameOrEmail: string;
  password: string;
};

export type RegisterFormFields = {
  email: string;
  password: string;
  confirmPassword: string;
  username: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  agreeToTerms: boolean;
};

// Update Profile Request for API matching backend UpdateProfileDto
export interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  bio?: string;
  profilePictureUrl?: string;
  phone?: string;
  address?: {
    city: string;
    country: string;
  };
  city?: string; // Legacy support
  state?: string;
  country?: string; // Legacy support
  postalCode?: string;
  latitude?: number;
  longitude?: number;
}

// Update Privacy Settings Request matching backend
export interface UpdatePrivacySettingsRequest {
  showEmail?: boolean;
  showPhone?: boolean;
  showFullName?: boolean;
  showProfilePicture?: boolean;
  showBio?: boolean;
  showLocation?: boolean;
  showLastSeen?: boolean;
  allowDirectMessages?: boolean;
  searchableByEmail?: boolean;
  searchableByPhone?: boolean;
}

// Update Notification Preferences Request matching backend
export interface UpdateNotificationPreferencesRequest {
  email?: boolean;
  sms?: boolean;
  push?: boolean;
  marketing?: boolean;
  orderUpdates?: boolean;
  messages?: boolean;
  advertisements?: boolean;
  security?: boolean;
}

// Auth Response for login/register matching backend AuthResponseDto
export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  tokenType: string; // "Bearer"
  expiresIn: number; // Seconds
  user: User;
}

// API response wrapper
export interface ApiResponse<T = unknown> {
  success: boolean;
  message: string;
  data?: T;
  errors?: Record<string, string[]>;
  statusCode?: number;
}

// Authentication status enum
export enum AuthStatus {
  IDLE = "idle",
  LOADING = "loading",
  AUTHENTICATED = "authenticated",
  UNAUTHENTICATED = "unauthenticated",
  ERROR = "error",
}

export default User;
