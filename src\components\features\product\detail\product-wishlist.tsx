"use client";

import { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Product } from "@/types/ecommerce";
import { useWishlistAuth } from "@/hooks/use-auth-action";
import { WishlistAuthGuard } from "@/components/features/auth/AuthGuard";

interface ProductWishlistProps {
  product: Product;
  className?: string;
}

// Simple wishlist management (in a real app, this would be connected to a backend)
const getWishlist = (): string[] => {
  if (typeof window === "undefined") return [];
  const wishlist = localStorage.getItem("wishlist");
  return wishlist ? JSON.parse(wishlist) : [];
};

const saveWishlist = (wishlist: string[]) => {
  if (typeof window === "undefined") return;
  localStorage.setItem("wishlist", JSON.stringify(wishlist));
};

const addToWishlist = (productId: string) => {
  const wishlist = getWishlist();
  if (!wishlist.includes(productId)) {
    wishlist.push(productId);
    saveWishlist(wishlist);
  }
};

const removeFromWishlist = (productId: string) => {
  const wishlist = getWishlist();
  const updatedWishlist = wishlist.filter((id) => id !== productId);
  saveWishlist(updatedWishlist);
};

const isInWishlist = (productId: string): boolean => {
  const wishlist = getWishlist();
  return wishlist.includes(productId);
};

export function ProductWishlist({
  product,
  className = "",
}: ProductWishlistProps) {
  const [isFavorited, setIsFavorited] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const { requireWishlistAuth, isAuthenticated } = useWishlistAuth();

  useEffect(() => {
    // Only check wishlist status if user is authenticated
    if (isAuthenticated) {
      setIsFavorited(isInWishlist(product.id));
    }
  }, [product.id, isAuthenticated]);

  const handleToggleFavorite = () => {
    requireWishlistAuth(() => {
      setIsAnimating(true);

      if (isFavorited) {
        removeFromWishlist(product.id);
        setIsFavorited(false);
      } else {
        addToWishlist(product.id);
        setIsFavorited(true);
        setShowConfirmation(true);
        setTimeout(() => setShowConfirmation(false), 2000);
      }

      setTimeout(() => setIsAnimating(false), 300);
    });
  };

  return (
    <div className="relative">
      <WishlistAuthGuard>
        <Button
          variant="ghost"
          size="lg"
          onClick={handleToggleFavorite}
          className={`text-gray-600 border px-16 text-lg py-3 transition-all duration-300 ${
            isAnimating ? "scale-110" : "scale-100"
          } ${className}`}
        >
          <Icon
            icon="lucide:heart"
            className={`h-5 w-5 mr-2 transition-all duration-300 ${
              isFavorited ? "text-red-500 fill-red-500" : "text-[#478085]"
            } ${isAnimating ? "scale-125" : "scale-100"}`}
          />
          {isFavorited ? "Saved" : "Save"}
        </Button>

        {/* Confirmation Toast */}
        {showConfirmation && (
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg z-10 flex items-center gap-2 animate-in slide-in-from-top-2 duration-300">
            <Icon icon="lucide:check" className="h-4 w-4" />
            <span className="text-sm font-medium">Added to favorites!</span>
          </div>
        )}
      </WishlistAuthGuard>
    </div>
  );
}

// Wishlist management hook for other components
export function useWishlist() {
  const [wishlist, setWishlist] = useState<string[]>([]);

  useEffect(() => {
    setWishlist(getWishlist());
  }, []);

  const addItem = (productId: string) => {
    addToWishlist(productId);
    setWishlist(getWishlist());
  };

  const removeItem = (productId: string) => {
    removeFromWishlist(productId);
    setWishlist(getWishlist());
  };

  const isItemInWishlist = (productId: string) => {
    return wishlist.includes(productId);
  };

  const clearWishlist = () => {
    saveWishlist([]);
    setWishlist([]);
  };

  return {
    wishlist,
    addItem,
    removeItem,
    isItemInWishlist,
    clearWishlist,
    count: wishlist.length,
  };
}

// Wishlist display component for showing saved items
interface WishlistDisplayProps {
  products: Product[];
  onRemove?: (productId: string) => void;
  onViewProduct?: (product: Product) => void;
}

export function WishlistDisplay({
  products,
  onRemove,
  onViewProduct,
}: WishlistDisplayProps) {
  const { removeItem } = useWishlist();

  const handleRemove = (productId: string) => {
    removeItem(productId);
    if (onRemove) {
      onRemove(productId);
    }
  };

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <Icon
          icon="lucide:heart"
          className="h-12 w-12 text-gray-300 mx-auto mb-4"
        />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No saved items
        </h3>
        <p className="text-gray-600">Start browsing and save items you like!</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {products.map((product) => (
        <div
          key={product.id}
          className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
        >
          <div className="aspect-[4/3] bg-gray-100 relative">
            {/* Product image would go here */}
            <div className="absolute inset-0 flex items-center justify-center text-gray-400">
              Product Image
            </div>
          </div>

          <div className="p-4">
            <h3 className="font-medium text-gray-900 mb-1 line-clamp-2">
              {product.title}
            </h3>
            <p className="text-lg font-semibold text-gray-900 mb-2">
              {product.currency}
              {product.price.toLocaleString()}
            </p>
            <p className="text-sm text-gray-600 mb-3">{product.location}</p>

            <div className="flex gap-2">
              <Button
                onClick={() => onViewProduct?.(product)}
                className="flex-1 bg-teal-600 hover:bg-teal-700 text-white"
                size="sm"
              >
                View Details
              </Button>
              <Button
                onClick={() => handleRemove(product.id)}
                variant="outline"
                size="sm"
                className="text-red-600 border-red-200 hover:bg-red-50"
              >
                <Icon
                  icon="lucide:heart"
                  className="h-4 w-4 fill-red-500 text-red-500"
                />
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
