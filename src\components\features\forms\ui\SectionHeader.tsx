import type React from "react";
import { Label } from "@/components/ui/label";

interface SectionHeaderProps {
  icon: React.ReactNode;
  title: string;
  subtitle?: string;
  required?: boolean;
  iconBgColor?: string;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  icon,
  title,
  subtitle,
  required = false,
  iconBgColor = "bg-gray-50",
}) => (
  <div className="space-y-1">
    <Label className="text-lg font-medium text-gray-800 flex items-center">
      <div
        className={`w-8 h-8 ${iconBgColor} rounded flex items-center justify-center mr-3`}
      >
        {icon}
      </div>
      {title}
      {required && <span className="text-[#478085] ml-1">*</span>}
    </Label>
    {subtitle && <p className="text-sm text-gray-500 ml-11">{subtitle}</p>}
  </div>
);
