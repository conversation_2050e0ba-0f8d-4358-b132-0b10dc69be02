# shadcn/ui Setup Documentation

## Overview
This document outlines the complete shadcn/ui setup for the e-commerce project, including configuration, usage, and best practices.

## Setup Complete ✅

### 1. Configuration Files Created
- `components.json` - shadcn/ui configuration
- `tailwind.config.js` - Tailwind CSS configuration with shadcn/ui theme
- Updated `src/app/globals.css` with CSS variables and base styles

### 2. Dependencies Installed
- `tailwindcss-animate` - Animation utilities
- `lucide-react` - Icon library (as specified in components.json)

### 3. Project Structure
```
src/
├── components/
│   └── ui/           # shadcn/ui components
│       ├── button.tsx
│       ├── card.tsx
│       ├── input.tsx
│       ├── accordion.tsx  # Added during setup
│       └── ... (other existing components)
├── lib/
│   └── utils.ts      # cn() utility function
└── app/
    └── globals.css   # Updated with shadcn/ui CSS variables
```

## Usage

### Adding New Components
Use the shadcn/ui CLI to add new components:

```bash
# Add a single component
npx shadcn@latest add button

# Add multiple components
npx shadcn@latest add button card input

# Add all components (use with caution)
npx shadcn@latest add --all
```

### Using Components
Import and use components in your React files:

```tsx
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export function MyComponent() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Example Card</CardTitle>
      </CardHeader>
      <CardContent>
        <Button variant="default">Click me</Button>
      </CardContent>
    </Card>
  )
}
```

### Styling with CSS Variables
The setup uses CSS variables for theming. You can customize colors by modifying the CSS variables in `globals.css`:

```css
:root {
  --primary: 240 5.9% 10%;
  --primary-foreground: 0 0% 98%;
  /* ... other variables */
}
```

## Available Components
Your project already includes these shadcn/ui components:
- Alert
- Badge
- Button
- Card
- Checkbox
- Dialog
- Dropdown Menu
- Input
- Label
- Progress
- Radio Group
- Select
- Separator
- Switch
- Tabs
- Textarea
- Toast
- Accordion (added during setup)

## Customization

### Theme Colors
The current setup uses a dark theme by default. To customize:

1. Modify CSS variables in `src/app/globals.css`
2. Use the shadcn/ui theme generator: https://ui.shadcn.com/themes

### Component Variants
Components support multiple variants. Example with Button:

```tsx
<Button variant="default">Default</Button>
<Button variant="destructive">Destructive</Button>
<Button variant="outline">Outline</Button>
<Button variant="secondary">Secondary</Button>
<Button variant="ghost">Ghost</Button>
<Button variant="link">Link</Button>
```

### Size Variants
```tsx
<Button size="default">Default</Button>
<Button size="sm">Small</Button>
<Button size="lg">Large</Button>
<Button size="icon">Icon</Button>
```

## Integration with Existing Code

### With Your Current Background Color
The setup maintains your preferred background color (#EFEFEF) while adding shadcn/ui theming support.

### With Iconify
You can continue using Iconify icons alongside Lucide React icons from shadcn/ui components.

### With Redux Toolkit
shadcn/ui components work seamlessly with your existing Redux Toolkit state management.

## Best Practices

1. **Use the cn() utility**: Always use the `cn()` function from `@/lib/utils` for conditional classes
2. **Consistent imports**: Use the `@/` alias for imports
3. **Component composition**: Build complex components by composing simpler shadcn/ui components
4. **Accessibility**: shadcn/ui components are built with accessibility in mind - maintain this by following their patterns

## Troubleshooting

### Build Issues
If you encounter build issues:
1. Ensure all dependencies are installed: `npm install`
2. Clear Next.js cache: `rm -rf .next`
3. Rebuild: `npm run build`

### Import Errors
If you see import errors:
1. Check that the component exists in `src/components/ui/`
2. Verify the import path uses `@/` alias
3. Ensure TypeScript paths are configured correctly in `tsconfig.json`

## Next Steps

1. **Add more components**: Use `npx shadcn@latest add [component-name]` as needed
2. **Customize theme**: Modify CSS variables to match your brand colors
3. **Create composite components**: Build reusable components using shadcn/ui primitives
4. **Update existing components**: Gradually migrate existing UI components to use shadcn/ui patterns

## Resources

- [shadcn/ui Documentation](https://ui.shadcn.com/)
- [Radix UI Primitives](https://www.radix-ui.com/primitives)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Lucide React Icons](https://lucide.dev/guide/packages/lucide-react)
