"use client";

import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/toast";
import {
  useGetNotificationPreferencesQuery,
  useUpdateNotificationPreferencesMutation,
} from "@/store/api/userApi";
import { NotificationPreferences } from "@/types/auth";

interface NotificationPreferencesTabProps {
  className?: string;
}

const notificationOptions = [
  {
    key: "email" as keyof NotificationPreferences,
    title: "Email Notifications",
    description: "Receive notifications via email",
    icon: "lucide:mail",
    category: "General",
  },
  {
    key: "sms" as keyof NotificationPreferences,
    title: "SMS Notifications",
    description: "Receive notifications via text message",
    icon: "lucide:smartphone",
    category: "General",
  },
  {
    key: "push" as keyof NotificationPreferences,
    title: "Push Notifications",
    description: "Receive push notifications in your browser",
    icon: "lucide:bell",
    category: "General",
  },
  {
    key: "marketing" as keyof NotificationPreferences,
    title: "Marketing Emails",
    description: "Receive promotional emails and offers",
    icon: "lucide:megaphone",
    category: "Marketing",
  },
  {
    key: "orderUpdates" as keyof NotificationPreferences,
    title: "Order Updates",
    description: "Get notified about order status changes",
    icon: "lucide:package",
    category: "Orders",
  },
  {
    key: "messages" as keyof NotificationPreferences,
    title: "Message Notifications",
    description: "Receive notifications for new messages",
    icon: "lucide:message-circle",
    category: "Communication",
  },
  {
    key: "advertisements" as keyof NotificationPreferences,
    title: "Advertisement Notifications",
    description: "Get notified about new ads and listings",
    icon: "lucide:tag",
    category: "Listings",
  },
  {
    key: "security" as keyof NotificationPreferences,
    title: "Security Alerts",
    description: "Important security and account notifications",
    icon: "lucide:shield-alert",
    category: "Security",
  },
];

// Group options by category
const groupedOptions = notificationOptions.reduce((acc, option) => {
  if (!acc[option.category]) {
    acc[option.category] = [];
  }
  acc[option.category].push(option);
  return acc;
}, {} as Record<string, typeof notificationOptions>);

export default function NotificationPreferencesTab({
  className,
}: NotificationPreferencesTabProps) {
  const { addToast } = useToast();
  const [localPreferences, setLocalPreferences] =
    useState<NotificationPreferences | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  const {
    data: notificationPreferences,
    isLoading,
    error,
    refetch,
  } = useGetNotificationPreferencesQuery();

  const [updateNotificationPreferences, { isLoading: isUpdating }] =
    useUpdateNotificationPreferencesMutation();

  // Initialize local preferences when data is loaded
  useEffect(() => {
    if (notificationPreferences) {
      setLocalPreferences(notificationPreferences);
      setHasChanges(false);
    }
  }, [notificationPreferences]);

  const handleToggle = (key: keyof NotificationPreferences) => {
    if (!localPreferences) return;

    const newPreferences = {
      ...localPreferences,
      [key]: !localPreferences[key],
    };

    setLocalPreferences(newPreferences);
    setHasChanges(true);
  };

  const handleSave = async () => {
    if (!localPreferences || !hasChanges) return;

    try {
      await updateNotificationPreferences(localPreferences).unwrap();
      setHasChanges(false);
      addToast({
        type: "success",
        title: "Notification Preferences Updated",
        description: "Your notification settings have been saved successfully.",
      });
    } catch (error) {
      console.error("Failed to update notification preferences:", error);
      addToast({
        type: "error",
        title: "Update Failed",
        description:
          "Failed to update notification preferences. Please try again.",
      });
    }
  };

  const handleReset = () => {
    if (notificationPreferences) {
      setLocalPreferences(notificationPreferences);
      setHasChanges(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center space-x-2">
          <Icon icon="lucide:loader-2" className="h-4 w-4 animate-spin" />
          <span>Loading notification preferences...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <Icon
          icon="lucide:alert-circle"
          className="h-8 w-8 text-red-500 mb-2"
        />
        <h3 className="text-lg font-semibold text-gray-900 mb-1">
          Error Loading Preferences
        </h3>
        <p className="text-gray-600 mb-4">
          Unable to load your notification preferences.
        </p>
        <Button onClick={() => refetch()} variant="outline">
          <Icon icon="lucide:refresh-cw" className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  if (!localPreferences) {
    return null;
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Icon icon="lucide:bell" className="h-5 w-5" />
            <span>Notification Preferences</span>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Choose how and when you want to receive notifications from
            SastoBazar.
          </p>
        </CardHeader>
        <CardContent className="space-y-8">
          {Object.entries(groupedOptions).map(([category, options]) => (
            <div key={category}>
              <h3 className="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                <Icon
                  icon={
                    category === "Security"
                      ? "lucide:shield"
                      : category === "Marketing"
                      ? "lucide:megaphone"
                      : category === "Orders"
                      ? "lucide:package"
                      : category === "Communication"
                      ? "lucide:message-circle"
                      : category === "Listings"
                      ? "lucide:tag"
                      : "lucide:settings"
                  }
                  className="h-4 w-4 mr-2"
                />
                {category}
              </h3>
              <div className="space-y-4">
                {options.map((option) => (
                  <div
                    key={option.key}
                    className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0"
                  >
                    <div className="flex items-start space-x-3 flex-1">
                      <Icon
                        icon={option.icon}
                        className="h-5 w-5 text-gray-500 mt-0.5"
                      />
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900">
                          {option.title}
                        </h4>
                        <p className="text-sm text-gray-600 mt-1">
                          {option.description}
                        </p>
                      </div>
                    </div>
                    <Switch
                      checked={localPreferences[option.key]}
                      onCheckedChange={() => handleToggle(option.key)}
                      className="ml-4"
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}

          {hasChanges && (
            <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
              <Button
                variant="outline"
                onClick={handleReset}
                disabled={isUpdating}
              >
                Reset
              </Button>
              <Button
                onClick={handleSave}
                disabled={isUpdating}
                className="min-w-[100px]"
              >
                {isUpdating ? (
                  <>
                    <Icon
                      icon="lucide:loader-2"
                      className="h-4 w-4 mr-2 animate-spin"
                    />
                    Saving...
                  </>
                ) : (
                  <>
                    <Icon icon="lucide:save" className="h-4 w-4 mr-2" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
