"use client"

import type React from "react"
import { useState, use<PERSON><PERSON>back, useEffect, useMemo } from "react"
import { useRouter } from "next/navigation"
import { Icon } from "@iconify/react"

// UI Components
import { LoadingButton } from "@/components/ui"
import { BackButton } from "@/components/ui/back-button"
import { Alert, AlertDescription } from "@/components/ui/alert"

// API hooks and utilities
import {
  useCreateAdvertisementMutation,
  useUpdateAdvertisementMutation,
  useUploadAdvertisementImagesMutation,
  useSubmitAdvertisementForApprovalMutation,
} from "@/store/api/advertisementApi"
import {
  transformFormDataToApiRequest,
  validateFormData,
  createDefaultFormData,
  sanitizeFormData,
} from "@/utils/form-transformer"
import { handleAdvertisementError } from "@/utils/api-error-handler"
import { validateImageFiles } from "@/utils/imageValidation"

// Form components and types
import { PhotoUpload } from "./ui/PhotoUpload"
import { ConditionSelector } from "./ui/ConditionSelector"
import { CategorySelector } from "./ui/CategorySelector"
import { type FormData, MAX_TITLE_LENGTH, VALIDATION_RULES, CURRENCY_OPTIONS } from "./types"

// Internal components
import { FormProgress } from "./components/FormProgress"
import { SectionHeader } from "./components/SectionHeader"
import { ProductTitleSection } from "./sections/ProductTitleSection"
import { ProductPhotosSection } from "./sections/ProductPhotosSection"
import { CategorySection } from "./sections/CategorySection"
import { DescriptionSection } from "./sections/DescriptionSection"
import { ConditionSection } from "./sections/ConditionSection"
import { PricingSection } from "./sections/PricingSection"
import { LocationSection } from "./sections/LocationSection"
import { AdditionalSettingsSection } from "./sections/AdditionalSettingsSection"

// Custom hooks
import { useFormValidation } from "./hooks/useFormValidation"
import { useAutoSave } from "./hooks/useAutoSave"
import { useImageHandling } from "./hooks/useImageHandling"

// Main PostAdsForm Component

const PostAdsForm = () => {
  const router = useRouter()
  const [errors, setErrors] = useState<string[]>([])
  const [formData, setFormData] = useState<FormData>(createDefaultFormData)

  // API mutation hooks
  const [uploadImages, { isLoading: isUploadingImages }] = useUploadAdvertisementImagesMutation()
  const [submitForApproval, { isLoading: isSubmittingForApproval }] = useSubmitAdvertisementForApprovalMutation()

  // Custom hooks
  const validation = useFormValidation(formData)
  const autoSave = useAutoSave(formData)

  const updateFormData = useCallback((updates: Partial<FormData>) => {
    setFormData((prev) => ({ ...prev, ...updates }))
  }, [])

  const imageHandling = useImageHandling(formData, updateFormData, setErrors)

  const isSubmitting = isUploadingImages || isSubmittingForApproval

  // Utility functions
  const handleBackClick = () => {
    if (typeof window !== "undefined") {
      window.history.back()
    }
  }

  const handleSubmit = async () => {
    try {
      setErrors([])

      if (!validation.isFormValid) {
        setErrors(validation.errors)
        return
      }

      const sanitizedData = sanitizeFormData(formData)
      const apiRequest = transformFormDataToApiRequest(sanitizedData)

      let adId = autoSave.advertisementId

      if (!adId) {
        // If no draft exists, we need to create one first
        await autoSave.saveDraft()
        adId = autoSave.advertisementId
      }

      if (formData.photos.length > 0 && adId) {
        await uploadImages({ id: adId, images: formData.photos }).unwrap()
      }

      if (adId) {
        await submitForApproval(adId).unwrap()
        alert("Ad submitted successfully! It will be reviewed and published soon.")
        router.push("/profile/my-ads")
      }
    } catch (error) {
      const errorMessage = handleAdvertisementError(error, "submit")
      setErrors([errorMessage])

      if (process.env.NODE_ENV === "development") {
        console.error("Error submitting advertisement:", error)
      }
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header */}
        <div className="flex items-center gap-6 mb-8">
          <BackButton
            onClick={handleBackClick}
            size="lg"
            className="hover:scale-105 transition-transform duration-200 shadow-lg"
          />
          <div className="flex-1">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">Create Your Listing</h1>
            <p className="text-gray-600">Fill in the details below to post your advertisement</p>
          </div>
        </div>

        {/* Progress Indicator */}
        <FormProgress
          currentStep={1}
          totalSteps={validation.totalRequiredFields}
          completedFields={validation.completedFields}
        />

        {/* Error Display */}
        {errors.length > 0 && (
          <Alert className="mb-8 border-red-200 bg-red-50">
            <Icon icon="lucide:alert-circle" className="h-4 w-4 text-red-600" />
            <AlertDescription>
              <div className="font-medium text-red-800 mb-2">Please fix the following errors:</div>
              <ul className="space-y-1">
                {errors.map((error, index) => (
                  <li key={index} className="text-red-700 text-sm flex items-center gap-2">
                    <div className="w-1 h-1 bg-red-500 rounded-full" />
                    {error}
                  </li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-8">
          {/* Form Sections */}
          <ProductTitleSection formData={formData} updateFormData={updateFormData} />
          <ProductPhotosSection imageHandling={imageHandling} />
          <CategorySection formData={formData} updateFormData={updateFormData} />
          <DescriptionSection formData={formData} updateFormData={updateFormData} />
          <ConditionSection formData={formData} updateFormData={updateFormData} />
          <PricingSection formData={formData} updateFormData={updateFormData} />
          <LocationSection formData={formData} updateFormData={updateFormData} />
          <AdditionalSettingsSection formData={formData} updateFormData={updateFormData} />
          {/* Submit Button */}
          <div className="flex justify-center pt-8 pb-12">
            <LoadingButton
              onClick={handleSubmit}
              isLoading={isSubmitting}
              loadingText="Publishing Your Ad..."
              disabled={isSubmitting || validation.completedFields < 4}
              className="bg-[#478085] hover:from-blue-700 hover:to-indigo-700 text-white px-12 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              <Icon icon="lucide:send" className="w-5 h-5 mr-2" />
              Publish Your Ad
            </LoadingButton>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PostAdsForm
