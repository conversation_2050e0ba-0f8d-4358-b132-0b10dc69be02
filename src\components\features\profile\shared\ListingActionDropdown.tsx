"use client";

import React from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/components/ui/toast";
import type { UserListing, ListingAction } from "@/types/ecommerce";

interface ListingActionDropdownProps {
  listing: UserListing;
  onAction: (action: ListingAction) => void;
  onMarkSoldClick?: () => void;
  onChangePriceClick?: () => void;
  className?: string;
}

export function ListingActionDropdown({
  listing,
  onAction,
  onMarkSoldClick,
  onChangePriceClick,
  className = "",
}: ListingActionDropdownProps) {
  const { addToast } = useToast();

  const handleAction = (actionType: string) => {
    switch (actionType) {
      case "delete":
        if (confirm("Are you sure you want to delete this listing?")) {
          onAction({ type: "delete", productId: listing.id });
          addToast({
            type: "success",
            title: "Listing deleted",
            description: `${listing.title} has been permanently removed.`,
          });
        }
        break;
      case "edit":
        onAction({ type: "edit", productId: listing.id });
        addToast({
          type: "info",
          title: "Redirecting to edit",
          description: "Opening the edit form for your listing.",
        });
        break;
      case "promote":
        onAction({ type: "promote", productId: listing.id });
        addToast({
          type: "info",
          title: "Promotion started",
          description: `${listing.title} is now being promoted for better visibility.`,
        });
        break;
      case "mark_active":
        onAction({ type: "mark_active", productId: listing.id });
        addToast({
          type: "success",
          title: "Listing activated!",
          description: `${listing.title} is now active and visible to buyers.`,
        });
        break;
      case "mark_inactive":
        onAction({ type: "mark_inactive", productId: listing.id });
        addToast({
          type: "info",
          title: "Listing deactivated",
          description: `${listing.title} has been hidden from buyers.`,
        });
        break;
      default:
        break;
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={`h-8 w-8 p-0 bg-white/90 hover:bg-white shadow-sm backdrop-blur-sm transition-all duration-300 ${className}`}
        >
          <Icon
            icon="lucide:more-vertical"
            className="w-4 h-4 text-gray-600"
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {listing.status !== "sold" && (
          <>
            <DropdownMenuItem
              onClick={onMarkSoldClick}
              className="text-green-600 hover:text-green-700 hover:bg-green-50"
            >
              <Icon
                icon="lucide:check-circle-2"
                className="w-4 h-4 mr-2"
              />
              Mark as Sold
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={onChangePriceClick}
              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
            >
              <Icon
                icon="lucide:indian-rupee"
                className="w-4 h-4 mr-2"
              />
              Change Price
            </DropdownMenuItem>
            <DropdownMenuSeparator />
          </>
        )}
        
        <DropdownMenuItem
          onClick={() => handleAction("edit")}
          className="hover:bg-blue-50"
        >
          <Icon icon="lucide:edit" className="w-4 h-4 mr-2" />
          Edit Listing
        </DropdownMenuItem>

        {listing.status === "active" ? (
          <DropdownMenuItem
            onClick={() => handleAction("mark_inactive")}
            className="text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50"
          >
            <Icon icon="lucide:eye-off" className="w-4 h-4 mr-2" />
            Make Inactive
          </DropdownMenuItem>
        ) : listing.status === "inactive" ? (
          <DropdownMenuItem
            onClick={() => handleAction("mark_active")}
            className="text-green-600 hover:text-green-700 hover:bg-green-50"
          >
            <Icon icon="lucide:eye" className="w-4 h-4 mr-2" />
            Make Active
          </DropdownMenuItem>
        ) : null}

        <DropdownMenuItem
          onClick={() => handleAction("promote")}
          className="text-purple-600 hover:text-purple-700 hover:bg-purple-50"
        >
          <Icon icon="lucide:trending-up" className="w-4 h-4 mr-2" />
          Promote Listing
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem
          onClick={() => handleAction("delete")}
          className="text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          <Icon icon="lucide:trash-2" className="w-4 h-4 mr-2" />
          Delete Listing
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
