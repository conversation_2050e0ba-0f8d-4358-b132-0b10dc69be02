#!/usr/bin/env node

/**
 * Performance Monitoring Script
 * Tracks bundle size changes and performance metrics
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const PERFORMANCE_LOG = path.join(__dirname, '../.performance-log.json');

function getCurrentBundleSize() {
  try {
    // Build the project to get accurate bundle sizes
    console.log('Building project for bundle analysis...');
    execSync('npm run build', { stdio: 'inherit' });
    
    // Read the build output to get bundle sizes
    const buildDir = path.join(__dirname, '../.next');
    const buildManifest = path.join(buildDir, 'build-manifest.json');
    
    if (fs.existsSync(buildManifest)) {
      const manifest = JSON.parse(fs.readFileSync(buildManifest, 'utf8'));
      return {
        timestamp: new Date().toISOString(),
        pages: manifest.pages,
        // Add more detailed analysis here
      };
    }
    
    return null;
  } catch (error) {
    console.error('Error analyzing bundle size:', error.message);
    return null;
  }
}

function loadPerformanceHistory() {
  if (fs.existsSync(PERFORMANCE_LOG)) {
    try {
      return JSON.parse(fs.readFileSync(PERFORMANCE_LOG, 'utf8'));
    } catch (error) {
      console.warn('Could not read performance log:', error.message);
    }
  }
  return { history: [] };
}

function savePerformanceData(data) {
  const history = loadPerformanceHistory();
  history.history.push(data);
  
  // Keep only last 50 entries
  if (history.history.length > 50) {
    history.history = history.history.slice(-50);
  }
  
  fs.writeFileSync(PERFORMANCE_LOG, JSON.stringify(history, null, 2));
}

function analyzePerformanceChanges(current, previous) {
  if (!previous) {
    console.log('📊 First performance measurement recorded');
    return;
  }
  
  console.log('📈 Performance Analysis:');
  console.log(`Previous: ${previous.timestamp}`);
  console.log(`Current:  ${current.timestamp}`);
  
  // Add bundle size comparison logic here
  console.log('Bundle analysis complete. Check .performance-log.json for details.');
}

function main() {
  console.log('🔍 Starting performance monitoring...');
  
  const currentData = getCurrentBundleSize();
  if (!currentData) {
    console.error('❌ Could not analyze current bundle size');
    process.exit(1);
  }
  
  const history = loadPerformanceHistory();
  const previousData = history.history[history.history.length - 1];
  
  savePerformanceData(currentData);
  analyzePerformanceChanges(currentData, previousData);
  
  console.log('✅ Performance monitoring complete');
}

if (require.main === module) {
  main();
}

module.exports = {
  getCurrentBundleSize,
  loadPerformanceHistory,
  savePerformanceData,
  analyzePerformanceChanges,
};
