"use client";

import React, { useState } from "react";
import Image from "next/image";
import { Icon } from "@iconify/react";
import { Badge } from "@/components/ui/badge";
import { StatusBadge } from "./shared/StatusBadge";
import { ListingActionDropdown } from "./shared/ListingActionDropdown";
import { MarkAsSoldDialog } from "./shared/MarkAsSoldDialog";
import { ChangePriceDialog } from "./shared/ChangePriceDialog";
import { formatDate } from "./shared/utils";
import type { UserListing, ListingAction } from "@/types/ecommerce";

interface EnhancedAdPostCardProps {
  listing: UserListing;
  onAction: (action: ListingAction) => void;
}

export default function EnhancedAdPostCard({
  listing,
  onAction,
}: EnhancedAdPostCardProps) {
  const [showMarkSoldDialog, setShowMarkSoldDialog] = useState(false);
  const [showChangePriceDialog, setShowChangePriceDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [imageError, setImageError] = useState(false);

  return (
    <>
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-lg hover:border-gray-200 transition-all duration-300 group">
        <div className="flex flex-col h-full">
          {/* Image Section */}
          <div className="relative h-48 overflow-hidden">
            <Image
              src={
                imageError
                  ? "/assets/images/placeholders/placeholder.jpg"
                  : listing.image
              }
              alt={listing.title}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              onError={() => setImageError(true)}
            />

            {/* Overlay gradient */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            {/* Status Badge */}
            <div className="absolute top-3 left-3">
              <StatusBadge status={listing.status} />
            </div>

            {/* Action Menu */}
            <div className="absolute top-3 right-3">
              <ListingActionDropdown
                listing={listing}
                onAction={onAction}
                onMarkSoldClick={() => setShowMarkSoldDialog(true)}
                onChangePriceClick={() => setShowChangePriceDialog(true)}
                className="rounded-full"
              />
            </div>
          </div>

          {/* Content Section */}
          <div className="p-5 flex-1 flex flex-col justify-between">
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3 text-lg line-clamp-2 group-hover:text-teal-600 transition-colors duration-300">
                {listing.title}
              </h3>

              <div className="flex items-center gap-2 mb-3">
                <Badge className="bg-teal-100 text-teal-800 border-teal-200 text-xs">
                  {listing.condition}
                </Badge>
              </div>

              <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
                <Icon icon="lucide:clock" className="w-4 h-4" />
                <span>{formatDate(listing.postedAt)}</span>
              </div>
            </div>

            {/* Price and Stats */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold text-gray-900">
                  {listing.price}
                </span>
              </div>

              <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                <div className="flex items-center gap-1 text-sm text-gray-500">
                  <Icon icon="lucide:eye" className="w-4 h-4" />
                  <span>{listing.views || 0}</span>
                </div>
                <div className="flex items-center gap-1 text-sm text-gray-500">
                  <Icon icon="lucide:message-circle" className="w-4 h-4" />
                  <span>{listing.inquiries || 0}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mark as Sold Dialog */}
      <MarkAsSoldDialog
        open={showMarkSoldDialog}
        onOpenChange={setShowMarkSoldDialog}
        listing={listing}
        onAction={onAction}
        isLoading={isLoading}
        onLoadingChange={setIsLoading}
      />

      {/* Change Price Dialog */}
      <ChangePriceDialog
        open={showChangePriceDialog}
        onOpenChange={setShowChangePriceDialog}
        listing={listing}
        onAction={onAction}
        isLoading={isLoading}
        onLoadingChange={setIsLoading}
      />
    </>
  );
}
