// API Error handling utilities for advertisement operations

import type { ApiErrorResponse } from '@/types/advertisement';

// RTK Query error structure
interface RTKQueryError {
  status: number | string;
  data?: ApiErrorResponse | string;
  error?: string;
}

// Network error structure
interface NetworkError {
  name: string;
  message: string;
  stack?: string;
}

// Union type for all possible error types
type ApiError = RTKQueryError | NetworkError | Error | unknown;

/**
 * Handles API errors and returns user-friendly error messages
 * @param error - The error object from API call
 * @returns User-friendly error message string
 */
export const handleApiError = (error: ApiError): string => {
  // Handle RTK Query errors
  if (error && typeof error === 'object' && 'status' in error) {
    const rtkError = error as RTKQueryError;
    
    // Handle different HTTP status codes
    switch (rtkError.status) {
      case 400:
        return extractErrorMessage(rtkError.data) || 'Invalid data provided. Please check your input and try again.';
      
      case 401:
        return 'You need to be logged in to perform this action. Please log in and try again.';
      
      case 403:
        return 'You do not have permission to perform this action.';
      
      case 404:
        return 'The requested advertisement was not found. It may have been deleted or moved.';
      
      case 409:
        return 'This action conflicts with the current state. Please refresh and try again.';
      
      case 413:
        return 'The uploaded files are too large. Please reduce file sizes and try again.';
      
      case 422:
        return extractErrorMessage(rtkError.data) || 'Validation failed. Please check your input and try again.';
      
      case 429:
        return 'Too many requests. Please wait a moment before trying again.';
      
      case 500:
        return 'Server error occurred. Please try again later or contact support if the problem persists.';
      
      case 502:
      case 503:
      case 504:
        return 'Service temporarily unavailable. Please try again in a few minutes.';
      
      case 'FETCH_ERROR':
        return 'Network connection failed. Please check your internet connection and try again.';
      
      case 'PARSING_ERROR':
        return 'Failed to process server response. Please try again.';
      
      case 'TIMEOUT_ERROR':
        return 'Request timed out. Please check your connection and try again.';
      
      default:
        return extractErrorMessage(rtkError.data) || 'An unexpected error occurred. Please try again.';
    }
  }
  
  // Handle network errors
  if (error && typeof error === 'object' && 'name' in error) {
    const networkError = error as NetworkError;
    
    switch (networkError.name) {
      case 'NetworkError':
        return 'Network connection failed. Please check your internet connection.';
      
      case 'TimeoutError':
        return 'Request timed out. Please try again.';
      
      case 'AbortError':
        return 'Request was cancelled. Please try again.';
      
      default:
        return networkError.message || 'Network error occurred. Please try again.';
    }
  }
  
  // Handle generic Error objects
  if (error instanceof Error) {
    return error.message || 'An unexpected error occurred.';
  }
  
  // Fallback for unknown error types
  return 'An unexpected error occurred. Please try again.';
};

/**
 * Extracts error message from API response data
 * @param data - The response data from API error
 * @returns Extracted error message or null
 */
const extractErrorMessage = (data: ApiErrorResponse | string | undefined): string | null => {
  if (!data) return null;
  
  // Handle string error messages
  if (typeof data === 'string') {
    return data;
  }
  
  // Handle structured error responses
  if (typeof data === 'object' && data.message) {
    return data.message;
  }
  
  return null;
};

/**
 * Handles specific advertisement-related errors with contextual messages
 * @param error - The error object from advertisement API call
 * @param context - The context of the operation (create, update, upload, etc.)
 * @returns Contextual error message
 */
export const handleAdvertisementError = (error: ApiError, context: string): string => {
  const baseMessage = handleApiError(error);
  
  // Add context-specific prefixes for better user experience
  switch (context) {
    case 'create':
      return `Failed to create advertisement: ${baseMessage}`;
    
    case 'update':
      return `Failed to update advertisement: ${baseMessage}`;
    
    case 'upload':
      return `Failed to upload images: ${baseMessage}`;
    
    case 'submit':
      return `Failed to submit advertisement for approval: ${baseMessage}`;
    
    case 'delete':
      return `Failed to delete advertisement: ${baseMessage}`;
    
    case 'fetch':
      return `Failed to load advertisement: ${baseMessage}`;
    
    default:
      return baseMessage;
  }
};

/**
 * Checks if an error is a validation error
 * @param error - The error object to check
 * @returns True if it's a validation error
 */
export const isValidationError = (error: ApiError): boolean => {
  if (error && typeof error === 'object' && 'status' in error) {
    const rtkError = error as RTKQueryError;
    return rtkError.status === 400 || rtkError.status === 422;
  }
  return false;
};

/**
 * Checks if an error is an authentication error
 * @param error - The error object to check
 * @returns True if it's an authentication error
 */
export const isAuthError = (error: ApiError): boolean => {
  if (error && typeof error === 'object' && 'status' in error) {
    const rtkError = error as RTKQueryError;
    return rtkError.status === 401;
  }
  return false;
};

/**
 * Checks if an error is a network/connectivity error
 * @param error - The error object to check
 * @returns True if it's a network error
 */
export const isNetworkError = (error: ApiError): boolean => {
  if (error && typeof error === 'object' && 'status' in error) {
    const rtkError = error as RTKQueryError;
    return rtkError.status === 'FETCH_ERROR' || rtkError.status === 'TIMEOUT_ERROR';
  }
  
  if (error && typeof error === 'object' && 'name' in error) {
    const networkError = error as NetworkError;
    return ['NetworkError', 'TimeoutError', 'AbortError'].includes(networkError.name);
  }
  
  return false;
};

/**
 * Gets retry-able status for an error
 * @param error - The error object to check
 * @returns True if the operation can be retried
 */
export const isRetryableError = (error: ApiError): boolean => {
  if (error && typeof error === 'object' && 'status' in error) {
    const rtkError = error as RTKQueryError;
    
    // Retry for server errors, network errors, and timeouts
    const retryableStatuses = [500, 502, 503, 504, 'FETCH_ERROR', 'TIMEOUT_ERROR'];
    return retryableStatuses.includes(rtkError.status as any);
  }
  
  return isNetworkError(error);
};

/**
 * Formats validation errors for display
 * @param error - The validation error object
 * @returns Array of formatted error messages
 */
export const formatValidationErrors = (error: ApiError): string[] => {
  if (!isValidationError(error)) {
    return [handleApiError(error)];
  }
  
  if (error && typeof error === 'object' && 'data' in error) {
    const rtkError = error as RTKQueryError;
    const data = rtkError.data;
    
    if (typeof data === 'object' && data && 'message' in data) {
      // If the message contains multiple errors, try to split them
      const message = data.message;
      if (typeof message === 'string' && message.includes('\n')) {
        return message.split('\n').filter(msg => msg.trim().length > 0);
      }
      return [message];
    }
  }
  
  return [handleApiError(error)];
};
