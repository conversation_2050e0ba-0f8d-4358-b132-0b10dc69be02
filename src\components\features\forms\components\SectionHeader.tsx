import React from "react"
import { Badge } from "@/components/ui/badge"

interface SectionHeaderProps {
  icon: React.ReactNode
  title: string
  subtitle?: string
  required?: boolean
  step?: number
  totalSteps?: number
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  icon,
  title,
  subtitle,
  required = false,
  step,
  totalSteps,
}) => (
  <div className="flex items-start justify-between mb-6">
    <div className="flex items-start gap-4">
      <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
        {icon}
      </div>
      <div>
        <div className="flex items-center gap-3">
          <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
          {required && (
            <Badge variant="destructive" className="text-xs px-2 py-1">
              Required
            </Badge>
          )}
        </div>
        {subtitle && <p className="text-sm text-gray-600 mt-1">{subtitle}</p>}
      </div>
    </div>
    {step && totalSteps && (
      <div className="flex items-center gap-2 text-sm text-gray-500">
        <span className="font-medium">{step}</span>
        <span>/</span>
        <span>{totalSteps}</span>
      </div>
    )}
  </div>
)
