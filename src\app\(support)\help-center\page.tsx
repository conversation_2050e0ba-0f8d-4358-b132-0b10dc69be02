"use client";

import { useRouter } from "next/navigation";
import { BackButton } from "@/components/ui/back-button";
import { Icon } from "@iconify/react";

export default function HelpCenterPage() {
  const router = useRouter();

  const popularTopics = [
    {
      title: "How to post an ad",
      icon: "material-symbols:post-add",
      description: "Learn how to create and publish your first advertisement",
    },
    {
      title: "Managing your profile",
      icon: "material-symbols:person-edit",
      description: "Update your profile information and settings",
    },
    {
      title: "Payment and top-up issues",
      icon: "material-symbols:account-balance-wallet",
      description: "Resolve payment problems and account balance issues",
    },
    {
      title: "Job application process",
      icon: "material-symbols:work",
      description: "Understand how to apply for jobs and track applications",
    },
  ];

  const faqCategories = [
    {
      title: "Getting Started",
      icon: "material-symbols:rocket-launch",
      questions: [
        "How do I create an account?",
        "How to verify my email address?",
        "What information do I need to provide?",
      ],
    },
    {
      title: "Buying & Selling",
      icon: "material-symbols:shopping-cart",
      questions: [
        "How do I make a purchase?",
        "How to contact a seller?",
        "What payment methods are accepted?",
      ],
    },
    {
      title: "Account & Security",
      icon: "material-symbols:security",
      questions: [
        "How to change my password?",
        "How to enable two-factor authentication?",
        "What to do if my account is compromised?",
      ],
    },
  ];

  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Back Button */}
        <div className="mb-6">
          <BackButton onClick={() => router.back()} size="lg" />
        </div>

        {/* Main Content Card */}
        <div className="bg-white rounded-xl shadow-lg px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Help Center
            </h1>
            <p className="text-gray-600 text-lg">
              Find answers to common questions and get support.
            </p>
          </div>

          {/* Welcome Message */}
          <div className="mb-8 p-6 bg-gray-50 rounded-lg">
            <p className="text-gray-700 leading-relaxed">
              Welcome to our Help Center! Here you can find guides, FAQs, and
              contact options for any assistance you might need.
            </p>
          </div>

          {/* Popular Topics */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Popular Topics:
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {popularTopics.map((topic, index) => (
                <div
                  key={index}
                  className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer group"
                >
                  <div className="flex items-start space-x-3">
                    <Icon
                      icon={topic.icon}
                      className="w-6 h-6 text-[#478085] mt-1 group-hover:text-[#356267]"
                    />
                    <div>
                      <h3 className="font-semibold text-gray-900 group-hover:text-[#478085]">
                        {topic.title}
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        {topic.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* FAQ Categories */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Frequently Asked Questions
            </h2>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {faqCategories.map((category, index) => (
                <div
                  key={index}
                  className="border border-gray-200 rounded-lg p-6"
                >
                  <div className="flex items-center space-x-3 mb-4">
                    <Icon
                      icon={category.icon}
                      className="w-6 h-6 text-[#478085]"
                    />
                    <h3 className="font-semibold text-gray-900">
                      {category.title}
                    </h3>
                  </div>
                  <ul className="space-y-2">
                    {category.questions.map((question, qIndex) => (
                      <li key={qIndex}>
                        <button className="text-sm text-gray-600 hover:text-[#478085] text-left transition-colors">
                          {question}
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          {/* Contact Support */}
          <div className="border-t border-gray-200 pt-8">
            <div className="text-center">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Still need help?
              </h3>
              <p className="text-gray-600 mb-6">
                For further assistance, please contact our support team at{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="text-[#478085] underline"
                >
                  <EMAIL>
                </a>
                .
              </p>
              <button
                onClick={() => router.push("/contact-support")}
                className="bg-[#478085] text-white px-6 py-3 rounded-lg hover:bg-[#356267] transition-colors font-medium"
              >
                Contact Support
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
