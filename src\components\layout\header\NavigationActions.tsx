"use client";
import Link from "next/link";
import { Icon } from "@iconify/react";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { PostAdAuthGuard } from "@/components/features/auth/AuthGuard";
import { UserProfile } from "@/types/ecommerce";

import { CartButton } from "./CartButton";
import { NotificationButton } from "./NotificationButton";
import { ChatButton } from "./ChatButton";
import { AuthSection } from "./AuthSection";
import { LanguageSelector } from "./LanguageSelector";
import { ProfileDropdown } from "./ProfileDropdown";

interface NavigationActionsProps {
  isAuthenticated: boolean;
  currentUser: UserProfile | null;
  cartTotalItems: number;
  onLogout: () => void;
}

export function NavigationActions({
  isAuthenticated,
  currentUser,
  cartTotalItems,
  onLogout,
}: NavigationActionsProps) {
  const [isHydrated, setIsHydrated] = useState(false);

  // Handle client-side hydration to prevent SSR mismatch
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Show basic navigation without authentication-dependent elements during hydration
  if (!isHydrated) {
    return (
      <nav className="flex items-center space-x-1 sm:space-x-2 md:space-x-2 lg:space-x-3 xl:space-x-4 flex-shrink-0">
        {/* Shopping Cart - always visible */}
        <CartButton totalItems={0} />

        {/* Notification Bell */}
        <div className="hidden md:block">
          <NotificationButton />
        </div>

        {/* Post Ad Button - without auth guard during hydration */}
        <Link href="/postad">
          <Button className="bg-[#1F5E64] hover:bg-[#1a5157] text-white rounded-xl px-2 sm:px-3 md:px-4 lg:px-6 py-1.5 md:py-2 text-xs md:text-sm lg:text-base font-medium transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-white min-w-[44px] min-h-[36px] md:min-h-[40px] flex items-center justify-center">
            <span className="hidden md:inline">Post Ad</span>
            <span className="md:hidden">
              <Icon icon="material-symbols:add" className="w-4 h-4" />
            </span>
          </Button>
        </Link>

        {/* Language Selector */}
        <div className="hidden lg:block">
          <LanguageSelector />
        </div>

        {/* Profile Dropdown - without auth state during hydration */}
        <ProfileDropdown
          isAuthenticated={false}
          currentUser={null}
          onLogout={onLogout}
        />
      </nav>
    );
  }

  return (
    <nav className="flex items-center space-x-1 sm:space-x-2 md:space-x-2 lg:space-x-3 xl:space-x-4 flex-shrink-0">


      {/* Shopping Cart */}
      <CartButton totalItems={cartTotalItems} />

      {/* Notification Bell */}
      <div className="hidden md:block">
        <NotificationButton />
      </div>

      {/* Chat Messages */}
      {isAuthenticated && (
        <div className="hidden lg:block">
          <ChatButton />
        </div>
      )}

      {/* Sales Operations - only show when authenticated */}
      {isAuthenticated && (
        <div className="hidden xl:block">
          <Link href="/sales">
            <Button
              variant="ghost"
              className="text-white hover:text-gray-200 hover:bg-white/10 transition-all duration-300 p-0 w-9 h-9 lg:w-10 lg:h-10"
              title="Sales Operations"
            >
              <div className="w-full h-full bg-[#1F5E64] border border-white rounded-lg flex items-center justify-center hover:bg-[#1a5157] transition-all duration-300">
                <Icon
                  icon="carbon:sales-ops"
                  className="w-4 h-4 lg:w-5 lg:h-5"
                />
              </div>
            </Button>
          </Link>
        </div>
      )}

      {/* Authentication Section */}
      {!isAuthenticated && (
        <div className="hidden md:block">
          <AuthSection />
        </div>
      )}

      {/* Post Ad Button */}
      <PostAdAuthGuard>
        <Link href="/postad">
          <Button className="bg-[#1F5E64] hover:bg-[#1a5157] text-white rounded-xl px-2 sm:px-3 md:px-4 lg:px-6 py-1.5 md:py-2 text-xs md:text-sm lg:text-base font-medium transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-white min-w-[44px] min-h-[36px] md:min-h-[40px] flex items-center justify-center">
            <span className="hidden md:inline">Post Ad</span>
            <span className="md:hidden">
              <Icon icon="material-symbols:add" className="w-4 h-4" />
            </span>
          </Button>
        </Link>
      </PostAdAuthGuard>

      {/* Language Selector - hidden on mobile and tablet */}
      <div className="hidden lg:block">
        <LanguageSelector />
      </div>

      {/* Profile Dropdown */}
      <ProfileDropdown
        isAuthenticated={isAuthenticated}
        currentUser={currentUser}
        onLogout={onLogout}
      />
    </nav>
  );
}
