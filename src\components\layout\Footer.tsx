import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Icon } from "@iconify/react";
import Link from "next/link";

export default function Footer() {
  return (
    <footer className="bg-gradient-to-b from-teal-700 to-teal-800 text-white shadow-2xl">
      <div className="container-responsive spacing-responsive-lg">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-6 sm:gap-8 lg:gap-10">
          {/* About Us */}
          <div className="space-y-4 sm:space-y-5 lg:space-y-6">
            <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-teal-100  border-teal-500 pb-2 sm:pb-3">
              About Us
            </h3>
            <ul className="space-y-3 sm:space-y-4">
              <li>
                <Link
                  href="/why-choose-us"
                  className="text-gray-200 hover:text-white transition-all duration-300 hover:underline text-sm sm:text-base hover:translate-x-1 touch-manipulation min-h-[44px] flex items-center"
                >
                  Why Choose Us
                </Link>
              </li>
              <li>
                <Link
                  href="/blogs"
                  className="text-gray-200 hover:text-white transition-all duration-300 hover:underline text-sm sm:text-base hover:translate-x-1 touch-manipulation min-h-[44px] flex items-center"
                >
                  Blogs
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-gray-200 hover:text-white transition-all duration-300 hover:underline text-sm sm:text-base hover:translate-x-1 touch-manipulation min-h-[44px] flex items-center"
                >
                  Contact Us
                </Link>
              </li>
              <li>
                <Link
                  href="/faqs"
                  className="text-gray-200 hover:text-white transition-all duration-300 hover:underline text-sm sm:text-base hover:translate-x-1 touch-manipulation min-h-[44px] flex items-center"
                >
                  FAQs
                </Link>
              </li>
              <li>
                <Link
                  href="/community"
                  className="text-gray-200 hover:text-white transition-all duration-300 hover:underline text-sm sm:text-base hover:translate-x-1 touch-manipulation min-h-[44px] flex items-center"
                >
                  Online Community
                </Link>
              </li>
              <li>
                <Link
                  href="/testimonials"
                  className="text-gray-200 hover:text-white transition-all duration-300 hover:underline text-sm sm:text-base hover:translate-x-1 touch-manipulation min-h-[44px] flex items-center"
                >
                  Testimonials
                </Link>
              </li>
            </ul>
          </div>

          {/* Policies */}
          <div className="space-y-4 sm:space-y-5 lg:space-y-6">
            <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-teal-100  border-teal-500 pb-2 sm:pb-3">
              Policies
            </h3>
            <ul className="space-y-3 sm:space-y-4">
              <li>
                <Link
                  href="/cookies-policy"
                  className="text-gray-200 hover:text-white transition-all duration-300 hover:underline text-sm sm:text-base hover:translate-x-1 touch-manipulation min-h-[44px] flex items-center"
                >
                  Cookies Policy
                </Link>
              </li>
              <li>
                <Link
                  href="/privacy-policy"
                  className="text-gray-200 hover:text-white transition-all duration-300 hover:underline text-sm sm:text-base hover:translate-x-1 touch-manipulation min-h-[44px] flex items-center"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link
                  href="/terms-of-service"
                  className="text-gray-200 hover:text-white transition-all duration-300 hover:underline text-sm sm:text-base hover:translate-x-1 touch-manipulation min-h-[44px] flex items-center"
                >
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>

          {/* Subscribe */}
          <div className="sm:col-span-2 lg:col-span-2 xl:col-span-2 space-y-4 sm:space-y-5 lg:space-y-6">
            <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-teal-100  border-teal-500 pb-2 sm:pb-3">
              Subscribe
            </h3>
            <div className="space-y-4 sm:space-y-6">
              <p className="text-gray-200 text-sm sm:text-base leading-relaxed">
                Stay updated with the latest deals and exclusive offers
              </p>
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-2 items-stretch sm:items-start">
                <div className="relative flex-1 sm:flex-none">
                  <Icon
                    icon="lucide:mail"
                    className="absolute left-3 sm:left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 sm:h-5 sm:w-5"
                  />
                  <Input
                    type="email"
                    placeholder="Enter Your Email"
                    className="pl-10 sm:pl-12 py-3 sm:py-3 text-sm sm:text-base bg-white/10 border-white/20 text-white placeholder:text-gray-300 focus:border-white focus:ring-white rounded-lg w-full sm:w-64 md:w-80 min-h-[44px]"
                  />
                </div>
                <Button className="bg-teal-500 hover:bg-teal-400 text-white px-6 sm:px-8 py-3 font-semibold text-sm sm:text-base rounded-lg transition-all duration-300 hover:shadow-lg whitespace-nowrap min-h-[44px] flex items-center justify-center">
                  Subscribe
                </Button>
              </div>
            </div>
          </div>

          {/* Follow Us */}
          <div className="space-y-4 sm:space-y-5 lg:space-y-6">
            <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-teal-100  border-teal-500 pb-2 sm:pb-3">
              Follow Us
            </h3>
            <div className="flex gap-3 sm:gap-4">
              <Link
                href="https://facebook.com"
                className="bg-white/10 hover:bg-white/20 p-3 sm:p-4 rounded-xl transition-all duration-300 hover:scale-110 hover:shadow-lg min-w-[44px] min-h-[44px] flex items-center justify-center"
                aria-label="Facebook"
              >
                <Icon
                  icon="lucide:facebook"
                  className="h-5 w-5 sm:h-6 sm:w-6"
                />
              </Link>
              <Link
                href="https://instagram.com"
                className="bg-white/10 hover:bg-white/20 p-3 sm:p-4 rounded-xl transition-all duration-300 hover:scale-110 hover:shadow-lg min-w-[44px] min-h-[44px] flex items-center justify-center"
                aria-label="Instagram"
              >
                <Icon
                  icon="lucide:instagram"
                  className="h-5 w-5 sm:h-6 sm:w-6"
                />
              </Link>
              <Link
                href="https://twitter.com"
                className="bg-white/10 hover:bg-white/20 p-3 sm:p-4 rounded-xl transition-all duration-300 hover:scale-110 hover:shadow-lg min-w-[44px] min-h-[44px] flex items-center justify-center"
                aria-label="Twitter"
              >
                <Icon icon="lucide:twitter" className="h-5 w-5 sm:h-6 sm:w-6" />
              </Link>
            </div>
            <p className="text-gray-300 text-sm sm:text-base leading-relaxed">
              Follow us for updates and news
            </p>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-white/30 mt-8 sm:mt-12 lg:mt-16 pt-6 sm:pt-8 lg:pt-10">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 sm:gap-6">
            <p className="text-gray-300 text-sm sm:text-base font-medium text-center sm:text-left">
              © {new Date().getFullYear()} Nepal Marketplace. All rights
              reserved.
            </p>
            <div className="flex flex-wrap justify-center sm:justify-end gap-4 sm:gap-6 lg:gap-8 text-sm sm:text-base">
              <Link
                href="/terms"
                className="text-gray-300 hover:text-white transition-all duration-300 hover:underline font-medium touch-manipulation min-h-[44px] flex items-center"
              >
                Terms
              </Link>
              <Link
                href="/privacy"
                className="text-gray-300 hover:text-white transition-all duration-300 hover:underline font-medium touch-manipulation min-h-[44px] flex items-center"
              >
                Privacy
              </Link>
              <Link
                href="/support"
                className="text-gray-300 hover:text-white transition-all duration-300 hover:underline font-medium touch-manipulation min-h-[44px] flex items-center"
              >
                Support
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
