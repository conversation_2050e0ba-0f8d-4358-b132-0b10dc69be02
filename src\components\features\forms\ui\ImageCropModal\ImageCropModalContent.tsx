"use client";
import React from "react";
import { Icon } from "@iconify/react";
import { useImageCropModal } from "./ImageCropModalProvider";
import { CropControls } from "./components/CropControls";
import { CropAreaComponent } from "./components/CropArea";
import { PreviewArea } from "./components/PreviewArea";
import { useKeyboardShortcuts } from "./hooks/useKeyboardShortcuts";
import { useCanvasUpdater } from "./hooks/useCanvasUpdater";

export const ImageCropModalContent: React.FC = () => {
  const { onCancel, imageLoaded, imageError, imageSrc, handleCropComplete } =
    useImageCropModal();

  // Initialize hooks
  useKeyboardShortcuts();
  useCanvasUpdater();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[95vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            Enhanced Crop Tool
          </h2>
          <div className="flex items-center gap-2">
            <span className="text-xs text-gray-500">
              Ctrl+Z: Undo | Ctrl+Shift+Z: Redo | Ctrl +/-: Zoom
            </span>
            <button
              onClick={onCancel}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <Icon icon="lucide:x" className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Controls */}
        <CropControls />

        {/* Main Content Area */}
        <div className="flex-1 p-4 overflow-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
            {/* Crop Area */}
            <div className="lg:col-span-2 flex justify-center">
              <CropAreaComponent />
            </div>

            {/* Preview Area */}
            <div className="lg:col-span-1">
              <PreviewArea />
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-4 border-t bg-gray-50">
          <div className="text-sm text-gray-600">
            Drag crop area to move • Drag corner to resize • Drag image to pan
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={onCancel}
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleCropComplete}
              disabled={!imageLoaded || imageError || !imageSrc}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
            >
              Apply Crop
            </button>
          </div>
        </div>

        {/* Hidden canvas for processing */}
        <canvas
          ref={useImageCropModal().hiddenCanvasRef}
          style={{ display: "none" }}
        />
      </div>
    </div>
  );
};
