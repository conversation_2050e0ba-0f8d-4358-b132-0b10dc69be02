"use client";

import { VerticalProductList } from "./index";
import type { Product } from "@/types/ecommerce";

// Sample data that matches the image you provided
const sampleProducts: Product[] = [
  {
    id: "1",
    title: "Toyota Corolla",
    price: 12345678,
    currency: "Rs.",
    location: "Lalitpur",
    images: ["/placeholder.svg?height=80&width=120"],
    seller: {
      id: "seller1",
      name: "<PERSON>",
      rating: 4.2,
    },
    postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
    views: 300,
    category: "vehicles",
    subcategory: "cars",
    slug: "toyota-corolla-1",
    description: "Well maintained Toyota Corolla",
    condition: "used",
    featured: false,
    delivery: {
      available: true,
      type: "both",
      cost: 100,
    },
    status: "active",
  },
  {
    id: "2",
    title: "Suzuki",
    price: 12345678,
    currency: "Rs.",
    location: "Lalitpur",
    images: ["/placeholder.svg?height=80&width=120"],
    seller: {
      id: "seller2",
      name: "<PERSON>",
      rating: 4.2,
    },
    postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    views: 300,
    category: "vehicles",
    subcategory: "motorcycles",
    slug: "suzuki-1",
    description: "Suzuki motorcycle in excellent condition",
    condition: "used",
    featured: false,
    delivery: {
      available: true,
      type: "both",
      cost: 100,
    },
    status: "active",
  },
  {
    id: "3",
    title: "Land for sale",
    price: 12345678,
    currency: "Rs.",
    location: "Pokhara",
    images: ["/placeholder.svg?height=80&width=120"],
    seller: {
      id: "seller3",
      name: "John",
      rating: 4.2,
    },
    postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    views: 300,
    category: "property",
    subcategory: "land",
    slug: "land-for-sale-1",
    description: "Prime land for sale in Pokhara",
    condition: "new",
    featured: false,
    delivery: {
      available: false,
      type: "pickup",
    },
    status: "active",
  },
  {
    id: "4",
    title: "Honda CBR 150R",
    price: 12345678,
    currency: "Rs.",
    location: "Lalitpur",
    images: ["/placeholder.svg?height=80&width=120"],
    seller: {
      id: "seller4",
      name: "John",
      rating: 4.2,
    },
    postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    views: 300,
    category: "vehicles",
    subcategory: "motorcycles",
    slug: "honda-cbr-150r-1",
    description: "Honda CBR 150R in perfect condition",
    condition: "used",
    featured: false,
    delivery: {
      available: true,
      type: "both",
      cost: 150,
    },
    status: "active",
  },
  {
    id: "5",
    title: "Toyota Corolla",
    price: 12345678,
    currency: "Rs.",
    location: "Lalitpur",
    images: ["/placeholder.svg?height=80&width=120"],
    seller: {
      id: "seller5",
      name: "John",
      rating: 4.2,
    },
    postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    views: 300,
    category: "vehicles",
    subcategory: "cars",
    slug: "toyota-corolla-2",
    description: "Another Toyota Corolla listing",
    condition: "used",
    featured: false,
    delivery: {
      available: true,
      type: "both",
      cost: 100,
    },
    status: "active",
  },
];

export function VerticalProductListExample() {
  const handleProductClick = (product: Product) => {
    console.log("Product clicked:", product);
    // You can navigate to product detail page here
    // router.push(`/product/${product.slug}`);
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">
        Vertical Product List Example
      </h1>

      <VerticalProductList
        products={sampleProducts}
        loading={false}
        onProductClick={handleProductClick}
        className="max-w-3xl"
      />
    </div>
  );
}
