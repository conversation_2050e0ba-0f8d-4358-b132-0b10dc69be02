"use client";
import Link from "next/link";
import { Icon } from "@iconify/react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

interface CartButtonProps {
  totalItems: number;
}

export function CartButton({ totalItems }: CartButtonProps) {
  return (
    <Link href="/cart">
      <Button
        variant="ghost"
        className="relative text-white hover:text-gray-200 hover:bg-white/10 transition-all duration-300 group p-0 w-10 h-10"
      >
        <div className="w-full h-full bg-[#1F5E64] border border-white rounded-lg flex items-center justify-center hover:bg-[#1a5157] transition-all duration-300">
          <Icon
            icon="material-symbols:shopping-cart"
            className="w-5 h-5 group-hover:scale-110 transition-transform duration-300"
          />
        </div>
        {totalItems > 0 && (
          <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 bg-red-500 hover:bg-red-600 text-white text-xs rounded-full flex items-center justify-center animate-pulse">
            {totalItems > 99 ? "99+" : totalItems}
          </Badge>
        )}
      </Button>
    </Link>
  );
}
