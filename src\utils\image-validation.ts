/**
 * Image validation utilities for ensuring reliable image loading
 * Provides validation, fallback handling, and URL sanitization
 */

import {
  validateAndFixImageUrl,
  getPlaceholderImageUrl,
  generateFallbackDataUrl,
} from "@/services/image-service";

// Known problematic image patterns
const PROBLEMATIC_PATTERNS = [
  "images.unsplash.com",
  "picsum.photos",
  "localhost:3000/images/placeholder.jpg",
  "photo-1594736797933-d0401ba2fe65",
  // Add more patterns as needed
];

// Valid image extensions
const VALID_IMAGE_EXTENSIONS = [
  ".jpg",
  ".jpeg",
  ".png",
  ".gif",
  ".webp",
  ".svg",
  ".bmp",
  ".ico",
];

/**
 * Check if a URL is a valid image URL
 */
export function isValidImageUrl(url: string): boolean {
  if (!url || typeof url !== "string") {
    return false;
  }

  // Allow blob and data URLs
  if (url.startsWith("blob:") || url.startsWith("data:")) {
    return true;
  }

  // Check for valid image extensions
  const hasValidExtension = VALID_IMAGE_EXTENSIONS.some((ext) =>
    url.toLowerCase().includes(ext)
  );

  // Check for known image services
  const isImageService =
    url.includes("via.placeholder.com") || url.includes("placeholder.com");

  return hasValidExtension || isImageService;
}

/**
 * Check if a URL contains problematic patterns
 */
export function hasProblematicPattern(url: string): boolean {
  return PROBLEMATIC_PATTERNS.some((pattern) => url.includes(pattern));
}

/**
 * Validate and fix an image URL
 */
export function validateImageUrl(
  url: string,
  category: string = "art-crafts",
  index: number = 0
): string {
  // Handle empty or invalid URLs
  if (!url || typeof url !== "string") {
    return getPlaceholderImageUrl();
  }

  // Handle blob and data URLs (these are valid)
  if (url.startsWith("blob:") || url.startsWith("data:")) {
    return url;
  }

  // Handle local assets (these should be valid)
  if (url.startsWith("/assets/") || url.startsWith("/images/")) {
    return url;
  }

  // Check for problematic patterns
  if (hasProblematicPattern(url)) {
    return validateAndFixImageUrl(url, category, index);
  }

  // Check if it's a valid image URL
  if (!isValidImageUrl(url)) {
    return getPlaceholderImageUrl();
  }

  return url;
}

/**
 * Validate an array of image URLs
 */
export function validateImageUrls(
  urls: string[],
  category: string = "art-crafts"
): string[] {
  if (!Array.isArray(urls) || urls.length === 0) {
    return [getPlaceholderImageUrl()];
  }

  return urls.map((url, index) => validateImageUrl(url, category, index));
}

/**
 * Get a safe image URL with fallback
 */
export function getSafeImageUrl(
  url: string | undefined,
  category: string = "art-crafts",
  index: number = 0
): string {
  if (!url) {
    return getPlaceholderImageUrl();
  }

  return validateImageUrl(url, category, index);
}

/**
 * Create a complete image configuration for a product
 */
export function createSafeImageConfig(
  images: string[] | undefined,
  category: string = "art-crafts",
  productId: string = "default"
) {
  const safeImages = validateImageUrls(images || [], category);
  const primaryImage = safeImages[0] || getPlaceholderImageUrl();

  return {
    primary: primaryImage,
    gallery: safeImages,
    placeholder: getPlaceholderImageUrl(),
    fallback: generateFallbackDataUrl(400, 400, "Product Image"),
    count: safeImages.length,
  };
}

/**
 * Preload an image to check if it loads successfully
 */
export function preloadImage(url: string): Promise<boolean> {
  return new Promise((resolve) => {
    const img = new Image();

    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);

    // Set a timeout to avoid hanging
    setTimeout(() => resolve(false), 5000);

    img.src = url;
  });
}

/**
 * Validate multiple images concurrently
 */
export async function validateImagesAsync(urls: string[]): Promise<string[]> {
  const validationPromises = urls.map(async (url, index) => {
    const isValid = await preloadImage(url);
    return isValid ? url : getPlaceholderImageUrl();
  });

  return Promise.all(validationPromises);
}

/**
 * Get image dimensions from URL (for responsive images)
 */
export function getImageDimensions(url: string): {
  width: number;
  height: number;
} {
  // Default dimensions
  const defaultDimensions = { width: 400, height: 400 };

  // No special handling needed for picsum URLs anymore

  // Extract dimensions from placeholder URLs
  if (url.includes("placeholder.com") || url.includes("via.placeholder.com")) {
    const match = url.match(/(\d+)x(\d+)/);
    if (match) {
      return {
        width: parseInt(match[1], 10),
        height: parseInt(match[2], 10),
      };
    }
  }

  return defaultDimensions;
}

/**
 * Generate responsive image URLs for different screen sizes
 */
export function generateResponsiveImageUrls(baseUrl: string) {
  const sizes = [
    { name: "thumbnail", width: 150, height: 150 },
    { name: "small", width: 300, height: 300 },
    { name: "medium", width: 600, height: 600 },
    { name: "large", width: 1200, height: 1200 },
  ];

  // For all URLs, return the same URL for all sizes (no picsum handling)
  return sizes.reduce((acc, size) => {
    acc[size.name] = baseUrl;
    return acc;
  }, {} as Record<string, string>);
}
