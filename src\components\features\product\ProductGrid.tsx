"use client";

import { memo, useMemo, useCallback } from "react";
import { useRouter } from "next/navigation";
// Redux imports - replacing context API
import { useAppSelector } from "@/store/hooks";
import {
  selectSelectedCategory,
  selectCurrentPage,
  selectSortBy,
} from "@/store/selectors";
// Advertisement API imports
import { useGetAdvertisementsQuery } from "@/store/api/advertisementApi";
import { convertAdvertisementsToProducts } from "@/utils/slug-utils";
import type { Product } from "@/types/ecommerce";
import type { QueryAdvertisementParams } from "@/types/advertisement";

import ProductDisplay from "./ProductDisplay";
import { ProductCardSkeleton, EmptyState } from "../../ui";
import { cn } from "@/lib/utils";

interface ProductGridProps {
  className?: string;
  maxProducts?: number;
  showAnimations?: boolean;
  viewMode?: "grid" | "list";
}

const ProductGrid = memo(function ProductGrid({
  className = "",
  maxProducts = 9,
  showAnimations = true,
  viewMode = "grid",
}: ProductGridProps) {
  const router = useRouter();

  // Redux selectors for filtering/sorting context
  const selectedCategory = useAppSelector(selectSelectedCategory);
  const currentPage = useAppSelector(selectCurrentPage);
  const sortBy = useAppSelector(selectSortBy);

  // Build query parameters for advertisement API
  const queryParams = useMemo((): QueryAdvertisementParams => {
    const params: QueryAdvertisementParams = {
      page: currentPage,
      limit: maxProducts,
      status: "active" as any, // Only show active advertisements
    };

    // Add category filter if selected
    if (selectedCategory?.id) {
      params.categoryId = selectedCategory.id;
    }

    // Add sorting
    if (sortBy === "newest") {
      params.sortBy = "createdAt";
      params.sortOrder = "DESC";
    } else if (sortBy === "oldest") {
      params.sortBy = "createdAt";
      params.sortOrder = "ASC";
    } else if (sortBy === "price-low") {
      params.sortBy = "price";
      params.sortOrder = "ASC";
    } else if (sortBy === "price-high") {
      params.sortBy = "price";
      params.sortOrder = "DESC";
    } else if (sortBy === "popular") {
      params.sortBy = "viewCount";
      params.sortOrder = "DESC";
    }

    return params;
  }, [currentPage, maxProducts, selectedCategory, sortBy]);

  // Fetch advertisements using RTK Query
  const {
    data: advertisementsResponse,
    isLoading: loading,
    error,
  } = useGetAdvertisementsQuery(queryParams);

  // Debug logging to see what's happening
  console.log("ProductGrid Debug:", {
    queryParams,
    advertisementsResponse,
    loading,
    error,
    apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL,
  });

  // Convert advertisements to products
  const currentProducts = useMemo(() => {
    if (!advertisementsResponse?.data) return [];
    return convertAdvertisementsToProducts(advertisementsResponse.data);
  }, [advertisementsResponse]);

  // Handle product selection - navigate to product detail page
  const handleProductSelect = useCallback(
    (product: Product) => {
      router.push(`/product/${product.slug}`);
    },
    [router]
  );

  // Handle error state
  if (error) {
    return (
      <EmptyState
        title="Failed to load products"
        description="There was an error loading the products. Please try again later."
      />
    );
  }

  // Loading state with skeleton cards
  if (loading) {
    return (
      <div
        className={cn(
          "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",
          className
        )}
      >
        {Array.from({ length: 8 }).map((_, index) => (
          <ProductCardSkeleton key={index} />
        ))}
      </div>
    );
  }

  // Main view using unified ProductDisplay component
  return (
    <ProductDisplay
      products={currentProducts}
      config={{
        viewMode,
        showAnimations,
      }}
      loading={loading}
      error={error}
      className={className}
      onProductSelect={handleProductSelect}
    />
  );
});

export default ProductGrid;

// ProductGridContent component removed - now using unified ProductDisplay
