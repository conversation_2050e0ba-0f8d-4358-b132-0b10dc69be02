"use client";

import React, { useState } from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import SavedListings from "./SavedListings";

interface SavedListingsTabProps {
  className?: string;
}

export default function SavedListingsTab({ className }: SavedListingsTabProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedCategory, setSelectedCategory] = useState("All");

  const categories = [
    { id: "All", label: "All Categories", count: 24 },
    { id: "Electronics", label: "Electronics", count: 8 },
    { id: "Vehicles", label: "Vehicles", count: 6 },
    { id: "Property", label: "Property", count: 4 },
    { id: "Fashion", label: "Fashion", count: 6 },
  ];

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Saved Items</h2>
          <p className="text-gray-600 mt-2">
            Your saved and favorite product listings
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode("grid")}
            className={
              viewMode === "grid"
                ? "bg-teal-50 border-teal-200 text-teal-700"
                : ""
            }
          >
            <Icon icon="mdi:grid" className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode("list")}
            className={
              viewMode === "list"
                ? "bg-teal-50 border-teal-200 text-teal-700"
                : ""
            }
          >
            <Icon icon="mdi:format-list-bulleted" className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-gray-200 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-pink-100 rounded-lg">
                <Icon icon="mdi:heart" className="w-6 h-6 text-pink-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Saved</p>
                <p className="text-2xl font-bold text-gray-900">24</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-gray-200 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Icon icon="mdi:eye" className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Recently Viewed</p>
                <p className="text-2xl font-bold text-gray-900">12</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-gray-200 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-green-100 rounded-lg">
                <Icon
                  icon="mdi:message-outline"
                  className="w-6 h-6 text-green-600"
                />
              </div>
              <div>
                <p className="text-sm text-gray-600">Inquiries Made</p>
                <p className="text-2xl font-bold text-gray-900">8</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-gray-200 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-orange-100 rounded-lg">
                <Icon icon="mdi:delete" className="w-6 h-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Removed</p>
                <p className="text-2xl font-bold text-gray-900">3</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="border-gray-200 shadow-sm">
        <CardContent className="p-6">
          {/* Category Filter */}
          <div className="flex flex-wrap gap-2 mb-6">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`
                  flex items-center gap-2 px-4 py-2 rounded-full transition-all duration-300
                  border-none cursor-pointer
                  ${
                    selectedCategory === category.id
                      ? "bg-teal-100 text-teal-700 border border-teal-200"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                  }
                `}
              >
                <span>{category.label}</span>
                <Badge
                  className={`
                    ${
                      selectedCategory === category.id
                        ? "bg-teal-200 text-teal-800"
                        : "bg-gray-200 text-gray-600"
                    }
                  `}
                >
                  {category.count}
                </Badge>
              </button>
            ))}
          </div>

          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Icon
                icon="mdi:magnify"
                className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
              />
              <input
                type="text"
                placeholder="Search saved items..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-3 bg-white border border-gray-200 rounded-lg shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-all duration-300"
              />
            </div>
            <Button
              variant="outline"
              className="px-6 py-3 border-gray-200 hover:border-teal-500 hover:text-teal-600 hover:bg-teal-50 transition-all duration-300"
            >
              <Icon icon="mdi:filter-variant" className="w-4 h-4 mr-2" />
              Sort & Filter
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Saved Listings Component */}
      <Card className="border-gray-200 shadow-sm">
        <CardContent className="p-6">
          <SavedListings />
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card className="border-gray-200 shadow-sm">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Quick Actions
          </h3>
          <div className="flex flex-wrap gap-3">
            <Button variant="outline" size="sm">
              <Icon icon="mdi:heart" className="w-4 h-4 mr-2" />
              Clear All Saved
            </Button>
            <Button variant="outline" size="sm">
              <Icon icon="mdi:delete" className="w-4 h-4 mr-2" />
              Remove Selected
            </Button>
            <Button variant="outline" size="sm">
              <Icon icon="mdi:message-outline" className="w-4 h-4 mr-2" />
              Contact Sellers
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
