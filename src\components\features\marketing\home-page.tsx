"use client";

import React from "react";
import { CategoryProductSectionsWrapper, ShopPageContent } from "../../layout";
import { AdBanner } from "./banner";

export default function HomePage() {
  console.log("HomePage component is rendering - API integration");

  return (
    <>
      {/* Ads Banner below navbar */}
      <div className="w-full mb-6">
        <div className=" ">
          <AdBanner
            className="w-full"
            autoPlay={true}
            showControls={true}
            muted={true}
          />
        </div>
      </div>

      <ShopPageContent
        showHero={false}
        showCategoryProductSections={false}
        renderCategoryProductSections={false}
        maxProducts={12}
      />
      {/* Full Width Category Product Sections */}
      <CategoryProductSectionsWrapper
        categoryProductSectionIds={undefined} // Use all available categories from API
      />
    </>
  );
}
