"use client";

import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Icon } from "@iconify/react";

interface JobProfileCardProps {
  onEdit?: () => void;
}

export default function JobProfileCard({
  onEdit: _onEdit,
}: JobProfileCardProps) {
  // Download handlers
  const handleDownloadCV = () => {
    // Create a temporary link element to trigger download
    const link = document.createElement("a");
    link.href = "/documents/ram-cv.pdf"; // Replace with actual CV file path
    link.download = "Ram_Shrestha_CV.pdf";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDownloadCoverLetter = () => {
    // Create a temporary link element to trigger download
    const link = document.createElement("a");
    link.href = "/documents/ram-cover-letter.pdf"; // Replace with actual cover letter file path
    link.download = "Ram_Shrestha_Cover_Letter.pdf";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="max-w-8xl mx-auto p-2 sm:p-4">
      <Card className="w-full border-gray-200">
        <CardContent className="p-4 sm:p-6">
          <div className="flex flex-col lg:flex-row gap-4 sm:gap-6 lg:items-stretch">
            {/* Main Content - 2/3 width */}
            <div className="flex-1 lg:w-2/3 space-y-4 sm:space-y-6 flex flex-col">
              {/* Basic Info */}
              <div className="text-center sm:text-left">
                <h2 className="text-base sm:text-lg font-medium text-gray-900">
                  Prakash Rai
                </h2>
                <p className="text-gray-600 text-sm sm:text-base break-all">
                  <EMAIL>
                </p>
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                <div className="text-center sm:text-left">
                  <span className="text-sm sm:text-base font-medium text-gray-700">
                    Phone:{" "}
                  </span>
                  <span className="text-sm sm:text-base text-gray-600">
                    9876543210
                  </span>
                </div>
                <div className="text-center sm:text-left">
                  <span className="text-sm sm:text-base font-medium text-gray-700">
                    Address:{" "}
                  </span>
                  <span className="text-sm sm:text-base text-gray-600">
                    Bhaktapur
                  </span>
                </div>
              </div>

              {/* Professional Bio Section */}
              <div>
                <h3 className="text-base sm:text-lg font-semibold text-gray-700 mb-2 text-center sm:text-left">
                  Professional Bio:
                </h3>
                <p className="text-sm sm:text-base text-gray-600 leading-relaxed text-center sm:text-left">
                  Experienced software developer with 5+ years in web
                  development. Passionate about creating user-friendly
                  applications and working with modern technologies like React,
                  Node.js, and cloud platforms.
                </p>
              </div>

              {/* Documents Section */}
              <div>
                <h3 className="text-base sm:text-lg font-semibold text-gray-700 mb-2 sm:mb-3 text-center sm:text-left">
                  Documents:
                </h3>
                <div className="flex flex-col sm:flex-row flex-wrap gap-3 sm:gap-4">
                  <div className="flex items-center justify-between gap-2 bg-gray-50 rounded-md p-2 sm:p-3 border border-gray-100 min-w-0 sm:min-w-[200px]">
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <div className="w-5 h-5 sm:w-6 sm:h-6 relative flex-shrink-0">
                        <Image
                          src="/document-icon-blue.png"
                          alt="CV icon"
                          fill
                          className="object-contain"
                        />
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="text-sm sm:text-base font-medium truncate">
                          CV/ Resume
                        </p>
                        <p className="text-xs text-gray-500 truncate">
                          ram.pdf
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-blue-100 flex-shrink-0"
                      onClick={handleDownloadCV}
                      title="Download CV"
                    >
                      <Icon
                        icon="lucide:download"
                        className="w-3 h-3 sm:w-4 sm:h-4 text-blue-600"
                      />
                    </Button>
                  </div>

                  <div className="flex items-center justify-between gap-2 bg-gray-50 rounded-md p-2 sm:p-3 border border-gray-100 min-w-0 sm:min-w-[200px]">
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <div className="w-5 h-5 sm:w-6 sm:h-6 relative flex-shrink-0">
                        <Image
                          src="/document-icon-green.png"
                          alt="Cover letter icon"
                          fill
                          className="object-contain"
                        />
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="text-sm sm:text-base font-medium truncate">
                          Cover Letter
                        </p>
                        <p className="text-xs text-gray-500 truncate">
                          Cover.pdf
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-green-100 flex-shrink-0"
                      onClick={handleDownloadCoverLetter}
                      title="Download Cover Letter"
                    >
                      <Icon
                        icon="lucide:download"
                        className="w-3 h-3 sm:w-4 sm:h-4 text-green-600"
                      />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Status Badges */}
              <div className="flex flex-wrap gap-1.5 sm:gap-2 justify-center sm:justify-start">
                <Badge
                  variant="secondary"
                  className="bg-green-100 text-green-800 hover:bg-green-100 text-xs sm:text-sm"
                >
                  Job Profile Completed
                </Badge>
                <Badge
                  variant="secondary"
                  className="bg-blue-100 text-blue-800 hover:bg-blue-100 text-xs sm:text-sm"
                >
                  Ready For Applications
                </Badge>
              </div>
            </div>

            {/* Skills & Expertise Section - 1/3 width on the right */}
            <div className="lg:w-1/3 lg:pl-4 lg:border-l border-gray-200 flex flex-col">
              <div>
                <h3 className="text-base sm:text-lg font-semibold text-gray-700 mb-2 sm:mb-3 text-center lg:text-left">
                  Skills & Expertise:
                </h3>
                <div className="space-y-3 sm:space-y-4">
                  {/* Technical Skills */}
                  <div>
                    <h4 className="text-sm sm:text-base font-medium text-gray-600 mb-2 text-center lg:text-left">
                      Technical Skills
                    </h4>
                    <div className="flex flex-wrap gap-1.5 sm:gap-2 justify-center lg:justify-start">
                      <Badge
                        variant="outline"
                        className="bg-blue-50 text-blue-700 border-blue-200 text-xs sm:text-sm"
                      >
                        React.js
                      </Badge>
                      <Badge
                        variant="outline"
                        className="bg-blue-50 text-blue-700 border-blue-200 text-xs sm:text-sm"
                      >
                        Node.js
                      </Badge>
                      <Badge
                        variant="outline"
                        className="bg-blue-50 text-blue-700 border-blue-200 text-xs sm:text-sm"
                      >
                        TypeScript
                      </Badge>
                      <Badge
                        variant="outline"
                        className="bg-blue-50 text-blue-700 border-blue-200 text-xs sm:text-sm"
                      >
                        Next.js
                      </Badge>
                      <Badge
                        variant="outline"
                        className="bg-blue-50 text-blue-700 border-blue-200 text-xs sm:text-sm"
                      >
                        MongoDB
                      </Badge>
                      <Badge
                        variant="outline"
                        className="bg-blue-50 text-blue-700 border-blue-200 text-xs sm:text-sm"
                      >
                        AWS
                      </Badge>
                    </div>
                  </div>

                  {/* Soft Skills */}
                  <div>
                    <h4 className="text-sm sm:text-base font-medium text-gray-600 mb-2 text-center lg:text-left">
                      Soft Skills
                    </h4>
                    <div className="flex flex-wrap gap-1.5 sm:gap-2 justify-center lg:justify-start">
                      <Badge
                        variant="outline"
                        className="bg-green-50 text-green-700 border-green-200 text-xs sm:text-sm"
                      >
                        Team Leadership
                      </Badge>
                      <Badge
                        variant="outline"
                        className="bg-green-50 text-green-700 border-green-200 text-xs sm:text-sm"
                      >
                        Problem Solving
                      </Badge>
                      <Badge
                        variant="outline"
                        className="bg-green-50 text-green-700 border-green-200 text-xs sm:text-sm"
                      >
                        Communication
                      </Badge>
                      <Badge
                        variant="outline"
                        className="bg-green-50 text-green-700 border-green-200 text-xs sm:text-sm"
                      >
                        Project Management
                      </Badge>
                    </div>
                  </div>

                  {/* Experience Level */}
                  <div>
                    <h4 className="text-sm sm:text-base font-medium text-gray-600 mb-2 text-center lg:text-left">
                      Experience Level
                    </h4>
                    <div className="flex flex-wrap gap-1.5 sm:gap-2 justify-center lg:justify-start">
                      <Badge
                        variant="outline"
                        className="bg-purple-50 text-purple-700 border-purple-200 text-xs sm:text-sm"
                      >
                        5+ Years Experience
                      </Badge>
                      <Badge
                        variant="outline"
                        className="bg-orange-50 text-orange-700 border-orange-200 text-xs sm:text-sm"
                      >
                        Senior Developer
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
