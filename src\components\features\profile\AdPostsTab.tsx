"use client";

import React, { useState } from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import EnhancedAdPostCard from "./EnhancedAdPostCard";
import type { UserListing, ListingAction } from "@/types/ecommerce";

interface AdPostsTabProps {
  listings: UserListing[];
  onListingAction: (action: ListingAction) => void;
}

const statusFilters = [
  { id: "All", label: "All", count: 0 },
  { id: "Hold", label: "On Hold", count: 0 },
  { id: "Sold", label: "Sold", count: 0 },
  { id: "Expired", label: "Expired", count: 0 },
];

export default function AdPostsTab({
  listings,
  onListingAction,
}: AdPostsTabProps) {
  const [statusFilter, setStatusFilter] = useState("All");
  const [searchQuery, setSearchQuery] = useState("");

  // Calculate counts for each status
  const statusCounts = {
    All: listings.length,
    Hold: listings.filter((l) => l.status === "hold").length,
    Sold: listings.filter((l) => l.status === "sold").length,
    Expired: listings.filter((l) => l.status === "expired").length,
  };

  // Filter listings based on status and search query
  const filteredListings = listings.filter((listing) => {
    const matchesSearch = listing.title
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesStatus =
      statusFilter === "All" || listing.status === statusFilter.toLowerCase();
    return matchesSearch && matchesStatus;
  });

  // Calculate stats
  const totalViews = listings.reduce(
    (sum, listing) => sum + (listing.views || 0),
    0
  );
  const totalInquiries = listings.reduce(
    (sum, listing) => sum + (listing.inquiries || 0),
    0
  );
  const activeListings = listings.filter((l) => l.status === "active").length;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">My Listings</h2>
          <p className="text-gray-600 mt-2">
            Manage your product listings and advertisements
          </p>
        </div>
        <Button className="bg-teal-600 hover:bg-teal-700 text-white">
          <Icon icon="lucide:plus" className="w-4 h-4 mr-2" />
          Create New Listing
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-gray-200 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Icon icon="lucide:package" className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Listings</p>
                <p className="text-2xl font-bold text-gray-900">
                  {listings.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-gray-200 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-green-100 rounded-lg">
                <Icon
                  icon="lucide:trending-up"
                  className="w-6 h-6 text-green-600"
                />
              </div>
              <div>
                <p className="text-sm text-gray-600">Active Listings</p>
                <p className="text-2xl font-bold text-gray-900">
                  {activeListings}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-gray-200 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-purple-100 rounded-lg">
                <Icon icon="lucide:eye" className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Views</p>
                <p className="text-2xl font-bold text-gray-900">{totalViews}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-gray-200 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-orange-100 rounded-lg">
                <Icon
                  icon="lucide:message-circle"
                  className="w-6 h-6 text-orange-600"
                />
              </div>
              <div>
                <p className="text-sm text-gray-600">Inquiries</p>
                <p className="text-2xl font-bold text-gray-900">
                  {totalInquiries}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="border-gray-200 shadow-sm">
        <CardContent className="p-6">
          {/* Status Filter Tabs */}
          <div className="flex flex-wrap gap-2 mb-6">
            {statusFilters.map((status) => (
              <button
                key={status.id}
                onClick={() => setStatusFilter(status.id)}
                className={`
                  flex items-center gap-2 px-4 py-2 rounded-full transition-all duration-300
                  border-none cursor-pointer
                  ${
                    statusFilter === status.id
                      ? "bg-teal-100 text-teal-700 border border-teal-200"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                  }
                `}
              >
                <span>{status.label}</span>
                <Badge
                  className={`
                    ${
                      statusFilter === status.id
                        ? "bg-teal-200 text-teal-800"
                        : "bg-gray-200 text-gray-600"
                    }
                  `}
                >
                  {statusCounts[status.id as keyof typeof statusCounts]}
                </Badge>
              </button>
            ))}
          </div>

          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Icon
                icon="lucide:search"
                className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
              />
              <input
                type="text"
                placeholder="Search your listings..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-3 bg-white border border-gray-200 rounded-lg shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-all duration-300"
              />
            </div>
            <Button
              variant="outline"
              className="px-6 py-3 border-gray-200 hover:border-teal-500 hover:text-teal-600 hover:bg-teal-50 transition-all duration-300"
            >
              <Icon icon="lucide:filter" className="w-4 h-4 mr-2" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Listings Grid */}
      {filteredListings.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredListings.map((listing) => (
            <EnhancedAdPostCard
              key={listing.id}
              listing={listing}
              onAction={onListingAction}
            />
          ))}
        </div>
      ) : (
        <Card className="border-gray-200 shadow-sm">
          <CardContent className="p-12">
            <div className="text-center">
              <Icon
                icon="lucide:package"
                className="w-16 h-16 text-gray-400 mx-auto mb-4"
              />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {searchQuery || statusFilter !== "All"
                  ? "No listings found"
                  : "No listings yet"}
              </h3>
              <p className="text-gray-600 mb-6 max-w-md mx-auto">
                {searchQuery || statusFilter !== "All"
                  ? "Try adjusting your search criteria or filters to find what you're looking for."
                  : "Start selling by creating your first product listing. It's quick and easy!"}
              </p>
              {!searchQuery && statusFilter === "All" && (
                <Button className="bg-teal-600 hover:bg-teal-700 text-white">
                  <Icon icon="lucide:plus" className="w-4 h-4 mr-2" />
                  Create Your First Listing
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
