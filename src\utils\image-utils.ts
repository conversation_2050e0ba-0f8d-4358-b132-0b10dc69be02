/**
 * Image utility functions for product images
 * Provides consistent image paths and fallback handling
 */

// Base path for product images in the public folder
const PRODUCT_IMAGES_BASE_PATH = "/assets/images/products";
const PLACEHOLDER_IMAGE_PATH = "/assets/images/placeholders/placeholder.jpg";

// Fallback data URL for when even placeholder fails
const FALLBACK_DATA_URL =
  "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+CiAgPHJlY3QgeD0iNTAlIiB5PSI1MCUiIHdpZHRoPSI4MCIgaGVpZ2h0PSI4MCIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTQwLCAtNDApIiBmaWxsPSIjZDFkNWRiIiByeD0iOCIvPgogIDxjaXJjbGUgY3g9IjUwJSIgY3k9IjQwJSIgcj0iMTIiIGZpbGw9IiM5Y2EzYWYiLz4KICA8cmVjdCB4PSI1MCUiIHk9IjU1JSIgd2lkdGg9IjQwIiBoZWlnaHQ9IjQiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMCwgMCkiIGZpbGw9IiM5Y2EzYWYiIHJ4PSIyIi8+CiAgPHJlY3QgeD0iNTAlIiB5PSI2NSUiIHdpZHRoPSI2MCIgaGVpZ2h0PSI0IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtMzAsIDApIiBmaWxsPSIjOWNhM2FmIiByeD0iMiIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iODUlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNmI3MjgwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTQiPk5vIEltYWdlIEF2YWlsYWJsZTwvdGV4dD4KPC9zdmc+";

/**
 * Get the full path for a product image
 * @param imageName - The image file name (e.g., 'product1.jpg', 'laptop-dell.png')
 * @returns Full path to the image
 */
export function getProductImagePath(imageName: string): string {
  if (!imageName) {
    return PLACEHOLDER_IMAGE_PATH;
  }

  // Remove leading slash if present
  const cleanImageName = imageName.startsWith("/")
    ? imageName.slice(1)
    : imageName;

  return `${PRODUCT_IMAGES_BASE_PATH}/${cleanImageName}`;
}

/**
 * Get multiple product image paths
 * @param imageNames - Array of image file names
 * @returns Array of full image paths
 */
export function getProductImagePaths(imageNames: string[]): string[] {
  if (!imageNames || imageNames.length === 0) {
    return [PLACEHOLDER_IMAGE_PATH];
  }

  return imageNames.map(getProductImagePath);
}

/**
 * Get the first product image or fallback to placeholder
 * @param images - Array of image file names or paths
 * @returns First image path or placeholder
 */
export function getFirstProductImage(images?: string[]): string {
  if (!images || images.length === 0) {
    return PLACEHOLDER_IMAGE_PATH;
  }

  // If it's already a full path (starts with /), return as is
  if (images[0].startsWith("/")) {
    return images[0];
  }

  return getProductImagePath(images[0]);
}

/**
 * Get the fallback data URL for when all other images fail
 * @returns Base64 encoded SVG data URL
 */
export function getFallbackDataUrl(): string {
  return FALLBACK_DATA_URL;
}

/**
 * Check if an image path is a placeholder
 * @param imagePath - The image path to check
 * @returns True if it's a placeholder image
 */
export function isPlaceholderImage(imagePath: string): boolean {
  return (
    imagePath === PLACEHOLDER_IMAGE_PATH ||
    imagePath.includes("placeholder") ||
    imagePath.includes("api/placeholder") ||
    !imagePath ||
    imagePath === "/placeholder.svg" ||
    imagePath === "/placeholder.jpg"
  );
}

/**
 * Generate a placeholder image URL for development/testing
 * @param width - Image width
 * @param height - Image height
 * @param text - Optional text to display
 * @returns Placeholder image URL
 */
export function generatePlaceholderUrl(
  width: number,
  height: number,
  text?: string
): string {
  const displayText = text ? encodeURIComponent(text) : "Product";
  return `https://via.placeholder.com/${width}x${height}/cccccc/969696?text=${displayText}`;
}

/**
 * Validate and sanitize image URLs
 * @param url - The image URL to validate
 * @returns A safe image URL or fallback
 */
export function validateImageUrl(url: string): string {
  if (!url) {
    return PLACEHOLDER_IMAGE_PATH;
  }

  // Handle blob URLs (from file uploads) - these are valid and should be returned as-is
  if (url.startsWith("blob:")) {
    return url;
  }

  // Handle data URLs - these are valid and should be returned as-is
  if (url.startsWith("data:")) {
    return url;
  }

  // Handle local assets
  if (url.startsWith("/assets/") || url.startsWith("/images/")) {
    return url;
  }

  // List of known problematic URLs or patterns
  const problematicPatterns = [
    "images.unsplash.com",
    "picsum.photos",
    "localhost:3000/images/placeholder.jpg",
    "photo-1594736797933-d0401ba2fe65",
    // Add more problematic patterns here as needed
  ];

  // Check if URL contains problematic patterns
  const isProblematic = problematicPatterns.some((pattern) =>
    url.includes(pattern)
  );

  if (isProblematic) {
    return PLACEHOLDER_IMAGE_PATH;
  }

  // If it's a local path, ensure it starts with /
  if (!url.startsWith("http") && !url.startsWith("/")) {
    return `/${url}`;
  }

  return url;
}

// Predefined image file names for easy reference
export const SAMPLE_PRODUCT_IMAGES = {
  // Electronics
  LAPTOP_1: "laptop-1.png",
  LAPTOP_2: "laptop-1.png",
  PHONE_1: "laptop-1.png",
  PHONE_2: "laptop-1.png",
  TABLET_1: "tablet-1.jpg",
  HEADPHONES_1: "headphones-1.jpg",

  // Fashion
  SHIRT_1: "shirt-1.jpg",
  DRESS_1: "dress-1.jpg",
  SHOES_1: "shoes-1.jpg",
  BAG_1: "bag-1.jpg",

  // Home & Garden
  CHAIR_1: "chair-1.jpg",
  TABLE_1: "table-1.jpg",
  LAMP_1: "lamp-1.jpg",
  PLANT_1: "plant-1.jpg",

  // Sports
  BIKE_1: "bike-1.jpg",
  BALL_1: "ball-1.jpg",

  // Books
  BOOK_1: "book-1.jpg",

  // Automotive
  CAR_1: "car-1.jpg",

  // Default placeholder
  PLACEHOLDER: "placeholder.jpg",
} as const;
