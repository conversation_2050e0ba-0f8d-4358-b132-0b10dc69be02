/**
 * Debug script to test profile API endpoints
 * Run this in browser console to debug the profile API issue
 */

// Check if we're in browser environment
if (typeof window !== 'undefined') {
  console.log('🔍 Profile API Debug Script');
  console.log('============================');
  
  // 1. Check authentication token
  const authToken = localStorage.getItem('auth_token');
  console.log('1. Auth Token:', authToken ? '✅ Present' : '❌ Missing');
  if (authToken) {
    console.log('   Token preview:', authToken.substring(0, 20) + '...');
  }
  
  // 2. Check API base URL
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://sasto-api.webstudiomatrix.com/api/v1';
  console.log('2. API Base URL:', apiBaseUrl);
  
  // 3. Test profile endpoint manually
  async function testProfileEndpoint() {
    console.log('\n3. Testing Profile Endpoint...');
    
    try {
      const response = await fetch(`${apiBaseUrl}/users/profile`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          ...(authToken && { 'Authorization': `Bearer ${authToken}` })
        }
      });
      
      console.log('   Status:', response.status);
      console.log('   Status Text:', response.statusText);
      console.log('   Headers:', Object.fromEntries(response.headers.entries()));
      
      if (response.ok) {
        const data = await response.json();
        console.log('   ✅ Success! Profile data:', data);
      } else {
        const errorText = await response.text();
        console.log('   ❌ Error response:', errorText);
      }
    } catch (error) {
      console.log('   ❌ Network error:', error.message);
    }
  }
  
  // 4. Test authentication endpoint
  async function testAuthEndpoint() {
    console.log('\n4. Testing Auth Endpoint...');
    
    try {
      const response = await fetch(`${apiBaseUrl}/auth/profile`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          ...(authToken && { 'Authorization': `Bearer ${authToken}` })
        }
      });
      
      console.log('   Status:', response.status);
      console.log('   Status Text:', response.statusText);
      
      if (response.ok) {
        const data = await response.json();
        console.log('   ✅ Auth profile works! Data:', data);
      } else {
        const errorText = await response.text();
        console.log('   ❌ Auth profile error:', errorText);
      }
    } catch (error) {
      console.log('   ❌ Auth endpoint error:', error.message);
    }
  }
  
  // 5. Test API health
  async function testApiHealth() {
    console.log('\n5. Testing API Health...');
    
    try {
      const response = await fetch(`${apiBaseUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      console.log('   Status:', response.status);
      
      if (response.ok) {
        const data = await response.json();
        console.log('   ✅ API is healthy:', data);
      } else {
        console.log('   ❌ API health check failed');
      }
    } catch (error) {
      console.log('   ❌ API health error:', error.message);
    }
  }
  
  // Run all tests
  async function runAllTests() {
    await testApiHealth();
    await testAuthEndpoint();
    await testProfileEndpoint();
    
    console.log('\n📋 Summary:');
    console.log('- If auth token is missing, you need to log in first');
    console.log('- If API health fails, the backend might be down');
    console.log('- If auth profile works but users/profile fails, there might be a routing issue');
    console.log('\n💡 Next steps:');
    console.log('1. Make sure you are logged in');
    console.log('2. Check if the backend is running');
    console.log('3. Verify the correct API endpoints');
  }
  
  // Export functions for manual testing
  window.debugProfileApi = {
    testProfileEndpoint,
    testAuthEndpoint,
    testApiHealth,
    runAllTests
  };
  
  console.log('\n🚀 Run debugProfileApi.runAllTests() to test all endpoints');
  
  // Auto-run tests
  runAllTests();
} else {
  console.log('This script should be run in a browser environment');
}
