"use client";

import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/toast";
import {
  useGetPrivacySettingsQuery,
  useUpdatePrivacySettingsMutation,
} from "@/store/api/userApi";
import { PrivacySettings } from "@/types/auth";

interface PrivacySettingsTabProps {
  className?: string;
}

const privacyOptions = [
  {
    key: "showEmail" as keyof PrivacySettings,
    title: "Show Email Address",
    description: "Allow others to see your email address on your profile",
    icon: "lucide:mail",
  },
  {
    key: "showPhone" as keyof PrivacySettings,
    title: "Show Phone Number",
    description: "Allow others to see your phone number on your profile",
    icon: "lucide:phone",
  },
  {
    key: "showFullName" as keyof PrivacySettings,
    title: "Show Full Name",
    description: "Display your full name on your public profile",
    icon: "lucide:user",
  },
  {
    key: "showProfilePicture" as keyof PrivacySettings,
    title: "Show Profile Picture",
    description: "Display your profile picture to other users",
    icon: "lucide:image",
  },
  {
    key: "showBio" as keyof PrivacySettings,
    title: "Show Bio",
    description: "Allow others to see your bio/description",
    icon: "lucide:file-text",
  },
  {
    key: "showLocation" as keyof PrivacySettings,
    title: "Show Location",
    description: "Display your location information publicly",
    icon: "lucide:map-pin",
  },
  {
    key: "showLastSeen" as keyof PrivacySettings,
    title: "Show Last Seen",
    description: "Let others see when you were last active",
    icon: "lucide:clock",
  },
  {
    key: "allowDirectMessages" as keyof PrivacySettings,
    title: "Allow Direct Messages",
    description: "Allow users to send you direct messages",
    icon: "lucide:message-circle",
  },
  {
    key: "searchableByEmail" as keyof PrivacySettings,
    title: "Searchable by Email",
    description: "Allow others to find your profile using your email",
    icon: "lucide:search",
  },
  {
    key: "searchableByPhone" as keyof PrivacySettings,
    title: "Searchable by Phone",
    description: "Allow others to find your profile using your phone number",
    icon: "lucide:smartphone",
  },
];

export default function PrivacySettingsTab({
  className,
}: PrivacySettingsTabProps) {
  const { addToast } = useToast();
  const [localSettings, setLocalSettings] = useState<PrivacySettings | null>(
    null
  );
  const [hasChanges, setHasChanges] = useState(false);

  const {
    data: privacySettings,
    isLoading,
    error,
    refetch,
  } = useGetPrivacySettingsQuery();

  const [updatePrivacySettings, { isLoading: isUpdating }] =
    useUpdatePrivacySettingsMutation();

  // Initialize local settings when data is loaded
  useEffect(() => {
    if (privacySettings) {
      setLocalSettings(privacySettings);
      setHasChanges(false);
    }
  }, [privacySettings]);

  const handleToggle = (key: keyof PrivacySettings) => {
    if (!localSettings) return;

    const newSettings = {
      ...localSettings,
      [key]: !localSettings[key],
    };

    setLocalSettings(newSettings);
    setHasChanges(true);
  };

  const handleSave = async () => {
    if (!localSettings || !hasChanges) return;

    try {
      await updatePrivacySettings(localSettings).unwrap();
      setHasChanges(false);
      addToast({
        type: "success",
        title: "Privacy Settings Updated",
        description: "Your privacy preferences have been saved successfully.",
      });
    } catch (error) {
      console.error("Failed to update privacy settings:", error);
      addToast({
        type: "error",
        title: "Update Failed",
        description: "Failed to update privacy settings. Please try again.",
      });
    }
  };

  const handleReset = () => {
    if (privacySettings) {
      setLocalSettings(privacySettings);
      setHasChanges(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center space-x-2">
          <Icon icon="lucide:loader-2" className="h-4 w-4 animate-spin" />
          <span>Loading privacy settings...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <Icon
          icon="lucide:alert-circle"
          className="h-8 w-8 text-red-500 mb-2"
        />
        <h3 className="text-lg font-semibold text-gray-900 mb-1">
          Error Loading Settings
        </h3>
        <p className="text-gray-600 mb-4">
          Unable to load your privacy settings.
        </p>
        <Button onClick={() => refetch()} variant="outline">
          <Icon icon="lucide:refresh-cw" className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  if (!localSettings) {
    return null;
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Icon icon="lucide:shield" className="h-5 w-5" />
            <span>Privacy Settings</span>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Control what information is visible to other users and how they can
            interact with you.
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {privacyOptions.map((option) => (
            <div
              key={option.key}
              className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0"
            >
              <div className="flex items-start space-x-3 flex-1">
                <Icon
                  icon={option.icon}
                  className="h-5 w-5 text-gray-500 mt-0.5"
                />
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-gray-900">
                    {option.title}
                  </h4>
                  <p className="text-sm text-gray-600 mt-1">
                    {option.description}
                  </p>
                </div>
              </div>
              <Switch
                checked={localSettings[option.key]}
                onCheckedChange={() => handleToggle(option.key)}
                className="ml-4"
              />
            </div>
          ))}

          {hasChanges && (
            <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
              <Button
                variant="outline"
                onClick={handleReset}
                disabled={isUpdating}
              >
                Reset
              </Button>
              <Button
                onClick={handleSave}
                disabled={isUpdating}
                className="min-w-[100px]"
              >
                {isUpdating ? (
                  <>
                    <Icon
                      icon="lucide:loader-2"
                      className="h-4 w-4 mr-2 animate-spin"
                    />
                    Saving...
                  </>
                ) : (
                  <>
                    <Icon icon="lucide:save" className="h-4 w-4 mr-2" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
