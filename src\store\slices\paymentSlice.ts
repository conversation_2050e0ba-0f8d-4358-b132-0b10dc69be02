import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import {
  TransactionResponseDto,
  PaginatedTransactionsResponse,
  WalletResponseDto,
  WalletTopupDto,
  WalletWithdrawDto,
  WalletTransferDto,
  WalletTransferResponse,
  CreateTransactionDto,
  CreatePaymentMethodDto,
  PaymentMethodResponseDto,
  TransactionSummary,
} from "@/types/orders";
import { PaymentService } from "@/services/payment-service";

// Define the payment state interface
export interface PaymentState {
  // Transactions
  transactions: TransactionResponseDto[];
  transactionsLoading: boolean;
  transactionsError?: string;
  transactionsPagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };

  // Wallet
  wallet?: WalletResponseDto;
  walletLoading: boolean;
  walletError?: string;

  // Payment Methods
  paymentMethods: PaymentMethodResponseDto[];
  paymentMethodsLoading: boolean;
  paymentMethodsError?: string;

  // Transaction Summary
  transactionSummary?: TransactionSummary;
  summaryLoading: boolean;
  summaryError?: string;

  // Current operation loading states
  topupLoading: boolean;
  withdrawLoading: boolean;
  transferLoading: boolean;
  createTransactionLoading: boolean;
}

// Initial state
const initialState: PaymentState = {
  transactions: [],
  transactionsLoading: false,
  transactionsError: undefined,
  transactionsPagination: {
    page: 1,
    limit: 20,
    total: 0,
    hasMore: false,
  },

  wallet: undefined,
  walletLoading: false,
  walletError: undefined,

  paymentMethods: [],
  paymentMethodsLoading: false,
  paymentMethodsError: undefined,

  transactionSummary: undefined,
  summaryLoading: false,
  summaryError: undefined,

  topupLoading: false,
  withdrawLoading: false,
  transferLoading: false,
  createTransactionLoading: false,
};

// ===== ASYNC THUNKS =====

// Transaction Management
export const createTransaction = createAsyncThunk(
  "payment/createTransaction",
  async (data: CreateTransactionDto, { rejectWithValue }) => {
    try {
      return await PaymentService.createTransaction(data);
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to create transaction");
    }
  }
);

export const fetchTransactions = createAsyncThunk(
  "payment/fetchTransactions",
  async ({ page = 1, limit = 20 }: { page?: number; limit?: number } = {}, { rejectWithValue }) => {
    try {
      return await PaymentService.getUserTransactions(page, limit);
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to fetch transactions");
    }
  }
);

export const fetchTransactionSummary = createAsyncThunk(
  "payment/fetchTransactionSummary",
  async (_, { rejectWithValue }) => {
    try {
      return await PaymentService.getTransactionSummary();
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to fetch transaction summary");
    }
  }
);

// Wallet Operations
export const fetchWallet = createAsyncThunk(
  "payment/fetchWallet",
  async (_, { rejectWithValue }) => {
    try {
      return await PaymentService.getWallet();
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to fetch wallet");
    }
  }
);

export const walletTopup = createAsyncThunk(
  "payment/walletTopup",
  async (data: WalletTopupDto, { rejectWithValue }) => {
    try {
      return await PaymentService.walletTopup(data);
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to top up wallet");
    }
  }
);

export const walletWithdraw = createAsyncThunk(
  "payment/walletWithdraw",
  async (data: WalletWithdrawDto, { rejectWithValue }) => {
    try {
      return await PaymentService.walletWithdraw(data);
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to withdraw from wallet");
    }
  }
);

export const walletTransfer = createAsyncThunk(
  "payment/walletTransfer",
  async (data: WalletTransferDto, { rejectWithValue }) => {
    try {
      return await PaymentService.walletTransfer(data);
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to transfer money");
    }
  }
);

// Payment Methods
export const createPaymentMethod = createAsyncThunk(
  "payment/createPaymentMethod",
  async (data: CreatePaymentMethodDto, { rejectWithValue }) => {
    try {
      return await PaymentService.createPaymentMethod(data);
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to create payment method");
    }
  }
);

// Create the payment slice
const paymentSlice = createSlice({
  name: "payment",
  initialState,
  reducers: {
    // Clear errors
    clearTransactionsError: (state) => {
      state.transactionsError = undefined;
    },
    clearWalletError: (state) => {
      state.walletError = undefined;
    },
    clearPaymentMethodsError: (state) => {
      state.paymentMethodsError = undefined;
    },
    clearSummaryError: (state) => {
      state.summaryError = undefined;
    },

    // Pagination
    setTransactionsPagination: (state, action: PayloadAction<Partial<PaymentState['transactionsPagination']>>) => {
      state.transactionsPagination = { ...state.transactionsPagination, ...action.payload };
    },

    // Reset states
    resetTransactions: (state) => {
      state.transactions = [];
      state.transactionsPagination = {
        page: 1,
        limit: 20,
        total: 0,
        hasMore: false,
      };
    },
  },
  extraReducers: (builder) => {
    builder
      // Create transaction
      .addCase(createTransaction.pending, (state) => {
        state.createTransactionLoading = true;
        state.transactionsError = undefined;
      })
      .addCase(createTransaction.fulfilled, (state, action) => {
        state.createTransactionLoading = false;
        // Add the new transaction to the beginning of the list
        state.transactions.unshift(action.payload);
      })
      .addCase(createTransaction.rejected, (state, action) => {
        state.createTransactionLoading = false;
        state.transactionsError = action.payload as string;
      })

      // Fetch transactions
      .addCase(fetchTransactions.pending, (state) => {
        state.transactionsLoading = true;
        state.transactionsError = undefined;
      })
      .addCase(fetchTransactions.fulfilled, (state, action) => {
        state.transactionsLoading = false;
        const { transactions, total, page, limit } = action.payload;
        
        if (page === 1) {
          state.transactions = transactions;
        } else {
          state.transactions.push(...transactions);
        }
        
        state.transactionsPagination = {
          page,
          limit,
          total,
          hasMore: transactions.length === limit,
        };
      })
      .addCase(fetchTransactions.rejected, (state, action) => {
        state.transactionsLoading = false;
        state.transactionsError = action.payload as string;
      })

      // Fetch transaction summary
      .addCase(fetchTransactionSummary.pending, (state) => {
        state.summaryLoading = true;
        state.summaryError = undefined;
      })
      .addCase(fetchTransactionSummary.fulfilled, (state, action) => {
        state.summaryLoading = false;
        state.transactionSummary = action.payload;
      })
      .addCase(fetchTransactionSummary.rejected, (state, action) => {
        state.summaryLoading = false;
        state.summaryError = action.payload as string;
      })

      // Fetch wallet
      .addCase(fetchWallet.pending, (state) => {
        state.walletLoading = true;
        state.walletError = undefined;
      })
      .addCase(fetchWallet.fulfilled, (state, action) => {
        state.walletLoading = false;
        state.wallet = action.payload;
      })
      .addCase(fetchWallet.rejected, (state, action) => {
        state.walletLoading = false;
        state.walletError = action.payload as string;
      })

      // Wallet topup
      .addCase(walletTopup.pending, (state) => {
        state.topupLoading = true;
        state.walletError = undefined;
      })
      .addCase(walletTopup.fulfilled, (state, action) => {
        state.topupLoading = false;
        // Add the transaction to the list
        state.transactions.unshift(action.payload);
        // Update wallet balance if we have wallet data
        if (state.wallet) {
          state.wallet.balance += action.payload.amount;
          state.wallet.formattedBalance = PaymentService.formatAmount(state.wallet.balance, state.wallet.currency);
        }
      })
      .addCase(walletTopup.rejected, (state, action) => {
        state.topupLoading = false;
        state.walletError = action.payload as string;
      })

      // Wallet withdraw
      .addCase(walletWithdraw.pending, (state) => {
        state.withdrawLoading = true;
        state.walletError = undefined;
      })
      .addCase(walletWithdraw.fulfilled, (state, action) => {
        state.withdrawLoading = false;
        // Add the transaction to the list
        state.transactions.unshift(action.payload);
        // Update wallet balance if we have wallet data
        if (state.wallet) {
          state.wallet.balance -= action.payload.amount;
          state.wallet.formattedBalance = PaymentService.formatAmount(state.wallet.balance, state.wallet.currency);
        }
      })
      .addCase(walletWithdraw.rejected, (state, action) => {
        state.withdrawLoading = false;
        state.walletError = action.payload as string;
      })

      // Wallet transfer
      .addCase(walletTransfer.pending, (state) => {
        state.transferLoading = true;
        state.walletError = undefined;
      })
      .addCase(walletTransfer.fulfilled, (state, action) => {
        state.transferLoading = false;
        // Add the sender transaction to the list
        state.transactions.unshift(action.payload.senderTransaction);
        // Update wallet balance if we have wallet data
        if (state.wallet) {
          state.wallet.balance -= action.payload.senderTransaction.amount;
          state.wallet.formattedBalance = PaymentService.formatAmount(state.wallet.balance, state.wallet.currency);
        }
      })
      .addCase(walletTransfer.rejected, (state, action) => {
        state.transferLoading = false;
        state.walletError = action.payload as string;
      })

      // Create payment method
      .addCase(createPaymentMethod.pending, (state) => {
        state.paymentMethodsLoading = true;
        state.paymentMethodsError = undefined;
      })
      .addCase(createPaymentMethod.fulfilled, (state, action) => {
        state.paymentMethodsLoading = false;
        state.paymentMethods.push(action.payload);
        // If this is set as default, update other payment methods
        if (action.payload.isDefault) {
          state.paymentMethods.forEach(method => {
            if (method.id !== action.payload.id) {
              method.isDefault = false;
            }
          });
        }
      })
      .addCase(createPaymentMethod.rejected, (state, action) => {
        state.paymentMethodsLoading = false;
        state.paymentMethodsError = action.payload as string;
      });
  },
});

// Export actions
export const {
  clearTransactionsError,
  clearWalletError,
  clearPaymentMethodsError,
  clearSummaryError,
  setTransactionsPagination,
  resetTransactions,
} = paymentSlice.actions;

// Export reducer
export default paymentSlice.reducer;
