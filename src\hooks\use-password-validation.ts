import { useState, useEffect } from "react";

export interface PasswordValidation {
  isValid: boolean;
  errors: string[];
  strength: "weak" | "medium" | "strong";
}

export function usePasswordValidation(password: string): PasswordValidation {
  const [validation, setValidation] = useState<PasswordValidation>({
    isValid: false,
    errors: [],
    strength: "weak",
  });

  useEffect(() => {
    const errors: string[] = [];
    let score = 0;

    // Check length
    if (password.length < 8) {
      errors.push("At least 8 characters");
    } else {
      score += 1;
    }

    // Check uppercase
    if (!/[A-Z]/.test(password)) {
      errors.push("One uppercase letter");
    } else {
      score += 1;
    }

    // Check lowercase
    if (!/[a-z]/.test(password)) {
      errors.push("One lowercase letter");
    } else {
      score += 1;
    }

    // Check numbers
    if (!/\d/.test(password)) {
      errors.push("One number");
    } else {
      score += 1;
    }

    // Check special characters (optional for strength)
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 1;
    }

    // Determine strength
    let strength: "weak" | "medium" | "strong" = "weak";
    if (score >= 4) {
      strength = "strong";
    } else if (score >= 3) {
      strength = "medium";
    }

    setValidation({
      isValid: errors.length === 0,
      errors,
      strength,
    });
  }, [password]);

  return validation;
}

export function usePasswordMatch(password: string, confirmPassword: string) {
  const [isMatch, setIsMatch] = useState(true);

  useEffect(() => {
    if (confirmPassword.length > 0) {
      setIsMatch(password === confirmPassword);
    } else {
      setIsMatch(true);
    }
  }, [password, confirmPassword]);

  return isMatch;
}
