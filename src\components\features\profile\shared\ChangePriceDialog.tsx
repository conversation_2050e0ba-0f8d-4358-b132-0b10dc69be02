"use client";

import React, { useState } from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/toast";
import type { UserListing, ListingAction } from "@/types/ecommerce";

interface ChangePriceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  listing: UserListing;
  onAction: (action: ListingAction) => void;
  isLoading?: boolean;
  onLoadingChange?: (loading: boolean) => void;
}

export function ChangePriceDialog({
  open,
  onOpenChange,
  listing,
  onAction,
  isLoading = false,
  onLoadingChange,
}: ChangePriceDialogProps) {
  const [newPrice, setNewPrice] = useState(listing.price.replace(/[^\d]/g, ""));
  const { addToast } = useToast();

  const handleChangePrice = async () => {
    if (!newPrice.trim()) {
      addToast({
        type: "error",
        title: "Price required",
        description: "Please enter a valid price.",
      });
      return;
    }

    onLoadingChange?.(true);
    try {
      onAction({
        type: "edit",
        productId: listing.id,
        data: {
          price: `₹. ${newPrice}`,
        },
      });

      addToast({
        type: "success",
        title: "Price updated!",
        description: `Price for ${listing.title} has been updated to ₹. ${newPrice}`,
      });

      onOpenChange(false);
    } catch (_error) {
      addToast({
        type: "error",
        title: "Error",
        description: "Failed to update price. Please try again.",
      });
    } finally {
      onLoadingChange?.(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    setNewPrice(listing.price.replace(/[^\d]/g, ""));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Update Price</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="newPrice">New Price</Label>
            <div className="relative">
              <Icon
                icon="lucide:indian-rupee"
                className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"
              />
              <Input
                id="newPrice"
                type="number"
                placeholder="Enter new price"
                value={newPrice}
                onChange={(e) => setNewPrice(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleChangePrice}
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? "Updating..." : "Update Price"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
