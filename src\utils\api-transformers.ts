import { Category, Subcategory } from "@/types/ecommerce";
import {
  CategoryResponseDto,
  SubcategoryResponseDto,
  CategoriesListResponseDto,
} from "@/types/api";

/**
 * Transform backend subcategory response to frontend format
 */
export const transformSubcategoryResponse = (
  backendSubcategory: SubcategoryResponseDto
): Subcategory => {
  if (!backendSubcategory) {
    console.warn(
      "transformSubcategoryResponse: backendSubcategory is null/undefined"
    );
    throw new Error("Invalid subcategory data");
  }

  return {
    id: backendSubcategory.id,
    name: backendSubcategory.name,
    slug: backendSubcategory.slug,
    description: backendSubcategory.description,
    productCount: backendSubcategory.advertisementCount || 0,
  };
};

/**
 * Transform backend category response to frontend format
 */
export const transformCategoryResponse = (
  backendCategory: CategoryResponseDto
): Category => {
  if (!backendCategory) {
    console.warn(
      "transformCategoryResponse: backendCategory is null/undefined"
    );
    throw new Error("Invalid category data");
  }

  return {
    id: backendCategory.id,
    name: backendCategory.name,
    slug: backendCategory.slug,
    description: backendCategory.description,
    icon: backendCategory.iconUrl || "📦",
    productCount: backendCategory.advertisementCount || 0,
    subcategories:
      backendCategory.subcategories?.map(transformSubcategoryResponse) || [],
  };
};

/**
 * Transform backend categories list response to frontend format
 */
export const transformCategoriesListResponse = (
  response: CategoriesListResponseDto
): Category[] => {
  // Handle cases where response or response.data might be undefined/null
  if (!response) {
    console.warn("transformCategoriesListResponse: response is null/undefined");
    return [];
  }

  if (!response.data) {
    console.warn(
      "transformCategoriesListResponse: response.data is null/undefined",
      response
    );
    return [];
  }

  if (!Array.isArray(response.data)) {
    console.warn(
      "transformCategoriesListResponse: response.data is not an array",
      response.data
    );
    return [];
  }

  return response.data.map(transformCategoryResponse);
};

/**
 * Transform single category response (for individual category endpoints)
 */
export const transformSingleCategoryResponse = (
  backendCategory: CategoryResponseDto
): Category => {
  return transformCategoryResponse(backendCategory);
};

/**
 * Transform subcategories array response
 */
export const transformSubcategoriesResponse = (
  backendSubcategories: SubcategoryResponseDto[]
): Subcategory[] => {
  if (!backendSubcategories) {
    console.warn(
      "transformSubcategoriesResponse: backendSubcategories is null/undefined"
    );
    return [];
  }

  if (!Array.isArray(backendSubcategories)) {
    console.warn(
      "transformSubcategoriesResponse: backendSubcategories is not an array",
      backendSubcategories
    );
    return [];
  }

  return backendSubcategories.map(transformSubcategoryResponse);
};

/**
 * Map icon URLs to emoji icons for fallback
 */
export const getIconFromUrl = (iconUrl?: string): string => {
  if (!iconUrl) return "📦";

  // If it's already an emoji, return as is
  if (
    /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u.test(
      iconUrl
    )
  ) {
    return iconUrl;
  }

  // Map category names to icons (fallback)
  const categoryIconMap: Record<string, string> = {
    "art-crafts": "🎨",
    electronics: "📱",
    vehicles: "🚗",
    property: "🏠",
    jobs: "💼",
    "pet-animal": "🐕",
    photography: "📸",
    "clothes-fashions": "👗",
    "sports-fitness": "⚽",
    "books-media": "📚",
    "home-garden": "🏡",
    services: "🔧",
  };

  // Try to extract category from URL or use default
  const categoryKey = Object.keys(categoryIconMap).find((key) =>
    iconUrl.toLowerCase().includes(key)
  );

  return categoryKey ? categoryIconMap[categoryKey] : "📦";
};
