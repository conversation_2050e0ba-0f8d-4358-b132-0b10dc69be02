# E-Commerce Platform

A modern, full-featured e-commerce platform built with Next.js 15, TypeScript, and Tailwind CSS.

🌐 **Live Demo**: [https://ecom.webstudiomatrix.com/](https://ecom.webstudiomatrix.com/)

## 🚀 Features

- **Modern Tech Stack**: Next.js 15 with App Router, TypeScript, Tailwind CSS
- **State Management**: Redux Toolkit with Redux Persist
- **Authentication**: Secure user authentication system
- **Product Management**: Advanced product filtering and categorization
- **Responsive Design**: Mobile-first approach with modern UI components
- **Performance Optimized**: Image optimization, lazy loading, and bundle optimization

## 🛠️ Tech Stack

- **Framework**: Next.js 15
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4
- **State Management**: Redux Toolkit
- **UI Components**: Radix UI
- **Icons**: Iconify
- **Forms**: Formik with Yup validation
- **Image Processing**: React Image Crop
- **Deployment**: Docker

## 📁 Project Structure

```
ecommerce-platform/
├── docs/                           # All documentation
│   ├── authentication/             # Auth system docs
│   ├── deployment/                 # Deployment guides
│   ├── performance/                # Performance analysis
│   ├── project/                    # Project overview
│   └── redux/                      # State management docs
├── public/
│   └── assets/                     # Organized static assets
│       ├── images/                 # Images (logos, products, placeholders)
│       └── icons/                  # SVG icons
├── src/
│   ├── app/                        # Next.js App Router with route groups
│   │   ├── (auth)/                 # Authentication pages
│   │   ├── (shop)/                 # Shopping pages
│   │   ├── (user)/                 # User account pages
│   │   ├── (legal)/                # Legal pages
│   │   ├── (support)/              # Support pages
│   │   ├── (marketing)/            # Marketing pages
│   │   └── api/                    # API routes
│   ├── components/
│   │   ├── features/               # Feature-based components
│   │   │   ├── auth/               # Authentication components
│   │   │   ├── cart/               # Shopping cart components
│   │   │   ├── category/           # Category components
│   │   │   ├── forms/              # Form components
│   │   │   ├── marketing/          # Marketing components
│   │   │   ├── product/            # Product components
│   │   │   ├── profile/            # Profile components
│   │   │   └── settings/           # Settings components
│   │   ├── layout/                 # Layout components
│   │   ├── ui/                     # Reusable UI components
│   │   └── common/                 # Common components
│   ├── constants/                  # Application constants
│   ├── hooks/                      # Custom React hooks
│   ├── lib/                        # Core utilities and configurations
│   ├── schemas/                    # Validation schemas
│   ├── services/                   # API services
│   ├── store/                      # Redux store configuration
│   ├── types/                      # TypeScript type definitions
│   └── utils/                      # Utility functions
├── scripts/                        # Build and utility scripts
└── config files...
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd ecommerce-platform
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📜 Available Scripts

### Development
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

### Utilities
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting
- `npm run clean` - Clean build artifacts
- `npm run clean:install` - Clean and reinstall dependencies

### Docker
- `npm run docker:build` - Build Docker image
- `npm run docker:run` - Run Docker container
- `npm run docker:dev` - Run with Docker Compose
- `npm run docker:stop` - Stop Docker containers
- `npm run docker:clean` - Clean Docker resources

### Performance
- `npm run analyze` - Analyze bundle size
- `npm run perf:monitor` - Monitor performance
- `npm run perf:baseline` - Establish performance baseline

## 🐳 Docker Deployment

Build and run with Docker:

```bash
npm run docker:build
npm run docker:run
```

Or use Docker Compose:

```bash
npm run docker:dev
```

## 📖 Documentation

Detailed documentation is available in the `/docs` folder:

- [Project Review](./docs/project/PROJECT_REVIEW_SUMMARY.md)
- [Deployment Guide](./docs/deployment/DEPLOYMENT.md)
- [Performance Analysis](./docs/performance/PERFORMANCE_ANALYSIS.md)
- [Redux Implementation](./docs/redux/REDUX_IMPLEMENTATION_SUMMARY.md)
- [Authentication Setup](./docs/authentication/AUTHENTICATION_README.md)

## 🏗️ Architecture Highlights

### Route Organization
- **Route Groups**: Organized using Next.js 15 route groups for better structure
- **Feature-based**: Components organized by features rather than technical concerns
- **Type Safety**: Comprehensive TypeScript coverage with strict mode

### Performance Features
- **Image Optimization**: Smart image components with fallbacks
- **Bundle Optimization**: Tree-shaking and code splitting
- **State Management**: Optimized Redux store with selective persistence
- **Loading States**: Comprehensive loading system with skeletons

### Developer Experience
- **Modern Tooling**: ESLint, Prettier, TypeScript
- **Docker Support**: Production-ready containerization
- **Documentation**: Comprehensive docs for all major features
- **Scripts**: Useful npm scripts for common tasks

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting: `npm run lint && npm run type-check`
5. Format code: `npm run format`
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
