"use client";

import React, { Component, ErrorInfo, ReactNode } from "react";
import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export default class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("ErrorBoundary caught an error:", error, errorInfo);
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-[400px] flex items-center justify-center p-8">
          <div className="text-center max-w-md">
            <div className="mb-4">
              <Icon
                icon="lucide:alert-triangle"
                className="h-12 w-12 text-red-500 mx-auto"
              />
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">
              Something went wrong
            </h2>
            <p className="text-gray-600 mb-6">
              We encountered an error while loading this section. Please try
              again.
            </p>
            <div className="space-y-3">
              <Button
                onClick={this.handleRetry}
                className="bg-teal-600 hover:bg-teal-700"
              >
                <Icon icon="lucide:refresh-cw" className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              <div className="text-sm text-gray-500">
                {this.state.error?.message && (
                  <details className="mt-2">
                    <summary className="cursor-pointer hover:text-gray-700">
                      Error details
                    </summary>
                    <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                      {this.state.error.message}
                    </pre>
                  </details>
                )}
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Lightweight error boundary for smaller components
export function SimpleErrorBoundary({ children, fallback }: Props) {
  return (
    <ErrorBoundary
      fallback={
        fallback || (
          <div className="p-4 text-center text-gray-500">
            <Icon
              icon="lucide:alert-triangle"
              className="h-6 w-6 mx-auto mb-2"
            />
            <p className="text-sm">Unable to load this section</p>
          </div>
        )
      }
    >
      {children}
    </ErrorBoundary>
  );
}
