"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTit<PERSON>,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

interface EmailNotificationsProps {
  emailNotifications: {
    offers: boolean;
    watchlist: boolean;
    adsActivity: boolean;
    priceReductions: boolean;
  };
  setEmailNotifications: React.Dispatch<
    React.SetStateAction<{
      offers: boolean;
      watchlist: boolean;
      adsActivity: boolean;
      priceReductions: boolean;
    }>
  >;
}

export default function EmailNotifications({
  emailNotifications,
  setEmailNotifications,
}: EmailNotificationsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl font-semibold">
          Email Notifications
        </CardTitle>
        <CardDescription className="text-md text-gray-600">
          Choose what email notifications you want to receive
        </CardDescription>
        <div className="w-full h-px bg-gray-300" />
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label className="text-md font-medium">Offers & Promotions</Label>
            <p className="text-sm text-gray-600">
              Receive emails about special offers and promotions
            </p>
          </div>
          <Switch
            checked={emailNotifications.offers}
            onCheckedChange={(checked) =>
              setEmailNotifications((prev) => ({ ...prev, offers: checked }))
            }
            className={`${
              emailNotifications.offers
                ? "bg-[#478085] data-[state=checked]:bg-[#478085]"
                : "bg-gray-300 data-[state=unchecked]:bg-gray-300"
            } transition-colors duration-200`}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label className="text-md font-medium">Watchlist Updates</Label>
            <p className="text-sm text-gray-600">
              Get notified when items in your watchlist are updated
            </p>
          </div>
          <Switch
            checked={emailNotifications.watchlist}
            onCheckedChange={(checked) =>
              setEmailNotifications((prev) => ({ ...prev, watchlist: checked }))
            }
            className={`${
              emailNotifications.watchlist
                ? "bg-[#478085] data-[state=checked]:bg-[#478085]"
                : "bg-gray-300 data-[state=unchecked]:bg-gray-300"
            } transition-colors duration-200`}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label className="text-md font-medium">My Ads Activity</Label>
            <p className="text-sm text-gray-600">
              Notifications about views, messages, and activity on your ads
            </p>
          </div>
          <Switch
            checked={emailNotifications.adsActivity}
            onCheckedChange={(checked) =>
              setEmailNotifications((prev) => ({
                ...prev,
                adsActivity: checked,
              }))
            }
            className={`${
              emailNotifications.adsActivity
                ? "bg-[#478085] data-[state=checked]:bg-[#478085]"
                : "bg-gray-300 data-[state=unchecked]:bg-gray-300"
            } transition-colors duration-200`}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label className="text-md font-medium">Price Reductions</Label>
            <p className="text-sm text-gray-600">
              Alert when prices drop on items in your watchlist
            </p>
          </div>
          <Switch
            checked={emailNotifications.priceReductions}
            onCheckedChange={(checked) =>
              setEmailNotifications((prev) => ({
                ...prev,
                priceReductions: checked,
              }))
            }
            className={`${
              emailNotifications.priceReductions
                ? "bg-[#478085] data-[state=checked]:bg-[#478085]"
                : "bg-gray-300 data-[state=unchecked]:bg-gray-300"
            } transition-colors duration-200`}
          />
        </div>
      </CardContent>
    </Card>
  );
}
