/**
 * Test script to verify the profile API fix
 * This script helps verify that the authentication and API endpoints are working correctly
 */

console.log('🧪 Testing Profile API Fix');
console.log('==========================');

// Test 1: Check authentication status
function checkAuthStatus() {
  console.log('\n1. Checking Authentication Status...');
  
  const authToken = localStorage.getItem('auth_token');
  const refreshToken = localStorage.getItem('refresh_token');
  
  console.log('   Auth Token:', authToken ? '✅ Present' : '❌ Missing');
  console.log('   Refresh Token:', refreshToken ? '✅ Present' : '❌ Missing');
  
  if (authToken) {
    try {
      // Try to decode JWT token (basic check)
      const payload = JSON.parse(atob(authToken.split('.')[1]));
      const now = Date.now() / 1000;
      
      console.log('   Token Expiry:', payload.exp > now ? '✅ Valid' : '❌ Expired');
      console.log('   User ID:', payload.sub || payload.userId || 'Not found');
      console.log('   Username:', payload.username || 'Not found');
    } catch (error) {
      console.log('   Token Format:', '❌ Invalid JWT format');
    }
  }
  
  return !!authToken;
}

// Test 2: Test API endpoints
async function testApiEndpoints() {
  console.log('\n2. Testing API Endpoints...');
  
  const baseUrl = 'https://sasto-api.webstudiomatrix.com/api/v1';
  const authToken = localStorage.getItem('auth_token');
  
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...(authToken && { 'Authorization': `Bearer ${authToken}` })
  };
  
  // Test health endpoint
  try {
    console.log('   Testing health endpoint...');
    const healthResponse = await fetch(`${baseUrl}/health`, { headers });
    console.log('   Health Status:', healthResponse.status === 200 ? '✅ OK' : `❌ ${healthResponse.status}`);
  } catch (error) {
    console.log('   Health Status:', '❌ Network Error');
  }
  
  // Test auth profile endpoint
  if (authToken) {
    try {
      console.log('   Testing auth profile endpoint...');
      const authResponse = await fetch(`${baseUrl}/auth/profile`, { headers });
      console.log('   Auth Profile:', authResponse.status === 200 ? '✅ OK' : `❌ ${authResponse.status}`);
      
      if (authResponse.ok) {
        const authData = await authResponse.json();
        console.log('   Auth Data:', authData);
      }
    } catch (error) {
      console.log('   Auth Profile:', '❌ Network Error');
    }
    
    // Test users profile endpoint
    try {
      console.log('   Testing users profile endpoint...');
      const usersResponse = await fetch(`${baseUrl}/users/profile`, { headers });
      console.log('   Users Profile:', usersResponse.status === 200 ? '✅ OK' : `❌ ${usersResponse.status}`);
      
      if (usersResponse.ok) {
        const userData = await usersResponse.json();
        console.log('   User Data:', userData);
      } else {
        const errorText = await usersResponse.text();
        console.log('   Error Details:', errorText);
      }
    } catch (error) {
      console.log('   Users Profile:', '❌ Network Error:', error.message);
    }
  } else {
    console.log('   ⚠️ Skipping authenticated endpoints - no auth token');
  }
}

// Test 3: Check Redux store state
function checkReduxState() {
  console.log('\n3. Checking Redux Store State...');
  
  // Try to access Redux store if available
  if (window.__REDUX_DEVTOOLS_EXTENSION__) {
    console.log('   Redux DevTools:', '✅ Available');
  } else {
    console.log('   Redux DevTools:', '❌ Not available');
  }
  
  // Check if store is accessible
  try {
    const storeState = window.store?.getState();
    if (storeState) {
      console.log('   Store State:', '✅ Accessible');
      console.log('   API State:', storeState.api ? '✅ Present' : '❌ Missing');
    } else {
      console.log('   Store State:', '❌ Not accessible');
    }
  } catch (error) {
    console.log('   Store State:', '❌ Error accessing store');
  }
}

// Test 4: Simulate login if needed
async function simulateLogin() {
  console.log('\n4. Login Simulation...');
  
  const authToken = localStorage.getItem('auth_token');
  if (authToken) {
    console.log('   Already logged in ✅');
    return true;
  }
  
  console.log('   ⚠️ Not logged in. To test the profile API:');
  console.log('   1. Go to /login page');
  console.log('   2. Log in with valid credentials');
  console.log('   3. Run this test again');
  console.log('   4. Or manually set a valid token in localStorage');
  
  return false;
}

// Main test function
async function runProfileApiTests() {
  const isAuthenticated = checkAuthStatus();
  await testApiEndpoints();
  checkReduxState();
  
  if (!isAuthenticated) {
    await simulateLogin();
  }
  
  console.log('\n📋 Test Summary:');
  console.log('- Authentication:', isAuthenticated ? '✅ Ready' : '❌ Required');
  console.log('- Next Steps:', isAuthenticated ? 'Try accessing /profile/settings' : 'Please log in first');
}

// Export for manual use
window.testProfileApiFix = {
  checkAuthStatus,
  testApiEndpoints,
  checkReduxState,
  simulateLogin,
  runProfileApiTests
};

console.log('\n🚀 Run testProfileApiFix.runProfileApiTests() to run all tests');

// Auto-run tests
runProfileApiTests();
