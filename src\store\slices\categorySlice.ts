import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Category } from "@/types/ecommerce";

// Define the category state interface
export interface CategoryState {
  selectedCategory: string | null;
  showFilters: boolean;
  categories: Category[];
  loading: boolean;
  error?: string;
}

// Initial state
const initialState: CategoryState = {
  selectedCategory: null,
  showFilters: true,
  categories: [],
  loading: false,
  error: undefined,
};

// Create the category slice
const categorySlice = createSlice({
  name: "category",
  initialState,
  reducers: {
    selectCategory: (state, action: PayloadAction<string | null>) => {
      state.selectedCategory = action.payload;
    },
    toggleFilters: (state, action: PayloadAction<boolean>) => {
      state.showFilters = action.payload;
    },
    setCategoriesLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setCategoriesError: (state, action: PayloadAction<string | undefined>) => {
      state.error = action.payload;
    },
    setCategories: (state, action: PayloadAction<Category[]>) => {
      state.categories = action.payload;
      state.loading = false;
      state.error = undefined;
    },
  },
});

// Export actions
export const {
  selectCategory,
  toggleFilters,
  setCategoriesLoading,
  setCategoriesError,
  setCategories,
} = categorySlice.actions;

// Export reducer
export default categorySlice.reducer;
