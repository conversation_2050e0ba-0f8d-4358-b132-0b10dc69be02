"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  Formik,
  Form,
  Field,
  ErrorMessage,
  FormikHelpers,
  FieldProps,
} from "formik";
import { Icon } from "@iconify/react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";

import { loginSchema, type LoginFormValues } from "@/schemas/auth-schemas";
import { ApiError } from "@/lib/api";
import { useUser } from "@/store/compatibility";
import { handleFormError, logError } from "@/utils/error-handling";
import { LoadingOverlay, LoadingButton } from "@/components/ui";

const initialValues: LoginFormValues = {
  usernameOrEmail: "",
  password: "",
};

export default function LoginForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [apiError, setApiError] = useState<string>("");
  const router = useRouter();
  const { login: loginUser } = useUser();

  const handleSubmit = async (
    values: LoginFormValues,
    { setSubmitting, setFieldError }: FormikHelpers<LoginFormValues>
  ) => {
    try {
      setApiError("");
      await loginUser({
        usernameOrEmail: values.usernameOrEmail,
        password: values.password,
      });

      await new Promise((resolve) => setTimeout(resolve, 500));
      router.push("/");
    } catch (error) {
      handleFormError(error as ApiError | Error, setFieldError, setApiError);
      logError(error as Error, "Login form submission");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="mx-auto max-w-lg mt-8 space-y-4 p-8 border-2 border-gray-100 rounded-lg shadow-md bg-white">
      <div className="text-center">
        <h1 className="text-4xl font-normal text-black mb-12">Login</h1>
      </div>

      <Formik
        initialValues={initialValues}
        validationSchema={loginSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, errors, touched }) => (
          <Form className="space-y-6 relative">
            <LoadingOverlay
              isLoading={isSubmitting}
              message="Signing you in..."
            />

            {apiError && (
              <Alert variant="destructive">
                <Icon icon="lucide:alert-circle" className="h-4 w-4" />
                <AlertDescription>{apiError}</AlertDescription>
              </Alert>
            )}

            {/* INLINE Email + Password */}
            <div className="space-y-4">
              {/* Email Field */}
              <div className="flex items-center gap-4">
                <Label
                  htmlFor="usernameOrEmail"
                  className="text-base font-normal text-black w-20 flex-shrink-0"
                >
                  Email
                </Label>
                <div className="flex-1">
                  <Field name="usernameOrEmail">
                    {({ field }: FieldProps) => (
                      <Input
                        {...field}
                        id="usernameOrEmail"
                        type="email"
                        placeholder="<EMAIL>"
                        className={`h-12 text-base border-gray-300 rounded-lg focus:border-gray-400 focus:ring-0 bg-white ${
                          errors.usernameOrEmail && touched.usernameOrEmail
                            ? "border-red-500"
                            : ""
                        }`}
                        disabled={isSubmitting}
                      />
                    )}
                  </Field>
                  <ErrorMessage
                    name="usernameOrEmail"
                    component="div"
                    className="text-sm text-red-600 mt-1"
                  />
                </div>
              </div>

              {/* Password Field */}
              <div className="flex items-center gap-4">
                <Label
                  htmlFor="password"
                  className="text-base font-normal text-black w-20 flex-shrink-0"
                >
                  Password
                </Label>
                <div className="flex-1">
                  <div className="relative">
                    <Field name="password">
                      {({ field }: FieldProps) => (
                        <Input
                          {...field}
                          id="password"
                          type={showPassword ? "text" : "password"}
                          placeholder="***********"
                          className={`h-12 text-base border-gray-300 rounded-lg pr-12 focus:border-gray-400 focus:ring-0 bg-white ${
                            errors.password && touched.password
                              ? "border-red-500"
                              : ""
                          }`}
                          disabled={isSubmitting}
                        />
                      )}
                    </Field>
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                      disabled={isSubmitting}
                    >
                      {showPassword ? (
                        <Icon icon="lucide:eye-off" className="h-5 w-5" />
                      ) : (
                        <Icon icon="lucide:eye" className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                  <ErrorMessage
                    name="password"
                    component="div"
                    className="text-sm text-red-600 mt-1"
                  />
                </div>
              </div>
            </div>

            {/* I'm not a robot checkbox */}
            <div className="flex items-center mt-4 space-x-3 p-4 border border-gray-300 rounded-lg bg-white">
              <Checkbox id="robot-check" className="h-4 w-4" />
              <Label
                htmlFor="robot-check"
                className="text-base font-normal text-black cursor-pointer"
              >
                I&apos;m not a robot
              </Label>
            </div>

            {/* Forgot Password Link */}
            <div className="text-right">
              <Link
                href="/forgot-password"
                className="text-base text-black underline hover:text-gray-700"
              >
                Forgot Password?
              </Link>
            </div>

            {/* Submit Button */}
            <LoadingButton
              type="submit"
              isLoading={isSubmitting}
              loadingText="Signing in..."
              className="h-14 w-full bg-teal-600 text-white hover:bg-teal-700 rounded-lg font-normal text-lg mt-6"
            >
              Login
            </LoadingButton>
          </Form>
        )}
      </Formik>

      {/* Separator */}
      <div className="relative my-8">
        <Separator className="bg-gray-300" />
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="bg-white px-4 text-sm text-gray-500 uppercase tracking-wide">
            OR CONTINUE WITH
          </span>
        </div>
      </div>

      {/* Social Buttons */}
      <div className="grid grid-cols-2 gap-4">
        <Button
          variant="outline"
          className="h-12 border-gray-300 text-gray-600 hover:bg-gray-50 bg-white rounded-lg font-normal text-base"
        >
          <svg className="mr-2 h-5 w-5" viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="currentColor"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="currentColor"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="currentColor"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
          Google
        </Button>
        <Button
          variant="outline"
          className="h-12 border-gray-300 text-gray-600 hover:bg-gray-50 bg-white rounded-lg font-normal text-base"
        >
          <svg className="mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
          </svg>
          Facebook
        </Button>
      </div>

      {/* Signup Prompt */}
      <div className="text-center text-base text-gray-500 mt-8">
        {"Don't Have an Account? "}
        <Link
          href="/signup"
          className="text-black underline hover:text-gray-700"
        >
          Register Now
        </Link>
      </div>
    </div>
  );
}
