"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";

import ListingActions from "./ListingActions";
import type { UserListing, ListingAction } from "@/types/ecommerce";

// Enhanced mock user listings data
const mockUserListings: UserListing[] = [
  {
    id: "1",
    title: "Nicon D3400",
    condition: "Like New",
    price: "₹. 45,000",
    status: "expired",
    image:
      "https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=400&h=300&fit=crop&crop=center",
    postedAt: "2024-01-10",
    views: 156,
    inquiries: 8,
  },
  {
    id: "2",
    title: "iPhone 13 Pro",
    condition: "Excellent",
    price: "₹. 75,000",
    status: "hold",
    image:
      "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop&crop=center",
    postedAt: "2024-01-15",
    views: 234,
    inquiries: 12,
  },
  {
    id: "3",
    title: "MacBook Air M1",
    condition: "Like New",
    price: "₹. 85,000",
    status: "sold",
    image:
      "https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=400&h=300&fit=crop&crop=center",
    postedAt: "2024-01-08",
    views: 189,
    inquiries: 15,
    soldAt: "2024-01-20",
    soldPrice: "₹. 82,000",
  },
  {
    id: "4",
    title: "Sony WH-1000XM4",
    condition: "New",
    price: "₹. 22,990",
    status: "active",
    image:
      "https://images.unsplash.com/photo-1583394838336-acd977736f90?w=400&h=300&fit=crop&crop=center",
    postedAt: "2024-01-18",
    views: 98,
    inquiries: 5,
  },
  {
    id: "5",
    title: "Gaming Chair Pro",
    condition: "Like New",
    price: "₹. 15,500",
    status: "active",
    image:
      "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop&crop=center",
    postedAt: "2024-01-20",
    views: 67,
    inquiries: 3,
  },
  {
    id: "6",
    title: "Nike Air Max 270",
    condition: "New",
    price: "₹. 8,995",
    status: "inactive",
    image:
      "https://images.unsplash.com/photo-1556906781-9a412961c28c?w=400&h=300&fit=crop&crop=center",
    postedAt: "2024-01-12",
    views: 45,
    inquiries: 2,
  },
];

interface EnhancedUserListingsProps {
  searchQuery: string;
  statusFilter: string;
  onSearchChange: (query: string) => void;
  onStatusFilterChange: (status: string) => void;
}

export default function EnhancedUserListings({
  searchQuery,
  statusFilter,
  onSearchChange,
}: EnhancedUserListingsProps) {
  const [listings, setListings] = useState<UserListing[]>(mockUserListings);

  const filteredListings = listings.filter((listing) => {
    const matchesSearch = listing.title
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesStatus =
      statusFilter === "All" || listing.status === statusFilter.toLowerCase();
    return matchesSearch && matchesStatus;
  });

  const handleListingAction = (action: ListingAction) => {
    setListings((prev) =>
      prev.map((listing) => {
        if (listing.id === action.productId) {
          switch (action.type) {
            case "mark_sold":
              return {
                ...listing,
                status: "sold",
                soldAt: action.data?.soldAt,
                soldPrice: action.data?.soldPrice,
              };
            case "mark_active":
              return { ...listing, status: "active" };
            case "mark_inactive":
              return { ...listing, status: "inactive" };
            case "delete":
              return listing; // Handle deletion separately
            default:
              return listing;
          }
        }
        return listing;
      })
    );

    // Handle deletion
    if (action.type === "delete") {
      setListings((prev) =>
        prev.filter((listing) => listing.id !== action.productId)
      );
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Icon icon="lucide:trending-up" className="w-4 h-4 text-green-600" />
        );
      case "sold":
        return (
          <Icon
            icon="lucide:check-circle-2"
            className="w-4 h-4 text-gray-600"
          />
        );
      case "hold":
        return <Icon icon="lucide:clock" className="w-4 h-4 text-blue-600" />;
      case "expired":
        return (
          <Icon icon="lucide:alert-circle" className="w-4 h-4 text-red-600" />
        );
      case "inactive":
        return <Icon icon="lucide:eye" className="w-4 h-4 text-yellow-600" />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4 flex-1">
          <div className="relative flex-1 max-w-md">
            <Icon
              icon="lucide:search"
              className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
            />
            <Input
              placeholder="Search your listings..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-12 pr-4 py-3 bg-white border-gray-200 rounded-full shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-all duration-300"
            />
          </div>
          <Button
            variant="outline"
            size="sm"
            className="px-4 py-3 rounded-full border-gray-200 hover:border-teal-500 hover:text-teal-600 hover:bg-teal-50 transition-all duration-300 shadow-sm"
          >
            <Icon icon="lucide:filter" className="w-4 h-4 mr-2" />
            Filter
          </Button>
        </div>

        <Link href="/create-listing">
          <Button className="bg-teal-600 hover:bg-teal-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
            <Icon icon="lucide:plus" className="w-4 h-4 mr-2" />
            Create Listing
          </Button>
        </Link>
      </div>

      {/* Listings Grid */}
      {filteredListings.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredListings.map((listing) => (
            <Card
              key={listing.id}
              className="group hover:shadow-xl transition-all duration-300 border-0 shadow-md"
            >
              <CardContent className="p-0">
                <div className="relative overflow-hidden rounded-t-lg">
                  <Image
                    src={listing.image}
                    alt={listing.title}
                    width={300}
                    height={200}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300"></div>

                  {/* Status and Actions */}
                  <div className="absolute top-3 right-3">
                    <ListingActions
                      listing={listing}
                      onAction={handleListingAction}
                    />
                  </div>

                  {/* Sold Overlay */}
                  {listing.status === "sold" && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                      <div className="bg-white px-4 py-2 rounded-full shadow-lg">
                        <div className="flex items-center gap-2 text-green-600 font-semibold">
                          <Icon
                            icon="lucide:check-circle-2"
                            className="w-5 h-5"
                          />
                          SOLD
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="p-5 space-y-4">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2 text-lg group-hover:text-teal-600 transition-colors duration-300">
                      {listing.title}
                    </h3>
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <p className="text-sm text-gray-600 font-medium">
                        {listing.condition}
                      </p>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Icon icon="lucide:calendar" className="w-4 h-4" />
                      Posted {new Date(listing.postedAt).toLocaleDateString()}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xl font-bold text-gray-900 group-hover:text-teal-600 transition-colors duration-300">
                        {listing.price}
                      </p>
                      {listing.soldPrice && listing.status === "sold" && (
                        <p className="text-sm text-green-600 font-medium">
                          Sold for {listing.soldPrice}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-1">
                      {getStatusIcon(listing.status)}
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center gap-1">
                      <Icon icon="lucide:eye" className="w-4 h-4" />
                      <span>{listing.views} views</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Icon icon="lucide:message-circle" className="w-4 h-4" />
                      <span>{listing.inquiries} inquiries</span>
                    </div>
                  </div>

                  {listing.status === "sold" && listing.soldAt && (
                    <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                      <p className="text-sm text-green-800">
                        <Icon
                          icon="lucide:check-circle-2"
                          className="w-4 h-4 inline mr-1"
                        />
                        Sold on {new Date(listing.soldAt!).toLocaleDateString()}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon icon="lucide:search" className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            {searchQuery ? "No matching listings found" : "No listings yet"}
          </h3>
          <p className="text-gray-600 mb-6">
            {searchQuery
              ? "Try adjusting your search terms or filters"
              : "Create your first listing to start selling"}
          </p>
          <Link href="/create-listing">
            <Button className="bg-teal-600 hover:bg-teal-700 text-white">
              <Icon icon="lucide:plus" className="w-4 h-4 mr-2" />
              Create Your First Listing
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}
