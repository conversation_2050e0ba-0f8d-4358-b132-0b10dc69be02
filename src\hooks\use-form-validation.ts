import { useState, useCallback, useMemo } from "react";

interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: string) => string | null;
}

interface FieldConfig {
  [fieldName: string]: ValidationRule;
}

interface ValidationErrors {
  [fieldName: string]: string;
}

interface UseFormValidationReturn {
  values: Record<string, string>;
  errors: ValidationErrors;
  isValid: boolean;
  isSubmitting: boolean;
  setValue: (field: string, value: string) => void;
  setValues: (values: Record<string, string>) => void;
  validateField: (field: string) => boolean;
  validateAll: () => boolean;
  clearErrors: () => void;
  clearField: (field: string) => void;
  reset: () => void;
  handleSubmit: (
    onSubmit: (values: Record<string, string>) => Promise<void> | void
  ) => (e: React.FormEvent) => Promise<void>;
}

/**
 * Custom hook for form validation with configurable rules
 * Provides consistent form handling across all form components
 */
export function useFormValidation(
  initialValues: Record<string, string> = {},
  validationConfig: FieldConfig = {}
): UseFormValidationReturn {
  const [values, setValuesState] =
    useState<Record<string, string>>(initialValues);
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateField = useCallback(
    (field: string): boolean => {
      const value = values[field] || "";
      const rules = validationConfig[field];

      if (!rules) return true;

      let error: string | null = null;

      // Required validation
      if (rules.required && !value.trim()) {
        error = `${field} is required`;
      }
      // Min length validation
      else if (rules.minLength && value.length < rules.minLength) {
        error = `${field} must be at least ${rules.minLength} characters`;
      }
      // Max length validation
      else if (rules.maxLength && value.length > rules.maxLength) {
        error = `${field} must be no more than ${rules.maxLength} characters`;
      }
      // Pattern validation
      else if (rules.pattern && !rules.pattern.test(value)) {
        error = `${field} format is invalid`;
      }
      // Custom validation
      else if (rules.custom) {
        error = rules.custom(value);
      }

      setErrors((prev) => ({
        ...prev,
        [field]: error || "",
      }));

      return !error;
    },
    [values, validationConfig]
  );

  const validateAll = useCallback((): boolean => {
    const fieldNames = Object.keys(validationConfig);
    const results = fieldNames.map((field) => validateField(field));
    return results.every((result) => result);
  }, [validationConfig, validateField]);

  const setValue = useCallback(
    (field: string, value: string) => {
      setValuesState((prev) => ({
        ...prev,
        [field]: value,
      }));

      // Clear error when user starts typing
      if (errors[field]) {
        setErrors((prev) => ({
          ...prev,
          [field]: "",
        }));
      }
    },
    [errors]
  );

  const setValues = useCallback((newValues: Record<string, string>) => {
    setValuesState(newValues);
    setErrors({});
  }, []);

  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  const clearField = useCallback((field: string) => {
    setValuesState((prev) => ({
      ...prev,
      [field]: "",
    }));
    setErrors((prev) => ({
      ...prev,
      [field]: "",
    }));
  }, []);

  const reset = useCallback(() => {
    setValuesState(initialValues);
    setErrors({});
    setIsSubmitting(false);
  }, [initialValues]);

  const handleSubmit = useCallback(
    (onSubmit: (values: Record<string, string>) => Promise<void> | void) => {
      return async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateAll()) {
          return;
        }

        setIsSubmitting(true);
        try {
          await onSubmit(values);
        } catch (error) {
          console.error("Form submission error:", error);
        } finally {
          setIsSubmitting(false);
        }
      };
    },
    [values, validateAll]
  );

  const isValid = useMemo(() => {
    const hasErrors = Object.values(errors).some((error) => error);
    const hasRequiredFields = Object.keys(validationConfig).every((field) => {
      const rules = validationConfig[field];
      if (rules.required) {
        return values[field]?.trim();
      }
      return true;
    });

    return !hasErrors && hasRequiredFields;
  }, [errors, values, validationConfig]);

  return {
    values,
    errors,
    isValid,
    isSubmitting,
    setValue,
    setValues,
    validateField,
    validateAll,
    clearErrors,
    clearField,
    reset,
    handleSubmit,
  };
}
