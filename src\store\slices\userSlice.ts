import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { UserProfile } from "@/types/ecommerce";
import { User, LoginRequest } from "@/types/auth";
import { AuthService } from "@/services/auth-service";

// Define the user state interface
export interface UserState {
  currentUser: UserProfile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: UserState = {
  currentUser: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

// Helper function to convert API User to UserProfile
const convertApiUserToProfile = (apiUser: User): UserProfile => {
  return {
    id: apiUser.id,
    username: apiUser.username,
    email: apiUser.email || "",
    phoneNumber: apiUser.phone,
    profilePicture: apiUser.profilePicture,
    address: apiUser.address
      ? {
          city: apiUser.address.city || "",
          country: apiUser.address.country || "",
        }
      : {
          city: "",
          country: "",
        },
    createdAt: apiUser.createdAt, // Store as string instead of Date object
    updatedAt: apiUser.updatedAt || new Date().toISOString(), // Store as string instead of Date object
    isVerified: apiUser.isVerified,
    preferences: {
      notifications: apiUser.preferences?.notifications || {
        email: true,
        sms: true,
        push: true,
      },
      privacy: apiUser.preferences?.privacy || {
        showEmail: false,
        showPhone: true,
      },
    },
    savedListings: [],
    totalListings: 0,
    totalSold: 0,
  };
};

// Async thunks for user operations
export const loginUser = createAsyncThunk(
  "user/loginUser",
  async (credentials: LoginRequest) => {
    const response = await AuthService.login(credentials);
    // Assuming response has a 'user' property of type User
    return convertApiUserToProfile(response.user);
  }
);

export const logoutUser = createAsyncThunk("user/logoutUser", async () => {
  await AuthService.logout();
});

export const getCurrentUser = createAsyncThunk(
  "user/getCurrentUser",
  async () => {
    const apiUser = await AuthService.getCurrentUser();
    return convertApiUserToProfile(apiUser);
  }
);

export const updateUserProfile = createAsyncThunk(
  "user/updateUserProfile",
  async (updates: Partial<UserProfile>) => {
    // Simulate API call - replace with actual API call
    await new Promise((resolve) => setTimeout(resolve, 500));
    return updates;
  }
);

// Create the user slice
const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setCurrentUser: (state, action: PayloadAction<UserProfile | null>) => {
      state.currentUser = action.payload;
      state.isAuthenticated = !!action.payload;
    },
    setUserLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setUserError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearUserError: (state) => {
      state.error = null;
    },
    updateUserField: (
      state,
      action: PayloadAction<{ field: keyof UserProfile; value: any }>
    ) => {
      if (state.currentUser) {
        const { field, value } = action.payload;
        (state.currentUser as any)[field] = value;
      }
    },
    addSavedListing: (state, action: PayloadAction<string>) => {
      if (state.currentUser && state.currentUser.savedListings) {
        if (!state.currentUser.savedListings.includes(action.payload)) {
          state.currentUser.savedListings.push(action.payload);
        }
      }
    },
    removeSavedListing: (state, action: PayloadAction<string>) => {
      if (state.currentUser && state.currentUser.savedListings) {
        state.currentUser.savedListings =
          state.currentUser.savedListings.filter((id) => id !== action.payload);
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Login user
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentUser = action.payload;
        state.isAuthenticated = true;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Login failed";
        state.currentUser = null;
        state.isAuthenticated = false;
      })
      // Logout user
      .addCase(logoutUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.isLoading = false;
        state.currentUser = null;
        state.isAuthenticated = false;
        state.error = null;
      })
      .addCase(logoutUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Logout failed";
      })
      // Get current user
      .addCase(getCurrentUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getCurrentUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentUser = action.payload;
        state.isAuthenticated = true;
      })
      .addCase(getCurrentUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to get user";
        state.currentUser = null;
        state.isAuthenticated = false;
      })
      // Update user profile
      .addCase(updateUserProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.currentUser) {
          state.currentUser = { ...state.currentUser, ...action.payload };
        }
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to update profile";
      });
  },
});

// Export actions
export const {
  setCurrentUser,
  setUserLoading,
  setUserError,
  clearUserError,
  updateUserField,
  addSavedListing,
  removeSavedListing,
} = userSlice.actions;

// Export reducer
export default userSlice.reducer;
