"use client";
import Link from "next/link";
import { Icon } from "@iconify/react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface MobileSectionProps {
  isAuthenticated: boolean;
}

export function MobileSection({ isAuthenticated }: MobileSectionProps) {
  return (
    <div className="md:hidden container-responsive-sm pb-2 sm:pb-3">
      {/* Mobile Search Bar */}
      <div className="relative">
        <Icon
          icon="material-symbols:search"
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-4 h-4 z-10"
        />
        <Input
          type="search"
          placeholder="Search products..."
          className="pl-10 pr-4 py-2.5 sm:py-3 w-full bg-gray-50 backdrop-blur-md border-0 rounded-xl focus:bg-white focus:ring-2 focus:ring-black/20 transition-all duration-300 text-sm sm:text-base"
        />
      </div>

      {/* Mobile Balance and Top Up - only show when authenticated */}
      {isAuthenticated && (
        <div className="flex items-center justify-between mt-2 sm:mt-3 gap-2 sm:gap-3">
          {/* Balance Display */}
          <div className="flex items-center bg-[#356267] backdrop-blur-md border border-white/20 rounded-full px-2 sm:px-3 py-1.5 sm:py-2 flex-1 min-w-0">
            <Icon
              icon="material-symbols:account-balance-wallet"
              className="w-3 h-3 sm:w-4 sm:h-4 text-white mr-1 sm:mr-2 flex-shrink-0"
            />
            <span className="text-white text-xs sm:text-sm font-medium truncate">
              Balance: Rs 2,500.75
            </span>
          </div>

          {/* Top Up Button */}
          <Link href="/top-up">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-3 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 flex items-center gap-1 sm:gap-2 flex-shrink-0">
              <Icon
                icon="material-symbols:add-card"
                className="w-3 h-3 sm:w-4 sm:h-4"
              />
              <span className="hidden sm:inline">Top Up</span>
              <span className="sm:hidden">+</span>
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}
