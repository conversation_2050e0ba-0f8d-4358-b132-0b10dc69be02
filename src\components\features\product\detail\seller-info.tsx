import type { Product } from "@/types/ecommerce";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Image from "next/image";
import Link from "next/link";
import { Icon } from "@iconify/react";
import { ProductSharing } from "./product-sharing";
import { ProductWishlist } from "./product-wishlist";
import { ProductReport } from "./product-report";
import { useGetSellerStatsQuery } from "@/store/api/productDetailsApi";

interface SellerInfoProps {
  product: Product;
  onContactSeller?: () => void;
}

export function SellerInfo({ product, onContactSeller }: SellerInfoProps) {
  // Use basic seller info from product data instead of API
  const sellerStats = null; // Disabled API call
  const statsLoading = false;
  const statsError = null;

  // Loading state
  if (statsLoading) {
    return (
      <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 min-h-[450px] flex flex-col">
        <Skeleton className="h-8 w-48 mb-4" />
        <div className="flex items-center gap-3 mb-6">
          <Skeleton className="w-20 h-20 rounded-md" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>
        <div className="space-y-4 mb-8">
          <Skeleton className="h-5 w-40" />
          <Skeleton className="h-5 w-36" />
          <Skeleton className="h-5 w-44" />
        </div>
        <div className="mt-auto space-y-4">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
        </div>
      </div>
    );
  }

  // Error state - fallback to basic product seller info
  if (statsError) {
    console.warn("Failed to load seller stats, using basic info:", statsError);
  }

  // Use seller stats if available, otherwise fallback to product seller info
  const displayStats = sellerStats || {
    username: product.seller.name,
    fullName: product.seller.name,
    avatar: product.seller.avatar,
    rating: product.seller.rating || 0,
    totalRatings: 0,
    responseRate: 0,
    responseTime: "Unknown",
    memberSince: "Unknown",
    totalListings: 0,
    activeListings: 0,
    soldListings: 0,
    isVerified: false,
  };

  return (
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 min-h-[450px] flex flex-col">
      <h3 className="text-2xl font-semibold text-gray-900 mb-4">
        Seller Information
      </h3>

      {/* Error alert if stats failed to load */}
      {statsError && (
        <Alert className="mb-4">
          <Icon icon="lucide:info" className="h-4 w-4" />
          <AlertDescription className="text-sm">
            Some seller details may be limited due to a connection issue.
          </AlertDescription>
        </Alert>
      )}

      {/* After the title, wrap the content in a flex container */}
      <div className="flex-1 flex flex-col justify-between">
        <div>
          {/* Seller Profile */}
          <div className="flex items-center gap-3 mb-6">
            <div className="w-20 h-20 rounded-md overflow-hidden bg-gray-100 flex items-center justify-center">
              {displayStats.avatar ? (
                <Image
                  src={displayStats.avatar}
                  alt="Seller avatar"
                  width={80}
                  height={80}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-teal-600 flex items-center justify-center text-white font-semibold text-2xl">
                  {displayStats.username.charAt(0).toUpperCase()}
                </div>
              )}
            </div>
            <div className="flex-1 gap-8">
              <div className="font-semibold text-gray-900 text-xl flex items-center gap-2">
                {product.seller.id ? (
                  <Link
                    href={`/profile/${product.seller.id}`}
                    className="text-teal-600 hover:text-teal-700 hover:underline"
                  >
                    {displayStats.fullName || displayStats.username}
                  </Link>
                ) : (
                  <span>{displayStats.fullName || displayStats.username}</span>
                )}
                {displayStats.isVerified && (
                  <Icon
                    icon="lucide:badge-check"
                    className="h-5 w-5 text-blue-500"
                  />
                )}
              </div>
              <div className="flex items-center gap-1 mt-1">
                <div className="flex items-center gap-0.5">
                  {[...Array(5)].map((_, i) => (
                    <Icon
                      icon="lucide:star"
                      key={i}
                      className={`h-3 w-3 ${
                        i < Math.floor(displayStats.rating)
                          ? "text-yellow-400 fill-current"
                          : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-xl font-medium text-gray-700">
                  {displayStats.rating.toFixed(1)}
                </span>
                <span className="text-xl text-gray-500">
                  ({displayStats.totalRatings} reviews)
                </span>
              </div>
            </div>
          </div>

          {/* Seller Stats */}
          <div className="space-y-4 mb-8">
            <div className="flex items-center gap-2 text-lg text-gray-600">
              <Icon icon="lucide:user" className="h-5 w-5 text-gray-400" />
              <span>
                Member since{" "}
                {new Date(displayStats.memberSince).getFullYear() || "Unknown"}
              </span>
            </div>
            <div className="flex items-center gap-2 text-lg text-gray-600">
              <Icon icon="lucide:shield" className="h-5 w-5 text-gray-400" />
              <span>Response rate: {displayStats.responseRate}%</span>
            </div>
            <div className="flex items-center gap-2 text-lg text-gray-600">
              <Icon icon="lucide:clock" className="h-5 w-5 text-gray-400" />
              <span>Response time: {displayStats.responseTime}</span>
            </div>
            <div className="flex items-center gap-2 text-lg text-gray-600">
              <Icon icon="lucide:package" className="h-5 w-5 text-gray-400" />
              <span>{displayStats.activeListings} active listings</span>
            </div>
            <div className="flex items-center gap-2 text-lg text-gray-600">
              <Icon
                icon="lucide:check-circle"
                className="h-5 w-5 text-gray-400"
              />
              <span>{displayStats.soldListings} items sold</span>
            </div>
          </div>
        </div>

        <div className="mt-auto">
          {/* Action Buttons - increase spacing */}
          <div className="space-y-4 mb-6">
            <Button
              className="w-full bg-teal-600 hover:bg-teal-700 text-white text-lg py-4"
              onClick={onContactSeller}
            >
              <Icon icon="lucide:phone" className="h-5 w-5 mr-2" />
              Contact Seller
            </Button>
            <Button
              variant="outline"
              className="w-full text-lg py-4  border-gray-300 bg-transparent"
              onClick={onContactSeller}
            >
              <Icon icon="lucide:message-circle" className="h-5 w-5 mr-2" />
              Send Message
            </Button>
          </div>

          {/* Secondary Actions */}
          <div className="flex items-center justify-around pt-4 border-gray-100">
            <ProductSharing product={product} />
            <ProductWishlist product={product} />
          </div>

          {/* Report Link */}
          <div className="flex justify-center pt-2">
            <ProductReport product={product} />
          </div>
        </div>
      </div>
    </div>
  );
}
