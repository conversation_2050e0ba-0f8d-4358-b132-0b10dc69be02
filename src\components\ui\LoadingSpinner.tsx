"use client";

import { memo } from "react";

interface LoadingSpinnerProps {
  message?: string;
  size?: "sm" | "md" | "lg";
  className?: string;
}

const LoadingSpinner = memo(function LoadingSpinner({
  message = "Loading...",
  size = "md",
  className = "",
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-8 w-8",
    lg: "h-12 w-12",
  };

  return (
    <div className={`bg-white rounded-b-lg shadow-lg ${className}`}>
      <div className="flex items-center justify-center h-64">
        <div
          className={`animate-spin rounded-full border-b-2 border-teal-600 ${sizeClasses[size]}`}
          role="status"
          aria-label="Loading"
        />
        {message && (
          <span className="ml-3 text-gray-500" aria-live="polite">
            {message}
          </span>
        )}
      </div>
    </div>
  );
});

export default LoadingSpinner;
