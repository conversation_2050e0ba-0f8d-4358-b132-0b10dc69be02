"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import NextImage from "next/image";

interface BannerContent {
  id: string;
  type: "video" | "image";
  url: string;
  title: string;
  description: string;
  buttonText: string;
  buttonLink?: string;
  backgroundColor?: string;
  priority?: "high" | "medium" | "low";
  alt?: string;
  preloadNext?: boolean;
}

interface BannerCarouselProps {
  className?: string;
  banners?: BannerContent[];
  autoSlide?: boolean;
  slideInterval?: number;
  showDots?: boolean;
  showArrows?: boolean;
  onClose?: () => void;
  onSlideChange?: (index: number, banner: BannerContent) => void;
  enableKeyboardNavigation?: boolean;
  enableSwipeGestures?: boolean;
  lazyLoad?: boolean;
  preloadImages?: boolean;
}

export default function BannerCarousel({
  className = "",
  banners,
  autoSlide = true,
  slideInterval = 5000,
  showDots = true,
  showArrows = true,
  onClose,
  onSlideChange,
  enableKeyboardNavigation = true,
  enableSwipeGestures = true,
  lazyLoad = true,
  preloadImages = true,
}: BannerCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [isPaused, setIsPaused] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const [failedImages, setFailedImages] = useState<Set<string>>(new Set());
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const [progress, setProgress] = useState(0);
  const [lastScrollTime, setLastScrollTime] = useState(0);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const carouselRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Professional banner content with high-quality assets
  const defaultBanners: BannerContent[] = [
    {
      id: "hero-1",
      type: "image",
      url: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=2560&q=80",
      title: "Black Friday Mega Sale",
      description: "Up to 70% off on premium electronics and gadgets",
      buttonText: "Shop Now",
      buttonLink: "/black-friday",
      backgroundColor: "from-red-600/85 to-black/75",
      priority: "high",
      alt: "Black Friday electronics sale banner",
      preloadNext: true,
    },
    {
      id: "hero-2",
      type: "image",
      url: "https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?ixlib=rb-4.0.3&auto=format&fit=crop&w=2560&q=80",
      title: "Smart Home Revolution",
      description: "Transform your living space with cutting-edge technology",
      buttonText: "Explore Smart Home",
      buttonLink: "/smart-home",
      backgroundColor: "from-blue-600/80 to-indigo-700/80",
      priority: "high",
      alt: "Smart home technology showcase",
    },
    {
      id: "hero-3",
      type: "image",
      url: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=2560&q=80",
      title: "Fashion Week Collection",
      description:
        "Exclusive designer pieces from the world's top fashion houses",
      buttonText: "Shop Collection",
      buttonLink: "/fashion-week",
      backgroundColor: "from-pink-500/80 to-purple-600/80",
      priority: "medium",
      alt: "Fashion week designer collection",
    },
    {
      id: "hero-4",
      type: "image",
      url: "https://images.unsplash.com/photo-1542291026-7eec264c27ff?ixlib=rb-4.0.3&auto=format&fit=crop&w=2560&q=80",
      title: "Athletic Performance",
      description: "Professional-grade sports equipment for peak performance",
      buttonText: "Shop Athletic",
      buttonLink: "/athletic",
      backgroundColor: "from-orange-500/80 to-red-600/80",
      priority: "medium",
      alt: "Professional athletic equipment showcase",
    },
    {
      id: "hero-5",
      type: "image",
      url: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2560&q=80",
      title: "Luxury Lifestyle",
      description: "Curated premium products for the sophisticated lifestyle",
      buttonText: "Explore Luxury",
      buttonLink: "/luxury",
      backgroundColor: "from-amber-500/80 to-yellow-600/80",
      priority: "low",
      alt: "Luxury lifestyle products showcase",
    },
  ];

  const activeBanners = banners || defaultBanners;

  // Image preloading functionality with error handling
  const preloadImage = useCallback(
    (url: string) => {
      if (loadedImages.has(url) || failedImages.has(url)) return;

      const img = new Image();
      img.onload = () => {
        setLoadedImages((prev) => new Set([...prev, url]));
      };
      img.onerror = () => {
        setFailedImages((prev) => new Set([...prev, url]));
      };
      img.src = url;
    },
    [loadedImages, failedImages]
  );

  // Preload images on mount and when banners change
  useEffect(() => {
    if (preloadImages) {
      activeBanners.forEach((banner) => {
        if (banner.type === "image") {
          preloadImage(banner.url);
        }
      });
    }
  }, [activeBanners, preloadImages, preloadImage]);

  // Navigation functions with callbacks
  const goToSlide = useCallback(
    (index: number) => {
      if (index === currentIndex) return;
      setIsLoading(true);
      setCurrentIndex(index);
      setProgress(0); // Reset progress when changing slides
      onSlideChange?.(index, activeBanners[index]);
      setTimeout(() => setIsLoading(false), 300);
    },
    [currentIndex, activeBanners, onSlideChange]
  );

  const goToPrevious = useCallback(() => {
    const newIndex =
      currentIndex === 0 ? activeBanners.length - 1 : currentIndex - 1;
    goToSlide(newIndex);
  }, [currentIndex, activeBanners.length, goToSlide]);

  const goToNext = useCallback(() => {
    const newIndex = (currentIndex + 1) % activeBanners.length;
    goToSlide(newIndex);
  }, [currentIndex, activeBanners.length, goToSlide]);

  // Auto-slide functionality with progress tracking
  useEffect(() => {
    if (autoSlide && !isPaused && activeBanners.length > 1) {
      // Reset progress when starting auto-slide
      setProgress(0);

      // Update progress every 100ms
      progressIntervalRef.current = setInterval(() => {
        setProgress((prev) => {
          const newProgress = prev + (100 / slideInterval) * 100;
          if (newProgress >= 100) {
            return 0; // Reset for next slide
          }
          return newProgress;
        });
      }, 100);

      // Change slide at the specified interval
      intervalRef.current = setInterval(goToNext, slideInterval);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, [autoSlide, isPaused, slideInterval, activeBanners.length, goToNext]);

  const handleClose = useCallback(() => {
    setIsVisible(false);
    if (onClose) {
      onClose();
    }
  }, [onClose]);

  // Keyboard navigation
  useEffect(() => {
    if (!enableKeyboardNavigation) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "ArrowLeft") {
        event.preventDefault();
        goToPrevious();
      } else if (event.key === "ArrowRight") {
        event.preventDefault();
        goToNext();
      } else if (event.key === "Escape") {
        event.preventDefault();
        handleClose();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [enableKeyboardNavigation, goToPrevious, goToNext, handleClose]);

  // Scroll detection to advance banner
  useEffect(() => {
    const handleScroll = () => {
      const currentTime = Date.now();

      // Throttle scroll events to prevent too frequent banner changes
      if (currentTime - lastScrollTime < 1000) return; // 1 second throttle

      setLastScrollTime(currentTime);

      // Clear any existing timeout
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      // Set a timeout to advance to next banner after scroll stops
      scrollTimeoutRef.current = setTimeout(() => {
        if (activeBanners.length > 1) {
          goToNext();
        }
      }, 300); // Wait 300ms after scroll stops
    };

    // Add scroll event listener
    window.addEventListener("scroll", handleScroll, { passive: true });

    // Cleanup
    return () => {
      window.removeEventListener("scroll", handleScroll);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [lastScrollTime, activeBanners.length, goToNext]);

  // Touch gesture support
  const handleTouchStart = (e: React.TouchEvent) => {
    if (!enableSwipeGestures) return;
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!enableSwipeGestures) return;
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!enableSwipeGestures || !touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      goToNext();
    } else if (isRightSwipe) {
      goToPrevious();
    }
  };

  const handleMouseEnter = () => {
    setIsPaused(true);
    // Clear progress interval when paused
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }
  };

  const handleMouseLeave = () => {
    setIsPaused(false);
    // Resume progress tracking when unpaused
    if (autoSlide && activeBanners.length > 1) {
      progressIntervalRef.current = setInterval(() => {
        setProgress((prev) => {
          const newProgress = prev + (100 / slideInterval) * 100;
          if (newProgress >= 100) {
            return 0;
          }
          return newProgress;
        });
      }, 100);
    }
  };

  if (!isVisible || activeBanners.length === 0) return null;

  const currentBanner = activeBanners[currentIndex];

  return (
    <div
      ref={carouselRef}
      className={`relative w-full overflow-hidden rounded-lg shadow-2xl ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      role="region"
      aria-label="Banner carousel"
      aria-live="polite"
    >
      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-black/20 flex items-center justify-center z-50">
          <div className="bg-white/90 rounded-full p-3">
            <Icon
              icon="mdi:loading"
              className="h-6 w-6 animate-spin text-[#356267]"
            />
          </div>
        </div>
      )}

      {/* Banner Container */}
      <div
        id={`banner-${currentBanner.id}`}
        className="relative w-full h-40 sm:h-48 md:h-56 lg:h-64 xl:h-72"
        role="tabpanel"
        aria-labelledby={`banner-tab-${currentIndex}`}
      >
        {/* Background Image/Video */}
        <div className="absolute inset-0">
          {currentBanner.type === "video" ? (
            <video
              className="w-full h-full object-cover transition-opacity duration-500"
              autoPlay
              muted
              loop
              playsInline
              preload="metadata"
            >
              <source src={currentBanner.url} type="video/mp4" />
              <track kind="captions" />
            </video>
          ) : (
            <NextImage
              src={
                failedImages.has(currentBanner.url)
                  ? "/assets/images/placeholders/placeholder.jpg"
                  : currentBanner.url
              }
              alt={currentBanner.alt || currentBanner.title}
              fill
              className={`object-cover transition-opacity duration-500 ${
                lazyLoad &&
                !loadedImages.has(currentBanner.url) &&
                !failedImages.has(currentBanner.url)
                  ? "opacity-0"
                  : "opacity-100"
              }`}
              priority={currentBanner.priority === "high"}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              onError={() => {
                setFailedImages(
                  (prev) => new Set([...prev, currentBanner.url])
                );
              }}
            />
          )}
        </div>

        {/* Gradient Overlay */}
        <div
          className={`absolute inset-0 bg-gradient-to-r ${
            currentBanner.backgroundColor || "from-black/40 to-black/20"
          }`}
        />

        {/* Content Overlay */}
        <div className="absolute inset-0 flex items-center justify-center text-white">
          <div className="text-center px-4 sm:px-6 max-w-5xl">
            <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold mb-3 sm:mb-4 drop-shadow-lg">
              {currentBanner.title}
            </h1>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl mb-6 sm:mb-8 opacity-95 drop-shadow-md max-w-3xl mx-auto">
              {currentBanner.description}
            </p>
            <Button
              variant="default"
              size="lg"
              className="bg-[#356267] hover:bg-[#478085] text-white px-6 py-3 sm:px-8 sm:py-4 text-sm sm:text-base lg:text-lg font-semibold transition-all duration-300 hover:scale-105 hover:shadow-xl focus:ring-4 focus:ring-[#356267]/50"
              onClick={() => {
                if (currentBanner.buttonLink) {
                  window.location.href = currentBanner.buttonLink;
                }
              }}
              aria-label={`${currentBanner.buttonText} - ${currentBanner.title}`}
            >
              <span className="flex items-center gap-2">
                {currentBanner.buttonText}
                <Icon
                  icon="mdi:arrow-right"
                  className="h-4 w-4 sm:h-5 sm:w-5"
                />
              </span>
            </Button>
          </div>
        </div>

        {/* Navigation Arrows */}
        {showArrows && activeBanners.length > 1 && (
          <>
            <Button
              variant="secondary"
              size="sm"
              onClick={goToPrevious}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white border-none p-3 sm:p-4 rounded-full transition-all duration-300 hover:scale-110 focus:ring-4 focus:ring-white/50"
              title="Previous banner"
              aria-label="Go to previous banner"
            >
              <Icon icon="mdi:chevron-left" className="h-5 w-5 sm:h-6 sm:w-6" />
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={goToNext}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white border-none p-3 sm:p-4 rounded-full transition-all duration-300 hover:scale-110 focus:ring-4 focus:ring-white/50"
              title="Next banner"
              aria-label="Go to next banner"
            >
              <Icon
                icon="mdi:chevron-right"
                className="h-5 w-5 sm:h-6 sm:w-6"
              />
            </Button>
          </>
        )}
      </div>

      {/* Dots Indicator */}
      {showDots && activeBanners.length > 1 && (
        <div
          className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2"
          role="tablist"
          aria-label="Banner slides"
        >
          {activeBanners.map((banner, index) => (
            <button
              key={banner.id}
              onClick={() => goToSlide(index)}
              className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white/50 ${
                index === currentIndex
                  ? "bg-white scale-125"
                  : "bg-white/50 hover:bg-white/75"
              }`}
              title={`Go to slide ${index + 1}: ${banner.title}`}
              aria-label={`Go to slide ${index + 1}: ${banner.title}`}
              role="tab"
              aria-selected={index === currentIndex}
              aria-controls={`banner-${banner.id}`}
            />
          ))}
        </div>
      )}

      {/* Progress Bar */}
      {autoSlide && !isPaused && activeBanners.length > 1 && (
        <div className="absolute bottom-0 left-0 w-full h-1 bg-black/20">
          <div
            className="h-full bg-[#356267] transition-all duration-100 ease-linear"
            style={{
              width: `${progress}%`,
            }}
          />
        </div>
      )}
    </div>
  );
}
