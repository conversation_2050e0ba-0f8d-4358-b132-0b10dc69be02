# Stage 1: Build the application
FROM node:20-slim AS builder
WORKDIR /app

# Accept build arguments
ARG NEXT_PUBLIC_API_BASE_URL
ENV NEXT_PUBLIC_API_BASE_URL=$NEXT_PUBLIC_API_BASE_URL

# Copy package files first for better caching
COPY package.json package-lock.json* ./

# Install dependencies
RUN npm install

# Copy all files including .env
COPY . .

# Build the application
RUN npm run build

# Stage 2: Production image
FROM node:20-slim
WORKDIR /app

# Install curl for health check and ca-certificates for SSL
RUN apt-get update && apt-get install -y curl ca-certificates && rm -rf /var/lib/apt/lists/*

# Copy from builder
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
# Copy any environment files needed
COPY --from=builder /app/.env* ./

# Create uploads directory with proper permissions for user uploads
RUN mkdir -p /app/public/uploads && chmod 777 /app/public/uploads

# Install production dependencies only
RUN npm prune --production

# Expose the port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Start the application
CMD ["npm", "start"]
