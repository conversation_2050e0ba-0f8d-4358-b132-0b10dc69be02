import { useMemo } from "react"
import { validateFormData } from "@/utils/form-transformer"
import { type FormData } from "../types"

export interface FormValidationResult {
  completedFields: number
  totalRequiredFields: number
  isFormValid: boolean
  errors: string[]
  progress: number
}

export const useFormValidation = (formData: FormData): FormValidationResult => {
  return useMemo(() => {
    // Calculate completed fields
    let completedFields = 0
    const totalRequiredFields = 6

    if (formData.adTitle.trim()) completedFields++
    if (formData.photos.length > 0) completedFields++
    if (formData.categoryId) completedFields++
    if (formData.description.trim()) completedFields++
    if (formData.condition) completedFields++
    if (formData.price) completedFields++

    // Validate all steps
    const step1Errors = validateFormData(formData, 1)
    const step2Errors = validateFormData(formData, 2)
    const step3Errors = validateFormData(formData, 3)
    const allErrors = [...step1Errors, ...step2Errors, ...step3Errors]

    const isFormValid = allErrors.length === 0 && completedFields >= 4 // Minimum required fields
    const progress = (completedFields / totalRequiredFields) * 100

    return {
      completedFields,
      totalRequiredFields,
      isFormValid,
      errors: allErrors,
      progress,
    }
  }, [formData])
}
