import React from "react"
import { Icon } from "@iconify/react"
import { type AutoSaveStatus } from "../hooks/useAutoSave"

interface AutoSaveIndicatorProps {
  status: AutoSaveStatus
}

export const AutoSaveIndicator: React.FC<AutoSaveIndicatorProps> = ({ status }) => {
  const getStatusConfig = () => {
    switch (status) {
      case "saving":
        return { icon: "lucide:loader-2", text: "Saving draft...", color: "text-blue-600" }
      case "saved":
        return { icon: "lucide:check", text: "Draft saved", color: "text-green-600" }
      case "error":
        return { icon: "lucide:alert-circle", text: "Save failed", color: "text-red-600" }
      default:
        return null
    }
  }

  const config = getStatusConfig()
  if (!config) return null

  return (
    <div className={`flex items-center gap-2 text-sm ${config.color} transition-all duration-300`}>
      <Icon icon={config.icon} className={`w-4 h-4 ${status === "saving" ? "animate-spin" : ""}`} />
      <span>{config.text}</span>
    </div>
  )
}
