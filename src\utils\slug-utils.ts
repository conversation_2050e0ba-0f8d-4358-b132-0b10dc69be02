/**
 * Slug utility functions for generating URL-friendly slugs from product titles
 * and finding products by slug
 */

import type { Product } from "@/types/ecommerce";
import type { AdvertisementResponse } from "@/types/advertisement";
import { createProductImageConfig } from "@/services/api-image-service";

/**
 * Generate a URL-friendly slug from a product title
 * @param title - The product title to convert to a slug
 * @param id - Optional product ID to append for uniqueness
 * @returns URL-friendly slug
 */
export function generateSlug(title: string, id?: string): string {
  if (!title) {
    return id ? `product-${id}` : "product";
  }

  // Convert to lowercase and replace spaces and special characters
  let slug = title
    .toLowerCase()
    .trim()
    // Replace spaces and multiple spaces with hyphens
    .replace(/\s+/g, "-")
    // Remove special characters except hyphens and alphanumeric
    .replace(/[^a-z0-9-]/g, "")
    // Remove multiple consecutive hyphens
    .replace(/-+/g, "-")
    // Remove leading and trailing hyphens
    .replace(/^-+|-+$/g, "");

  // If slug is empty after cleaning, use fallback
  if (!slug) {
    slug = id ? `product-${id}` : "product";
  }

  // Optionally append ID for uniqueness (recommended for e-commerce)
  if (id) {
    slug = `${slug}-${id}`;
  }

  return slug;
}

/**
 * Extract product ID from a slug that includes the ID at the end
 * @param slug - The slug to extract ID from (e.g., "iphone-14-pro-123" or "iphone-14-pro-550e8400-e29b-41d4-a716-************")
 * @returns The extracted ID or null if not found
 */
export function extractIdFromSlug(slug: string): string | null {
  if (!slug) return null;

  // UUID pattern: 8-4-4-4-12 hexadecimal characters separated by hyphens
  const uuidPattern = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

  // Check if the slug ends with a UUID
  const uuidMatch = slug.match(uuidPattern);
  if (uuidMatch) {
    return uuidMatch[0];
  }

  // Fallback: Split by hyphens and get the last part for simple IDs
  const parts = slug.split("-");
  const lastPart = parts[parts.length - 1];

  // Check if the last part looks like a simple ID (alphanumeric only)
  if (/^[a-zA-Z0-9]+$/.test(lastPart)) {
    return lastPart;
  }

  return null;
}

/**
 * Find a product by its slug from an array of products
 * @param products - Array of products to search
 * @param slug - The slug to search for
 * @returns The found product or null
 */
export function findProductBySlug(
  products: Product[],
  slug: string
): Product | null {
  if (!slug || !products.length) return null;

  // First try to find by exact slug match
  const exactMatch = products.find((product) => product.slug === slug);
  if (exactMatch) return exactMatch;

  // If no exact match, try to extract ID from slug and find by ID
  const extractedId = extractIdFromSlug(slug);
  if (extractedId) {
    const idMatch = products.find((product) => product.id === extractedId);
    if (idMatch) return idMatch;
  }

  return null;
}

/**
 * Validate if a slug is properly formatted
 * @param slug - The slug to validate
 * @returns True if the slug is valid
 */
export function isValidSlug(slug: string): boolean {
  if (!slug) return false;

  // Check if slug contains only lowercase letters, numbers, and hyphens
  // Should not start or end with hyphens, and should not have consecutive hyphens
  const slugPattern = /^[a-z0-9]+(-[a-z0-9]+)*$/;
  return slugPattern.test(slug);
}

/**
 * Generate a unique slug by checking against existing products
 * @param title - The product title
 * @param id - The product ID
 * @param existingProducts - Array of existing products to check against
 * @returns A unique slug
 */
export function generateUniqueSlug(
  title: string,
  id: string,
  existingProducts: Product[] = []
): string {
  const baseSlug = generateSlug(title, id);

  // Check if the slug already exists
  const existingSlug = existingProducts.find(
    (product) => product.slug === baseSlug
  );

  if (!existingSlug) {
    return baseSlug;
  }

  // If slug exists, append a number to make it unique
  let counter = 1;
  let uniqueSlug = `${baseSlug}-${counter}`;

  while (existingProducts.find((product) => product.slug === uniqueSlug)) {
    counter++;
    uniqueSlug = `${baseSlug}-${counter}`;
  }

  return uniqueSlug;
}

/**
 * Update product with generated slug
 * @param product - The product to update
 * @param existingProducts - Array of existing products to ensure uniqueness
 * @returns Updated product with slug
 */
export function addSlugToProduct(
  product: Product,
  existingProducts: Product[] = []
): Product {
  const slug = generateUniqueSlug(product.title, product.id, existingProducts);

  return {
    ...product,
    slug,
  };
}

/**
 * Convert AdvertisementResponse to Product format for compatibility with existing components
 * @param advertisement - The advertisement to convert
 * @returns Product object compatible with existing ProductGrid component
 */
export function convertAdvertisementToProduct(
  advertisement: AdvertisementResponse
): Product {
  // Generate slug from title and ID
  const slug = generateSlug(advertisement.title, advertisement.id);

  // Use the API image service to handle images properly
  const imageConfig = createProductImageConfig(
    advertisement.images,
    advertisement.category?.name,
    advertisement.title
  );

  // Format location string
  const locationParts = [
    advertisement.location,
    advertisement.city,
    advertisement.state,
  ].filter(Boolean);
  const location = locationParts.join(", ") || "Location not specified";

  // Convert condition to match Product interface
  const conditionMap: Record<string, "new" | "used" | "refurbished"> = {
    new: "new",
    used: "used",
    refurbished: "refurbished",
  };

  return {
    id: advertisement.id,
    slug,
    title: advertisement.title,
    description: advertisement.description,
    price: advertisement.price || 0,
    currency: advertisement.currency === "NPR" ? "Rs." : advertisement.currency,
    images: imageConfig.gallery,
    category: advertisement.category?.name || "Uncategorized",
    subcategory: advertisement.subcategory?.name,
    location,
    seller: {
      id: advertisement.user.id,
      name: advertisement.user.firstName && advertisement.user.lastName
        ? `${advertisement.user.firstName} ${advertisement.user.lastName}`
        : advertisement.user.firstName || advertisement.user.lastName || advertisement.user.username,
      avatar: advertisement.user.profilePictureUrl,
      rating: 4.2, // Default rating since not available in advertisement
    },
    condition: conditionMap[advertisement.condition || "used"] || "used",
    brand: undefined, // Not available in advertisement
    postedAt: advertisement.createdAt,
    delivery: {
      available: false, // Default since not available in advertisement
      type: undefined,
      cost: undefined,
    },
    featured: advertisement.isFeatured,
    status:
      advertisement.status === "active"
        ? "active"
        : advertisement.status === "sold"
        ? "sold"
        : advertisement.status === "expired"
        ? "expired"
        : "inactive",
    // Additional properties for enhanced UI
    originalPrice: undefined,
    postedDate: advertisement.createdAt,
    views: advertisement.viewCount,
    features: undefined,
    additionalInfo: advertisement.youtubeVideoUrl
      ? `Video: ${advertisement.youtubeVideoUrl}`
      : undefined,
    // Enhanced functionality properties
    soldAt:
      advertisement.status === "sold" ? advertisement.updatedAt : undefined,
    soldPrice:
      advertisement.status === "sold" ? advertisement.price : undefined,
    isSavedByUser: false, // Default - would need to check user's favorites
    saveCount: advertisement.favoriteCount,
  };
}

/**
 * Convert array of AdvertisementResponse to Product array
 * @param advertisements - Array of advertisements to convert
 * @returns Array of Product objects
 */
export function convertAdvertisementsToProducts(
  advertisements: AdvertisementResponse[]
): Product[] {
  return advertisements.map(convertAdvertisementToProduct);
}
