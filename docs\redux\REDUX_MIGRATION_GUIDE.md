# Redux Toolkit Migration Guide

This guide explains how to migrate from React Context to Redux Toolkit in your e-commerce application.

## Overview

The application has been successfully migrated from React Context + useReducer to Redux Toolkit with the following benefits:

- **Better Performance**: Memoized selectors and optimized re-renders
- **DevTools Integration**: Advanced debugging with Redux DevTools
- **Persistence**: Automatic state persistence with redux-persist
- **Middleware Support**: Logger, thunk, and custom middleware
- **Type Safety**: Full TypeScript support with typed hooks
- **Scalability**: Better organization for complex state management

## Architecture

### Store Structure
```
src/store/
├── index.ts              # Store configuration
├── hooks.ts              # Typed hooks and actions
├── selectors.ts          # Memoized selectors
├── ReduxProvider.tsx     # Provider component
├── compatibility.tsx     # Backward compatibility layer
├── slices/              # Redux slices
│   ├── searchSlice.ts
│   ├── categorySlice.ts
│   ├── filterSlice.ts
│   ├── productSlice.ts
│   ├── cartSlice.ts
│   ├── userSlice.ts
│   └── modalSlice.ts
└── thunks/              # Async actions
    └── ecommerceThunks.ts
```

## Migration Steps

### 1. Install Dependencies
```bash
npm install @reduxjs/toolkit react-redux redux-persist
npm install --save-dev @types/react-redux redux-logger @types/redux-logger
```

### 2. Replace Context Usage

#### Before (Context):
```tsx
import { useEcommerce, useEcommerceActions } from "@/context/EcommerceContext";

function MyComponent() {
  const { state } = useEcommerce();
  const { setSortBy, addToCart } = useEcommerceActions();
  
  const { products, sortBy } = state.product;
  const { items } = state.cart;
  
  return <div>...</div>;
}
```

#### After (Redux):
```tsx
import { useAppSelector, useEcommerceActions } from "@/store/hooks";
import { selectProducts, selectSortBy, selectCartItems } from "@/store/selectors";

function MyComponent() {
  const products = useAppSelector(selectProducts);
  const sortBy = useAppSelector(selectSortBy);
  const cartItems = useAppSelector(selectCartItems);
  const { setSortBy, addToCart } = useEcommerceActions();
  
  return <div>...</div>;
}
```

### 3. Use Selectors for Performance

#### Memoized Selectors:
```tsx
// Instead of accessing nested state directly
const products = useAppSelector(state => state.product.filteredProducts);

// Use memoized selectors
const products = useAppSelector(selectFilteredProducts);
```

### 4. Async Operations with Thunks

#### Complex Operations:
```tsx
import { useAppDispatch } from "@/store/hooks";
import { updateSortAndProducts } from "@/store/thunks/ecommerceThunks";

function SortComponent() {
  const dispatch = useAppDispatch();
  
  const handleSortChange = (sortBy: SortBy) => {
    // This will update sort AND re-filter products
    dispatch(updateSortAndProducts(sortBy));
  };
  
  return <SortDropdown onSortChange={handleSortChange} />;
}
```

## Key Features

### 1. State Persistence
Cart and user data are automatically persisted to localStorage:
```tsx
const persistConfig = {
  key: 'ecommerce-root',
  storage,
  whitelist: ['cart', 'user'], // Only persist these slices
  blacklist: ['search', 'modal'], // Don't persist these
};
```

### 2. Redux DevTools
Enhanced debugging with:
- Action history and time travel
- State inspection
- Performance monitoring
- Data sanitization for sensitive information

### 3. Middleware Stack
- **Redux Thunk**: For async actions
- **Redux Logger**: Development logging
- **Redux Persist**: State persistence
- **Custom Middleware**: For specific business logic

### 4. Type Safety
```tsx
// Typed hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Typed selectors
export const selectProducts = (state: RootState) => state.product.products;
```

## Best Practices

### 1. Use Selectors
Always use selectors instead of accessing state directly:
```tsx
// ❌ Don't do this
const products = useAppSelector(state => state.product.products);

// ✅ Do this
const products = useAppSelector(selectProducts);
```

### 2. Batch Related Updates
Use thunks for operations that update multiple slices:
```tsx
// ❌ Multiple dispatches
dispatch(setCategory(categoryId));
dispatch(clearFilters());
dispatch(updateAvailableFilters(categoryId));

// ✅ Single thunk
dispatch(selectCategoryWithFilters(categoryId));
```

### 3. Optimistic Updates
For better UX, update UI immediately and handle errors:
```tsx
export const addToCartOptimistic = createAsyncThunk(
  'cart/addToCartOptimistic',
  async ({ product, quantity }, { dispatch, rejectWithValue }) => {
    try {
      // Optimistically update UI
      dispatch(addToCart({ product, quantity }));
      
      // Sync with server
      await syncWithServer();
    } catch (error) {
      // Revert on error
      dispatch(removeFromCart(product.id));
      return rejectWithValue('Failed to add item');
    }
  }
);
```

### 4. Error Handling
Use consistent error handling patterns:
```tsx
const handleAsyncAction = async () => {
  try {
    await dispatch(someAsyncAction()).unwrap();
    // Success handling
  } catch (error) {
    // Error handling
    console.error('Action failed:', error);
  }
};
```

## Backward Compatibility

The migration includes compatibility layers to ensure existing components continue working:

1. **CompatibilityProvider**: Wraps Redux state to match old context API
2. **useEcommerce**: Maintains the same interface as the original hook
3. **Gradual Migration**: Components can be migrated one at a time

## Performance Benefits

1. **Selective Re-renders**: Components only re-render when their selected state changes
2. **Memoized Selectors**: Expensive computations are cached
3. **Normalized State**: Efficient updates and lookups
4. **Middleware Optimization**: Custom middleware for performance monitoring

## Testing

Redux Toolkit makes testing easier:
```tsx
import { store } from '@/store';
import { selectProducts } from '@/store/selectors';

// Test selectors
const state = store.getState();
const products = selectProducts(state);

// Test actions
store.dispatch(addToCart({ product, quantity: 1 }));
```

## Next Steps

1. **Migrate Components**: Gradually update components to use Redux hooks
2. **Add RTK Query**: For server state management and caching
3. **Implement Middleware**: Add custom middleware for analytics, logging, etc.
4. **Optimize Selectors**: Add more specific selectors for better performance
5. **Add Tests**: Write tests for slices, selectors, and thunks

## Common Patterns

### Loading States
```tsx
const isLoading = useAppSelector(selectGlobalLoading);
const error = useAppSelector(selectGlobalError);

if (isLoading) return <LoadingSpinner />;
if (error) return <ErrorMessage error={error} />;
```

### Conditional Rendering
```tsx
const isAuthenticated = useAppSelector(selectIsAuthenticated);
const cartItemCount = useAppSelector(selectCartTotalItems);

return (
  <div>
    {isAuthenticated && <UserMenu />}
    {cartItemCount > 0 && <CartBadge count={cartItemCount} />}
  </div>
);
```

### Form Handling
```tsx
const dispatch = useAppDispatch();
const [formData, setFormData] = useState({});

const handleSubmit = async (e) => {
  e.preventDefault();
  try {
    await dispatch(updateUserProfile(formData)).unwrap();
    // Success
  } catch (error) {
    // Handle error
  }
};
```

This migration provides a solid foundation for scaling your e-commerce application with better performance, debugging capabilities, and maintainability.
