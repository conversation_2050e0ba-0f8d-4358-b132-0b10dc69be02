"use client";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

export function AuthSection() {
  return (
    <div className="flex items-center space-x-2">
      <Link href="/login">
        <Button
          variant="ghost"
          className="text-white hover:text-gray-200 hover:bg-[#1a5157] rounded-xl px-4 py-2 text-md font-medium transition-all duration-300 border border-white backdrop-blur-md bg-[#1F5E64]"
        >
          Login
        </Button>
      </Link>
      <Link href="/signup">
        <Button className="bg-[#1F5E64] text-white hover:bg-[#1a5157] rounded-xl px-4 py-2 text-md font-medium transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-white">
          Sign Up
        </Button>
      </Link>
    </div>
  );
}
