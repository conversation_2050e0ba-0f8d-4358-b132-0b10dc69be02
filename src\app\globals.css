@import "tailwindcss";

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --radius: 0.5rem;
  }

  .light {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background: #efefef;
    color: #171717;
    font-family: Arial, Helvetica, sans-serif;
  }
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-heading: var(--font-playfair-display);
  --font-playfair: var(--font-playfair-display);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Apply Playfair Display to all headings */
h1,
h2,
h3,
h4,
h5,
h6,
.heading,
.font-heading,
.font-playfair {
  font-family: var(--font-playfair-display), serif;
}

/* Utility classes for Playfair Display font */
.font-playfair-display {
  font-family: var(--font-playfair-display), serif;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation utilities */
.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.2s ease-out;
}

/* Smooth transitions for interactive elements */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Hide scrollbar while keeping scroll functionality */
.scrollbar-hide {
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

/* Responsive Container Utilities */
.container-responsive {
  @apply mx-[3%] sm:mx-[4%] md:mx-[5%] max-w-none;
}

.container-responsive-sm {
  @apply mx-[2%] sm:mx-[3%] md:mx-[4%] lg:mx-[5%];
}

.container-responsive-lg {
  @apply mx-[3%] sm:mx-[5%] md:mx-[6%] lg:mx-[8%] xl:mx-[10%];
}

.container-responsive-xl {
  @apply mx-[2%] sm:mx-[3%] md:mx-[5%] lg:mx-[8%] xl:mx-[12%] 2xl:mx-[15%];
}

/* Responsive Text Utilities */
.text-responsive-xs {
  @apply text-xs sm:text-sm md:text-sm;
}

.text-responsive-sm {
  @apply text-sm sm:text-base md:text-base;
}

.text-responsive-base {
  @apply text-sm sm:text-base md:text-lg lg:text-lg;
}

.text-responsive-lg {
  @apply text-base sm:text-lg md:text-xl lg:text-xl;
}

.text-responsive-xl {
  @apply text-lg sm:text-xl md:text-2xl lg:text-2xl;
}

.text-responsive-2xl {
  @apply text-xl sm:text-2xl md:text-3xl lg:text-3xl xl:text-4xl;
}

.text-responsive-3xl {
  @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl;
}

/* Responsive Spacing Utilities */
.spacing-responsive-xs {
  @apply p-1 sm:p-2 md:p-2 lg:p-3;
}

.spacing-responsive-sm {
  @apply p-2 sm:p-3 md:p-3 lg:p-4;
}

.spacing-responsive-md {
  @apply p-3 sm:p-4 md:p-5 lg:p-6;
}

.spacing-responsive-lg {
  @apply p-4 sm:p-6 md:p-7 lg:p-8 xl:p-10;
}

.spacing-responsive-xl {
  @apply p-6 sm:p-8 md:p-10 lg:p-12 xl:p-16;
}

.gap-responsive-xs {
  @apply gap-1 sm:gap-2 md:gap-2 lg:gap-3;
}

.gap-responsive-sm {
  @apply gap-2 sm:gap-3 md:gap-3 lg:gap-4;
}

.gap-responsive-md {
  @apply gap-3 sm:gap-4 md:gap-5 lg:gap-6;
}

.gap-responsive-lg {
  @apply gap-4 sm:gap-6 md:gap-7 lg:gap-8 xl:gap-10;
}

.gap-responsive-xl {
  @apply gap-6 sm:gap-8 md:gap-10 lg:gap-12 xl:gap-16;
}

/* Responsive Grid Utilities */
.grid-responsive-1-2-3 {
  @apply grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3;
}

.grid-responsive-1-2-4 {
  @apply grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4;
}

.grid-responsive-2-3-4 {
  @apply grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4;
}

.grid-responsive-1-3-5 {
  @apply grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5;
}

.grid-responsive-2-4-6 {
  @apply grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6;
}

/* Responsive Flex Utilities */
.flex-responsive-col-row {
  @apply flex-col sm:flex-row;
}

.flex-responsive-row-col {
  @apply flex-row sm:flex-col;
}

/* Mobile-first responsive heights */
.h-responsive-screen {
  @apply h-screen sm:h-auto;
}

/* Mobile compact product cards */
.mobile-compact {
  @apply min-h-[280px] max-h-[320px];
}

@media (max-width: 640px) {
  .mobile-compact {
    @apply min-h-[260px] max-h-[300px];
  }
}

.h-responsive-auto {
  @apply h-auto sm:h-screen;
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-scaleIn {
  animation: scaleIn 0.4s ease-out forwards;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

@keyframes slideRight {
  0% {
    left: 0%;
    width: 0%;
  }
  50% {
    left: 0%;
    width: 100%;
  }
  100% {
    left: 100%;
    width: 0%;
  }
}

/* Custom select styling */
select option {
  padding: 8px 12px;
  background: white;
  color: #374151;
  font-weight: 500;
}

select option:hover {
  background: #f0fdfa;
  color: #0d9488;
}

/* Enhanced focus states */
.group:focus-within .group-hover\:shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Modal animations */
@keyframes modal-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modal-zoom-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-in {
  animation-fill-mode: both;
}

.fade-in-0 {
  animation-name: modal-fade-in;
}

.zoom-in-95 {
  animation-name: modal-zoom-in;
}

.duration-200 {
  animation-duration: 200ms;
}

/* Product Card Grid Utilities */
.product-grid-equal-height {
  display: grid;
  grid-auto-rows: 1fr;
}

.product-card-flex {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.product-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-card-button {
  margin-top: auto;
}

/* Additional Responsive Utilities */

/* Touch-friendly minimum sizes */
.touch-target {
  @apply min-w-[44px] min-h-[44px];
}

.touch-target-sm {
  @apply min-w-[40px] min-h-[40px];
}

.touch-target-lg {
  @apply min-w-[48px] min-h-[48px];
}

/* Responsive border radius */
.rounded-responsive {
  @apply rounded-md sm:rounded-lg md:rounded-xl;
}

.rounded-responsive-sm {
  @apply rounded sm:rounded-md md:rounded-lg;
}

.rounded-responsive-lg {
  @apply rounded-lg sm:rounded-xl md:rounded-2xl;
}

/* Responsive shadows */
.shadow-responsive {
  @apply shadow-sm sm:shadow md:shadow-lg;
}

.shadow-responsive-lg {
  @apply shadow-md sm:shadow-lg md:shadow-xl;
}

/* Responsive line heights */
.leading-responsive {
  @apply leading-tight sm:leading-normal md:leading-relaxed;
}

.leading-responsive-tight {
  @apply leading-tight sm:leading-snug md:leading-normal;
}

/* Responsive max widths */
.max-w-responsive-sm {
  @apply max-w-xs sm:max-w-sm md:max-w-md;
}

.max-w-responsive-md {
  @apply max-w-sm sm:max-w-md md:max-w-lg lg:max-w-xl;
}

.max-w-responsive-lg {
  @apply max-w-md sm:max-w-lg md:max-w-xl lg:max-w-2xl xl:max-w-3xl;
}

/* Mobile-first responsive visibility */
.mobile-only {
  @apply block sm:hidden;
}

.tablet-only {
  @apply hidden sm:block lg:hidden;
}

.desktop-only {
  @apply hidden lg:block;
}

.mobile-tablet {
  @apply block lg:hidden;
}

.tablet-desktop {
  @apply hidden sm:block;
}

/* Edge case responsive utilities */

/* Very small screens (< 375px) */
@media (max-width: 374px) {
  .xs-hidden {
    display: none !important;
  }

  .xs-text-xs {
    font-size: 0.75rem !important;
  }

  .xs-p-1 {
    padding: 0.25rem !important;
  }

  .xs-gap-1 {
    gap: 0.25rem !important;
  }
}

/* Large tablets and small laptops (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  .tablet-specific {
    display: block;
  }

  .tablet-grid-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .tablet-grid-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

/* Ultra-wide screens (> 1920px) */
@media (min-width: 1920px) {
  .ultra-wide-container {
    max-width: 1800px;
    margin-left: auto;
    margin-right: auto;
  }

  .ultra-wide-grid-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .ultra-wide-text-lg {
    font-size: 1.125rem;
  }
}

/* Landscape mobile orientation */
@media (max-height: 500px) and (orientation: landscape) {
  .landscape-mobile-compact {
    padding: 0.5rem !important;
  }

  .landscape-mobile-hidden {
    display: none !important;
  }
}

/* High DPI screens */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .retina-border {
    border-width: 0.5px;
  }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .animate-fadeIn,
  .animate-slideIn,
  .animate-scaleIn {
    animation: none !important;
  }

  .transition-all,
  .transition-colors,
  .transition-transform {
    transition: none !important;
  }
}
