import React from "react"
import { Icon } from "@iconify/react"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { PhotoUpload } from "../ui/PhotoUpload"
import { SectionHeader } from "../components/SectionHeader"
import { type UseImageHandlingResult } from "../hooks/useImageHandling"

interface ProductPhotosSectionProps {
  imageHandling: UseImageHandlingResult
}

export const ProductPhotosSection: React.FC<ProductPhotosSectionProps> = ({
  imageHandling,
}) => {
  const { uploadedImages, handleImageUpload, handleImageUpdate, handleImageDelete } = imageHandling

  return (
    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
      <CardHeader className="pb-4">
        <SectionHeader
          icon={<Icon icon="lucide:camera" className="w-6 h-6 text-white" />}
          title="Product Photos"
          subtitle="Add high-quality images to attract more buyers"
          required
          step={2}
          totalSteps={6}
        />
      </CardHeader>
      <CardContent>
        <PhotoUpload
          uploadedImages={uploadedImages}
          onImageUpload={handleImageUpload}
          onImageUpdate={handleImageUpdate}
          onImageDelete={handleImageDelete}
        />
        {uploadedImages.length > 0 && (
          <div className="mt-4 flex items-center gap-2 text-green-600">
            <Icon icon="lucide:check-circle" className="w-4 h-4" />
            <span className="text-sm">
              {uploadedImages.length} photo{uploadedImages.length > 1 ? "s" : ""} uploaded
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
