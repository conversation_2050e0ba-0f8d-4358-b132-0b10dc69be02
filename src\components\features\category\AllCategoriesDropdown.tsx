"use client";

import React, { memo } from "react";
import { Icon } from "@iconify/react";
import { useAllCategoriesDropdown } from "@/hooks/use-all-categories-dropdown";
import type { Category } from "@/types/ecommerce";

// Types
interface AllCategoriesDropdownProps {
  onStateChange?: (state: {
    isOpen: boolean;
    categories: Category[];
    expandedCategories: Set<string>;
    handleCategorySelect: (categoryId: string) => void;
    handleToggleExpand: (categoryId: string) => void;
  }) => void;
}

// Dropdown Button Component
interface DropdownButtonProps {
  selectedCategoryData: Category | null;
  isOpen: boolean;
  onClick: () => void;
}

const DropdownButton = memo(function DropdownButton({
  selectedCategoryData,
  isOpen,
  onClick,
}: DropdownButtonProps) {
  return (
    <button
      onClick={onClick}
      className="w-full flex items-center justify-between p-2 bg-white rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
      aria-expanded={isOpen}
      aria-haspopup="listbox"
    >
      <div className="flex items-center gap-2">
        {selectedCategoryData && (
          <span className="text-xl" aria-hidden="true">
            {selectedCategoryData.icon}
          </span>
        )}
        <span
          className={`text-2xl p-1 font-medium text-gray-700 ${
            selectedCategoryData ? "ml-2" : ""
          }`}
        >
          {selectedCategoryData ? selectedCategoryData.name : "All Categories"}
        </span>
      </div>
      <Icon
        icon="lucide:chevron-down"
        className={`h-5 w-5 text-gray-500 transition-transform ${
          isOpen ? "rotate-180" : ""
        }`}
        aria-hidden="true"
      />
    </button>
  );
});

// Main Component
const AllCategoriesDropdown = memo(function AllCategoriesDropdown({
  onStateChange,
}: AllCategoriesDropdownProps) {
  const {
    categories,
    selectedCategoryData,
    isOpen,
    expandedCategories,
    handleToggleDropdown,
    handleCategorySelect,
    handleToggleExpand,
  } = useAllCategoriesDropdown();

  // Notify parent component of state changes
  React.useEffect(() => {
    if (onStateChange) {
      onStateChange({
        isOpen,
        categories,
        expandedCategories,
        handleCategorySelect,
        handleToggleExpand,
      });
    }
  }, [
    isOpen,
    categories,
    expandedCategories,
    handleCategorySelect,
    handleToggleExpand,
    onStateChange,
  ]);

  return (
    <>
      <DropdownButton
        selectedCategoryData={selectedCategoryData}
        isOpen={isOpen}
        onClick={handleToggleDropdown}
      />

      {/* Dropdown Content - Hidden since categories are now shown in sidebar */}
      {/* Categories are displayed in the sidebar when dropdown is open */}
    </>
  );
});

export default AllCategoriesDropdown;
