# E-commerce App Deployment Guide

This guide covers the Docker and CI/CD setup for the e-commerce Next.js application.

## 🐳 Docker Setup

### Files Created
- `Dockerfile` - Multi-stage Docker build configuration
- `.dockerignore` - Optimizes Docker build by excluding unnecessary files
- `src/app/api/health/route.ts` - Health check endpoint for container monitoring

### Docker Features
- **Multi-stage build** for optimized production images
- **Health checks** for container monitoring
- **Persistent uploads** directory for user-uploaded content
- **Production optimizations** with dependency pruning

### Local Docker Usage

```bash
# Build the Docker image
docker build -t ecom-app .

# Run the container locally
docker run -p 3000:3000 \
  -e MONGODB_URI="your_mongodb_uri" \
  -e JWT_SECRET="your_jwt_secret" \
  -e NEXTAUTH_SECRET="your_nextauth_secret" \
  -e NEXTAUTH_URL="http://localhost:3000" \
  ecom-app

# Run with persistent uploads
docker run -p 3000:3000 \
  -v $(pwd)/uploads:/app/public/uploads \
  -e MONGODB_URI="your_mongodb_uri" \
  ecom-app
```

## 🚀 CI/CD Pipeline

### GitHub Actions Workflow
- **File**: `.github/workflows/deploy.yml`
- **Trigger**: Push to `main` branch
- **Registry**: GitHub Container Registry (GHCR)
- **Deployment**: Automated VPS deployment via SSH

### Required GitHub Secrets

Set these secrets in your GitHub repository settings:

```
SSH_PRIVATE_KEY     # SSH private key for VPS access
VPS_USER           # VPS username
VPS_HOST           # VPS IP address or hostname
MONGODB_URI        # MongoDB connection string
JWT_SECRET         # JWT signing secret
NEXTAUTH_SECRET    # NextAuth.js secret
NEXTAUTH_URL       # Production URL (e.g., https://yourdomain.com)
```

### Pipeline Steps
1. **Checkout** repository code
2. **Build** Docker image with caching
3. **Push** to GitHub Container Registry
4. **Deploy** to VPS via SSH
5. **Health check** verification

## 🖥️ VPS Deployment

### Prerequisites
- VPS with Docker installed
- SSH access configured
- GitHub Container Registry access

### Deployment Process
The CI/CD pipeline automatically:
1. Pulls the latest Docker image
2. Stops and removes the old container
3. Creates persistent uploads directory
4. Starts new container with environment variables
5. Sets proper permissions for uploads

### Manual VPS Deployment

```bash
# Login to GHCR
echo $GITHUB_TOKEN | docker login ghcr.io -u $GITHUB_USERNAME --password-stdin

# Pull latest image
docker pull ghcr.io/yourusername/ecom:latest

# Stop existing container
docker stop ecom || true
docker rm ecom || true

# Run new container
docker run -d \
  --name ecom \
  --restart unless-stopped \
  -p 3000:3000 \
  -v /home/<USER>/ecom-uploads:/app/public/uploads \
  -e MONGODB_URI="your_mongodb_uri" \
  -e JWT_SECRET="your_jwt_secret" \
  -e NEXTAUTH_SECRET="your_nextauth_secret" \
  -e NEXTAUTH_URL="https://yourdomain.com" \
  ghcr.io/yourusername/ecom:latest
```

## 🔧 Configuration

### Environment Variables
- `MONGODB_URI` - Database connection string
- `JWT_SECRET` - JWT token signing secret
- `NEXTAUTH_SECRET` - NextAuth.js encryption secret
- `NEXTAUTH_URL` - Application base URL
- `NODE_ENV` - Environment (production/development)

### Port Configuration
- **Container Port**: 3000
- **Host Port**: Configurable (default: 3000)
- **Health Check**: `GET /api/health`

### Volume Mounts
- **Uploads**: `/app/public/uploads` - For user-uploaded content
- **Logs**: Optional log directory mounting

## 📊 Monitoring

### Health Check Endpoint
- **URL**: `/api/health`
- **Method**: GET
- **Response**: JSON with status, timestamp, uptime

### Container Monitoring
```bash
# Check container status
docker ps | grep ecom

# View container logs
docker logs ecom

# Monitor resource usage
docker stats ecom
```

## 🔄 Updates and Rollbacks

### Automatic Updates
- Push to `main` branch triggers automatic deployment
- Zero-downtime deployment with container replacement

### Manual Rollback
```bash
# Pull specific version
docker pull ghcr.io/yourusername/ecom:commit-hash

# Deploy specific version
docker run -d --name ecom-rollback \
  -p 3001:3000 \
  ghcr.io/yourusername/ecom:commit-hash
```

## 🛠️ Troubleshooting

### Common Issues
1. **Build failures**: Check Node.js version compatibility
2. **Permission errors**: Verify uploads directory permissions
3. **Environment variables**: Ensure all required secrets are set
4. **Network issues**: Check VPS firewall and port configuration

### Debug Commands
```bash
# Check container logs
docker logs ecom --tail 100

# Execute shell in container
docker exec -it ecom /bin/bash

# Test health endpoint
curl http://localhost:3000/api/health
```

## 📝 Notes

- The deployment uses GitHub Container Registry (free for public repos)
- Uploads are persisted using Docker volumes
- Health checks ensure container reliability
- Multi-stage builds optimize image size
- Production dependencies only in final image
