/**
 * Verification script to check if the profile API fix is working
 * Run this in browser console after the fix
 */

console.log('🔍 Verifying Profile API Fix');
console.log('============================');

// Check 1: Authentication status
function checkAuthStatus() {
  console.log('\n1. Authentication Status:');
  const authToken = localStorage.getItem('auth_token');
  console.log('   Auth Token:', authToken ? '✅ Present' : '❌ Missing');
  
  if (authToken) {
    try {
      const payload = JSON.parse(atob(authToken.split('.')[1]));
      const now = Date.now() / 1000;
      console.log('   Token Valid:', payload.exp > now ? '✅ Yes' : '❌ Expired');
    } catch (error) {
      console.log('   Token Valid:', '❌ Invalid format');
    }
  }
  
  return !!authToken;
}

// Check 2: Network requests monitoring
function monitorNetworkRequests() {
  console.log('\n2. Monitoring Network Requests:');
  
  // Override fetch to monitor API calls
  const originalFetch = window.fetch;
  let apiCallCount = 0;
  let profileApiCalls = [];
  
  window.fetch = function(...args) {
    const url = args[0];
    if (typeof url === 'string' && url.includes('/api/v1/')) {
      apiCallCount++;
      console.log(`   API Call ${apiCallCount}:`, url);
      
      if (url.includes('/profile')) {
        profileApiCalls.push({
          url,
          timestamp: new Date().toISOString(),
          hasAuth: args[1]?.headers?.Authorization || args[1]?.headers?.authorization
        });
      }
    }
    
    return originalFetch.apply(this, args);
  };
  
  // Restore original fetch after 10 seconds
  setTimeout(() => {
    window.fetch = originalFetch;
    console.log('\n📊 Profile API Calls Summary:');
    console.log('   Total Profile Calls:', profileApiCalls.length);
    profileApiCalls.forEach((call, index) => {
      console.log(`   ${index + 1}. ${call.url}`);
      console.log(`      Auth: ${call.hasAuth ? '✅ Yes' : '❌ No'}`);
      console.log(`      Time: ${call.timestamp}`);
    });
  }, 10000);
  
  console.log('   ⏱️ Monitoring for 10 seconds...');
}

// Check 3: Redux store state
function checkReduxState() {
  console.log('\n3. Redux Store State:');
  
  try {
    // Try to access Redux store through window (if dev tools are enabled)
    if (window.__REDUX_DEVTOOLS_EXTENSION__) {
      console.log('   Redux DevTools:', '✅ Available');
    }
    
    // Check if we can access the store state
    const storeElement = document.querySelector('[data-redux-store]');
    if (storeElement) {
      console.log('   Store Element:', '✅ Found');
    } else {
      console.log('   Store Element:', '❌ Not found');
    }
    
    console.log('   💡 Check Redux DevTools for detailed state inspection');
  } catch (error) {
    console.log('   Redux State:', '❌ Error accessing');
  }
}

// Check 4: Console errors
function checkConsoleErrors() {
  console.log('\n4. Console Error Monitoring:');
  
  const originalError = console.error;
  const originalWarn = console.warn;
  let errorCount = 0;
  let profileErrors = [];
  
  console.error = function(...args) {
    const message = args.join(' ');
    if (message.includes('profile') || message.includes('Cannot GET') || message.includes('/api/v1/')) {
      errorCount++;
      profileErrors.push({
        message,
        timestamp: new Date().toISOString()
      });
    }
    originalError.apply(this, args);
  };
  
  console.warn = function(...args) {
    const message = args.join(' ');
    if (message.includes('profile') || message.includes('API')) {
      console.log(`   ⚠️ Warning: ${message}`);
    }
    originalWarn.apply(this, args);
  };
  
  // Restore original console methods after 10 seconds
  setTimeout(() => {
    console.error = originalError;
    console.warn = originalWarn;
    
    console.log('\n🚨 Profile-related Errors:');
    if (profileErrors.length === 0) {
      console.log('   ✅ No profile-related errors detected!');
    } else {
      profileErrors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.message}`);
        console.log(`      Time: ${error.timestamp}`);
      });
    }
  }, 10000);
  
  console.log('   ⏱️ Monitoring errors for 10 seconds...');
}

// Check 5: Page navigation test
function testPageNavigation() {
  console.log('\n5. Page Navigation Test:');
  console.log('   💡 Try navigating to these pages to test:');
  console.log('   - /profile/settings (should require auth)');
  console.log('   - /profile (should require auth)');
  console.log('   - /profile/testuser (public profile)');
  console.log('   - / (home page)');
}

// Main verification function
function runVerification() {
  const isAuthenticated = checkAuthStatus();
  monitorNetworkRequests();
  checkReduxState();
  checkConsoleErrors();
  testPageNavigation();
  
  console.log('\n📋 Verification Summary:');
  console.log('- Authentication:', isAuthenticated ? '✅ Logged in' : '⚠️ Not logged in');
  console.log('- Monitoring:', '🔄 Active for 10 seconds');
  console.log('- Expected Result:', isAuthenticated ? 
    'Profile API should work without errors' : 
    'No automatic profile API calls should occur');
  
  console.log('\n🎯 Success Criteria:');
  console.log('1. No "Cannot GET /api/v1/user/profile" errors');
  console.log('2. Profile API only called when authenticated');
  console.log('3. Pages load without authentication errors');
  console.log('4. Redux store updates correctly');
}

// Export for manual use
window.verifyProfileFix = {
  checkAuthStatus,
  monitorNetworkRequests,
  checkReduxState,
  checkConsoleErrors,
  testPageNavigation,
  runVerification
};

console.log('\n🚀 Run verifyProfileFix.runVerification() to start verification');

// Auto-run verification
runVerification();
