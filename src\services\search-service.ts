import { apiClient, apiRequest, API_ENDPOINTS } from "@/lib/api";
import { PAGINATION_CONFIG } from "@/constants/api-constants";
import {
  AdvertisementResponseDto,
  PaginatedAdvertisementResponseDto,
  QueryAdvertisementDto,
} from "./advertisements-service";
import { User } from "@/types/auth";

/**
 * Search-related interfaces
 */
export interface GlobalSearchResultDto {
  advertisements: AdvertisementResponseDto[];
  users: User[];
  categories: any[];
  totalResults: number;
}

export interface SearchSuggestionDto {
  text: string;
  type: "query" | "category" | "location" | "user";
  count?: number;
  metadata?: any;
}

export interface LocationSearchParams {
  latitude: number;
  longitude: number;
  radius: number; // in kilometers
  query?: QueryAdvertisementDto;
}

export interface AdvancedSearchParams
  extends Omit<QueryAdvertisementDto, "sortBy"> {
  // Text search
  query?: string;
  searchInTitle?: boolean;
  searchInDescription?: boolean;

  // Location search
  latitude?: number;
  longitude?: number;
  radius?: number;

  // Date filters
  dateFrom?: string;
  dateTo?: string;

  // Advanced filters
  hasImages?: boolean;
  hasVideo?: boolean;
  isNegotiable?: boolean;

  // Seller filters
  verifiedSellersOnly?: boolean;
  businessSellersOnly?: boolean;

  // Sorting and pagination - extended sortBy options
  sortBy?:
    | "relevance"
    | "date"
    | "price"
    | "distance"
    | "popularity"
    | "createdAt"
    | "title"
    | "views";
  sortOrder?: "ASC" | "DESC";
  page?: number;
  limit?: number;
}

export interface SearchFilters {
  categories: string[];
  subcategories: string[];
  conditions: string[];
  priceRanges: Array<{ min: number; max: number; label: string }>;
  locations: string[];
  dateRanges: Array<{ value: string; label: string }>;
}

/**
 * Search Service
 * Handles all search-related API calls including Elasticsearch integration
 */
export class SearchService {
  /**
   * Global search across all content types
   */
  static async globalSearch(
    query: string,
    limit: number = 20
  ): Promise<GlobalSearchResultDto> {
    const params = { q: query, limit };

    return await apiRequest<GlobalSearchResultDto>(() =>
      apiClient.get(API_ENDPOINTS.SEARCH.GLOBAL, { params })
    );
  }

  /**
   * Search advertisements with advanced filtering
   */
  static async searchAdvertisements(
    params: AdvancedSearchParams
  ): Promise<PaginatedAdvertisementResponseDto> {
    const searchParams = {
      page: PAGINATION_CONFIG.DEFAULT_PAGE,
      limit: PAGINATION_CONFIG.DEFAULT_LIMIT,
      ...params,
    };

    return await apiRequest<PaginatedAdvertisementResponseDto>(() =>
      apiClient.get(API_ENDPOINTS.SEARCH.ADVERTISEMENTS, {
        params: searchParams,
      })
    );
  }

  /**
   * Search users
   */
  static async searchUsers(query: string, limit: number = 10): Promise<User[]> {
    const params = { q: query, limit };

    return await apiRequest<User[]>(() =>
      apiClient.get(API_ENDPOINTS.SEARCH.USERS, { params })
    );
  }

  /**
   * Get search suggestions
   */
  static async getSearchSuggestions(
    query: string,
    limit: number = 10
  ): Promise<SearchSuggestionDto[]> {
    const params = { q: query, limit };

    return await apiRequest<SearchSuggestionDto[]>(() =>
      apiClient.get(API_ENDPOINTS.SEARCH.SUGGESTIONS, { params })
    );
  }

  /**
   * Location-based search for advertisements
   */
  static async searchByLocation(
    params: LocationSearchParams
  ): Promise<PaginatedAdvertisementResponseDto> {
    const searchParams = {
      latitude: params.latitude,
      longitude: params.longitude,
      radius: params.radius,
      page: PAGINATION_CONFIG.DEFAULT_PAGE,
      limit: PAGINATION_CONFIG.DEFAULT_LIMIT,
      ...params.query,
    };

    return await apiRequest<PaginatedAdvertisementResponseDto>(() =>
      apiClient.get(API_ENDPOINTS.LOCATION_SEARCH, { params: searchParams })
    );
  }

  /**
   * Get available search filters
   */
  static async getSearchFilters(): Promise<SearchFilters> {
    // This would typically come from a dedicated endpoint
    // For now, we'll construct it from available data
    try {
      // You could call multiple endpoints to get filter options
      return {
        categories: [],
        subcategories: [],
        conditions: ["new", "used", "refurbished"],
        priceRanges: [
          { min: 0, max: 1000, label: "Under NPR 1,000" },
          { min: 1000, max: 5000, label: "NPR 1,000 - 5,000" },
          { min: 5000, max: 10000, label: "NPR 5,000 - 10,000" },
          { min: 10000, max: 50000, label: "NPR 10,000 - 50,000" },
          { min: 50000, max: -1, label: "Above NPR 50,000" },
        ],
        locations: [],
        dateRanges: [
          { value: "1d", label: "Last 24 hours" },
          { value: "7d", label: "Last 7 days" },
          { value: "30d", label: "Last 30 days" },
          { value: "90d", label: "Last 3 months" },
        ],
      };
    } catch (error) {
      return {
        categories: [],
        subcategories: [],
        conditions: [],
        priceRanges: [],
        locations: [],
        dateRanges: [],
      };
    }
  }

  /**
   * Build search query from filters
   */
  static buildSearchQuery(
    filters: Partial<AdvancedSearchParams>
  ): QueryAdvertisementDto {
    const query: QueryAdvertisementDto = {};

    // Basic filters
    if (filters.query) query.search = filters.query;
    if (filters.categoryId) query.categoryId = filters.categoryId;
    if (filters.subcategoryId) query.subcategoryId = filters.subcategoryId;
    if (filters.condition) query.condition = filters.condition;
    if (filters.minPrice !== undefined) query.minPrice = filters.minPrice;
    if (filters.maxPrice !== undefined) query.maxPrice = filters.maxPrice;
    if (filters.location) query.location = filters.location;
    if (filters.city) query.city = filters.city;
    if (filters.state) query.state = filters.state;
    if (filters.status) query.status = filters.status;
    if (filters.isFeatured !== undefined) query.isFeatured = filters.isFeatured;
    if (filters.isUrgent !== undefined) query.isUrgent = filters.isUrgent;

    // Sorting
    if (filters.sortBy) {
      switch (filters.sortBy) {
        case "date":
          query.sortBy = "createdAt";
          break;
        case "price":
          query.sortBy = "price";
          break;
        case "popularity":
          query.sortBy = "views";
          break;
        default:
          query.sortBy = "createdAt";
      }
    }
    if (filters.sortOrder) query.sortOrder = filters.sortOrder;

    // Pagination
    if (filters.page) query.page = filters.page;
    if (filters.limit) query.limit = filters.limit;

    return query;
  }

  /**
   * Parse search query string into structured filters
   */
  static parseSearchQuery(queryString: string): Partial<AdvancedSearchParams> {
    const filters: Partial<AdvancedSearchParams> = {};

    // Simple parsing - in a real app, you might use a more sophisticated parser
    const parts = queryString.split(" ");
    const queryParts: string[] = [];

    parts.forEach((part) => {
      if (part.startsWith("category:")) {
        // Handle category filter
        const category = part.replace("category:", "");
        filters.categoryId = category;
      } else if (part.startsWith("price:")) {
        // Handle price filter (e.g., price:100-500)
        const priceRange = part.replace("price:", "");
        const [min, max] = priceRange.split("-").map(Number);
        if (!isNaN(min)) filters.minPrice = min;
        if (!isNaN(max)) filters.maxPrice = max;
      } else if (part.startsWith("condition:")) {
        // Handle condition filter
        const condition = part.replace("condition:", "") as any;
        if (["new", "used", "refurbished"].includes(condition)) {
          filters.condition = condition;
        }
      } else if (part.startsWith("location:")) {
        // Handle location filter
        filters.location = part.replace("location:", "");
      } else {
        // Regular search term
        queryParts.push(part);
      }
    });

    if (queryParts.length > 0) {
      filters.query = queryParts.join(" ");
    }

    return filters;
  }

  /**
   * Get popular search terms
   */
  static async getPopularSearchTerms(limit: number = 10): Promise<string[]> {
    try {
      // This would typically come from analytics
      return [
        "mobile phone",
        "laptop",
        "car",
        "bike",
        "furniture",
        "electronics",
        "clothes",
        "books",
        "camera",
        "watch",
      ].slice(0, limit);
    } catch (error) {
      return [];
    }
  }

  /**
   * Get trending searches
   */
  static async getTrendingSearches(
    limit: number = 5
  ): Promise<SearchSuggestionDto[]> {
    try {
      // This would typically come from analytics
      return [
        { text: "iPhone 15", type: "query" as const, count: 150 },
        { text: "Gaming laptop", type: "query" as const, count: 120 },
        { text: "Electric bike", type: "query" as const, count: 95 },
        { text: "Office chair", type: "query" as const, count: 80 },
        { text: "DSLR camera", type: "query" as const, count: 65 },
      ].slice(0, limit);
    } catch (error) {
      return [];
    }
  }

  /**
   * Save search query (for analytics)
   */
  static async saveSearchQuery(
    query: string,
    filters?: Partial<AdvancedSearchParams>
  ): Promise<void> {
    try {
      // This would typically save to analytics service
      console.log("Search query saved:", { query, filters });
    } catch (error) {
      // Fail silently for analytics
      console.warn("Failed to save search query:", error);
    }
  }

  /**
   * Get search history for user
   */
  static getSearchHistory(): string[] {
    try {
      const history = localStorage.getItem("search_history");
      return history ? JSON.parse(history) : [];
    } catch (error) {
      return [];
    }
  }

  /**
   * Add search query to history
   */
  static addToSearchHistory(query: string): void {
    try {
      const history = this.getSearchHistory();
      const updatedHistory = [
        query,
        ...history.filter((h) => h !== query),
      ].slice(0, 10);
      localStorage.setItem("search_history", JSON.stringify(updatedHistory));
    } catch (error) {
      console.warn("Failed to save search history:", error);
    }
  }

  /**
   * Clear search history
   */
  static clearSearchHistory(): void {
    try {
      localStorage.removeItem("search_history");
    } catch (error) {
      console.warn("Failed to clear search history:", error);
    }
  }
}

export default SearchService;
