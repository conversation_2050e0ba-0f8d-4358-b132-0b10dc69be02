"use client";

import { useState, memo } from "react";
import { Icon } from "@iconify/react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

import { BackButton } from "@/components/ui/back-button";
import { useModal } from "@/store/compatibility";
import { useViewTracking } from "@/hooks/use-view-tracking";
import { JobApplyForm } from "@/components/dynamic";
import { cn } from "@/lib/utils";
import { useTrackProductViewMutation } from "@/store/api/productDetailsApi";

// Import existing detail components
import { SellerInfo } from "./detail/seller-info";
import { Purchase } from "./detail/purchase";
import { SafetyTips } from "./detail/safety-tips";
import { SimilarListings } from "./detail/similar-listings";
import { ContactSeller } from "./detail/contact-seller";
import ReviewsSection from "./detail/reviews-section";
import { DescriptionTab } from "./detail/tabs/description-tab";
import { SpecificationsTab } from "./detail/tabs/specifications-tab";
import { FeaturesTab } from "./detail/tabs/features-tab";
import { LocationsTab } from "./detail/tabs/locations-tab";
import { ProductQA } from "./detail/product-qa";
import { SellerVerification } from "./detail/seller-verification";
import { ImageGallery } from "./detail/image-gallery";

import type {
  ProductDetailProps,
  JobApplicationData,
  ProductDetailConfig,
} from "./types";
import {
  isJobProduct,
  generateJobData,
  formatPrice,
  transformProductToCardData,
} from "./utils";

const defaultConfig: ProductDetailConfig = {
  showSimilarProducts: true,
  showReviews: true,
  showQA: true,
  showSellerVerification: true,
  showSafetyTips: true,
  showSharing: true,
  showWishlist: true,
  showPurchaseOptions: true,
  tabsEnabled: true,
};

const BaseProductDetailView = memo(function BaseProductDetailView({
  product,
  config: configOverride,
  similarProducts = [],
  className = "",
  onBack,
  onContactSeller,
  onPurchase: _onPurchase,
  onAddToCart: _onAddToCart,
}: ProductDetailProps) {
  const config = { ...defaultConfig, ...configOverride };
  const [showContactSeller, setShowContactSeller] = useState(false);

  const { openModal, closeModal } = useModal();
  const [trackView] = useTrackProductViewMutation();

  const isJob = isJobProduct(product);
  const cardData = transformProductToCardData(product);
  const jobData = isJob ? generateJobData(product) : null;

  // Simple view tracking (no API needed)
  useViewTracking({
    productId: product.id,
    sellerId: product.seller.id,
    onViewTracked: (productId) => {
      // Simple console log or localStorage tracking
      console.log(`View tracked for product: ${productId}`);
      // Optional: Store in localStorage for analytics
      const views = JSON.parse(localStorage.getItem("productViews") || "{}");
      views[productId] = (views[productId] || 0) + 1;
      localStorage.setItem("productViews", JSON.stringify(views));
    },
  });

  const handleContactSeller = () => {
    setShowContactSeller(true);
    onContactSeller?.(product);
  };

  const handleJobApplication = () => {
    if (!isJob) return;

    openModal(
      <JobApplyForm
        jobTitle={product.title}
        companyName={product.seller.name}
        onSubmit={handleApplicationSubmit}
        onCancel={closeModal}
      />,
      {
        className: "max-w-6xl w-[95vw] max-h-[95vh]",
      }
    );
  };

  const handleApplicationSubmit = (applicationData: JobApplicationData) => {
    console.log("Job application submitted:", applicationData);
    closeModal();
    alert("Application submitted successfully!");
  };

  // Job-specific rendering
  if (isJob && jobData) {
    return (
      <div className={cn("w-full top-0 px-4 pb-8", className)}>
        {/* Back Button */}
        {onBack && (
          <div className="flex items-center gap-4 mb-6">
            <BackButton onClick={onBack} size="lg" />
          </div>
        )}

        {/* Job Header Card */}
        <Card className="mb-6">
          <CardHeader className="pb-4">
            <div className="flex items-start justify-between gap-4">
              <div className="flex items-start gap-4 flex-1">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Icon
                    icon="lucide:building-2"
                    className="w-8 h-8 text-white"
                  />
                </div>
                <div className="flex-1">
                  <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                    {product.title}
                  </h1>
                  <p className="text-lg text-gray-600 mb-3">
                    {product.seller.name}
                  </p>
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Icon icon="lucide:map-pin" className="w-4 h-4" />
                      <span>{product.location}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Icon icon="lucide:banknote" className="w-4 h-4" />
                      <span>
                        {formatPrice(product.price, product.currency)}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Icon icon="lucide:users" className="w-4 h-4" />
                      <span>{jobData.applicantCount} applicants</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col gap-2">
                <Badge
                  variant="secondary"
                  className="bg-green-100 text-green-800"
                >
                  {jobData.jobType}
                </Badge>
                <Badge variant="outline">{jobData.workMode}</Badge>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Job Details Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <Card>
              <CardContent className="p-6">
                <div className="space-y-6">
                  {/* Job Info Grid */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div className="flex items-center gap-2">
                      <Icon
                        icon="mdi:briefcase"
                        className="w-5 h-5 text-gray-500"
                      />
                      <div>
                        <p className="text-sm text-gray-500">
                          Experience Level:
                        </p>
                        <p className="font-medium">{jobData.experienceLevel}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Icon
                        icon="mdi:clock-outline"
                        className="w-5 h-5 text-gray-500"
                      />
                      <div>
                        <p className="text-sm text-gray-500">Posted:</p>
                        <p className="font-medium">{cardData.timeAgo}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Icon
                        icon="mdi:calendar"
                        className="w-5 h-5 text-gray-500"
                      />
                      <div>
                        <p className="text-sm text-gray-500">Deadline:</p>
                        <p className="font-medium">
                          {
                            new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                              .toISOString()
                              .split("T")[0]
                          }
                        </p>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Description Section */}
                  <div>
                    <h2 className="text-xl font-semibold mb-3">
                      Job Description
                    </h2>
                    <p className="text-gray-700 leading-relaxed mb-6">
                      {product.description}
                    </p>

                    <h4 className="text-lg font-semibold mb-3">
                      Key Features:
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-6">
                      {[
                        "Flexible working hours",
                        "Remote work options",
                        "Health insurance",
                        "Professional development",
                        "Team collaboration",
                        "Modern tech stack",
                        "Career growth",
                        "Competitive salary",
                      ].map((feature, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-2 text-gray-700"
                        >
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                          <span className="text-base">{feature}</span>
                        </div>
                      ))}
                    </div>

                    <div className="bg-gray-50 rounded-lg p-4 mb-6">
                      <p className="text-gray-700 text-base leading-relaxed">
                        We are looking for a dedicated professional to join our
                        growing team. This position offers excellent growth
                        opportunities and a collaborative work environment.
                      </p>
                    </div>
                  </div>

                  <Separator />

                  {/* Specifications Section */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">
                      Job Specifications
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                      {[
                        { label: "Company", value: product.seller.name },
                        { label: "Position", value: product.title },
                        {
                          label: "Experience Level",
                          value: jobData.experienceLevel,
                        },
                        { label: "Job Type", value: jobData.jobType },
                        { label: "Work Mode", value: jobData.workMode },
                        {
                          label: "Salary",
                          value: formatPrice(product.price, product.currency),
                        },
                        { label: "Location", value: product.location },
                        {
                          label: "Applicants",
                          value: `${jobData.applicantCount} applied`,
                        },
                      ].map((spec, index) => (
                        <div
                          key={index}
                          className="flex justify-between items-center py-3 px-4 bg-gray-100 rounded-lg"
                        >
                          <span className="text-gray-600 font-medium">
                            {spec.label}:
                          </span>
                          <span className="text-gray-900 font-semibold capitalize">
                            {spec.value}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  {/* Features Section */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">
                      Benefits & Features
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-6">
                      {[
                        { name: "Health Insurance", available: true },
                        { name: "Flexible Hours", available: true },
                        { name: "Remote Work", available: true },
                        { name: "Professional Development", available: true },
                        { name: "Paid Time Off", available: true },
                        { name: "Team Events", available: true },
                        { name: "Stock Options", available: false },
                        { name: "Gym Membership", available: false },
                        { name: "Lunch Provided", available: true },
                        { name: "Transportation", available: false },
                      ].map((feature, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-3 p-3 rounded-lg border"
                        >
                          {feature.available ? (
                            <Icon
                              icon="lucide:check-circle"
                              className="h-5 w-5 text-green-500 flex-shrink-0"
                            />
                          ) : (
                            <Icon
                              icon="lucide:x"
                              className="h-5 w-5 text-gray-400 flex-shrink-0"
                            />
                          )}
                          <span
                            className={`${
                              feature.available
                                ? "text-gray-900"
                                : "text-gray-500"
                            }`}
                          >
                            {feature.name}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  {/* Requirements */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">
                      Key Requirements
                    </h3>
                    <div className="flex flex-wrap gap-2 mb-6">
                      {jobData.requirements.map((skill, index) => (
                        <Badge key={index} variant="outline">
                          {skill}
                        </Badge>
                      ))}
                      <Badge variant="outline">3+ years exp</Badge>
                    </div>
                  </div>

                  <Separator />

                  {/* Location Section */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">
                      Location Details
                    </h3>
                    <div className="flex items-center gap-2 mb-4">
                      <Icon
                        icon="lucide:map-pin"
                        className="h-5 w-5 text-blue-500"
                      />
                      <span className="text-gray-900 font-medium">
                        {product.location}
                      </span>
                    </div>

                    {/* Map Placeholder */}
                    <div className="aspect-[3/1] bg-gray-100 rounded-lg overflow-hidden relative">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center">
                          <Icon
                            icon="lucide:map-pin"
                            className="h-12 w-12 text-gray-400 mx-auto mb-2"
                          />
                          <p className="text-gray-500">
                            Interactive map would be displayed here
                          </p>
                          <p className="text-sm text-gray-400 mt-1">
                            Showing approximate location for privacy
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Apply Button */}
            <Card>
              <CardContent className="p-6">
                <Button
                  onClick={handleJobApplication}
                  className="w-full bg-teal-600 hover:bg-teal-700 text-white"
                  size="lg"
                >
                  <Icon icon="lucide:send" className="w-4 h-4 mr-2" />
                  Apply Now
                </Button>
              </CardContent>
            </Card>

            {/* Seller Info */}
            <SellerInfo
              product={product}
              onContactSeller={handleContactSeller}
            />

            {/* Safety Tips */}
            {config.showSafetyTips && <SafetyTips />}
          </div>
        </div>

        {/* Similar Jobs */}
        {config.showSimilarProducts && (
          <div className="mt-12">
            <SimilarListings
              productId={product.id}
              similarProducts={similarProducts}
            />
          </div>
        )}
      </div>
    );
  }

  // Regular product rendering (existing ProductDetailView logic)
  return (
    <div className={cn("w-full top-0 px-4 pb-8", className)}>
      {/* Back Button */}
      {onBack && (
        <div className="flex items-center gap-4 mb-6">
          <BackButton onClick={onBack} size="lg" />
        </div>
      )}

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Images and Details */}
        <div className="lg:col-span-2 h-fit">
          {/* Product Title and Meta Info + Image Gallery */}
          <div className="bg-white rounded-lg p-6 shadow-sm mb-6">
            {/* Product Title and Meta Info */}
            <div className="mb-6">
              <h1 className="mb-4 font-medium text-2xl leading-normal text-black">
                {product.title}
              </h1>

              {/* Meta Information */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-1">
                  <Icon
                    icon="lucide:calendar"
                    className="w-5 h-5 text-[#478085]"
                  />
                  <span className="text-[#478085] text-lg font-medium leading-normal">
                    Posted {product.postedDate || cardData.timeAgo}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Icon icon="lucide:eye" className="w-5 h-5 text-[#478085]" />
                  <span className="text-[#478085] text-lg font-medium leading-normal">
                    {product.views || 0} views
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3 mb-4">
                <span className="text-2xl font-bold text-gray-900">
                  {formatPrice(product.price, product.currency)}
                </span>
                {product.originalPrice && (
                  <span className="text-lg text-gray-500 line-through">
                    {formatPrice(product.originalPrice, product.currency)}
                  </span>
                )}
              </div>

              {/* Location and Condition */}
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center gap-1">
                  <Icon
                    icon="lucide:map-pin"
                    className="w-5 h-5 text-gray-500"
                  />
                  <span className="text-gray-700">{product.location}</span>
                </div>
                <Badge
                  variant="secondary"
                  className="bg-green-100 text-green-800"
                >
                  {product.condition}
                </Badge>
              </div>
            </div>

            {/* Image Gallery */}
            <ImageGallery images={product.images} title={product.title} />
          </div>

          {/* Product Details Sections - Sequential Layout */}
          {config.tabsEnabled && (
            <div className="space-y-6">
              {/* Description Section */}
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <DescriptionTab product={product} />
              </div>

              {/* Specifications Section */}
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <SpecificationsTab product={product} />
              </div>

              {/* Features Section */}
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <FeaturesTab product={product} />
              </div>

              {/* Location Section */}
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <LocationsTab product={product} />
              </div>
            </div>
          )}

          {/* Q&A Section */}
          {config.showQA && (
            <div className="bg-white rounded-lg p-6 shadow-sm mb-6">
              <ProductQA product={product} />
            </div>
          )}
        </div>

        {/* Right Column - Actions and Info */}
        <div className="space-y-6">
          {/* Seller Info */}
          <SellerInfo product={product} onContactSeller={handleContactSeller} />

          {/* Purchase Options */}
          {config.showPurchaseOptions && (
            <Purchase
              basePrice={product.price}
              currency={product.currency}
              product={product}
            />
          )}

          {/* Seller Verification */}
          {config.showSellerVerification && (
            <SellerVerification product={product} />
          )}

          {/* Safety Tips */}
          {config.showSafetyTips && <SafetyTips />}
        </div>
      </div>

      {/* Similar Products */}
      {config.showSimilarProducts && (
        <div className="mt-12">
          <SimilarListings
            productId={product.id}
            similarProducts={similarProducts}
          />
        </div>
      )}

      {/* Reviews Section */}
      {config.showReviews && (
        <div className="mt-12">
          <ReviewsSection productId={product.id} />
        </div>
      )}

      {/* Contact Seller Modal */}
      {showContactSeller && (
        <ContactSeller
          product={product}
          onClose={() => setShowContactSeller(false)}
        />
      )}
    </div>
  );
});

export default BaseProductDetailView;
