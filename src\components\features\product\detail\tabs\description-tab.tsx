import type { Product } from "@/types/ecommerce";

interface DescriptionTabProps {
  product: Product;
}

export function DescriptionTab({ product }: DescriptionTabProps) {
  return (
    <div className="space-y-6 min-h-[478px]">
      <div>
        <h3 className="text-2xl font-semibold text-gray-900 mb-3">
          Description
        </h3>
        <p className="text-gray-700 leading-relaxed mb-6 text-lg">
          {product.description}
        </p>
      </div>

      <div>
        <h4 className="text-xl font-semibold text-gray-900 mb-3">
          Key Features:
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          {[
            "Automatic transmission",
            "Air conditioning",
            "Power steering",
            "Central locking",
            "Electric windows",
            "Music system with Bluetooth",
            "Alloy wheels",
            "ABS brakes",
          ].map((feature, index) => (
            <div key={index} className="flex items-center gap-2 text-gray-700">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              <span className="text-base">{feature}</span>
            </div>
          ))}
        </div>
      </div>

      <div className="bg-gray-50 rounded-lg p-4">
        <p className="text-gray-700 text-base leading-relaxed">
          This car has been garage kept and driven only on weekends. All
          maintenance records available. Serious buyers only please. Contact for
          viewing. Excellent condition both interior and exterior.
        </p>
      </div>
    </div>
  );
}
