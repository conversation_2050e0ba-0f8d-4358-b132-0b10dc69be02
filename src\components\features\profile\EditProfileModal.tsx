"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { FormField } from "./shared/FormField";
import type { UserProfile } from "@/types/ecommerce";

interface EditProfileModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userProfile: UserProfile;
  onSave: (updatedProfile: Partial<UserProfile>) => void;
}

export function EditProfileModal({
  open,
  onOpenChange,
  userProfile,
  onSave,
}: EditProfileModalProps) {
  const [formData, setFormData] = useState({
    username: userProfile.username,
    email: userProfile.email,
    phoneNumber: userProfile.phoneNumber || "",
    bio: userProfile.bio || "",
    city: userProfile.address?.city || "",
    country: userProfile.address?.country || "Nepal",
    // Note: firstName and lastName are not in UserProfile interface,
    // but we can add them for future compatibility
    firstName: "",
    lastName: "",
    address: "",
    state: "",
    postalCode: "",
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = () => {
    // Create updated profile object
    const updatedProfile: Partial<UserProfile> = {
      username: formData.username,
      email: formData.email,
      phoneNumber: formData.phoneNumber,
      bio: formData.bio,
      address: {
        ...userProfile.address,
        city: formData.city,
        country: formData.country,
      },
      updatedAt: new Date().toISOString(),
    };

    onSave(updatedProfile);
    onOpenChange(false);
  };

  const handleCancel = () => {
    // Reset form data to original values
    setFormData({
      username: userProfile.username,
      email: userProfile.email,
      phoneNumber: userProfile.phoneNumber || "",
      bio: userProfile.bio || "",
      city: userProfile.address?.city || "",
      country: userProfile.address?.country || "Nepal",
      firstName: "",
      lastName: "",
      address: "",
      state: "",
      postalCode: "",
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] p-0 flex flex-col">
        <DialogHeader className="px-6 py-4 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-lg font-semibold">
                Edit Profile
              </DialogTitle>
              <p className="text-sm text-gray-500 mt-1">
                Update your basic account information and personal details.
              </p>
            </div>
          </div>
        </DialogHeader>

        <div className="px-6 py-4 space-y-4 overflow-y-auto flex-1">
          <FormField
            id="username"
            label="Username"
            value={formData.username}
            onChange={(value) => handleInputChange("username", value)}
            placeholder="Enter your username"
          />

          <FormField
            id="email"
            label="Email Address"
            type="email"
            value={formData.email}
            onChange={(value) => handleInputChange("email", value)}
            placeholder="Enter your email address"
          />

          <FormField
            id="phoneNumber"
            label="Phone Number"
            type="tel"
            value={formData.phoneNumber}
            onChange={(value) => handleInputChange("phoneNumber", value)}
            placeholder="Enter your phone number"
          />

          <FormField
            id="city"
            label="City"
            value={formData.city}
            onChange={(value) => handleInputChange("city", value)}
            placeholder="Enter your city"
          />

          <FormField
            id="bio"
            label="Bio"
            type="textarea"
            value={formData.bio}
            onChange={(value) => handleInputChange("bio", value)}
            placeholder="Tell us about yourself..."
            rows={4}
          />

          {/* First Name and Last Name */}
          <div className="grid grid-cols-2 gap-4">
            <FormField
              id="firstName"
              label="First Name"
              value={formData.firstName}
              onChange={(value) => handleInputChange("firstName", value)}
              placeholder="Enter your first name"
            />
            <FormField
              id="lastName"
              label="Last Name"
              value={formData.lastName}
              onChange={(value) => handleInputChange("lastName", value)}
              placeholder="Enter your last name"
            />
          </div>

          <FormField
            id="address"
            label="Street Address"
            value={formData.address}
            onChange={(value) => handleInputChange("address", value)}
            placeholder="Enter your street address"
          />

          {/* State and Postal Code */}
          <div className="grid grid-cols-2 gap-4">
            <FormField
              id="state"
              label="State/Province"
              value={formData.state}
              onChange={(value) => handleInputChange("state", value)}
              placeholder="Enter your state"
            />
            <FormField
              id="postalCode"
              label="Postal Code"
              value={formData.postalCode}
              onChange={(value) => handleInputChange("postalCode", value)}
              placeholder="Enter postal code"
            />
          </div>

          <FormField
            id="country"
            label="Country"
            value={formData.country}
            onChange={(value) => handleInputChange("country", value)}
            placeholder="Enter your country"
          />
        </div>

        {/* Footer Buttons */}
        <div className="px-6 py-4 flex justify-end gap-2 border-t border-gray-200 flex-shrink-0">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            className="bg-teal-600 hover:bg-teal-700"
          >
            Save Changes
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
