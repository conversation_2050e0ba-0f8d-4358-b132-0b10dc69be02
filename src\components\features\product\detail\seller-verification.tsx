"use client";

import { Icon } from "@iconify/react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Product } from "@/types/ecommerce";

interface SellerVerificationProps {
  product: Product;
  className?: string;
}

interface VerificationBadge {
  id: string;
  name: string;
  icon: React.ReactNode;
  verified: boolean;
  description: string;
  color: string;
}

interface SellerStats {
  totalSales: number;
  responseRate: number;
  responseTime: string;
  memberSince: string;
  positiveReviews: number;
  totalReviews: number;
  verificationLevel: "basic" | "verified" | "premium";
}

// Mock seller verification data
const getSellerVerification = (_product: Product): SellerStats => {
  // TODO: Use _product parameter to fetch real seller verification data
  return {
    totalSales: 127,
    responseRate: 95,
    responseTime: "Within 2 hours",
    memberSince: "2020",
    positiveReviews: 118,
    totalReviews: 125,
    verificationLevel: "verified",
  };
};

const getVerificationBadges = (stats: SellerStats): VerificationBadge[] => {
  return [
    {
      id: "identity",
      name: "Identity Verified",
      icon: <Icon icon="lucide:check-circle" className="h-4 w-4" />,
      verified: stats.verificationLevel !== "basic",
      description: "Government ID verified",
      color: "text-green-600",
    },
    {
      id: "phone",
      name: "Phone Verified",
      icon: <Icon icon="lucide:phone" className="h-4 w-4" />,
      verified: true,
      description: "Phone number confirmed",
      color: "text-blue-600",
    },
    {
      id: "email",
      name: "Email Verified",
      icon: <Icon icon="lucide:mail" className="h-4 w-4" />,
      verified: true,
      description: "Email address confirmed",
      color: "text-purple-600",
    },
    {
      id: "address",
      name: "Address Verified",
      icon: <Icon icon="lucide:map-pin" className="h-4 w-4" />,
      verified: stats.verificationLevel === "premium",
      description: "Physical address confirmed",
      color: "text-orange-600",
    },
    {
      id: "business",
      name: "Business Verified",
      icon: <Icon icon="lucide:award" className="h-4 w-4" />,
      verified: stats.verificationLevel === "premium",
      description: "Business registration verified",
      color: "text-indigo-600",
    },
  ];
};

export function SellerVerification({
  product,
  className = "",
}: SellerVerificationProps) {
  const stats = getSellerVerification(product);
  const badges = getVerificationBadges(stats);
  const verifiedBadges = badges.filter((badge) => badge.verified);
  const verificationPercentage = (verifiedBadges.length / badges.length) * 100;

  const getVerificationLevelInfo = (level: string) => {
    switch (level) {
      case "premium":
        return {
          name: "Premium Seller",
          color: "bg-gradient-to-r from-yellow-400 to-orange-500",
          textColor: "text-white",
          icon: <Icon icon="lucide:award" className="h-4 w-4" />,
        };
      case "verified":
        return {
          name: "Verified Seller",
          color: "bg-green-100",
          textColor: "text-green-800",
          icon: <Icon icon="lucide:shield" className="h-4 w-4" />,
        };
      default:
        return {
          name: "Basic Seller",
          color: "bg-gray-100",
          textColor: "text-gray-800",
          icon: <Icon icon="lucide:shield" className="h-4 w-4" />,
        };
    }
  };

  const levelInfo = getVerificationLevelInfo(stats.verificationLevel);

  return (
    <div
      className={`bg-white rounded-lg p-4 shadow-sm border border-gray-200 ${className}`}
    >
      <div className="space-y-4">
        {/* Seller Level Badge */}
        <div className="flex items-center justify-between">
          <h3 className="text-3xl font-semibold text-gray-900">
            Seller Verification
          </h3>
          <Badge
            className={`${levelInfo.color} ${levelInfo.textColor} font-medium`}
          >
            {levelInfo.icon}
            <span className="ml-1">{levelInfo.name}</span>
          </Badge>
        </div>

        {/* Verification Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-base">
            <span className="text-gray-600">Verification Progress</span>
            <span className="font-medium text-gray-900">
              {Math.round(verificationPercentage)}%
            </span>
          </div>
          <Progress value={verificationPercentage} className="h-2" />
        </div>

        {/* Verification Badges */}
        <div className="space-y-2">
          <h4 className="text-base font-medium text-gray-900">
            Verification Status
          </h4>
          <div className="grid grid-cols-1 gap-2">
            {badges.map((badge) => (
              <div
                key={badge.id}
                className={`flex items-center gap-2 p-2 rounded-lg border ${
                  badge.verified
                    ? "bg-green-50 border-green-200"
                    : "bg-gray-50 border-gray-200"
                }`}
              >
                <div
                  className={
                    badge.verified ? badge.color : "text-gray-400 mr-4"
                  }
                >
                  {badge.icon}
                </div>
                <div className="flex-1">
                  <span
                    className={`text-base font-medium ${
                      badge.verified ? "text-gray-900" : "text-gray-500"
                    }`}
                  >
                    {badge.name}
                  </span>
                  <p className="text-sm text-gray-600">{badge.description}</p>
                </div>
                {badge.verified && (
                  <Icon
                    icon="lucide:check-circle"
                    className="h-4 w-4 text-green-600"
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Seller Statistics */}
        <div className=" pt-4">
          <h4 className="text-base font-medium text-gray-900 mb-3">
            Seller Statistics
          </h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-xl font-semibold text-gray-900">
                {stats.totalSales}
              </div>
              <div className="text-sm text-gray-600">Total Sales</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-semibold text-gray-900">
                {stats.responseRate}%
              </div>
              <div className="text-sm text-gray-600">Response Rate</div>
            </div>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-base">
            <Icon icon="lucide:clock" className="h-4 w-4 text-gray-500" />
            <span className="text-gray-600">Response time:</span>
            <span className="font-medium text-gray-900">
              {stats.responseTime}
            </span>
          </div>

          <div className="flex items-center gap-2 text-base">
            <Icon icon="lucide:star" className="h-4 w-4 text-yellow-500" />
            <span className="text-gray-600">Reviews:</span>
            <span className="font-medium text-gray-900">
              {stats.positiveReviews}/{stats.totalReviews} positive
            </span>
          </div>

          <div className="flex items-center gap-2 text-base">
            <Icon icon="lucide:shield" className="h-4 w-4 text-gray-500" />
            <span className="text-gray-600">Member since:</span>
            <span className="font-medium text-gray-900">
              {stats.memberSince}
            </span>
          </div>
        </div>

        {/* Trust Score */}
        <div className="bg-blue-50 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-base font-medium text-blue-900">
              Trust Score
            </span>
            <span className="text-xl font-bold text-blue-900">
              {Math.round((stats.responseRate + verificationPercentage) / 2)}%
            </span>
          </div>
          <p className="text-sm text-blue-800">
            Based on verification status, response rate, and seller history
          </p>
        </div>

        {/* Action Button */}
        <Button
          variant="outline"
          size="sm"
          className="w-full text-blue-600 border-blue-200 hover:bg-blue-50"
        >
          View Seller Profile
        </Button>
      </div>
    </div>
  );
}
