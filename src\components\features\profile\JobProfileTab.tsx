"use client";

import React from "react";
import Image from "next/image";
import { Icon } from "@iconify/react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface JobProfileTabProps {
  onEditJobProfile: () => void;
}

export default function JobProfileTab({
  onEditJobProfile,
}: JobProfileTabProps) {
  // Download handlers
  const handleDownloadCV = () => {
    const link = document.createElement("a");
    link.href = "/documents/beest-cv.pdf";
    link.download = "beest_CV.pdf";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDownloadCoverLetter = () => {
    const link = document.createElement("a");
    link.href = "/documents/beest-cover-letter.pdf";
    link.download = "beest_Cover_Letter.pdf";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900">
            Job Profile
          </h2>
          <p className="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">
            Your professional information and career details
          </p>
        </div>
        <Button
          onClick={onEditJobProfile}
          className="bg-teal-600 hover:bg-teal-700 text-white w-full sm:w-auto"
        >
          <Icon icon="lucide:edit-3" className="w-4 h-4 mr-2" />
          Edit Job Profile
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-4 sm:space-y-6">
          {/* Professional Summary */}
          <Card className="border-gray-200 shadow-sm">
            <CardContent className="p-4 sm:p-6">
              <div className="flex flex-col sm:flex-row items-start gap-4 sm:gap-6">
                {/* Profile Image */}
                <div className="flex-shrink-0 mx-auto sm:mx-0">
                  <div className="w-16 h-16 sm:w-20 sm:h-20 rounded-full overflow-hidden bg-gray-100">
                    <Image
                      src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face"
                      alt="Prakash Rai"
                      width={80}
                      height={80}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>

                {/* Basic Info */}
                <div className="flex-1 text-center sm:text-left">
                  <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">
                    Prakash Rai
                  </h3>
                  <p className="text-gray-600 mb-3 sm:mb-4 text-sm sm:text-base">
                    Senior Software Engineer
                  </p>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 text-sm">
                    <div className="flex items-center gap-2 text-gray-600 justify-center sm:justify-start">
                      <Icon
                        icon="lucide:mail"
                        className="w-4 h-4 flex-shrink-0"
                      />
                      <span className="break-all"><EMAIL></span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600 justify-center sm:justify-start">
                      <Icon
                        icon="lucide:phone"
                        className="w-4 h-4 flex-shrink-0"
                      />
                      <span>+977 9876543210</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600 justify-center sm:justify-start">
                      <Icon
                        icon="lucide:map-pin"
                        className="w-4 h-4 flex-shrink-0"
                      />
                      <span>Kathmandu, Nepal</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600 justify-center sm:justify-start">
                      <Icon
                        icon="lucide:clock"
                        className="w-4 h-4 flex-shrink-0"
                      />
                      <span>5+ years experience</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Skills & Expertise */}
          <Card className="border-gray-200 shadow-sm">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center gap-3 mb-4 sm:mb-6">
                <Icon icon="lucide:award" className="w-5 h-5 text-teal-600" />
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900">
                  Skills & Expertise
                </h3>
              </div>

              <div className="space-y-4 sm:space-y-6">
                {/* Technical Skills */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2 sm:mb-3">
                    Technical Skills
                  </h4>
                  <div className="flex flex-wrap gap-1.5 sm:gap-2">
                    {[
                      "React",
                      "Node.js",
                      "TypeScript",
                      "Python",
                      "AWS",
                      "Docker",
                      "MongoDB",
                      "PostgreSQL",
                      "GraphQL",
                      "Next.js",
                    ].map((skill) => (
                      <Badge
                        key={skill}
                        className="bg-blue-100 text-blue-800 border-blue-200 text-xs sm:text-sm"
                      >
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Soft Skills */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2 sm:mb-3">
                    Soft Skills
                  </h4>
                  <div className="flex flex-wrap gap-1.5 sm:gap-2">
                    {[
                      "Leadership",
                      "Team Management",
                      "Problem Solving",
                      "Communication",
                      "Project Management",
                    ].map((skill) => (
                      <Badge
                        key={skill}
                        className="bg-green-100 text-green-800 border-green-200 text-xs sm:text-sm"
                      >
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Experience Level */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2 sm:mb-3">
                    Experience Level
                  </h4>
                  <div className="flex flex-wrap gap-1.5 sm:gap-2">
                    {["Senior Level", "Full Stack", "Team Lead"].map(
                      (level) => (
                        <Badge
                          key={level}
                          className="bg-purple-100 text-purple-800 border-purple-200 text-xs sm:text-sm"
                        >
                          {level}
                        </Badge>
                      )
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Work Experience */}
          <Card className="border-gray-200 shadow-sm">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center gap-3 mb-4 sm:mb-6">
                <Icon
                  icon="lucide:briefcase"
                  className="w-5 h-5 text-teal-600"
                />
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900">
                  Work Experience
                </h3>
              </div>

              <div className="space-y-4 sm:space-y-6">
                <div className="border-l-2 border-teal-200 pl-4 sm:pl-6 relative">
                  <div className="absolute -left-2 top-0 w-4 h-4 bg-teal-500 rounded-full"></div>
                  <div className="mb-2">
                    <h4 className="text-base sm:text-lg font-semibold text-gray-900">
                      Senior Software Engineer
                    </h4>
                    <p className="text-teal-600 font-medium text-sm sm:text-base">
                      Tech Solutions Inc.
                    </p>
                    <p className="text-xs sm:text-sm text-gray-500">
                      2021 - Present
                    </p>
                  </div>
                  <p className="text-gray-700 text-sm sm:text-base leading-relaxed">
                    Leading a team of 5 developers in building scalable web
                    applications using React, Node.js, and cloud technologies.
                    Responsible for architecture decisions and mentoring junior
                    developers.
                  </p>
                </div>

                <div className="border-l-2 border-gray-200 pl-4 sm:pl-6 relative">
                  <div className="absolute -left-2 top-0 w-4 h-4 bg-gray-400 rounded-full"></div>
                  <div className="mb-2">
                    <h4 className="text-base sm:text-lg font-semibold text-gray-900">
                      Full Stack Developer
                    </h4>
                    <p className="text-gray-600 font-medium text-sm sm:text-base">
                      Digital Innovations
                    </p>
                    <p className="text-xs sm:text-sm text-gray-500">
                      2019 - 2021
                    </p>
                  </div>
                  <p className="text-gray-700 text-sm sm:text-base leading-relaxed">
                    Developed and maintained multiple client projects using
                    modern web technologies. Collaborated with design and
                    product teams to deliver high-quality solutions.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-4 sm:space-y-6">
          {/* Documents */}
          <Card className="border-gray-200 shadow-sm">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center gap-3 mb-4">
                <Icon
                  icon="lucide:file-text"
                  className="w-5 h-5 text-teal-600"
                />
                <h3 className="text-base sm:text-lg font-semibold text-gray-900">
                  Documents
                </h3>
              </div>

              <div className="space-y-3">
                <Button
                  onClick={handleDownloadCV}
                  variant="outline"
                  className="w-full justify-start text-sm"
                >
                  <Icon icon="lucide:download" className="w-4 h-4 mr-2" />
                  Download CV
                </Button>
                <Button
                  onClick={handleDownloadCoverLetter}
                  variant="outline"
                  className="w-full justify-start text-sm"
                >
                  <Icon icon="lucide:download" className="w-4 h-4 mr-2" />
                  Download Cover Letter
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Job Preferences */}
          <Card className="border-gray-200 shadow-sm">
            <CardContent className="p-4 sm:p-6">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-4">
                Job Preferences
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="text-xs sm:text-sm font-medium text-gray-500 mb-1 block">
                    Desired Salary
                  </label>
                  <div className="flex items-center gap-2 text-gray-900 text-sm">
                    <Icon
                      icon="lucide:dollar-sign"
                      className="w-4 h-4 flex-shrink-0"
                    />
                    <span>$80,000 - $120,000</span>
                  </div>
                </div>

                <div>
                  <label className="text-xs sm:text-sm font-medium text-gray-500 mb-1 block">
                    Work Type
                  </label>
                  <div className="flex flex-wrap gap-1.5 sm:gap-2">
                    <Badge className="bg-blue-100 text-blue-800 border-blue-200 text-xs sm:text-sm">
                      Remote
                    </Badge>
                    <Badge className="bg-blue-100 text-blue-800 border-blue-200 text-xs sm:text-sm">
                      Hybrid
                    </Badge>
                  </div>
                </div>

                <div>
                  <label className="text-xs sm:text-sm font-medium text-gray-500 mb-1 block">
                    Availability
                  </label>
                  <div className="flex items-center gap-2 text-gray-900 text-sm">
                    <Icon
                      icon="lucide:calendar"
                      className="w-4 h-4 flex-shrink-0"
                    />
                    <span>Available in 2 weeks</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Profile Stats */}
          <Card className="border-gray-200 shadow-sm">
            <CardContent className="p-4 sm:p-6">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-4">
                Profile Stats
              </h3>

              <div className="space-y-3 sm:space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-xs sm:text-sm text-gray-600">
                    Profile Views
                  </span>
                  <span className="font-medium text-gray-900 text-sm sm:text-base">
                    247
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs sm:text-sm text-gray-600">
                    Applications Sent
                  </span>
                  <span className="font-medium text-gray-900 text-sm sm:text-base">
                    12
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs sm:text-sm text-gray-600">
                    Interview Requests
                  </span>
                  <span className="font-medium text-gray-900 text-sm sm:text-base">
                    5
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
