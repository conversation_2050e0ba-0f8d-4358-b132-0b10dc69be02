"use client";

import { useState } from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Product } from "@/types/ecommerce";

interface ProductSharingProps {
  product: Product;
  className?: string;
}

export function ProductSharing({
  product,
  className = "",
}: ProductSharingProps) {
  const [copied, setCopied] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);

  // Generate the product URL (you might want to use your actual domain)
  const productUrl =
    typeof window !== "undefined"
      ? `${window.location.origin}/product/${product.slug}`
      : `https://yoursite.com/product/${product.slug}`;

  const shareText = `Check out this ${product.title} for ${
    product.currency
  }${product.price.toLocaleString()}`;

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(productUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy link:", err);
    }
  };

  const handleFacebookShare = () => {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
      productUrl
    )}`;
    window.open(facebookUrl, "_blank", "width=600,height=400");
  };

  const handleTwitterShare = () => {
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(
      shareText
    )}&url=${encodeURIComponent(productUrl)}`;
    window.open(twitterUrl, "_blank", "width=600,height=400");
  };

  const handleWhatsAppShare = () => {
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(
      `${shareText} ${productUrl}`
    )}`;
    window.open(whatsappUrl, "_blank");
  };

  const handleEmailShare = () => {
    const subject = encodeURIComponent(
      `Check out this product: ${product.title}`
    );
    const body = encodeURIComponent(`${shareText}\n\n${productUrl}`);
    window.location.href = `mailto:?subject=${subject}&body=${body}`;
  };

  return (
    <>
      {/* Share Button */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="lg"
            className={`text-gray-600 border px-16 text-lg py-3 ${className}`}
          >
            <Icon
              icon="lucide:share-2"
              className="text-[#478085] h-5 w-5 mr-2"
            />
            Share
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuItem onClick={handleCopyLink} className="cursor-pointer">
            {copied ? (
              <Icon
                icon="lucide:check"
                className="h-4 w-4 mr-2 text-green-600"
              />
            ) : (
              <Icon icon="lucide:copy" className="h-4 w-4 mr-2" />
            )}
            {copied ? "Link Copied!" : "Copy Link"}
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuItem
            onClick={handleFacebookShare}
            className="cursor-pointer"
          >
            <Icon
              icon="lucide:facebook"
              className="h-4 w-4 mr-2 text-blue-600"
            />
            Share on Facebook
          </DropdownMenuItem>

          <DropdownMenuItem
            onClick={handleTwitterShare}
            className="cursor-pointer"
          >
            <Icon
              icon="lucide:twitter"
              className="h-4 w-4 mr-2 text-blue-400"
            />
            Share on Twitter
          </DropdownMenuItem>

          <DropdownMenuItem
            onClick={handleWhatsAppShare}
            className="cursor-pointer"
          >
            <Icon
              icon="lucide:message-circle"
              className="h-4 w-4 mr-2 text-green-600"
            />
            Share on WhatsApp
          </DropdownMenuItem>

          <DropdownMenuItem
            onClick={handleEmailShare}
            className="cursor-pointer"
          >
            <Icon icon="lucide:mail" className="h-4 w-4 mr-2 text-gray-600" />
            Share via Email
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuItem
            onClick={() => setShowShareModal(true)}
            className="cursor-pointer"
          >
            <Icon icon="lucide:share-2" className="h-4 w-4 mr-2" />
            More Options
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Share Modal */}
      {showShareModal && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Share Product</h3>
              <button
                onClick={() => setShowShareModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>

            {/* Product Preview */}
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg mb-4">
              <div className="w-12 h-12 bg-gray-200 rounded-lg flex-shrink-0"></div>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-sm truncate">{product.title}</p>
                <p className="text-sm text-gray-600">
                  {product.currency}
                  {product.price.toLocaleString()}
                </p>
              </div>
            </div>

            {/* Copy Link Section */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Link
              </label>
              <div className="flex gap-2">
                <Input value={productUrl} readOnly className="flex-1 text-sm" />
                <Button
                  onClick={handleCopyLink}
                  variant="outline"
                  size="sm"
                  className="flex-shrink-0"
                >
                  {copied ? (
                    <Icon
                      icon="lucide:check"
                      className="h-4 w-4 text-green-600"
                    />
                  ) : (
                    <Icon icon="lucide:copy" className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            {/* Social Media Buttons */}
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-700 mb-3">
                Share on Social Media
              </p>

              <div className="grid grid-cols-2 gap-2">
                <Button
                  onClick={handleFacebookShare}
                  variant="outline"
                  className="justify-start"
                >
                  <Icon
                    icon="lucide:facebook"
                    className="h-4 w-4 mr-2 text-blue-600"
                  />
                  Facebook
                </Button>

                <Button
                  onClick={handleTwitterShare}
                  variant="outline"
                  className="justify-start"
                >
                  <Icon
                    icon="lucide:twitter"
                    className="h-4 w-4 mr-2 text-blue-400"
                  />
                  Twitter
                </Button>

                <Button
                  onClick={handleWhatsAppShare}
                  variant="outline"
                  className="justify-start"
                >
                  <Icon
                    icon="lucide:message-circle"
                    className="h-4 w-4 mr-2 text-green-600"
                  />
                  WhatsApp
                </Button>

                <Button
                  onClick={handleEmailShare}
                  variant="outline"
                  className="justify-start"
                >
                  <Icon
                    icon="lucide:mail"
                    className="h-4 w-4 mr-2 text-gray-600"
                  />
                  Email
                </Button>
              </div>
            </div>

            {/* Close Button */}
            <div className="mt-6 flex justify-end">
              <Button
                onClick={() => setShowShareModal(false)}
                variant="outline"
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
