"use client";

import React, { useState } from "react";
import { Icon } from "@iconify/react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import ProfilePictureUpload from "./ProfilePictureUpload";
import type { UserProfile, ProfileFormData } from "@/types/ecommerce";

interface ProfileSettingsFormProps {
  userProfile?: UserProfile;
  onSave: (data: ProfileFormData) => void;
  isLoading?: boolean;
}

export default function ProfileSettingsForm({
  userProfile,
  onSave,
  isLoading = false,
}: ProfileSettingsFormProps) {
  const [formData, setFormData] = useState<ProfileFormData>({
    username: userProfile?.username || "",
    email: userProfile?.email || "",
    phoneNumber: userProfile?.phoneNumber || "",
    city: userProfile?.address?.city || "",
    profilePicture: userProfile?.profilePicture || undefined,
  });

  const [errors, setErrors] = useState<Partial<ProfileFormData>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<ProfileFormData> = {};

    if (!formData.username.trim()) {
      newErrors.username = "Username is required";
    } else if (formData.username.length < 3) {
      newErrors.username = "Username must be at least 3 characters";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (
      formData.phoneNumber &&
      !/^\+?[\d\s\-\(\)]+$/.test(formData.phoneNumber)
    ) {
      newErrors.phoneNumber = "Please enter a valid phone number";
    }

    if (!formData.city.trim()) {
      newErrors.city = "City is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof ProfileFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleImageChange = (file: File | null) => {
    setFormData((prev) => ({ ...prev, profilePicture: file || undefined }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSave(formData);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl font-semibold text-gray-900 flex items-center gap-2">
          <Icon icon="lucide:user" className="w-6 h-6" />
          Profile Settings
        </CardTitle>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Profile Picture Section */}
          <div>
            <ProfilePictureUpload
              currentImage={
                typeof formData.profilePicture === "string"
                  ? formData.profilePicture
                  : undefined
              }
              onImageChange={handleImageChange}
            />
          </div>

          <Separator />

          {/* Personal Information */}
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">
              Personal Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Username */}
              <div className="space-y-2">
                <Label
                  htmlFor="username"
                  className="text-md font-medium text-gray-700"
                >
                  Username <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Icon
                    icon="lucide:user"
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
                  />
                  <Input
                    id="username"
                    type="text"
                    value={formData.username}
                    onChange={(e) =>
                      handleInputChange("username", e.target.value)
                    }
                    placeholder="Enter your username"
                    className={`h-12 pl-10 text-md border-gray-200 focus:border-gray-300 focus:ring-0 ${
                      errors.username ? "border-red-500" : ""
                    }`}
                  />
                </div>
                {errors.username && (
                  <p className="text-sm text-red-500">{errors.username}</p>
                )}
              </div>

              {/* Email */}
              <div className="space-y-2">
                <Label
                  htmlFor="email"
                  className="text-md font-medium text-gray-700"
                >
                  Email Address <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Icon
                    icon="lucide:mail"
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
                  />
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    placeholder="<EMAIL>"
                    className={`h-12 pl-10 text-md border-gray-200 focus:border-gray-300 focus:ring-0 ${
                      errors.email ? "border-red-500" : ""
                    }`}
                  />
                </div>
                {errors.email && (
                  <p className="text-sm text-red-500">{errors.email}</p>
                )}
              </div>

              {/* Phone Number */}
              <div className="space-y-2">
                <Label
                  htmlFor="phoneNumber"
                  className="text-md font-medium text-gray-700"
                >
                  Phone Number
                </Label>
                <div className="relative">
                  <Icon
                    icon="lucide:phone"
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
                  />
                  <Input
                    id="phoneNumber"
                    type="tel"
                    value={formData.phoneNumber}
                    onChange={(e) =>
                      handleInputChange("phoneNumber", e.target.value)
                    }
                    placeholder="+****************"
                    className={`h-12 pl-10 text-md border-gray-200 focus:border-gray-300 focus:ring-0 ${
                      errors.phoneNumber ? "border-red-500" : ""
                    }`}
                  />
                </div>
                {errors.phoneNumber && (
                  <p className="text-sm text-red-500">{errors.phoneNumber}</p>
                )}
              </div>

              {/* City */}
              <div className="space-y-2">
                <Label
                  htmlFor="city"
                  className="text-md font-medium text-gray-700"
                >
                  City <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Icon
                    icon="lucide:map-pin"
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
                  />
                  <Input
                    id="city"
                    type="text"
                    value={formData.city}
                    onChange={(e) => handleInputChange("city", e.target.value)}
                    placeholder="Enter your city"
                    className={`h-12 pl-10 text-md border-gray-200 focus:border-gray-300 focus:ring-0 ${
                      errors.city ? "border-red-500" : ""
                    }`}
                  />
                </div>
                {errors.city && (
                  <p className="text-sm text-red-500">{errors.city}</p>
                )}
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end pt-6">
            <Button
              type="submit"
              disabled={isLoading}
              className="h-12 px-8 bg-teal-600 text-white hover:bg-teal-700 flex items-center gap-2"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Icon icon="lucide:save" className="w-4 h-4" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
