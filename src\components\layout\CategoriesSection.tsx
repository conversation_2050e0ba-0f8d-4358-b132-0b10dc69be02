"use client";
import { useState } from "react";
import Link from "next/link";
import { Icon } from "@iconify/react";
import { useGetCategoriesQuery } from "@/store/api/categoriesApi";
import { getCategoryUrl } from "@/utils/category-utils";
import type { Category } from "@/types/ecommerce";

interface FrequentCategory {
  id: string;
  label: string;
  href: string;
  icon: string;
}

// Frequent/Popular categories for horizontal scroll (from CategoryNavigationRow)
const frequentCategories = [
  {
    id: "electronics",
    label: "Electronics, TVs, & More",
    href: "/category/electronics",
    icon: "material-symbols:devices",
  },
  {
    id: "real-estate",
    label: "Real Estate",
    href: "/category/real-estate",
    icon: "material-symbols:home-work",
  },
  {
    id: "mobile-phones",
    label: "Mobile Phones",
    href: "/category/mobile-phones",
    icon: "material-symbols:smartphone",
  },
  {
    id: "vehicles",
    label: "Vehicles",
    href: "/category/vehicles",
    icon: "material-symbols:directions-car",
  },
  {
    id: "jobs",
    label: "Jobs",
    href: "/category/jobs",
    icon: "material-symbols:work",
  },
  {
    id: "pets",
    label: "Pet & Animal",
    href: "/category/pets",
    icon: "material-symbols:pets",
  },
  {
    id: "photography",
    label: "Photography",
    href: "/category/photography",
    icon: "material-symbols:photo-camera",
  },
  {
    id: "art-crafts",
    label: "Art & Crafts",
    href: "/category/art-crafts",
    icon: "material-symbols:palette",
  },
  {
    id: "fashion",
    label: "Fashion",
    href: "/category/fashion",
    icon: "material-symbols:checkroom",
  },
  {
    id: "books",
    label: "Books",
    href: "/category/books",
    icon: "material-symbols:book",
  },
  {
    id: "sports",
    label: "Sports & Fitness",
    href: "/category/sports",
    icon: "material-symbols:sports",
  },

  {
    id: "home-garden",
    label: "Home & Garden",
    href: "/category/home-garden",
    icon: "material-symbols:home",
  },
  {
    id: "services",
    label: "Services",
    href: "/category/services",
    icon: "material-symbols:task-outlined",
  },
];

export function CategoriesSection() {
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [isGridOpen, setIsGridOpen] = useState(false);

  // Use RTK Query to fetch categories
  const {
    data: categories = [],
    isLoading,
    error,
  } = useGetCategoriesQuery({
    includeSubcategories: true,
    activeOnly: true,
  });

  return (
    <>
      <div className="bg-white border-b border-gray-200 shadow-sm lg:hidden">
        <div className="px-3 py-3">
          <div className="flex items-center gap-4">
            {/* All Categories Grid Button */}
            <button
              onClick={() => setIsGridOpen(true)}
              className="flex items-center gap-2 px-4 py-2 bg-[#478085] text-white rounded-lg hover:bg-[#356267] transition-colors flex-shrink-0"
            >
              <Icon icon="mynaui:grid" className="w-5 h-5" />
              <span className="font-medium">All Categories</span>
            </button>

            {/* Horizontal Scrollable Frequent Categories */}
            <div className="flex-1 overflow-hidden">
              <div
                className="flex overflow-x-auto scrollbar-hide space-x-1"
                style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
              >
                {frequentCategories.map((category: FrequentCategory) => (
                  <Link
                    key={category.id}
                    href={category.href}
                    onClick={() => setActiveCategory(category.id)}
                    className={`
                    flex items-center gap-2 px-3 py-2 rounded-full whitespace-nowrap text-sm font-medium transition-all duration-200 flex-shrink-0
                    ${
                      activeCategory === category.id
                        ? "bg-[#478085] text-white shadow-md"
                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    }
                  `}
                  >
                    <Icon
                      icon={category.icon}
                      className={`w-4 h-4 ${
                        activeCategory === category.id
                          ? "text-white"
                          : "text-gray-500"
                      }`}
                    />
                    <span className="text-xs">{category.label}</span>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Custom scrollbar styles */}
        <style jsx>{`
          .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
          }
          .scrollbar-hide::-webkit-scrollbar {
            display: none;
          }
        `}</style>
      </div>

      {/* All Categories Grid Modal */}
      {isGridOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setIsGridOpen(false)}
          />

          {/* Modal */}
          <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[90vw] max-w-4xl max-h-[80vh] bg-white rounded-lg shadow-xl z-50 overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-[#478085]">
              <h2 className="text-xl font-bold text-white">All Categories</h2>
              <button
                onClick={() => setIsGridOpen(false)}
                className="text-white hover:text-gray-200 p-1"
                aria-label="Close categories"
              >
                <Icon icon="material-symbols:close" className="w-6 h-6" />
              </button>
            </div>

            {/* Categories Grid */}
            <div className="p-6 overflow-y-auto max-h-[calc(80vh-80px)]">
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#478085]"></div>
                  <span className="ml-2 text-gray-600">
                    Loading categories...
                  </span>
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <p className="text-red-600 mb-2">Failed to load categories</p>
                  <p className="text-gray-500 text-sm">
                    Please try again later
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {categories.map((category: Category) => (
                    <Link
                      key={category.id}
                      href={getCategoryUrl(category)}
                      onClick={() => setIsGridOpen(false)}
                      className="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-[#478085] hover:shadow-md transition-all duration-200 group"
                    >
                      <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-2 group-hover:bg-[#478085] transition-colors">
                        <span className="text-2xl group-hover:text-white transition-colors">
                          {category.icon}
                        </span>
                      </div>
                      <span className="text-sm font-medium text-gray-700 text-center group-hover:text-[#478085] transition-colors">
                        {category.name}
                      </span>
                      <span className="text-xs text-gray-500 mt-1">
                        {category.productCount} items
                      </span>
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </>
  );
}
