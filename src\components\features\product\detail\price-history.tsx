"use client";

import { useState } from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON><PERSON>,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog";
import { Product } from "@/types/ecommerce";

interface PriceHistoryProps {
  product: Product;
  className?: string;
}

interface PricePoint {
  date: Date;
  price: number;
  change?: number;
  changeType?: "increase" | "decrease";
}

// Mock price history data
const generatePriceHistory = (currentPrice: number): PricePoint[] => {
  const history: PricePoint[] = [];
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 30);

  let price = currentPrice * 1.15; // Start 15% higher

  for (let i = 0; i < 8; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i * 4);

    if (i > 0) {
      const previousPrice = history[i - 1].price;
      const change = price - previousPrice;
      const changeType = change > 0 ? "increase" : "decrease";

      history.push({
        date,
        price,
        change: Math.abs(change),
        changeType,
      });
    } else {
      history.push({ date, price });
    }

    // Simulate price changes
    if (i === 2) price *= 0.95; // 5% drop
    if (i === 4) price *= 0.92; // 8% drop
    if (i === 6) price *= 0.97; // 3% drop
  }

  return history;
};

export function PriceHistory({ product, className = "" }: PriceHistoryProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [priceAlertEnabled, setPriceAlertEnabled] = useState(false);
  const priceHistory = generatePriceHistory(product.price);

  const lowestPrice = Math.min(...priceHistory.map((p) => p.price));
  const highestPrice = Math.max(...priceHistory.map((p) => p.price));
  const currentPrice = product.price;
  const originalPrice = priceHistory[0]?.price || currentPrice;

  const totalSavings = originalPrice - currentPrice;
  const savingsPercentage = (totalSavings / originalPrice) * 100;

  const isAtLowestPrice = currentPrice === lowestPrice;

  const formatPrice = (price: number) => {
    return `${product.currency}${price.toLocaleString()}`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString([], { month: "short", day: "numeric" });
  };

  const getPriceChangeIcon = (changeType?: string) => {
    if (changeType === "decrease") {
      return (
        <Icon icon="mdi:trending-down" className="h-4 w-4 text-green-600" />
      );
    }
    if (changeType === "increase") {
      return <Icon icon="mdi:trending-up" className="h-4 w-4 text-red-600" />;
    }
    return null;
  };

  const handlePriceAlert = () => {
    setPriceAlertEnabled(!priceAlertEnabled);
    // In a real app, this would set up a price alert
  };

  return (
    <div className={className}>
      {/* Price Summary */}
      <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 mb-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
            <Icon icon="mdi:currency-usd" className="h-5 w-5 text-green-600" />
            Price Information
          </h3>
          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                View History
              </Button>
            </DialogTrigger>
          </Dialog>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Current Price:</span>
            <span className="text-lg font-semibold text-gray-900">
              {formatPrice(currentPrice)}
            </span>
          </div>

          {totalSavings > 0 && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">You Save:</span>
              <div className="text-right">
                <span className="text-lg font-semibold text-green-600">
                  {formatPrice(totalSavings)}
                </span>
                <Badge className="ml-2 bg-green-100 text-green-800 text-xs">
                  {savingsPercentage.toFixed(1)}% OFF
                </Badge>
              </div>
            </div>
          )}

          {isAtLowestPrice && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <div className="flex items-center gap-2">
                <Icon
                  icon="mdi:trending-down"
                  className="h-4 w-4 text-green-600"
                />
                <span className="text-sm font-medium text-green-800">
                  Lowest Price in 30 Days!
                </span>
              </div>
            </div>
          )}

          <Button
            onClick={handlePriceAlert}
            variant="outline"
            size="sm"
            className={`w-full ${
              priceAlertEnabled
                ? "bg-blue-50 border-blue-200 text-blue-700"
                : "border-gray-300"
            }`}
          >
            <Icon
              icon="mdi:bell-outline"
              className={`h-4 w-4 mr-2 ${
                priceAlertEnabled ? "text-blue-600" : ""
              }`}
            />
            {priceAlertEnabled ? "Price Alert Enabled" : "Set Price Alert"}
          </Button>
        </div>
      </div>

      {/* Price History Modal */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Icon
                icon="mdi:currency-usd"
                className="h-5 w-5 text-green-600"
              />
              Price History - Last 30 Days
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Price Statistics */}
            <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">
                  {formatPrice(currentPrice)}
                </div>
                <div className="text-xs text-gray-600">Current</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-green-600">
                  {formatPrice(lowestPrice)}
                </div>
                <div className="text-xs text-gray-600">Lowest</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-red-600">
                  {formatPrice(highestPrice)}
                </div>
                <div className="text-xs text-gray-600">Highest</div>
              </div>
            </div>

            {/* Price Chart (Simple Timeline) */}
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Price Timeline</h4>
              <div className="space-y-2">
                {priceHistory.reverse().map((point, index) => (
                  <div
                    key={index}
                    className={`flex items-center justify-between p-3 rounded-lg border ${
                      point.price === currentPrice
                        ? "bg-blue-50 border-blue-200"
                        : point.price === lowestPrice
                        ? "bg-green-50 border-green-200"
                        : "bg-gray-50 border-gray-200"
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <Icon
                        icon="mdi:calendar"
                        className="h-4 w-4 text-gray-500"
                      />
                      <span className="text-sm text-gray-900">
                        {formatDate(point.date)}
                      </span>
                      {point.price === currentPrice && (
                        <Badge className="bg-blue-100 text-blue-800 text-xs">
                          Current
                        </Badge>
                      )}
                      {point.price === lowestPrice && (
                        <Badge className="bg-green-100 text-green-800 text-xs">
                          Lowest
                        </Badge>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      {point.change && getPriceChangeIcon(point.changeType)}
                      <span className="font-medium text-gray-900">
                        {formatPrice(point.price)}
                      </span>
                      {point.change && (
                        <span
                          className={`text-xs ${
                            point.changeType === "decrease"
                              ? "text-green-600"
                              : "text-red-600"
                          }`}
                        >
                          ({point.changeType === "decrease" ? "-" : "+"}
                          {formatPrice(point.change)})
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Price Alert Section */}
            <div className="border-t pt-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900">Price Alerts</h4>
                <Button
                  onClick={handlePriceAlert}
                  size="sm"
                  variant={priceAlertEnabled ? "default" : "outline"}
                  className={
                    priceAlertEnabled ? "bg-blue-600 hover:bg-blue-700" : ""
                  }
                >
                  <Icon icon="mdi:bell-outline" className="h-4 w-4 mr-1" />
                  {priceAlertEnabled ? "Enabled" : "Enable"}
                </Button>
              </div>
              <p className="text-sm text-gray-600">
                Get notified when the price drops below{" "}
                {formatPrice(currentPrice * 0.9)} (10% off current price)
              </p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
