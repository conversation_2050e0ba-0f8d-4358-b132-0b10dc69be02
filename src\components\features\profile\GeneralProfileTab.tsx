"use client";

import React from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import type { UserProfile } from "@/types/ecommerce";

interface GeneralProfileTabProps {
  userProfile: UserProfile;
  onEditProfile: () => void;
  onProfilePictureChange?: (file: File) => void;
}

export default function GeneralProfileTab({
  userProfile,
  onEditProfile,
}: GeneralProfileTabProps) {
  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatLastLogin = (date: string) => {
    const now = new Date();
    const targetDate = new Date(date);
    const diffInHours = Math.floor(
      (now.getTime() - targetDate.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) {
      return "Just now";
    } else if (diffInHours < 24) {
      return `${diffInHours} hours ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} days ago`;
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">General Profile</h2>
          <p className="text-gray-600 mt-2">
            Your basic account information and personal details
          </p>
        </div>
        <Button
          onClick={onEditProfile}
          className="bg-teal-600 hover:bg-teal-700 text-white"
        >
          <Icon icon="lucide:edit-3" className="w-4 h-4 mr-2" />
          Edit Profile
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Profile Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Personal Information Card */}
          <Card className="border-gray-200 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-6">
                <Icon icon="lucide:user" className="w-5 h-5 text-teal-600" />
                <h3 className="text-xl font-semibold text-gray-900">
                  Personal Information
                </h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500 mb-1 block">
                      Full Name
                    </label>
                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                      <Icon
                        icon="lucide:user"
                        className="w-4 h-4 text-gray-400"
                      />
                      <span className="text-gray-900 font-medium">
                        {userProfile.username}
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-500 mb-1 block">
                      Email Address
                    </label>
                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                      <Icon
                        icon="lucide:mail"
                        className="w-4 h-4 text-gray-400"
                      />
                      <span className="text-gray-900">{userProfile.email}</span>
                      {userProfile.isVerified && (
                        <Badge className="bg-green-100 text-green-800 border-green-200 ml-auto">
                          <Icon
                            icon="lucide:check-circle"
                            className="w-3 h-3 mr-1"
                          />
                          Verified
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-500 mb-1 block">
                      Phone Number
                    </label>
                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                      <Icon
                        icon="lucide:phone"
                        className="w-4 h-4 text-gray-400"
                      />
                      <span className="text-gray-900">
                        {userProfile.phoneNumber || "Not provided"}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500 mb-1 block">
                      Location
                    </label>
                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                      <Icon
                        icon="lucide:map-pin"
                        className="w-4 h-4 text-gray-400"
                      />
                      <span className="text-gray-900">
                        {userProfile.address.city},{" "}
                        {userProfile.address.country}
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-500 mb-1 block">
                      Member Since
                    </label>
                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                      <Icon
                        icon="lucide:calendar"
                        className="w-4 h-4 text-gray-400"
                      />
                      <span className="text-gray-900">
                        {formatDate(userProfile.createdAt)}
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-500 mb-1 block">
                      Last Active
                    </label>
                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                      <Icon
                        icon="lucide:clock"
                        className="w-4 h-4 text-gray-400"
                      />
                      <span className="text-gray-900">
                        {formatLastLogin(userProfile.updatedAt)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* About Me Section */}
          <Card className="border-gray-200 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <Icon icon="lucide:globe" className="w-5 h-5 text-teal-600" />
                <h3 className="text-xl font-semibold text-gray-900">
                  About Me
                </h3>
              </div>
              <p className="text-gray-700 leading-relaxed">
                I&apos;m a passionate individual who loves exploring new
                opportunities and connecting with people. I enjoy technology,
                reading, and outdoor activities. Always looking for ways to
                learn and grow, both personally and professionally.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Account Status */}
          <Card className="border-gray-200 shadow-sm">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Account Status
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Email Verified</span>
                  <Badge className="bg-green-100 text-green-800 border-green-200">
                    <Icon icon="lucide:check-circle" className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Phone Verified</span>
                  <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                    Pending
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    Identity Verified
                  </span>
                  <Badge className="bg-green-100 text-green-800 border-green-200">
                    <Icon icon="lucide:shield" className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Profile Completion */}
          <Card className="border-gray-200 shadow-sm">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Profile Completion
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Progress</span>
                  <span className="font-medium text-gray-900">85%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-teal-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: "85%" }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500">
                  Complete your profile to increase visibility and trust
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="border-gray-200 shadow-sm">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Quick Actions
              </h3>
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Icon icon="lucide:shield" className="w-4 h-4 mr-2" />
                  Verify Phone Number
                </Button>

                <Button variant="outline" className="w-full justify-start">
                  <Icon icon="lucide:edit-3" className="w-4 h-4 mr-2" />
                  Update Profile Picture
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Icon icon="lucide:globe" className="w-4 h-4 mr-2" />
                  Share Profile
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
