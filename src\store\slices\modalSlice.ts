import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ReactNode } from 'react';

// Define the modal state interface
export interface ModalState {
  isOpen: boolean;
  modalContent: ReactNode | null;
  modalProps: Record<string, unknown>;
  modalType?: string;
}

// Initial state
const initialState: ModalState = {
  isOpen: false,
  modalContent: null,
  modalProps: {},
  modalType: undefined,
};

// Create the modal slice
const modalSlice = createSlice({
  name: 'modal',
  initialState,
  reducers: {
    openModal: (state, action: PayloadAction<{
      content: ReactNode;
      props?: Record<string, unknown>;
      type?: string;
    }>) => {
      const { content, props = {}, type } = action.payload;
      state.isOpen = true;
      state.modalContent = content;
      state.modalProps = props;
      state.modalType = type;
    },
    closeModal: (state) => {
      state.isOpen = false;
      state.modalContent = null;
      state.modalProps = {};
      state.modalType = undefined;
    },
    updateModalProps: (state, action: PayloadAction<Record<string, unknown>>) => {
      state.modalProps = { ...state.modalProps, ...action.payload };
    },
    setModalContent: (state, action: PayloadAction<ReactNode>) => {
      state.modalContent = action.payload;
    },
    setModalType: (state, action: PayloadAction<string | undefined>) => {
      state.modalType = action.payload;
    },
  },
});

// Export actions
export const {
  openModal,
  closeModal,
  updateModalProps,
  setModalContent,
  setModalType,
} = modalSlice.actions;

// Export reducer
export default modalSlice.reducer;
