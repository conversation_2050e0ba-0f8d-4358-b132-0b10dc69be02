"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useUser } from "@/store/compatibility";
import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react";

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
  showLoginPrompt?: boolean;
  requireRole?: string[];
  onAuthRequired?: () => void;
  promptTitle?: string;
  promptMessage?: string;
}

/**
 * AuthGuard Component
 *
 * Conditionally renders content based on authentication status.
 * Provides flexible fallback content and customizable login prompts.
 *
 * @param children - Content to show when authenticated
 * @param fallback - Custom fallback UI when not authenticated
 * @param redirectTo - Custom redirect path for login
 * @param showLoginPrompt - Whether to show default login prompt (default: true)
 * @param requireRole - Array of required roles (future enhancement)
 * @param onAuthRequired - Custom handler when authentication is required
 * @param promptTitle - Custom title for login prompt
 * @param promptMessage - Custom message for login prompt
 */
export function AuthGuard({
  children,
  fallback,
  redirectTo = "/login",
  showLoginPrompt = true,
  requireRole,
  onAuthRequired,
  promptTitle = "Authentication Required",
  promptMessage = "Please log in to access this feature",
}: AuthGuardProps) {
  const { isAuthenticated, isLoading, currentUser } = useUser();
  const _router = useRouter();
  const [isHydrated, setIsHydrated] = useState(false);

  // Handle client-side hydration to prevent SSR mismatch
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Show loading state during hydration or while checking authentication
  if (!isHydrated || isLoading) {
    return (
      <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
        <div className="flex items-center justify-center p-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#478085]"></div>
        </div>
      </div>
    );
  }

  // Check role-based access (future enhancement)
  if (isAuthenticated && requireRole && currentUser) {
    // This would check user roles against required roles
    // For now, we'll assume all authenticated users have access
    // In a real implementation, you'd check currentUser.roles against requireRole
  }

  // User is authenticated, show protected content
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // User is not authenticated
  // If custom handler is provided, call it
  if (onAuthRequired) {
    onAuthRequired();
    return null;
  }

  // If custom fallback is provided, use it
  if (fallback) {
    return <>{fallback}</>;
  }

  // If login prompt is disabled, don't show anything
  if (!showLoginPrompt) {
    return null;
  }

  // Show default login prompt with consistent layout
  return (
    <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
      <DefaultLoginPrompt
        title={promptTitle}
        message={promptMessage}
        redirectTo={redirectTo}
      />
    </div>
  );
}

interface DefaultLoginPromptProps {
  title: string;
  message: string;
  redirectTo: string;
}

function DefaultLoginPrompt({
  title,
  message,
  redirectTo,
}: DefaultLoginPromptProps) {
  const router = useRouter();

  const handleLogin = () => {
    router.push(redirectTo);
  };

  const handleSignup = () => {
    router.push("/signup");
  };

  return (
    <div className="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        {/* Main Card */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          {/* Header with gradient background */}
          <div className="bg-gradient-to-br from-[#478085] to-[#356267] px-8 py-10 text-center">
            <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon icon="lucide:log-in" className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-white mb-2">{title}</h3>
            <p className="text-white/90 text-base leading-relaxed">{message}</p>
          </div>

          {/* Content */}
          <div className="px-8 py-8">
            <div className="space-y-4">
              {/* Primary Login Button */}
              <Button
                onClick={handleLogin}
                className="w-full bg-[#478085] hover:bg-[#356267] text-white px-6 py-4 rounded-xl text-lg font-semibold transition-all duration-300 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl hover:scale-[1.02] transform"
              >
                <Icon icon="lucide:log-in" className="w-5 h-5" />
                Log In to Continue
              </Button>

              {/* Divider */}
              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-200"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-4 bg-white text-gray-500 font-medium">
                    Don't have an account?
                  </span>
                </div>
              </div>

              {/* Secondary Signup Button */}
              <Button
                onClick={handleSignup}
                variant="outline"
                className="w-full border-2 border-[#478085] text-[#478085] hover:bg-[#478085] hover:text-white px-6 py-4 rounded-xl text-lg font-semibold transition-all duration-300 flex items-center justify-center gap-3 hover:scale-[1.02] transform"
              >
                <Icon icon="lucide:user-plus" className="w-5 h-5" />
                Create New Account
              </Button>
            </div>

            {/* Additional Info */}
            <div className="mt-8 pt-6 border-t border-gray-100">
              <div className="text-center">
                <p className="text-sm text-gray-500 mb-3">
                  Join thousands of users already using SastoBazar
                </p>
                <div className="flex items-center justify-center space-x-6 text-xs text-gray-400">
                  <span className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    Secure & Safe
                  </span>
                  <span className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    Free to Join
                  </span>
                  <span className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                    Easy Setup
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Convenience components for common use cases
export function CommentAuthGuard({ children }: { children: React.ReactNode }) {
  return (
    <AuthGuard
      promptTitle="Join the Conversation!"
      promptMessage="Log in to share your thoughts and engage with the community"
    >
      {children}
    </AuthGuard>
  );
}

export function WishlistAuthGuard({ children }: { children: React.ReactNode }) {
  return (
    <AuthGuard
      promptTitle="Save Your Favorites"
      promptMessage="Create an account to save items and build your wishlist"
      fallback={
        <div className="p-3 bg-gray-50 rounded-lg text-center text-sm text-gray-600">
          <p>Log in to save favorites</p>
        </div>
      }
    >
      {children}
    </AuthGuard>
  );
}

export function PostAdAuthGuard({ children }: { children: React.ReactNode }) {
  const router = useRouter();

  return (
    <AuthGuard
      fallback={
        <Button
          onClick={() => router.push("/login")}
          className="bg-[#1F5E64] hover:bg-[#1a5157] text-white rounded-xl px-4 lg:px-6 py-2 text-md font-medium transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-white"
        >
          <span className="hidden md:inline">Login to Post Ad</span>
          <span className="md:hidden">Login</span>
        </Button>
      }
    >
      {children}
    </AuthGuard>
  );
}
