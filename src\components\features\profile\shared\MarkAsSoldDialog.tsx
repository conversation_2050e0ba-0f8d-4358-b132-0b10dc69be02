"use client";

import React, { useState } from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/toast";
import type { UserListing, ListingAction } from "@/types/ecommerce";

interface MarkAsSoldDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  listing: UserListing;
  onAction: (action: ListingAction) => void;
  isLoading?: boolean;
  onLoadingChange?: (loading: boolean) => void;
}

export function MarkAsSoldDialog({
  open,
  onOpenChange,
  listing,
  onAction,
  isLoading = false,
  onLoadingChange,
}: MarkAsSoldDialogProps) {
  const [soldPrice, setSoldPrice] = useState("");
  const { addToast } = useToast();

  const handleMarkAsSold = async () => {
    if (!soldPrice.trim()) {
      addToast({
        type: "error",
        title: "Price required",
        description: "Please enter the sold price.",
      });
      return;
    }

    onLoadingChange?.(true);
    try {
      onAction({
        type: "mark_sold",
        productId: listing.id,
        data: {
          soldPrice: `₹. ${soldPrice}`,
          soldAt: new Date().toISOString(),
        },
      });

      addToast({
        type: "success",
        title: "Marked as sold!",
        description: `${listing.title} has been marked as sold for ₹. ${soldPrice}`,
      });

      onOpenChange(false);
      setSoldPrice("");
    } catch (_error) {
      addToast({
        type: "error",
        title: "Error",
        description: "Failed to mark as sold. Please try again.",
      });
    } finally {
      onLoadingChange?.(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    setSoldPrice("");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Icon
              icon="lucide:check-circle-2"
              className="w-5 h-5 text-green-600"
            />
            Mark as Sold
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <h4 className="font-medium text-green-900 mb-1">
              {listing.title}
            </h4>
            <p className="text-sm text-green-700">
              Listed at: <span className="font-medium">{listing.price}</span>
            </p>
          </div>

          <div className="space-y-4">
            <div>
              <Label
                htmlFor="soldPrice"
                className="text-sm font-medium text-gray-700"
              >
                Final Selling Price
              </Label>
              <div className="relative mt-1">
                <Icon
                  icon="lucide:indian-rupee"
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"
                />
                <Input
                  id="soldPrice"
                  type="number"
                  placeholder="Enter sold price"
                  value={soldPrice}
                  onChange={(e) => setSoldPrice(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
              <div className="flex items-center gap-2 text-blue-800 text-sm">
                <Icon icon="lucide:calendar" className="w-4 h-4" />
                <span>Sold date will be set to today</span>
              </div>
            </div>
          </div>

          <div className="flex gap-3 justify-end">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleMarkAsSold}
              disabled={isLoading}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Marking...
                </>
              ) : (
                <>
                  <Icon
                    icon="lucide:check-circle-2"
                    className="w-4 h-4 mr-2"
                  />
                  Mark as Sold
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
