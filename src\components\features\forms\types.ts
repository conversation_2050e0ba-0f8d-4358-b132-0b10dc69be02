// Types for PostAdsForm components - Updated to match backend requirements exactly
import { CurrencyType, ConditionType } from "@/types/advertisement";

export interface FormData {
  // Step 1 - Photos, Title & Category (Required: title, categoryId)
  photos: File[]; // Optional - max 8 images
  adTitle: string; // Required - Maps to 'title' in API (5-200 chars)

  // Step 2 - Details & Location (Required: description)
  categoryId: string; // Required UUID
  subcategoryId?: string; // Optional UUID
  description: string; // Required - 10-5000 chars
  condition?: ConditionType; // Optional - Maps to condition enum
  location?: string; // Optional - Maps to location.location
  city?: string; // Optional - Maps to location.city
  state?: string; // Optional - Maps to location.state
  latitude?: number; // Optional - Maps to location.latitude
  longitude?: number; // Optional - Maps to location.longitude

  // Step 3 - Price & Settings (All optional)
  price?: string; // Optional - Convert to number for API
  currency: CurrencyType; // Default: NPR
  negotiable: boolean; // Default: true
  inventoryQuantity: number; // Default: 1, min: 1
  youtubeVideoUrl?: string; // Optional, max 500 chars
  isFeatured: boolean; // Default: false
  isUrgent: boolean; // Default: false
}

export interface ConditionOption {
  value: string;
  label: string;
  desc: string;
  color: string;
}

// Form validation rules matching backend constraints
export const VALIDATION_RULES = {
  title: { min: 5, max: 200 },
  description: { min: 10, max: 5000 },
  price: { min: 0 },
  inventoryQuantity: { min: 1 },
  youtubeVideoUrl: { max: 500 },
  location: { max: 200 },
  city: { max: 100 },
  state: { max: 100 },
  latitude: { min: -90, max: 90 },
  longitude: { min: -180, max: 180 },
  maxImages: 8,
} as const;

// Condition mapping for UI display - Updated to match backend enums
export const CONDITION_OPTIONS: ConditionOption[] = [
  {
    value: ConditionType.NEW,
    label: "New",
    desc: "Brand new, never used",
    color: "green",
  },
  {
    value: ConditionType.USED,
    label: "Used",
    desc: "Previously owned, shows normal wear",
    color: "yellow",
  },
  {
    value: ConditionType.REFURBISHED,
    label: "Refurbished",
    desc: "Professionally restored to working condition",
    color: "purple",
  },
] as const;

// Currency options for the form
export const CURRENCY_OPTIONS = [
  { value: CurrencyType.NPR, label: "NPR (₨)", symbol: "₨" },
  { value: CurrencyType.USD, label: "USD ($)", symbol: "$" },
  { value: CurrencyType.EUR, label: "EUR (€)", symbol: "€" },
  { value: CurrencyType.INR, label: "INR (₹)", symbol: "₹" },
] as const;

// Delivery area options for the form
export const DELIVERY_AREA_OPTIONS = [
  { value: "local", label: "Local Delivery", desc: "Within city limits" },
  { value: "nationwide", label: "Nationwide", desc: "Across the country" },
  { value: "pickup", label: "Pickup Only", desc: "Buyer pickup required" },
] as const;

// Legacy constants for backward compatibility
export const MAX_PHOTOS = VALIDATION_RULES.maxImages;
export const MAX_TITLE_LENGTH = VALIDATION_RULES.title.max;
export const MAX_DESCRIPTION_LENGTH = VALIDATION_RULES.description.max;
