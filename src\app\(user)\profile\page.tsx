"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>, Footer } from "@/components";
import { ToastProvider } from "@/components/ui/toast";
import { AuthGuard } from "@/components/features/auth";
import { BackButton, PageLoading } from "@/components/ui";
import type {
  UserProfile,
  UserListing,
  ListingAction,
} from "@/types/ecommerce";
import {
  useGetUserProfileQuery,
  useUpdateProfileMutation,
} from "@/store/api/userApi";
import { useGetUserAdvertisementsQuery } from "@/store/api/advertisementApi";
import { User } from "@/types/auth";
import { AdvertisementResponse } from "@/types/advertisement";
import { getAuthToken } from "@/lib/api";

// Import redesigned components
import {
  ProfileHeader,
  ProfileNavigation,
  GeneralProfileTab,
  JobProfileTab,
  AdPostsTab,
  SavedListingsTab,
  ReviewsTab,
  PrivacySettingsTab,
  NotificationPreferencesTab,
  EditJobProfileModal,
  EditProfileModal,
} from "@/components/features/profile";

// Helper function to convert API Advertisement to UserListing format
const convertAdvertisementToUserListing = (
  ad: AdvertisementResponse
): UserListing => {
  // Map API status to UserListing status
  const mapStatus = (apiStatus: string): UserListing["status"] => {
    switch (apiStatus) {
      case "active":
        return "active";
      case "sold":
        return "sold";
      case "expired":
        return "expired";
      case "suspended":
      case "rejected":
        return "inactive";
      case "draft":
      case "pending_approval":
        return "hold";
      default:
        return "inactive";
    }
  };

  return {
    id: ad.id,
    title: ad.title,
    condition: ad.condition || "Not specified",
    price: ad.price ? `Rs. ${ad.price.toLocaleString()}` : "Price on request",
    status: mapStatus(ad.status),
    image:
      ad.images && ad.images.length > 0
        ? ad.images[0].imageUrl
        : "/assets/images/placeholders/placeholder.jpg",
    postedAt: ad.createdAt,
    views: ad.viewCount || 0,
    inquiries: ad.inquiryCount || 0,
  };
};

// Helper function to convert API User to UserProfile format
const convertUserToUserProfile = (user: User): UserProfile => {
  return {
    id: user.id,
    username: user.username,
    email: user.email || "",
    phoneNumber: user.phone || "",
    profilePicture: user.profilePictureUrl || user.profilePicture,
    address: {
      city: user.city || "",
      country: user.country || "Nepal",
    },
    createdAt: user.createdAt,
    updatedAt: user.updatedAt || user.createdAt,
    isVerified: user.emailVerified || user.isVerified,
    preferences: {
      notifications: user.notificationPreferences
        ? {
            email: user.notificationPreferences.email,
            sms: user.notificationPreferences.sms,
            push: user.notificationPreferences.push,
          }
        : {
            email: true,
            sms: false,
            push: true,
          },
      privacy: user.privacySettings
        ? {
            showEmail: user.privacySettings.showEmail,
            showPhone: user.privacySettings.showPhone,
          }
        : {
            showEmail: false,
            showPhone: false,
          },
    },
    // Additional fields from the new User interface
    bio: user.bio,
    totalListings: 0, // Will be populated from stats API
    totalSold: 0, // Will be populated from stats API
    avatar: user.profilePictureUrl,
  };
};

function ProfilePageContent() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("general");
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isEditProfileModalOpen, setIsEditProfileModalOpen] = useState(false);

  // Check if user is authenticated before making API calls
  const isAuthenticated = !!getAuthToken();

  // Redirect to login if not authenticated
  React.useEffect(() => {
    if (!isAuthenticated) {
      router.push("/login");
      return;
    }
  }, [isAuthenticated, router]);

  // Use real API hooks - only call if authenticated
  const {
    data: userData,
    isLoading: userLoading,
    error: userError,
    refetch: refetchUser,
  } = useGetUserProfileQuery(undefined, {
    skip: !isAuthenticated, // Skip the query if not authenticated
  });

  // Get user's advertisements
  const {
    data: advertisementsData,
    isLoading: advertisementsLoading,
    error: advertisementsError,
    refetch: refetchAdvertisements,
  } = useGetUserAdvertisementsQuery(undefined, {
    skip: !isAuthenticated,
  });

  const [updateProfile] = useUpdateProfileMutation();

  // Transform advertisements to listings
  const listings: UserListing[] = React.useMemo(() => {
    if (!advertisementsData?.data) return [];
    return advertisementsData.data.map(convertAdvertisementToUserListing);
  }, [advertisementsData]);

  // Show loading while redirecting if not authenticated
  if (!isAuthenticated) {
    return <PageLoading />;
  }

  // Show loading while fetching user data or advertisements
  if (userLoading || advertisementsLoading) {
    return <PageLoading />;
  }

  // Show error if user data fetch failed
  if (userError || !userData) {
    return (
      <div
        className="min-h-screen flex items-center justify-center"
        style={{ backgroundColor: "#EFEFEF" }}
      >
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Error Loading Profile
          </h2>
          <p className="text-gray-600 mb-4">
            Unable to load your profile information.
          </p>
          <button
            onClick={() => refetchUser()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Convert API User data to UserProfile format
  const currentUserProfile = convertUserToUserProfile(userData);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleBack = () => {
    router.back();
  };

  // Legacy handler for components that expect File parameter
  const handleProfilePictureChange = (file: File) => {
    // This is now handled automatically by the ProfilePictureUpload component
    console.log("Profile picture file selected:", file.name);
  };

  const handleProfileSave = async (updatedProfile: Partial<UserProfile>) => {
    try {
      // Convert UserProfile format to UpdateProfileRequest format
      const updateData = {
        bio: updatedProfile.bio,
        phone: updatedProfile.phoneNumber,
        city: updatedProfile.address?.city,
        country: updatedProfile.address?.country,
      };

      await updateProfile(updateData).unwrap();

      // Refetch user data to get updated profile
      refetchUser();
      console.log("Profile updated successfully:", updatedProfile);
    } catch (error) {
      console.error("Failed to update profile:", error);
      // You could show a toast notification here
    }
  };

  const handleListingAction = (action: ListingAction) => {
    // TODO: Implement API mutations for listing actions
    console.log("Listing action:", action);

    // For now, show a message and refetch data
    switch (action.type) {
      case "mark_sold":
        console.log("Marking as sold:", action.productId);
        // TODO: Call updateAdvertisement API with status: 'sold'
        break;
      case "mark_active":
        console.log("Marking as active:", action.productId);
        // TODO: Call updateAdvertisement API with status: 'active'
        break;
      case "mark_inactive":
        console.log("Marking as inactive:", action.productId);
        // TODO: Call updateAdvertisement API with status: 'suspended'
        break;
      case "delete":
        console.log("Deleting listing:", action.productId);
        // TODO: Call deleteAdvertisement API
        break;
      case "edit":
        console.log("Edit listing:", action.productId);
        // TODO: Navigate to edit page or open edit modal
        break;
      case "promote":
        console.log("Promote listing:", action.productId);
        // TODO: Call promote advertisement API
        break;
    }

    // Refetch advertisements to get updated data
    refetchAdvertisements();
  };

  return (
    <AuthGuard
      promptTitle="Access Your Profile"
      promptMessage="Please log in to view and manage your profile"
    >
      <ToastProvider>
        <div className="min-h-screen bg-[#EFEFEF]">
          <Header />

          <div className="max-w-[90%] mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Back Button */}
            <div className="mb-6">
              <BackButton onClick={handleBack} size="lg" />
            </div>

            {/* Profile Content */}
            <div className="md:bg-white md:p-8 md:rounded-lg">
              {/* Profile Header */}
              <ProfileHeader
                userProfile={currentUserProfile}
                onProfilePictureChange={handleProfilePictureChange}
              />

              {/* Profile Navigation */}
              <ProfileNavigation
                activeTab={activeTab}
                onTabChange={handleTabChange}
              />

              {/* Tab Content */}
              <div className="mt-8">
                {activeTab === "general" && (
                  <GeneralProfileTab
                    userProfile={currentUserProfile}
                    onEditProfile={() => setIsEditProfileModalOpen(true)}
                    onProfilePictureChange={handleProfilePictureChange}
                  />
                )}

                {activeTab === "job" && (
                  <JobProfileTab
                    onEditJobProfile={() => setIsEditModalOpen(true)}
                  />
                )}

                {activeTab === "posts" && (
                  <AdPostsTab
                    listings={listings}
                    onListingAction={handleListingAction}
                  />
                )}

                {activeTab === "save-lists" && <SavedListingsTab />}

                {activeTab === "reviews" && <ReviewsTab />}

                {activeTab === "privacy" && <PrivacySettingsTab />}

                {activeTab === "notifications" && (
                  <NotificationPreferencesTab />
                )}
              </div>
            </div>
          </div>

          {/* Modals */}
          <EditJobProfileModal
            open={isEditModalOpen}
            onOpenChange={setIsEditModalOpen}
          />

          <EditProfileModal
            open={isEditProfileModalOpen}
            onOpenChange={setIsEditProfileModalOpen}
            userProfile={currentUserProfile}
            onSave={handleProfileSave}
          />
        </div>
        <Footer />
      </ToastProvider>
    </AuthGuard>
  );
}

export default function ProfilePage() {
  return <ProfilePageContent />;
}
