import type React from "react";
import { Icon } from "@iconify/react";
import { SectionHeader } from "./SectionHeader";
import { CONDITION_OPTIONS } from "../types";
import { ConditionType } from "@/types/advertisement";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface ConditionSelectorProps {
  selectedCondition: ConditionType | "" | undefined;
  onConditionChange: (condition: ConditionType | "" | undefined) => void;
}

export const ConditionSelector: React.FC<ConditionSelectorProps> = ({
  selectedCondition,
  onConditionChange,
}) => {
  const selectedOption = CONDITION_OPTIONS.find(
    (option) => option.value === selectedCondition
  );

  return (
    <div className="space-y-3">
      <SectionHeader
        icon={<Icon icon="lucide:shield" className="w-4 h-4 text-[#478085]" />}
        title="Product Condition"
        required
        iconBgColor="bg-gray-50"
      />

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button
            type="button"
            className="w-full h-14 px-4 py-3 bg-white/90 backdrop-blur-sm border-2 border-gray-200 rounded-xl text-left flex items-center justify-between transition-all duration-200 hover:border-gray-300 hover:bg-white focus:border-[#478085] focus:ring-4 focus:ring-[#478085]/20 focus:shadow-lg focus:outline-none shadow-sm"
          >
            <div className="flex-1">
              {selectedOption ? (
                <div>
                  <div className="font-medium text-gray-900">
                    {selectedOption.label}
                  </div>
                  <div className="text-sm text-gray-500">
                    {selectedOption.desc}
                  </div>
                </div>
              ) : (
                <div className="text-gray-400">Select product condition</div>
              )}
            </div>
            <Icon
              icon="lucide:chevron-down"
              className="w-5 h-5 text-gray-400 transition-transform duration-200 group-data-[state=open]:rotate-180"
            />
          </button>
        </DropdownMenuTrigger>

        <DropdownMenuContent
          className="w-[var(--radix-dropdown-menu-trigger-width)] max-h-80 overflow-y-auto z-[100] bg-white/95 backdrop-blur-sm border border-gray-200 shadow-xl rounded-xl p-2 animate-in fade-in-0 zoom-in-95 duration-200"
          align="start"
          sideOffset={6}
        >
          {CONDITION_OPTIONS.map((condition) => (
            <DropdownMenuItem
              key={condition.value}
              onClick={() => onConditionChange(condition.value as ConditionType)}
              className={`flex items-center justify-between p-4 cursor-pointer hover:bg-blue-50/80 focus:bg-blue-50/80 data-[highlighted]:bg-blue-50/80 transition-colors duration-200 rounded-lg mx-1 my-0.5 ${
                selectedCondition === condition.value
                  ? 'bg-[#478085]/10 border border-[#478085]/20'
                  : ''
              }`}
            >
              <div className="flex-1">
                <div className="font-medium text-gray-900">
                  {condition.label}
                </div>
                <div className="text-sm text-gray-500">{condition.desc}</div>
              </div>
              {selectedCondition === condition.value && (
                <div className="flex items-center justify-center w-6 h-6 bg-[#478085] rounded-full ml-2">
                  <Icon
                    icon="lucide:check"
                    className="w-4 h-4 text-white"
                  />
                </div>
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
