"use client";

import React, { memo } from "react";
import { Icon } from "@iconify/react";
import type { SortBy } from "@/types/ecommerce";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";

interface SortDropdownProps {
  sortBy: SortBy;
  onSortChange: (sortBy: SortBy) => void;
  className?: string;
}

const SortDropdown = memo(function SortDropdown({
  sortBy,
  onSortChange,
  className = "",
}: SortDropdownProps) {
  const sortOptions = [
    { value: "newest", label: "Recent" },
    { value: "oldest", label: "Older" },
    { value: "price-low", label: "Low to High" },
    { value: "price-high", label: "High to Low" },
    { value: "top-review", label: "Top Review" },
  ];

  return (
    <div className={`flex items-center ${className}`}>
      <Select
        value={sortBy}
        onValueChange={(value) => onSortChange(value as SortBy)}
      >
        <SelectTrigger className="w-[180px]  bg-white border border-gray-300 rounded-lg shadow-sm hover:border-gray-400 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 transition-colors">
          <div className="flex p-3 items-center gap-2 text-gray-600">
            {/* iconify icon  */}
            <Icon icon="ep:sort" className="w-5 h-5 text-gray-600" />

            <span className="text-xl ml-4 font-medium">Sort</span>
          </div>
        </SelectTrigger>
        <SelectContent className="min-w-[150px] border-none bg-white shadow-2xl">
          {sortOptions.map((option) => (
            <SelectItem
              key={option.value}
              value={option.value}
              className="text-md"
            >
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
});

export default SortDropdown;
