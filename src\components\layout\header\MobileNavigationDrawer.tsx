"use client";
import { useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useMobileNavigation } from "@/context/MobileNavigationContext";
import { useUser } from "@/store/compatibility";
import { useEcommerce } from "@/store/compatibility";
import { PostAdAuthGuard } from "@/components/features/auth/AuthGuard";

import { getCategoryUrl } from "@/utils/category-utils";

const navigationItems = [
  { label: "Home", href: "/", icon: "material-symbols:home" },
  {
    label: "Categories",
    href: "/categories",
    icon: "material-symbols:category",
  },
  { label: "My Ads", href: "/my-ads", icon: "material-symbols:inventory-2" },
  { label: "Favorites", href: "/favorites", icon: "material-symbols:favorite" },
  { label: "Messages", href: "/messages", icon: "material-symbols:message" },
  { label: "Profile", href: "/profile", icon: "material-symbols:person" },
  { label: "Settings", href: "/settings", icon: "material-symbols:settings" },
];

// Map of category IDs to their display labels and icons for mobile navigation
const categoryDisplayMap = {
  "electronics-appliances": {
    label: "Electronics, TVs, & More",
    icon: "material-symbols:devices",
  },
  property: {
    label: "Real Estate",
    icon: "material-symbols:home-work",
  },
  "mobile-phones": {
    label: "Mobile Phones",
    icon: "material-symbols:smartphone",
  },
  vehicles: {
    label: "Vehicles",
    icon: "material-symbols:directions-car",
  },
  jobs: {
    label: "Jobs",
    icon: "material-symbols:work",
  },
  "pets-animals": {
    label: "Pet & Animal",
    icon: "material-symbols:pets",
  },
  photography: {
    label: "Photography",
    icon: "material-symbols:photo-camera",
  },
  "art-crafts": {
    label: "Art & Crafts",
    icon: "material-symbols:palette",
  },
};

// Get featured categories for mobile navigation
const getFeaturedCategories = (categories: any[]) => {
  return categories
    .filter(
      (category) =>
        categoryDisplayMap[category.id as keyof typeof categoryDisplayMap]
    )
    .map((category) => ({
      id: category.id,
      slug: category.slug,
      label:
        categoryDisplayMap[category.id as keyof typeof categoryDisplayMap]
          ?.label || category.name,
      href: getCategoryUrl(category),
      icon:
        categoryDisplayMap[category.id as keyof typeof categoryDisplayMap]
          ?.icon || "material-symbols:category",
    }));
};

export function MobileNavigationDrawer() {
  const { isMobileMenuOpen, closeMobileMenu } = useMobileNavigation();
  const { isAuthenticated, currentUser, logout } = useUser();
  const { state } = useEcommerce();
  const { cart } = state;
  const categories = getFeaturedCategories(state.category.categories || []);

  // Close menu on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        closeMobileMenu();
      }
    };

    if (isMobileMenuOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isMobileMenuOpen, closeMobileMenu]);

  if (!isMobileMenuOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 z-40 md:hidden"
        onClick={closeMobileMenu}
      />

      {/* Drawer */}
      <div className="fixed top-0 left-0 h-full w-80 max-w-[85vw] bg-white z-50 md:hidden transform transition-transform duration-300 ease-in-out shadow-xl">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-[#478085]">
          <div className="flex items-center gap-3">
            <Image
              src="/logo.png"
              alt="SastoBazar"
              width={32}
              height={32}
              className="w-8 h-8"
            />
            <span
              className="text-white font-bold text-lg"
              style={{ fontFamily: "Bungee, cursive" }}
            >
              SastoBazar
            </span>
          </div>
          <button
            onClick={closeMobileMenu}
            className="text-white hover:text-gray-200 p-1"
            aria-label="Close menu"
          >
            <Icon icon="material-symbols:close" className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="flex flex-col h-full overflow-y-auto">
          {/* User Section */}
          {isAuthenticated && currentUser && (
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-[#478085] rounded-full flex items-center justify-center">
                  <Icon
                    icon="material-symbols:person"
                    className="w-6 h-6 text-white"
                  />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">
                    {currentUser.username}
                  </p>
                  <p className="text-sm text-gray-600">{currentUser.email}</p>
                </div>
              </div>
            </div>
          )}

          {/* Balance and Top-Up Section - Mobile Only */}
          {isAuthenticated && (
            <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-[#478085]/5 to-[#356267]/5">
              <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-3">
                Wallet
              </h3>

              {/* Current Balance Display */}
              <div className="bg-white rounded-lg p-3 mb-3 border border-gray-200 shadow-sm">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Icon
                      icon="tdesign:money-filled"
                      className="w-5 h-5 text-[#478085]"
                    />
                    <span className="text-sm font-medium text-gray-700">
                      Current Balance
                    </span>
                  </div>
                  <span className="text-lg font-bold text-[#478085]">
                    Rs 2,500.75
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="grid grid-cols-2 gap-2">
                <Link href="/top-up" onClick={closeMobileMenu}>
                  <button className="w-full bg-gradient-to-r from-[#478085] to-[#356267] hover:from-[#356267] hover:to-[#2d5459] text-white rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center gap-1.5">
                    <Icon
                      icon="material-symbols:add-card"
                      className="w-4 h-4"
                    />
                    <span>Top Up</span>
                  </button>
                </Link>
                <Link href="/transactions" onClick={closeMobileMenu}>
                  <button className="w-full bg-white border border-[#478085] text-[#478085] hover:bg-[#478085]/5 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-300 flex items-center justify-center gap-1.5">
                    <Icon icon="material-symbols:history" className="w-4 h-4" />
                    <span>History</span>
                  </button>
                </Link>
              </div>
            </div>
          )}

          {/* Quick Actions Section */}
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-3">
              Quick Actions
            </h3>
            <div className="grid grid-cols-2 gap-3">
              {/* Shopping Cart */}
              <Link href="/cart" onClick={closeMobileMenu}>
                <div className="bg-white border border-gray-200 rounded-lg p-3 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Icon
                        icon="material-symbols:shopping-cart"
                        className="w-5 h-5 text-[#478085]"
                      />
                      <span className="text-sm font-medium text-gray-700">
                        Cart
                      </span>
                    </div>
                    {cart.totalItems > 0 && (
                      <Badge className="bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                        {cart.totalItems > 99 ? "99+" : cart.totalItems}
                      </Badge>
                    )}
                  </div>
                </div>
              </Link>

              {/* Notifications */}
              <Link href="/notifications" onClick={closeMobileMenu}>
                <div className="bg-white border border-gray-200 rounded-lg p-3 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Icon
                        icon="material-symbols:notifications"
                        className="w-5 h-5 text-[#478085]"
                      />
                      <span className="text-sm font-medium text-gray-700">
                        Alerts
                      </span>
                    </div>
                    {/* Optional notification badge */}
                    <Badge className="bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                      3
                    </Badge>
                  </div>
                </div>
              </Link>

              {/* Messages/Chat - only show when authenticated */}
              {isAuthenticated && (
                <Link href="/profile/messages" onClick={closeMobileMenu}>
                  <div className="bg-white border border-gray-200 rounded-lg p-3 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Icon
                          icon="material-symbols:chat"
                          className="w-5 h-5 text-[#478085]"
                        />
                        <span className="text-sm font-medium text-gray-700">
                          Messages
                        </span>
                      </div>
                      {/* Optional message badge */}
                      <Badge className="bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                        2
                      </Badge>
                    </div>
                  </div>
                </Link>
              )}

              {/* Post Ad Button */}
              <PostAdAuthGuard>
                <Link href="/postad" onClick={closeMobileMenu}>
                  <div className="bg-gradient-to-r from-[#478085] to-[#356267] text-white rounded-lg p-3 hover:from-[#356267] hover:to-[#2d5459] transition-all duration-300">
                    <div className="flex items-center justify-center gap-2">
                      <Icon icon="material-symbols:add" className="w-5 h-5" />
                      <span className="text-sm font-medium">Post Ad</span>
                    </div>
                  </div>
                </Link>
              </PostAdAuthGuard>
            </div>
          </div>

          {/* Main Navigation */}
          <div className="p-4">
            <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-3">
              Navigation
            </h3>
            <nav className="space-y-1">
              {navigationItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={closeMobileMenu}
                  className="flex items-center gap-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
                >
                  <Icon icon={item.icon} className="w-5 h-5 text-gray-500" />
                  <span>{item.label}</span>
                </Link>
              ))}
            </nav>
          </div>

          {/* Categories */}
          <div className="p-4 border-t border-gray-200">
            <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-3">
              Categories
            </h3>
            <nav className="space-y-1">
              {categories.map((category) => (
                <Link
                  key={category.id}
                  href={category.href}
                  onClick={closeMobileMenu}
                  className="flex items-center gap-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
                >
                  <Icon
                    icon={category.icon}
                    className="w-5 h-5 text-gray-500"
                  />
                  <span className="text-sm">{category.label}</span>
                </Link>
              ))}
            </nav>
          </div>

          {/* Language & Settings Section */}
          <div className="p-4 border-t border-gray-200">
            <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-3">
              Preferences
            </h3>
            <div className="space-y-2">
              {/* Language Selector */}
              <div className="flex items-center justify-between bg-white border border-gray-200 rounded-lg p-3">
                <div className="flex items-center gap-2">
                  <Icon
                    icon="material-symbols:language"
                    className="w-5 h-5 text-[#478085]"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    Language
                  </span>
                </div>
                <select className="text-sm text-gray-600 bg-transparent border-none outline-none">
                  <option value="en">English</option>
                  <option value="ne">नेपाली</option>
                  <option value="hi">हिंदी</option>
                </select>
              </div>

              {/* Theme Toggle */}
              <div className="flex items-center justify-between bg-white border border-gray-200 rounded-lg p-3">
                <div className="flex items-center gap-2">
                  <Icon
                    icon="material-symbols:dark-mode"
                    className="w-5 h-5 text-[#478085]"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    Dark Mode
                  </span>
                </div>
                <button className="w-10 h-6 bg-gray-200 rounded-full relative transition-colors">
                  <div className="w-4 h-4 bg-white rounded-full absolute top-1 left-1 transition-transform"></div>
                </button>
              </div>
            </div>
          </div>

          {/* Bottom Actions */}
          <div className="mt-auto p-4 border-t border-gray-200 space-y-3">
            {!isAuthenticated ? (
              <div className="space-y-2">
                <Link href="/login" onClick={closeMobileMenu}>
                  <Button className="w-full bg-[#478085] hover:bg-[#356267] text-white">
                    Sign In
                  </Button>
                </Link>
                <Link href="/register" onClick={closeMobileMenu}>
                  <Button variant="outline" className="w-full">
                    Sign Up
                  </Button>
                </Link>
              </div>
            ) : (
              <Button
                onClick={() => {
                  logout();
                  closeMobileMenu();
                }}
                variant="outline"
                className="w-full text-red-600 border-red-600 hover:bg-red-50"
              >
                <Icon icon="material-symbols:logout" className="w-4 h-4 mr-2" />
                Sign Out
              </Button>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
