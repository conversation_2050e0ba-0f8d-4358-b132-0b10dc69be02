import { Button } from "@/components/ui/button";

interface ReviewFormData {
  rating: number;
  comment: string;
}

interface ReviewFormSectionProps {
  formData: ReviewFormData;
  isSubmitting: boolean;
  onCommentChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onCancel: () => void;
}

export default function ReviewFormSection({
  formData,
  isSubmitting,
  onCommentChange,
  onCancel,
}: ReviewFormSectionProps) {
  return (
    <>
      {/* Review Text Section */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          Your Review
        </h2>
        <textarea
          value={formData.comment}
          onChange={onCommentChange}
          placeholder="Share Your Experience with this product and seller..."
          className="w-full h-40 p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
          maxLength={1000}
        />
        <div className="flex justify-between items-center mt-2">
          <div className="text-sm text-gray-500">
            {formData.comment.length}/1000 characters
          </div>
        </div>
      </div>

      {/* Review Guidelines */}
      <div className="bg-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-blue-900 mb-3">
          Review Guidelines:
        </h3>
        <ul className="space-y-2 text-sm text-blue-800">
          <li className="flex items-start gap-2">
            <span className="text-blue-600 mt-1">•</span>
            Only buyers who purchased this item can leave reviews
          </li>
          <li className="flex items-start gap-2">
            <span className="text-blue-600 mt-1">•</span>
            Be honest and constructive in your feedback
          </li>
          <li className="flex items-start gap-2">
            <span className="text-blue-600 mt-1">•</span>
            Focus on the product quality and seller service
          </li>
        </ul>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-4 pt-4">
        <Button
          type="submit"
          disabled={isSubmitting || formData.rating === 0}
          className="flex-1 bg-teal-600 hover:bg-teal-700 text-white py-3 text-lg font-medium"
        >
          {isSubmitting ? "Submitting..." : "Submit Review"}
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          className="px-8 py-3 text-lg font-medium border-gray-300 hover:bg-gray-50"
        >
          Cancel
        </Button>
      </div>
    </>
  );
}
