import type React from "react";

import { MAX_PHOTOS } from "../types";

interface PhotoUploadProgressProps {
  uploadedImages: string[];
}

export const PhotoUploadProgress: React.FC<PhotoUploadProgressProps> = ({
  uploadedImages,
}) => (
  <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border-2 border-blue-200">
    <div className="flex items-center justify-center mb-4">
      <div className="flex space-x-2">
        {Array.from({ length: MAX_PHOTOS }).map((_, index) => (
          <div
            key={index}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index < uploadedImages.length
                ? "bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg"
                : "bg-gray-300"
            }`}
          />
        ))}
      </div>
    </div>
  </div>
);
