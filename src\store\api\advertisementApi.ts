// Advertisement API endpoints using RTK Query

import { apiSlice } from "./index";
import type {
  CreateAdvertisementRequest,
  UpdateAdvertisementRequest,
  AdvertisementResponse,
  PaginatedAdvertisementResponse,
  QueryAdvertisementParams,
  ImageUploadResponse,
  AdvertisementAnalytics,
} from "@/types/advertisement";
import { buildQueryParams } from "@/constants/api-constants";

export const advertisementApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Create advertisement
    createAdvertisement: builder.mutation<
      AdvertisementResponse,
      CreateAdvertisementRequest
    >({
      query: (data) => ({
        url: "/advertisements",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Advertisement"],
    }),

    // Update advertisement
    updateAdvertisement: builder.mutation<
      AdvertisementResponse,
      { id: string; data: UpdateAdvertisementRequest }
    >({
      query: ({ id, data }) => ({
        url: `/advertisements/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Advertisement", id },
        "Advertisement",
      ],
    }),

    // Upload images for advertisement
    uploadAdvertisementImages: builder.mutation<
      ImageUploadResponse,
      { id: string; images: File[] }
    >({
      query: ({ id, images }) => {
        const formData = new FormData();
        images.forEach((image) => {
          formData.append("images", image);
        });

        return {
          url: `/advertisements/${id}/images`,
          method: "POST",
          body: formData,
          // No need for prepareHeaders - global config will detect FormData automatically
        };
      },
      invalidatesTags: (result, error, { id }) => [
        { type: "Advertisement", id },
      ],
    }),

    // Submit advertisement for approval
    submitAdvertisementForApproval: builder.mutation<
      AdvertisementResponse,
      string
    >({
      query: (id) => ({
        url: `/advertisements/${id}/submit-for-approval`,
        method: "POST",
      }),
      invalidatesTags: (result, error, id) => [
        { type: "Advertisement", id },
        "Advertisement",
      ],
    }),

    // Get advertisement by ID
    getAdvertisementById: builder.query<AdvertisementResponse, string>({
      query: (id) => `/advertisements/${id}`,
      providesTags: (result, error, id) => [{ type: "Advertisement", id }],
    }),

    // Get all advertisements with filtering and pagination
    getAdvertisements: builder.query<
      PaginatedAdvertisementResponse,
      QueryAdvertisementParams | void
    >({
      query: (params?: QueryAdvertisementParams) => ({
        url: "/advertisements",
        params: buildQueryParams(params ?? {}),
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({
                type: "Advertisement" as const,
                id,
              })),
              "Advertisement",
            ]
          : ["Advertisement"],
      // Add automatic refetching every 5 minutes to catch newly approved ads
      keepUnusedDataFor: 300, // 5 minutes
    }),

    // Get user's advertisements
    getUserAdvertisements: builder.query<
      PaginatedAdvertisementResponse,
      QueryAdvertisementParams | void
    >({
      query: (params: QueryAdvertisementParams = {}) => ({
        url: "/advertisements/my-ads",
        params: buildQueryParams(params),
      }),
      providesTags: ["Advertisement"],
    }),

    // Get featured advertisements
    getFeaturedAdvertisements: builder.query<
      PaginatedAdvertisementResponse,
      { page?: number; limit?: number } | void
    >({
      query: (params = {}) => ({
        url: "/advertisements/featured",
        params,
      }),
      providesTags: ["Advertisement"],
    }),

    // Get popular advertisements
    getPopularAdvertisements: builder.query<
      PaginatedAdvertisementResponse,
      { page?: number; limit?: number } | void
    >({
      query: (params = {}) => ({
        url: "/advertisements/popular",
        params,
      }),
      providesTags: ["Advertisement"],
    }),

    // Get recent advertisements
    getRecentAdvertisements: builder.query<
      PaginatedAdvertisementResponse,
      { page?: number; limit?: number } | void
    >({
      query: (params = {}) => ({
        url: "/advertisements/recent",
        params,
      }),
      providesTags: ["Advertisement"],
    }),

    // Get similar advertisements
    getSimilarAdvertisements: builder.query<
      AdvertisementResponse[],
      { id: string; limit?: number }
    >({
      query: ({ id, limit = 5 }) => ({
        url: `/advertisements/${id}/similar`,
        params: { limit },
      }),
      providesTags: (result, error, { id }) => [{ type: "Advertisement", id }],
    }),

    // Delete advertisement
    deleteAdvertisement: builder.mutation<{ message: string }, string>({
      query: (id) => ({
        url: `/advertisements/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, id) => [
        { type: "Advertisement", id },
        "Advertisement",
      ],
    }),

    // Mark advertisement as sold
    markAdvertisementAsSold: builder.mutation<AdvertisementResponse, string>({
      query: (id) => ({
        url: `/advertisements/${id}/mark-sold`,
        method: "POST",
      }),
      invalidatesTags: (result, error, id) => [
        { type: "Advertisement", id },
        "Advertisement",
      ],
    }),

    // Add to favorites
    addToFavorites: builder.mutation<{ message: string }, string>({
      query: (id) => ({
        url: `/advertisements/${id}/favorite`,
        method: "POST",
      }),
      invalidatesTags: (result, error, id) => [{ type: "Advertisement", id }],
    }),

    // Remove from favorites
    removeFromFavorites: builder.mutation<{ message: string }, string>({
      query: (id) => ({
        url: `/advertisements/${id}/favorite`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, id) => [{ type: "Advertisement", id }],
    }),

    // Get user's favorite advertisements
    getFavoriteAdvertisements: builder.query<
      PaginatedAdvertisementResponse,
      QueryAdvertisementParams | void
    >({
      query: (params = {}) => ({
        url: "/advertisements/favorites",
        params,
      }),
      providesTags: ["Advertisement"],
    }),

    // Get advertisement statistics
    getAdvertisementStats: builder.query<AdvertisementAnalytics, string>({
      query: (id) => `/advertisements/${id}/stats`,
      providesTags: (result, error, id) => [{ type: "Advertisement", id }],
    }),

    // Remove image from advertisement
    removeAdvertisementImage: builder.mutation<
      { message: string },
      { advertisementId: string; imageId: string }
    >({
      query: ({ advertisementId, imageId }) => ({
        url: `/advertisements/${advertisementId}/images/${imageId}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, { advertisementId }) => [
        { type: "Advertisement", id: advertisementId },
      ],
    }),

    // Set primary image
    setPrimaryImage: builder.mutation<
      { message: string },
      { advertisementId: string; imageId: string }
    >({
      query: ({ advertisementId, imageId }) => ({
        url: `/advertisements/${advertisementId}/images/${imageId}/primary`,
        method: "POST",
      }),
      invalidatesTags: (result, error, { advertisementId }) => [
        { type: "Advertisement", id: advertisementId },
      ],
    }),

    // Reorder images
    reorderImages: builder.mutation<
      { message: string },
      { advertisementId: string; imageIds: string[] }
    >({
      query: ({ advertisementId, imageIds }) => ({
        url: `/advertisements/${advertisementId}/images/reorder`,
        method: "POST",
        body: { imageIds },
      }),
      invalidatesTags: (result, error, { advertisementId }) => [
        { type: "Advertisement", id: advertisementId },
      ],
    }),

    // Search advertisements by location
    searchAdvertisementsByLocation: builder.query<
      PaginatedAdvertisementResponse,
      {
        latitude: number;
        longitude: number;
        radius?: number;
        page?: number;
        limit?: number;
      }
    >({
      query: ({ latitude, longitude, radius = 10, ...params }) => ({
        url: "/advertisements/search/location",
        params: {
          latitude,
          longitude,
          radius,
          ...params,
        },
      }),
      providesTags: ["Advertisement"],
    }),
  }),
});

// Export hooks for use in components
export const {
  useCreateAdvertisementMutation,
  useUpdateAdvertisementMutation,
  useUploadAdvertisementImagesMutation,
  useSubmitAdvertisementForApprovalMutation,
  useGetAdvertisementByIdQuery,
  useGetAdvertisementsQuery,
  useGetUserAdvertisementsQuery,
  useGetFeaturedAdvertisementsQuery,
  useGetPopularAdvertisementsQuery,
  useGetRecentAdvertisementsQuery,
  useGetSimilarAdvertisementsQuery,
  useDeleteAdvertisementMutation,
  useMarkAdvertisementAsSoldMutation,
  useAddToFavoritesMutation,
  useRemoveFromFavoritesMutation,
  useGetFavoriteAdvertisementsQuery,
  useGetAdvertisementStatsQuery,
  useRemoveAdvertisementImageMutation,
  useSetPrimaryImageMutation,
  useReorderImagesMutation,
  useSearchAdvertisementsByLocationQuery,
} = advertisementApi;
