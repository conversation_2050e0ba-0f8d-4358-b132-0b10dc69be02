"use client";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Image from "next/image";
import { Icon } from "@iconify/react";
import { BackButton } from "@/components";
import { RootState } from "@/store";
import {
  fetchAddresses,
  validateCart,
  processCheckout,
  setCheckoutStep,
  setSelectedAddress,
  setSelectedPaymentMethod,
  resetCheckout,
} from "@/store/slices/orderSlice";
import { fetchCart } from "@/store/slices/cartSlice";
import {
  PaymentMethod,
  CheckoutState,
  AddressResponseDto,
} from "@/types/orders";
import { toast } from "sonner";

const paymentMethods = [
  {
    id: "esewa" as PaymentMethod,
    name: "eSewa",
    img: "/images/esewa.png",
    description: "Digital wallet payment",
  },
  {
    id: "khalti" as PaymentMethod,
    name: "Khalti",
    img: "/images/khalti.png",
    description: "Digital wallet payment",
  },
  {
    id: "cod" as PaymentMethod,
    name: "Cash on Delivery",
    img: "/images/cod.png",
    description: "Pay when you receive",
  },
  {
    id: "bank_transfer" as PaymentMethod,
    name: "Bank Transfer",
    img: "/images/bank.png",
    description: "Direct bank transfer",
  },
];

export default function CheckoutPage() {
  const router = useRouter();
  const dispatch = useDispatch();

  // Redux state
  const {
    addresses,
    addressesLoading,
    checkout,
    cartValidation,
    validationLoading,
  } = useSelector((state: RootState) => state.order);
  const { cart, loading: cartLoading } = useSelector(
    (state: RootState) => state.cart
  );

  // Local state
  const [notes, setNotes] = useState("");
  const [couponCode, setCouponCode] = useState("");

  // Load initial data
  useEffect(() => {
    dispatch(fetchAddresses() as any);
    dispatch(fetchCart() as any);
    dispatch(validateCart() as any);
  }, [dispatch]);

  // Reset checkout state on mount
  useEffect(() => {
    dispatch(resetCheckout());
  }, [dispatch]);

  // Handle address selection
  const handleAddressSelect = (address: AddressResponseDto) => {
    dispatch(setSelectedAddress(address));
    if (checkout.step === "address") {
      dispatch(setCheckoutStep("payment"));
    }
  };

  // Handle payment method selection
  const handlePaymentMethodSelect = (method: PaymentMethod) => {
    dispatch(setSelectedPaymentMethod(method));
    if (checkout.step === "payment") {
      dispatch(setCheckoutStep("review"));
    }
  };

  // Handle checkout process
  const handleCheckout = async () => {
    if (!checkout.selectedAddress || !checkout.selectedPaymentMethod) {
      toast.error("Please select address and payment method");
      return;
    }

    if (!cart?.items?.length) {
      toast.error("Your cart is empty");
      return;
    }

    if (cartValidation && !cartValidation.isValid) {
      toast.error("Please resolve cart issues before checkout");
      return;
    }

    try {
      dispatch(setCheckoutStep("processing"));

      const checkoutData = {
        shippingAddressId: checkout.selectedAddress.id,
        paymentMethod: checkout.selectedPaymentMethod,
        notes: notes.trim() || undefined,
        couponCode: couponCode.trim() || undefined,
      };

      await dispatch(processCheckout(checkoutData) as any).unwrap();

      dispatch(setCheckoutStep("complete"));
      toast.success("Order placed successfully!");

      // Redirect after a short delay to show success state
      setTimeout(() => {
        router.push("/orders");
      }, 2000);
    } catch (error: any) {
      dispatch(setCheckoutStep("review"));
      toast.error(error.message || "Failed to place order");
    }
  };

  // Handle step navigation
  const handleStepChange = (step: CheckoutState["step"]) => {
    dispatch(setCheckoutStep(step));
  };

  // Render step content
  const renderStepContent = () => {
    switch (checkout.step) {
      case "address":
        return renderAddressStep();
      case "payment":
        return renderPaymentStep();
      case "review":
        return renderReviewStep();
      case "processing":
        return renderProcessingStep();
      case "complete":
        return renderCompleteStep();
      default:
        return renderAddressStep();
    }
  };

  const renderAddressStep = () => (
    <Card className="p-6">
      <h2 className="text-2xl font-semibold mb-8 flex items-center gap-2">
        <Icon
          icon="mdi:map-marker-outline"
          className="text-blue-500 text-2xl"
        />
        Select Shipping Address
      </h2>

      {addressesLoading ? (
        <div className="flex justify-center py-8">
          <Icon icon="mdi:loading" className="animate-spin text-2xl" />
        </div>
      ) : addresses.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">No addresses found</p>
          <Button onClick={() => router.push("/profile/addresses")}>
            Add Address
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          {addresses.map((address: AddressResponseDto) => (
            <div
              key={address.id}
              onClick={() => handleAddressSelect(address)}
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                checkout.selectedAddress?.id === address.id
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-200 hover:border-gray-300"
              }`}
            >
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-medium">
                    {address.firstName} {address.lastName}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {address.addressLine1}
                    {address.addressLine2 && `, ${address.addressLine2}`}
                  </p>
                  <p className="text-sm text-gray-600">
                    {address.city}, {address.state} {address.postalCode}
                  </p>
                  <p className="text-sm text-gray-600">{address.phone}</p>
                  {address.isDefault && (
                    <Badge variant="secondary" className="mt-2">
                      Default
                    </Badge>
                  )}
                </div>
                {checkout.selectedAddress?.id === address.id && (
                  <Icon
                    icon="mdi:check-circle"
                    className="text-blue-500 text-xl"
                  />
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </Card>
  );

  const renderPaymentStep = () => (
    <Card className="p-6">
      <h2 className="text-2xl font-semibold mb-8 flex items-center gap-2">
        <Icon
          icon="mdi:credit-card-outline"
          className="text-blue-500 text-2xl"
        />
        Select Payment Method
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {paymentMethods.map((method) => (
          <button
            key={method.id}
            onClick={() => handlePaymentMethodSelect(method.id)}
            className={`p-6 rounded-2xl border-2 transition-all duration-200 hover:shadow-md ${
              checkout.selectedPaymentMethod === method.id
                ? "border-blue-500 bg-blue-50"
                : "border-gray-200 bg-white hover:border-gray-300"
            }`}
          >
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden">
                <img
                  src={method.img}
                  alt={method.name}
                  className="w-full h-full object-contain"
                  onError={(e) => {
                    e.currentTarget.style.display = "none";
                  }}
                />
              </div>
              <div className="text-left">
                <h3 className="font-medium text-gray-900">{method.name}</h3>
                <p className="text-sm text-gray-500">{method.description}</p>
              </div>
              {checkout.selectedPaymentMethod === method.id && (
                <Icon
                  icon="mdi:check-circle"
                  className="text-blue-500 text-xl ml-auto"
                />
              )}
            </div>
          </button>
        ))}
      </div>

      {/* Additional options */}
      <div className="mt-8 space-y-4">
        <div>
          <Label htmlFor="coupon">Coupon Code (Optional)</Label>
          <Input
            id="coupon"
            value={couponCode}
            onChange={(e) => setCouponCode(e.target.value)}
            placeholder="Enter coupon code"
            className="mt-1"
          />
        </div>
        <div>
          <Label htmlFor="notes">Order Notes (Optional)</Label>
          <Textarea
            id="notes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Any special instructions..."
            className="mt-1"
            rows={3}
          />
        </div>
      </div>
    </Card>
  );

  const renderReviewStep = () => (
    <Card className="p-6">
      <h2 className="text-2xl font-semibold mb-8 flex items-center gap-2">
        <Icon
          icon="mdi:clipboard-check-outline"
          className="text-blue-500 text-2xl"
        />
        Review Order
      </h2>

      <div className="space-y-6">
        {/* Selected Address */}
        <div>
          <h3 className="font-medium mb-2">Shipping Address</h3>
          {checkout.selectedAddress && (
            <div className="p-4 bg-gray-50 rounded-lg">
              <p className="font-medium">
                {checkout.selectedAddress.firstName}{" "}
                {checkout.selectedAddress.lastName}
              </p>
              <p className="text-sm text-gray-600">
                {checkout.selectedAddress.addressLine1}
                {checkout.selectedAddress.addressLine2 &&
                  `, ${checkout.selectedAddress.addressLine2}`}
              </p>
              <p className="text-sm text-gray-600">
                {checkout.selectedAddress.city},{" "}
                {checkout.selectedAddress.state}{" "}
                {checkout.selectedAddress.postalCode}
              </p>
              <p className="text-sm text-gray-600">
                {checkout.selectedAddress.phone}
              </p>
            </div>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleStepChange("address")}
            className="mt-2"
          >
            Change Address
          </Button>
        </div>

        {/* Selected Payment Method */}
        <div>
          <h3 className="font-medium mb-2">Payment Method</h3>
          {checkout.selectedPaymentMethod && (
            <div className="p-4 bg-gray-50 rounded-lg">
              <p className="font-medium">
                {
                  paymentMethods.find(
                    (m) => m.id === checkout.selectedPaymentMethod
                  )?.name
                }
              </p>
            </div>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleStepChange("payment")}
            className="mt-2"
          >
            Change Payment Method
          </Button>
        </div>

        {/* Order Notes */}
        {notes && (
          <div>
            <h3 className="font-medium mb-2">Order Notes</h3>
            <div className="p-4 bg-gray-50 rounded-lg">
              <p className="text-sm">{notes}</p>
            </div>
          </div>
        )}

        {/* Coupon Code */}
        {couponCode && (
          <div>
            <h3 className="font-medium mb-2">Coupon Code</h3>
            <div className="p-4 bg-gray-50 rounded-lg">
              <p className="text-sm font-mono">{couponCode}</p>
            </div>
          </div>
        )}

        {/* Cart Validation */}
        {cartValidation && !cartValidation.isValid && (
          <Alert variant="destructive">
            <Icon icon="mdi:alert-circle" className="h-4 w-4" />
            <AlertDescription>
              <ul className="list-disc list-inside">
                {cartValidation.errors.map((error: string, index: number) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}
      </div>
    </Card>
  );

  const renderProcessingStep = () => (
    <Card className="p-6">
      <div className="text-center py-8">
        <Icon
          icon="mdi:loading"
          className="animate-spin text-4xl text-blue-500 mx-auto mb-4"
        />
        <h2 className="text-2xl font-semibold mb-2">Processing Your Order</h2>
        <p className="text-gray-600">
          Please wait while we process your order...
        </p>
      </div>
    </Card>
  );

  const renderCompleteStep = () => (
    <Card className="p-6">
      <div className="text-center py-8">
        <Icon
          icon="mdi:check-circle"
          className="text-4xl text-green-500 mx-auto mb-4"
        />
        <h2 className="text-2xl font-semibold mb-2">
          Order Placed Successfully!
        </h2>
        <p className="text-gray-600 mb-6">
          Your order has been placed and you will receive a confirmation email
          shortly.
        </p>
        {checkout.orderData && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <p className="font-medium">
              Order Number: {checkout.orderData.orderNumber}
            </p>
            <p className="text-sm text-gray-600">
              Total: {checkout.orderData.currency}{" "}
              {checkout.orderData.totalAmount.toLocaleString()}
            </p>
          </div>
        )}
        <div className="space-x-4">
          <Button onClick={() => router.push("/orders")}>View Orders</Button>
          <Button variant="outline" onClick={() => router.push("/")}>
            Continue Shopping
          </Button>
        </div>
      </div>
    </Card>
  );

  // Early return for empty cart
  if (!cartLoading && !cart?.items?.length) {
    return (
      <div className="min-h-screen bg-gray-50 p-4 md:p-8">
        <div className="max-w-6xl mx-auto">
          <BackButton onClick={() => router.back()} size="lg" />
          <div className="flex flex-col items-center justify-center py-16">
            <Icon
              icon="mdi:cart-outline"
              className="text-6xl text-gray-400 mb-4"
            />
            <h2 className="text-2xl font-semibold text-gray-600 mb-2">
              Your cart is empty
            </h2>
            <p className="text-gray-500 mb-6">
              Add some items to your cart to proceed with checkout
            </p>
            <Button
              onClick={() => router.push("/")}
              className="bg-[#478085] text-white"
            >
              Continue Shopping
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-8">
      <div className="max-w-6xl mx-auto">
        <BackButton onClick={() => router.back()} size="lg" />

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4">
            {[
              { key: "address", label: "Address", icon: "mdi:map-marker" },
              { key: "payment", label: "Payment", icon: "mdi:credit-card" },
              { key: "review", label: "Review", icon: "mdi:clipboard-check" },
            ].map((step, index) => (
              <div key={step.key} className="flex items-center">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    checkout.step === step.key
                      ? "bg-blue-500 text-white"
                      : ["address", "payment", "review"].indexOf(
                          checkout.step
                        ) > index
                      ? "bg-green-500 text-white"
                      : "bg-gray-200 text-gray-500"
                  }`}
                >
                  <Icon icon={step.icon} className="text-lg" />
                </div>
                <span className="ml-2 text-sm font-medium">{step.label}</span>
                {index < 2 && (
                  <Icon
                    icon="mdi:chevron-right"
                    className="mx-4 text-gray-400"
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">{renderStepContent()}</div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl flex items-center gap-2">
                  <Icon
                    icon="mdi:cart-outline"
                    className="text-green-600 text-xl"
                  />
                  Order Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {cartLoading ? (
                  <div className="flex justify-center py-4">
                    <Icon icon="mdi:loading" className="animate-spin text-xl" />
                  </div>
                ) : cart?.items?.length ? (
                  <>
                    {/* Cart Items */}
                    <div className="space-y-4">
                      {cart.items.slice(0, 3).map((item: any) => (
                        <div key={item.id} className="flex gap-3">
                          <div className="w-12 h-12 bg-gray-200 rounded-lg flex-shrink-0">
                            {(item.productImage ||
                              item.product?.images?.[0]) && (
                              <Image
                                src={
                                  item.productImage ||
                                  item.product?.images?.[0] ||
                                  "/placeholder-image.jpg"
                                }
                                alt={
                                  item.productName ||
                                  item.product?.title ||
                                  "Product"
                                }
                                width={48}
                                height={48}
                                className="w-full h-full object-cover rounded-lg"
                              />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-medium text-sm leading-tight mb-1 truncate">
                              {item.productName ||
                                item.product?.title ||
                                "Product"}
                            </h3>
                            <p className="text-xs text-gray-500 mb-1">
                              by{" "}
                              {item.sellerName ||
                                item.product?.seller?.name ||
                                "Seller"}
                            </p>
                            <div className="flex items-center gap-2">
                              <span className="font-semibold text-sm">
                                {cart.currency}{" "}
                                {(
                                  item.price ||
                                  item.product?.price ||
                                  0
                                ).toLocaleString()}
                              </span>
                              <Badge variant="secondary" className="text-xs">
                                × {item.quantity}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      ))}
                      {cart.items.length > 3 && (
                        <p className="text-sm text-gray-500 text-center">
                          +{cart.items.length - 3} more items
                        </p>
                      )}
                    </div>

                    <Separator />

                    {/* Price Breakdown */}
                    <div className="space-y-3">
                      <div className="flex justify-between text-sm">
                        <span>Subtotal ({cart.totalItems} items):</span>
                        <span>
                          {cart.currency}{" "}
                          {(
                            cart.totalAmount ||
                            cart.totalPrice ||
                            0
                          ).toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Shipping Fee:</span>
                        <span>Free</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Tax:</span>
                        <span>{cart.currency} 0</span>
                      </div>

                      <Separator />

                      <div className="flex justify-between font-semibold text-lg">
                        <span>Total:</span>
                        <span className="text-blue-600">
                          {cart.currency}{" "}
                          {(
                            cart.totalAmount ||
                            cart.totalPrice ||
                            0
                          ).toLocaleString()}
                        </span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="space-y-3">
                      {checkout.step === "review" && (
                        <Button
                          className="w-full bg-[#478085] text-white py-3 rounded-lg font-medium"
                          onClick={handleCheckout}
                          disabled={
                            checkout.loading || !cartValidation?.isValid
                          }
                        >
                          {checkout.loading ? (
                            <>
                              <Icon
                                icon="mdi:loading"
                                className="animate-spin mr-2"
                              />
                              Processing...
                            </>
                          ) : (
                            "Place Order"
                          )}
                        </Button>
                      )}

                      {checkout.step !== "complete" &&
                        checkout.step !== "processing" && (
                          <Button
                            variant="outline"
                            className="w-full"
                            onClick={() => router.push("/cart")}
                          >
                            Back to Cart
                          </Button>
                        )}
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">Your cart is empty</p>
                    <Button
                      variant="outline"
                      onClick={() => router.push("/")}
                      className="mt-4"
                    >
                      Continue Shopping
                    </Button>
                  </div>
                )}

                {/* Validation Errors */}
                {validationLoading && (
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <Icon icon="mdi:loading" className="animate-spin" />
                    Validating cart...
                  </div>
                )}

                {cartValidation && !cartValidation.isValid && (
                  <Alert variant="destructive">
                    <Icon icon="mdi:alert-circle" className="h-4 w-4" />
                    <AlertDescription>
                      Please resolve cart issues before checkout
                    </AlertDescription>
                  </Alert>
                )}

                {checkout.error && (
                  <Alert variant="destructive">
                    <Icon icon="mdi:alert-circle" className="h-4 w-4" />
                    <AlertDescription>{checkout.error}</AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
