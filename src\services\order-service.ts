import { apiClient, apiRequest, API_ENDPOINTS } from "@/lib/api";
import {
  AddressResponseDto,
  CreateAddressDto,
  UpdateAddressDto,
  CheckoutDto,
  CartValidationResponse,
  OrderResponseDto,
  OrderFilterDto,
  PaginatedOrdersResponse,
  OrderStatusUpdateDto,
} from "@/types/orders";

/**
 * Order Service
 * Handles all order-related API calls including addresses, checkout, and order management
 */
export class OrderService {
  // ===== ADDRESS MANAGEMENT =====

  /**
   * Get all user addresses
   */
  static async getUserAddresses(): Promise<AddressResponseDto[]> {
    return await apiRequest<AddressResponseDto[]>(() =>
      apiClient.get(API_ENDPOINTS.ADDRESSES.LIST)
    );
  }

  /**
   * Create a new address
   */
  static async createAddress(data: CreateAddressDto): Promise<AddressResponseDto> {
    return await apiRequest<AddressResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.ADDRESSES.CREATE, data)
    );
  }

  /**
   * Update an existing address
   */
  static async updateAddress(
    addressId: string,
    data: UpdateAddressDto
  ): Promise<AddressResponseDto> {
    return await apiRequest<AddressResponseDto>(() =>
      apiClient.put(API_ENDPOINTS.ADDRESSES.UPDATE(addressId), data)
    );
  }

  /**
   * Delete an address
   */
  static async deleteAddress(addressId: string): Promise<void> {
    return await apiRequest<void>(() =>
      apiClient.delete(API_ENDPOINTS.ADDRESSES.DELETE(addressId))
    );
  }

  /**
   * Set an address as default
   */
  static async setDefaultAddress(addressId: string): Promise<AddressResponseDto> {
    return await apiRequest<AddressResponseDto>(() =>
      apiClient.put(API_ENDPOINTS.ADDRESSES.SET_DEFAULT(addressId))
    );
  }

  // ===== CHECKOUT OPERATIONS =====

  /**
   * Validate cart before checkout
   */
  static async validateCart(): Promise<CartValidationResponse> {
    return await apiRequest<CartValidationResponse>(() =>
      apiClient.post(API_ENDPOINTS.CHECKOUT.VALIDATE)
    );
  }

  /**
   * Process checkout and create order
   */
  static async processCheckout(data: CheckoutDto): Promise<OrderResponseDto> {
    return await apiRequest<OrderResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.CHECKOUT.PROCESS, data)
    );
  }

  // ===== ORDER MANAGEMENT =====

  /**
   * Get user orders with filtering and pagination
   */
  static async getUserOrders(
    filters: OrderFilterDto = {}
  ): Promise<PaginatedOrdersResponse> {
    const params = {
      page: 1,
      limit: 10,
      sortBy: 'createdAt',
      sortOrder: 'desc' as const,
      ...filters,
    };

    return await apiRequest<PaginatedOrdersResponse>(() =>
      apiClient.get(API_ENDPOINTS.ORDERS.LIST, { params })
    );
  }

  /**
   * Get order by ID
   */
  static async getOrderById(orderId: string): Promise<OrderResponseDto> {
    return await apiRequest<OrderResponseDto>(() =>
      apiClient.get(API_ENDPOINTS.ORDERS.BY_ID(orderId))
    );
  }

  /**
   * Cancel an order
   */
  static async cancelOrder(orderId: string): Promise<OrderResponseDto> {
    return await apiRequest<OrderResponseDto>(() =>
      apiClient.post(API_ENDPOINTS.ORDERS.CANCEL(orderId))
    );
  }

  /**
   * Update order status (Admin/Vendor only)
   */
  static async updateOrderStatus(
    orderId: string,
    data: OrderStatusUpdateDto
  ): Promise<OrderResponseDto> {
    return await apiRequest<OrderResponseDto>(() =>
      apiClient.put(API_ENDPOINTS.ORDERS.UPDATE_STATUS(orderId), data)
    );
  }

  // ===== HELPER METHODS =====

  /**
   * Get default address for user
   */
  static async getDefaultAddress(): Promise<AddressResponseDto | null> {
    try {
      const addresses = await this.getUserAddresses();
      return addresses.find(address => address.isDefault) || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if user has any addresses
   */
  static async hasAddresses(): Promise<boolean> {
    try {
      const addresses = await this.getUserAddresses();
      return addresses.length > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get order summary for user
   */
  static async getOrderSummary(): Promise<{
    totalOrders: number;
    pendingOrders: number;
    completedOrders: number;
    cancelledOrders: number;
    totalSpent: number;
    currency: string;
  }> {
    try {
      const ordersResponse = await this.getUserOrders({ limit: 1000 }); // Get all orders for summary
      const orders = ordersResponse.orders;

      const summary = {
        totalOrders: orders.length,
        pendingOrders: orders.filter(order => 
          ['pending', 'confirmed', 'processing'].includes(order.status)
        ).length,
        completedOrders: orders.filter(order => 
          order.status === 'delivered'
        ).length,
        cancelledOrders: orders.filter(order => 
          ['cancelled', 'refunded'].includes(order.status)
        ).length,
        totalSpent: orders
          .filter(order => order.status === 'delivered')
          .reduce((sum, order) => sum + order.totalAmount, 0),
        currency: orders.length > 0 ? orders[0].currency : 'NPR',
      };

      return summary;
    } catch (error) {
      return {
        totalOrders: 0,
        pendingOrders: 0,
        completedOrders: 0,
        cancelledOrders: 0,
        totalSpent: 0,
        currency: 'NPR',
      };
    }
  }

  /**
   * Get recent orders (last 5)
   */
  static async getRecentOrders(): Promise<OrderResponseDto[]> {
    try {
      const response = await this.getUserOrders({ limit: 5 });
      return response.orders;
    } catch (error) {
      return [];
    }
  }

  /**
   * Check if order can be cancelled
   */
  static canCancelOrder(order: OrderResponseDto): boolean {
    return ['pending', 'confirmed'].includes(order.status);
  }

  /**
   * Check if order can be tracked
   */
  static canTrackOrder(order: OrderResponseDto): boolean {
    return ['shipped', 'processing'].includes(order.status) && !!order.trackingNumber;
  }

  /**
   * Format order status for display
   */
  static formatOrderStatus(status: string): string {
    const statusMap: Record<string, string> = {
      pending: 'Pending',
      confirmed: 'Confirmed',
      processing: 'Processing',
      shipped: 'Shipped',
      delivered: 'Delivered',
      cancelled: 'Cancelled',
      refunded: 'Refunded',
    };
    return statusMap[status] || status;
  }

  /**
   * Format payment status for display
   */
  static formatPaymentStatus(status: string): string {
    const statusMap: Record<string, string> = {
      pending: 'Pending',
      processing: 'Processing',
      completed: 'Completed',
      failed: 'Failed',
      refunded: 'Refunded',
    };
    return statusMap[status] || status;
  }

  /**
   * Format payment method for display
   */
  static formatPaymentMethod(method: string): string {
    const methodMap: Record<string, string> = {
      esewa: 'eSewa',
      khalti: 'Khalti',
      cod: 'Cash on Delivery',
      bank_transfer: 'Bank Transfer',
    };
    return methodMap[method] || method;
  }

  /**
   * Calculate estimated delivery date
   */
  static calculateEstimatedDelivery(orderDate: Date, shippingDays: number = 3): Date {
    const deliveryDate = new Date(orderDate);
    deliveryDate.setDate(deliveryDate.getDate() + shippingDays);
    return deliveryDate;
  }

  /**
   * Format address for display
   */
  static formatAddress(address: AddressResponseDto): string {
    const parts = [
      address.addressLine1,
      address.addressLine2,
      address.city,
      address.state,
      address.postalCode,
      address.country,
    ].filter(Boolean);
    
    return parts.join(', ');
  }

  /**
   * Validate address data
   */
  static validateAddress(address: CreateAddressDto | UpdateAddressDto): string[] {
    const errors: string[] = [];

    if ('firstName' in address && !address.firstName?.trim()) {
      errors.push('First name is required');
    }
    if ('lastName' in address && !address.lastName?.trim()) {
      errors.push('Last name is required');
    }
    if ('addressLine1' in address && !address.addressLine1?.trim()) {
      errors.push('Address line 1 is required');
    }
    if ('city' in address && !address.city?.trim()) {
      errors.push('City is required');
    }
    if ('state' in address && !address.state?.trim()) {
      errors.push('State is required');
    }
    if ('postalCode' in address && !address.postalCode?.trim()) {
      errors.push('Postal code is required');
    }
    if ('country' in address && !address.country?.trim()) {
      errors.push('Country is required');
    }
    if ('phone' in address && !address.phone?.trim()) {
      errors.push('Phone number is required');
    }

    return errors;
  }

  /**
   * Validate checkout data
   */
  static validateCheckout(data: CheckoutDto): string[] {
    const errors: string[] = [];

    if (!data.shippingAddressId?.trim()) {
      errors.push('Shipping address is required');
    }
    if (!data.paymentMethod) {
      errors.push('Payment method is required');
    }
    if (data.notes && data.notes.length > 500) {
      errors.push('Notes cannot exceed 500 characters');
    }
    if (data.couponCode && data.couponCode.length > 50) {
      errors.push('Coupon code cannot exceed 50 characters');
    }

    return errors;
  }
}

export default OrderService;
