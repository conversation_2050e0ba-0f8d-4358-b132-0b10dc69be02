# SastoBazar Backend API Integration Guide

## Overview

This document provides comprehensive information about integrating the SastoBazar backend APIs with the frontend e-commerce application. The backend is built with NestJS and provides RESTful APIs with JWT authentication.

## Base Configuration

### API Base URL
- **Development**: `http://localhost:3000/api/v1`
- **Production**: `https://sasto-api.webstudiomatrix.com/api/v1`

### Authentication
- **Type**: Bearer <PERSON>ken (JWT)
- **Header**: `Authorization: Bearer <token>`
- **Token Storage**: localStorage (`auth_token`)

### CORS Configuration
The backend is configured to accept requests from:
- `http://localhost:3000`
- `https://ecom.webstudiomatrix.com`
- `https://www.ecom.webstudiomatrix.com`

## API Endpoints Overview

### 1. Authentication APIs (`/auth`)

#### POST `/auth/register`
Register a new user with optional role selection.

**Request Body:**
```typescript
{
  username: string;        // 3-50 chars, alphanumeric + underscore
  email: string;          // Valid email address
  password: string;       // Min 8 chars, must contain uppercase, lowercase, number, special char
  firstName?: string;     // Optional, max 100 chars
  lastName?: string;      // Optional, max 100 chars
  phone?: string;         // Optional, valid phone number format
  role?: 'USER' | 'VENDOR'; // Optional, defaults to USER
}
```

**Response:**
```typescript
{
  accessToken: string;
  refreshToken: string;
  user: UserProfileResponseDto;
}
```

#### POST `/auth/login`
Login with username/email and password.

**Request Body:**
```typescript
{
  usernameOrEmail: string;
  password: string;
}
```

**Response:** Same as register

#### GET `/auth/profile`
Get current user profile (requires authentication).

#### POST `/auth/logout`
Logout user (requires authentication).

#### POST `/auth/refresh`
Refresh access token.

**Request Body:**
```typescript
{
  refreshToken: string;
}
```

#### POST `/auth/forgot-password`
Request password reset email.

**Request Body:**
```typescript
{
  email: string;
}
```

#### POST `/auth/reset-password`
Reset password with token.

**Request Body:**
```typescript
{
  token: string;
  password: string;
}
```

#### POST `/auth/change-password`
Change password for authenticated user.

**Request Body:**
```typescript
{
  currentPassword: string;
  newPassword: string;
}
```

### 2. Categories APIs (`/categories`)

#### GET `/categories`
Get all categories with subcategories.

**Query Parameters:**
- `includeInactive?: boolean` - Include inactive categories (admin only)

**Response:**
```typescript
CategoryResponseDto[] = {
  id: string;
  name: string;
  slug: string;
  description?: string;
  iconUrl?: string;
  imageUrl?: string;
  isActive: boolean;
  sortOrder: number;
  subcategories: SubcategoryResponseDto[];
  createdAt: string;
  updatedAt: string;
}[]
```

#### GET `/categories/:id`
Get category by ID.

#### GET `/categories/slug/:slug`
Get category by slug.

#### GET `/categories/:categoryId/subcategories`
Get subcategories by category ID.

#### Admin Only Endpoints:
- `POST /categories` - Create category
- `PATCH /categories/:id` - Update category
- `DELETE /categories/:id` - Delete category
- `POST /categories/subcategories` - Create subcategory
- `PATCH /categories/subcategories/:id` - Update subcategory
- `DELETE /categories/subcategories/:id` - Delete subcategory

### 3. Advertisements APIs (`/advertisements`)

#### GET `/advertisements`
Get all advertisements with filtering and pagination.

**Query Parameters:**
```typescript
{
  page?: number;           // Default: 1
  limit?: number;          // Default: 20, max: 100
  search?: string;         // Search in title and description
  categoryId?: string;     // Filter by category
  subcategoryId?: string;  // Filter by subcategory
  minPrice?: number;       // Minimum price filter
  maxPrice?: number;       // Maximum price filter
  condition?: 'new' | 'used' | 'refurbished';
  location?: string;       // Location filter
  city?: string;          // City filter
  state?: string;         // State filter
  sortBy?: 'createdAt' | 'price' | 'title' | 'views';
  sortOrder?: 'ASC' | 'DESC';
  status?: 'draft' | 'pending_approval' | 'active' | 'sold' | 'expired' | 'rejected' | 'suspended';
  userId?: string;        // Filter by user (for my-ads)
  isFeatured?: boolean;   // Filter featured ads
  isUrgent?: boolean;     // Filter urgent ads
}
```

**Response:**
```typescript
{
  data: AdvertisementResponseDto[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
```

#### POST `/advertisements`
Create new advertisement (requires authentication).

**Request Body:**
```typescript
{
  title: string;              // 5-200 chars
  description: string;        // 10-5000 chars
  categoryId: string;         // UUID
  subcategoryId?: string;     // UUID
  price?: number;             // Min 0, max 2 decimal places
  currency?: 'NPR' | 'USD';   // Default: NPR
  condition?: 'new' | 'used' | 'refurbished';
  location?: {
    location?: string;        // Max 200 chars
    city?: string;           // Max 100 chars
    state?: string;          // Max 100 chars
    latitude?: number;       // -90 to 90
    longitude?: number;      // -180 to 180
  };
  negotiable?: boolean;       // Default: true
  inventoryQuantity?: number; // Min 1, default: 1
  youtubeVideoUrl?: string;   // Valid URL
  isFeatured?: boolean;       // Default: false
  isUrgent?: boolean;         // Default: false
}
```

#### GET `/advertisements/:id`
Get advertisement by ID (public).

#### PATCH `/advertisements/:id`
Update advertisement (requires authentication, owner or admin only).

#### DELETE `/advertisements/:id`
Delete advertisement (requires authentication, owner or admin only).

#### GET `/advertisements/my-ads`
Get current user's advertisements (requires authentication).

#### GET `/advertisements/favorites`
Get user's favorite advertisements (requires authentication).

#### POST `/advertisements/:id/favorite`
Add advertisement to favorites (requires authentication).

#### DELETE `/advertisements/:id/favorite`
Remove advertisement from favorites (requires authentication).

#### POST `/advertisements/:id/images`
Upload images for advertisement (requires authentication, owner only).

**Request:** Multipart form data with `images` field (max 8 files).

#### POST `/advertisements/:id/submit-for-approval`
Submit advertisement for approval (requires authentication, owner only).

#### POST `/advertisements/:id/mark-sold`
Mark advertisement as sold (requires authentication, owner only).

#### GET `/advertisements/:id/stats`
Get advertisement statistics (requires authentication, owner or admin only).

#### GET `/advertisements/featured`
Get featured advertisements (public).

#### GET `/advertisements/popular`
Get popular advertisements (public).

#### GET `/advertisements/recent`
Get recent advertisements (public).

#### GET `/advertisements/:id/similar`
Get similar advertisements (public).

#### GET `/advertisements/search/location`
Search advertisements by location (public).

**Query Parameters:**
- `latitude: number`
- `longitude: number`
- `radius: number` (in kilometers)

### 4. Cart APIs (`/cart`)

All cart endpoints require authentication.

#### GET `/cart`
Get user's cart.

#### POST `/cart/items`
Add item to cart.

**Request Body:**
```typescript
{
  advertisementId: string;
  quantity: number;
}
```

#### PUT `/cart/items/:itemId`
Update cart item quantity.

**Request Body:**
```typescript
{
  quantity: number;
}
```

#### DELETE `/cart/items/:itemId`
Remove item from cart.

#### DELETE `/cart/items/bulk`
Remove multiple items from cart.

**Request Body:**
```typescript
{
  itemIds: string[];
}
```

#### DELETE `/cart`
Clear entire cart.

#### POST `/cart/sync`
Sync cart with server (update availability).

### 5. Users APIs (`/users`)

All user endpoints require authentication.

#### GET `/users/profile`
Get current user profile.

#### GET `/users/profile/:username`
Get user profile by username.

#### PUT `/users/profile`
Update user profile.

**Request Body:**
```typescript
{
  firstName?: string;
  lastName?: string;
  bio?: string;
  profilePictureUrl?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
}
```

### 6. File Upload APIs (`/files`)

#### POST `/files/upload`
Upload files (requires authentication).

**Request:** Multipart form data with file field.

**Response:**
```typescript
{
  successful: {
    originalName: string;
    filename: string;
    url: string;
    size: number;
    mimeType: string;
  }[];
  failed: {
    originalName: string;
    error: string;
  }[];
}
```

## Status Values

### Advertisement Status
- `draft` - Draft advertisement
- `pending_approval` - Submitted for approval
- `active` - Active and visible
- `sold` - Marked as sold
- `expired` - Expired advertisement
- `rejected` - Rejected by admin
- `suspended` - Suspended by admin

### User Status
- `active` - Active user
- `suspended` - Suspended user
- `banned` - Banned user
- `pending_verification` - Pending email verification

## Error Handling

### Standard Error Response
```typescript
{
  statusCode: number;
  message: string | string[];
  error?: string;
  timestamp: string;
  path: string;
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `409` - Conflict (duplicate data)
- `422` - Unprocessable Entity (business logic errors)
- `500` - Internal Server Error

## Rate Limiting

The API implements rate limiting to prevent abuse. Default limits:
- 100 requests per 15 minutes per IP
- Stricter limits for sensitive endpoints (login, register)

## Pagination

Most list endpoints support pagination:
```typescript
{
  page: number;        // Current page (1-based)
  limit: number;       // Items per page
  total: number;       // Total items
  totalPages: number;  // Total pages
  hasNext: boolean;    // Has next page
  hasPrev: boolean;    // Has previous page
}
```

## Next Steps

1. Update frontend API configuration
2. Implement authentication service
3. Create API service classes for each module
4. Update Redux store to work with backend APIs
5. Implement error handling and loading states
6. Add comprehensive testing
