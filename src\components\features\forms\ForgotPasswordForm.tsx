"use client";

import { useState } from "react";
import Link from "next/link";
import { Icon } from "@iconify/react";

import { LoadingButton } from "@/components/ui";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AuthService } from "@/services/auth-service";
import { ApiError } from "@/lib/api";

export default function ForgotPasswordForm() {
  const [email, setEmail] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await AuthService.forgotPassword(email);
      setIsSubmitted(true);
    } catch (err) {
      if (err instanceof ApiError) {
        setError(err.message);
      } else {
        setError("Failed to send reset email. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="mx-auto max-w-md space-y-6 p-6">
        <div className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-teal-100">
            <Icon icon="lucide:mail" className="h-8 w-8 text-teal-600" />
          </div>
          <h1 className="text-3xl font-semibold text-gray-900">
            Check Your Email
          </h1>
          <p className="mt-4 text-gray-600">
            We&apos;ve sent a password reset link to{" "}
            <span className="font-medium text-gray-900">{email}</span>
          </p>
        </div>

        <div className="space-y-4">
          <p className="text-sm text-gray-600 text-center">
            Didn&apos;t receive the email? Check your spam folder or{" "}
            <button
              onClick={() => {
                setIsSubmitted(false);
                setError(null);
              }}
              className="text-teal-600 hover:text-teal-700 underline"
            >
              try again
            </button>
          </p>

          <div className="text-center">
            <Link
              href="/login"
              className="inline-flex items-center text-sm text-gray-600 hover:text-gray-800"
            >
              <Icon icon="lucide:arrow-left" className="mr-2 h-4 w-4" />
              Back to Login
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-md space-y-6 p-6">
      <div className="text-center">
        <h1 className="text-3xl font-semibold text-gray-900">
          Forgot Password?
        </h1>
        <p className="mt-4 text-gray-600">
          Enter your email address and we&apos;ll send you a link to reset your
          password.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-5">
        {error && (
          <div className="flex items-center gap-2 p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
            <Icon
              icon="lucide:alert-circle"
              className="h-4 w-4 flex-shrink-0"
            />
            <span>{error}</span>
          </div>
        )}

        <div className="space-y-3">
          <Label htmlFor="email" className="text-md font-medium text-gray-700">
            Email Address
          </Label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => {
              setEmail(e.target.value);
              if (error) setError(null);
            }}
            placeholder="<EMAIL>"
            className="h-12 text-lg mt-2 border-gray-200 focus:border-gray-300 focus:ring-0"
            required
          />
        </div>

        <LoadingButton
          type="submit"
          isLoading={isLoading}
          loadingText="Sending..."
          disabled={!email}
          className="h-12 w-full bg-teal-600 text-white hover:bg-teal-700"
        >
          Send Reset Link
        </LoadingButton>
      </form>

      <div className="text-center">
        <Link
          href="/login"
          className="inline-flex items-center text-sm text-gray-600 hover:text-gray-800"
        >
          <Icon icon="lucide:arrow-left" className="mr-2 h-4 w-4" />
          Back to Login
        </Link>
      </div>
    </div>
  );
}
