import { apiSlice } from "./index";
import {
  User,
  UpdateProfileRequest,
  UpdatePrivacySettingsRequest,
  UpdateNotificationPreferencesRequest,
  PrivacySettings,
  NotificationPreferences,
} from "@/types/auth";

export const userApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get current user profile
    getUserProfile: builder.query<User, void>({
      query: () => "/users/profile",
      providesTags: ["User"],
    }),

    // Get user by username for public profiles
    getUserByUsername: builder.query<User, string>({
      query: (username) => `/users/profile/${username}`,
      providesTags: (result, error, username) => [
        { type: "User", id: username },
      ],
    }),

    // Update current user profile
    updateProfile: builder.mutation<User, UpdateProfileRequest>({
      query: (data) => ({
        url: "/users/profile",
        method: "PUT",
        body: data,
      }),
      invalidatesTags: ["User"],
    }),

    // Upload profile picture
    uploadProfilePicture: builder.mutation<
      { profilePictureUrl: string },
      FormData
    >({
      query: (formData) => ({
        url: "/users/profile/picture",
        method: "POST",
        body: formData,
        // Don't set content-type header - let browser set it with boundary
        prepareHeaders: (headers: Headers) => {
          // Remove content-type to let browser set multipart/form-data with boundary
          headers.delete("content-type");
          return headers;
        },
      }),
      invalidatesTags: ["User"],
    }),

    // Get user privacy settings
    getPrivacySettings: builder.query<PrivacySettings, void>({
      query: () => "/users/privacy-settings",
      providesTags: ["User"],
    }),

    // Update user privacy settings
    updatePrivacySettings: builder.mutation<
      PrivacySettings,
      UpdatePrivacySettingsRequest
    >({
      query: (data) => ({
        url: "/users/privacy-settings",
        method: "PUT",
        body: data,
      }),
      invalidatesTags: ["User"],
    }),

    // Get user notification preferences
    getNotificationPreferences: builder.query<NotificationPreferences, void>({
      query: () => "/users/notification-preferences",
      providesTags: ["User"],
    }),

    // Update user notification preferences
    updateNotificationPreferences: builder.mutation<
      NotificationPreferences,
      UpdateNotificationPreferencesRequest
    >({
      query: (data) => ({
        url: "/users/notification-preferences",
        method: "PUT",
        body: data,
      }),
      invalidatesTags: ["User"],
    }),
  }),
});

export const {
  useGetUserProfileQuery,
  useGetUserByUsernameQuery,
  useUpdateProfileMutation,
  useUploadProfilePictureMutation,
  useGetPrivacySettingsQuery,
  useUpdatePrivacySettingsMutation,
  useGetNotificationPreferencesQuery,
  useUpdateNotificationPreferencesMutation,
} = userApi;
