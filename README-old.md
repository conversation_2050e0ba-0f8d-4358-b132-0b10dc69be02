# E-Commerce Marketplace

A modern, responsive e-commerce marketplace built with Next.js 15, TypeScript, and Tailwind CSS. This application provides a comprehensive platform for browsing, filtering, and purchasing products with an intuitive user interface.

🌐 **Live Demo**: [https://ecom.webstudiomatrix.com/](https://ecom.webstudiomatrix.com/)

## 🚀 Features

### Core Functionality

- **Product Browsing**: Browse products with advanced filtering and sorting capabilities
- **Category Navigation**: Hierarchical category system with subcategories
- **Search & Filter**: Real-time search with location-based filtering
- **Product Details**: Detailed product views with image galleries and seller information
- **Responsive Design**: Mobile-first design that works on all devices

### User Experience

- **Dynamic Filtering**: Filter by condition, type, location, delivery options, brand, and price range
- **Sorting Options**: Sort by newest, oldest, price (low to high), price (high to low), and relevance
- **Pagination**: Efficient pagination for large product catalogs
- **Error Handling**: Comprehensive error boundaries and loading states
- **Accessibility**: Built with accessibility best practices

## 🛠️ Tech Stack

### Frontend

- **Framework**: [Next.js 15](https://nextjs.org/) with App Router
- **Language**: [TypeScript](https://www.typescriptlang.org/) with strict mode
- **Styling**: [Tailwind CSS 4](https://tailwindcss.com/)
- **UI Components**: Custom components with [Radix UI](https://www.radix-ui.com/) primitives
- **Icons**: [Iconify React](https://iconify.design/) & [Lucide React](https://lucide.dev/)
- **State Management**: [Redux Toolkit](https://redux-toolkit.js.org/) with Redux Persist
- **Forms**: [Formik](https://formik.org/) with [Yup](https://github.com/jquense/yup) validation
- **HTTP Client**: [Axios](https://axios-http.com/) for API requests
- **Image Processing**: [React Image Crop](https://github.com/DominicTobias/react-image-crop)

### Development Tools

- **Linting**: ESLint with Next.js configuration
- **Type Checking**: TypeScript with strict mode
- **Package Manager**: npm
- **Build Tool**: Next.js built-in bundler with SWC
- **Containerization**: Docker with multi-stage builds
- **Development**: Hot reload with Fast Refresh

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # Global styles and Tailwind imports
│   ├── layout.tsx         # Root layout with providers
│   └── page.tsx           # Home page component
├── components/            # React components
│   ├── ui/               # Reusable UI components
│   │   ├── button.tsx
│   │   ├── input.tsx
│   │   └── select.tsx
│   ├── layout/           # Layout components
│   ├── product-detail/   # Product detail components
│   │   ├── ProductDetailView.tsx
│   │   ├── seller-info.tsx
│   │   ├── similar-listings.tsx
│   │   └── tabs/
│   ├── CategoryDropdown.tsx
│   ├── CategorySidebar.tsx
│   ├── ErrorBoundary.tsx
│   ├── FilterPanel.tsx
│   ├── Footer.tsx
│   ├── Header.tsx
│   ├── Hero.tsx
│   ├── HomePage.tsx
│   ├── ProductGrid.tsx
│   └── index.ts
├── context/              # React Context providers
│   └── EcommerceContext.tsx
├── data/                 # Mock data and constants
│   └── mockData.ts
├── hooks/                # Custom React hooks
│   └── useProductFiltering.ts
├── lib/                  # Utility libraries
├── types/                # TypeScript type definitions
│   └── ecommerce.ts
└── utils/                # Utility functions
    ├── imageUtils.ts
    └── productFilters.ts
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, pnpm, or bun

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd ecom
```

2. Install dependencies:

```bash
npm install
# or
yarn install
# or
pnpm install
# or
bun install
```

3. Start the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🎨 Design System

### Color Scheme

- **Background**: `#EFEFEF` (Light gray)
- **Foreground**: `#171717` (Dark gray)
- **Accent Colors**: Defined in Tailwind configuration

### Typography

- **Primary Font**: Geist Sans (via next/font)
- **Monospace Font**: Geist Mono
- **Logo Font**: Bungee (Google Fonts) - _planned_

### Layout

- **Container**: 5% margin on left and right
- **Responsive Breakpoints**: Tailwind CSS default breakpoints
- **Grid System**: CSS Grid and Flexbox

## 🏗️ Architecture

### State Management

The application uses Redux Toolkit for robust state management with the following features:

- **Redux Toolkit**: Modern Redux with simplified syntax and built-in best practices
- **Redux Persist**: Automatic state persistence for cart and user data
- **Async Thunks**: Handling complex async operations with proper loading states
- **Selectors**: Memoized state selectors for optimal performance
- **DevTools**: Enhanced debugging with Redux DevTools integration

#### State Structure:

- **Search State**: Query, category, location, results, loading, and error states
- **Category State**: Selected category, filter visibility, categories list
- **Filter State**: Active filters, available filter options
- **Product State**: Products list, filtered products, pagination, sorting
- **Cart State**: Shopping cart items, quantities, and totals
- **User State**: Authentication, profile, and preferences
- **Modal State**: Global modal management

### Redux Store Structure

```typescript
interface RootState {
  search: SearchState;
  category: CategoryState;
  filter: FilterState;
  product: ProductState;
  cart: CartState;
  user: UserState;
  modal: ModalState;
}
```

### Custom Hooks

- **useAppSelector()**: Type-safe Redux state selection
- **useAppDispatch()**: Type-safe Redux action dispatch
- **useEcommerceActions()**: Pre-built action creators for common operations
- **useProductFiltering()**: Handles product filtering logic and side effects
- **useImageHandler()**: Smart image loading with error handling
- **useViewTracking()**: Product view analytics tracking

### Component Architecture

- **Layout Components**: Header, Footer, Hero
- **Navigation Components**: CategoryDropdown, CategorySidebar
- **Product Components**: ProductGrid, ProductDetailView
- **Filter Components**: FilterPanel
- **UI Components**: Reusable button, input, select components
- **Error Handling**: ErrorBoundary for graceful error handling

## 📊 Data Models

### Product Interface

```typescript
interface Product {
  id: string;
  title: string;
  description: string;
  price: number;
  currency: string;
  images: string[];
  category: string;
  subcategory?: string;
  location: string;
  seller: {
    id: string;
    name: string;
    avatar?: string;
    rating?: number;
  };
  condition: "brand new" | "like new" | "used" | "refurbished";
  brand?: string;
  postedAt: Date;
  delivery: {
    available: boolean;
    type?: "home" | "pickup" | "both";
    cost?: number;
  };
  featured: boolean;
  status: "active" | "sold" | "inactive";
}
```

### Category Interface

```typescript
interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  subcategories?: Category[];
  productCount: number;
}
```

### Filter Options

```typescript
interface FilterOptions {
  condition: string[];
  type: string[];
  location: string[];
  delivery: string[];
  brand: string[];
  priceRange: {
    min: number;
    max: number;
  };
  postedWithin: string;
}
```

## ⚡ Performance Optimizations

### Image Optimization

- **Smart Image Component**: Automatically chooses between Next.js Image and regular img based on URL type
- **Lazy Loading**: Images load only when entering viewport with `loading="lazy"`
- **Image Error Handling**: Graceful fallbacks with placeholder images and data URLs
- **Responsive Images**: Proper `sizes` attribute for different screen sizes
- **Image Compression**: JPEG quality optimization for cropped images (95% quality)

### Code Optimization

- **Component Memoization**: Strategic use of `React.memo()` for expensive components
- **Bundle Splitting**: Automatic code splitting with Next.js App Router
- **Tree Shaking**: Unused code elimination in production builds
- **SWC Compiler**: Fast Rust-based compilation for better performance
- **TypeScript Optimization**: Strict mode for better type checking and optimization

### State Management Performance

- **Memoized Selectors**: Redux selectors with automatic memoization
- **Selective Persistence**: Only cart and user data persisted, not search/modal state
- **Optimized Re-renders**: Precise state subscriptions to minimize unnecessary renders
- **Async Thunks**: Non-blocking async operations with proper loading states

### Loading & UX Optimizations

- **Skeleton Components**: Smooth loading states for better perceived performance
- **Progressive Loading**: Content loads incrementally as data becomes available
- **Error Boundaries**: Graceful error handling without full page crashes
- **Debounced Search**: Reduced API calls with 300ms debounce on search input
- **View Tracking**: Efficient product view analytics with 3-second threshold

### Network Optimizations

- **HTTP Client**: Axios with request/response interceptors
- **Retry Logic**: Automatic retry for failed requests with exponential backoff
- **Error Handling**: Comprehensive error categorization and user-friendly messages
- **API Health Checks**: Built-in health monitoring endpoint

## 🔧 Key Features Implementation

### Product Filtering

The application implements a sophisticated filtering system:

1. **Category Filtering**: Filter products by main category and subcategories
2. **Search Filtering**: Text-based search across product titles and descriptions
3. **Location Filtering**: Filter by product location
4. **Advanced Filters**: Condition, type, delivery options, brand, price range
5. **Combined Filtering**: All filters work together seamlessly

### Sorting Options

- **Newest**: Sort by most recently posted
- **Oldest**: Sort by oldest posts first
- **Price Low to High**: Ascending price order
- **Price High to Low**: Descending price order
- **Relevance**: Based on search query relevance

### Pagination

- **Items per page**: 12 products per page (configurable)
- **Dynamic pagination**: Automatically adjusts based on filtered results
- **Page navigation**: Previous/Next navigation with page numbers

## 🎯 User Interface

### Layout Structure

```
┌─────────────────────────────────────────┐
│                Header                   │
├─────────────────────────────────────────┤
│                 Hero                    │
├─────────────────────────────────────────┤
│ Sidebar │        Main Content          │
│         │                              │
│ Filters │      Product Grid            │
│    or   │                              │
│Category │      Pagination              │
│         │                              │
├─────────────────────────────────────────┤
│               Footer                    │
└─────────────────────────────────────────┘
```

### Responsive Behavior

- **Desktop**: Sidebar + main content layout
- **Tablet**: Collapsible sidebar
- **Mobile**: Stack layout with drawer navigation

### Interactive Elements

- **Category Dropdown**: Toggles between filters and category view
- **Filter Panel**: Collapsible filter options
- **Product Cards**: Hover effects and click interactions
- **Search Bar**: Real-time search with debouncing
- **Sort Dropdown**: Dynamic sorting options

## 🧪 Testing

### Recommended Testing Strategy

1. **Unit Tests**: Test individual components and utility functions
2. **Integration Tests**: Test component interactions and context behavior
3. **E2E Tests**: Test complete user workflows

### Test Files Structure

```
__tests__/
├── components/
│   ├── ProductGrid.test.tsx
│   ├── FilterPanel.test.tsx
│   └── CategoryDropdown.test.tsx
├── hooks/
│   └── useProductFiltering.test.ts
├── utils/
│   └── productFilters.test.ts
└── context/
    └── EcommerceContext.test.tsx
```

### Testing Libraries (Recommended)

- **Jest**: Test runner and assertion library
- **React Testing Library**: Component testing utilities
- **Cypress** or **Playwright**: End-to-end testing

## 🚀 Deployment

### Build Process

```bash
npm run build
```

This creates an optimized production build in the `.next` folder.

### Deployment Options

1. **Vercel** (Recommended): Seamless deployment with Next.js
2. **Docker**: Multi-stage containerized deployment (current setup)
3. **Netlify**: Static site deployment
4. **AWS Amplify**: Full-stack deployment

### Docker Deployment

The application includes a production-ready Docker setup:

```bash
# Build and run with Docker
npm run docker:build
npm run docker:run

# Development with Docker Compose
npm run docker:dev

# With database (if needed)
npm run docker:dev-db

# Stop containers
npm run docker:stop

# Clean up
npm run docker:clean
```

#### Docker Features:

- **Multi-stage builds**: Optimized production image size
- **Health checks**: Built-in health monitoring
- **Security**: Non-root user execution
- **Caching**: Efficient layer caching for faster builds
- **Production optimizations**: Only production dependencies in final image

### Environment Variables

Create a `.env.local` file for environment-specific variables:

```env
NEXT_PUBLIC_API_URL=your_api_url
NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id
```

## 🔮 Future Enhancements

### Planned Features

- **User Authentication**: Login/signup functionality
- **Shopping Cart**: Add to cart and checkout process
- **Payment Integration**: Stripe/PayPal integration
- **User Profiles**: Seller and buyer profiles
- **Reviews & Ratings**: Product and seller reviews
- **Wishlist**: Save favorite products
- **Real-time Chat**: Buyer-seller communication
- **Advanced Search**: Elasticsearch integration
- **Mobile App**: React Native mobile application

### Technical Improvements

- **API Integration**: Replace mock data with real API
- **Database**: PostgreSQL or MongoDB integration
- **Caching**: Redis for improved performance
- **CDN**: Image optimization and delivery
- **SEO**: Enhanced meta tags and structured data
- **PWA**: Progressive Web App features
- **Internationalization**: Multi-language support

## 🤝 Contributing

### Development Workflow

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes
4. Run tests: `npm run test`
5. Run linting: `npm run lint`
6. Commit your changes: `git commit -m 'Add new feature'`
7. Push to the branch: `git push origin feature/new-feature`
8. Submit a pull request

### Code Style

- Follow TypeScript best practices
- Use ESLint configuration provided
- Write meaningful commit messages
- Add JSDoc comments for complex functions
- Maintain consistent naming conventions

### Component Guidelines

- Use functional components with hooks
- Implement proper TypeScript typing
- Follow the established folder structure
- Add error boundaries where appropriate
- Ensure accessibility compliance

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Next.js Team**: For the amazing framework
- **Tailwind CSS**: For the utility-first CSS framework
- **Radix UI**: For accessible component primitives
- **Lucide**: For the beautiful icon library
- **Vercel**: For hosting and deployment platform

## 📞 Support

For support, please open an issue in the GitHub repository or contact the development team.

---

## 🚀 Performance Optimization Recommendations

Based on the current codebase analysis, here are key areas for performance improvements:

### 1. Critical Performance Issues

#### Bundle Size Optimization

- **Issue**: Large bundle size due to multiple UI libraries
- **Solution**:
  - Replace Lucide React with Iconify React completely (currently using both)
  - Implement dynamic imports for heavy components
  - Use Next.js bundle analyzer to identify large dependencies

#### Image Optimization

- **Issue**: Missing WebP format support and image preloading
- **Solution**:
  - Add WebP format support in next.config.ts
  - Implement image preloading for above-the-fold content
  - Add responsive image breakpoints

### 2. Code Splitting Improvements

```typescript
// Implement dynamic imports for heavy components
const ImageCropModal = dynamic(
  () => import("@/components/forms/ui/ImageCropModal"),
  {
    loading: () => <LoadingSpinner />,
    ssr: false,
  }
);

const ProductDetailView = dynamic(
  () => import("@/components/product/detail/ProductDetailView"),
  {
    loading: () => <ProductCardSkeleton />,
  }
);
```

### 3. State Management Optimization

#### Redux Performance

- **Current**: Good Redux setup with persistence
- **Improvements**:
  - Add RTK Query for server state management
  - Implement selector memoization with reselect
  - Use Redux DevTools only in development

#### Memory Management

- **Issue**: Potential memory leaks in image handling
- **Solution**: Implement proper cleanup for blob URLs and event listeners

### 4. Network Performance

#### API Optimization

- **Add**: Request deduplication
- **Add**: Response caching with proper cache headers
- **Add**: Compression middleware for API responses

#### CDN Integration

- **Recommendation**: Implement CDN for static assets
- **Add**: Image optimization service (Cloudinary/ImageKit)

### 5. Monitoring & Analytics

#### Performance Monitoring

```typescript
// Add Web Vitals tracking
import { getCLS, getFID, getFCP, getLCP, getTTFB } from "web-vitals";

function sendToAnalytics(metric) {
  // Send to your analytics service
  console.log(metric);
}

getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);
```

#### Error Tracking

- **Add**: Sentry or similar error tracking service
- **Implement**: Performance budgets and alerts

### 6. SEO & Core Web Vitals

#### Meta Tags & Structured Data

- **Issue**: Generic meta tags in layout.tsx
- **Solution**: Dynamic meta tags per page with proper Open Graph tags

#### Core Web Vitals Optimization

- **LCP**: Optimize hero image loading with priority
- **FID**: Reduce JavaScript execution time
- **CLS**: Add proper dimensions to all images

### 7. Production Optimizations

#### Build Optimizations

```typescript
// next.config.ts improvements
const nextConfig = {
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ["@iconify/react", "lucide-react"],
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
  },
};
```

#### Caching Strategy

- **Add**: Service Worker for offline functionality
- **Implement**: Proper cache headers for static assets
- **Add**: Redis for server-side caching

### 8. Mobile Performance

#### Mobile Optimizations

- **Add**: Touch gesture optimizations
- **Implement**: Reduced motion preferences
- **Add**: Progressive Web App features

### 9. Implementation Priority

1. **High Priority**: Bundle size reduction, image optimization
2. **Medium Priority**: Code splitting, RTK Query implementation
3. **Low Priority**: PWA features, advanced caching

### 10. Performance Budget

- **Bundle Size**: < 250KB gzipped
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1

---

**Built with ❤️ using Next.js, TypeScript, and Tailwind CSS**
