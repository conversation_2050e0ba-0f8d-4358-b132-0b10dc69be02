name: CI/CD for E-commerce Next.js App (GHCR Deployment)
on:
  push:
    branches:
      - main

env:
  NODE_VERSION: 20
  APP_NAME: ecom
  DOCKER_IMAGE: ghcr.io/${{ github.repository_owner }}/ecom
  DOCKER_REGISTRY: ghcr.io

jobs:
  build-and-deploy:
    runs-on: self-hosted
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ env.DOCKER_IMAGE }}:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            NEXT_PUBLIC_API_BASE_URL=${{ secrets.NEXT_PUBLIC_API_BASE_URL }}

      - name: Set up SSH
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Deploy to VPS
        if: github.ref == 'refs/heads/main'
        env:
          DOCKER_IMAGE: ${{ env.DOCKER_IMAGE }}
        run: |
          # Create a script file to be executed on the remote server
          cat > deploy.sh << 'EOF'
          #!/bin/bash
          DOCKER_IMAGE="$1"
          GITHUB_TOKEN="$2"
          GITHUB_ACTOR="$3"
          MONGODB_URI="$4"
          JWT_SECRET="$5"
          NEXTAUTH_SECRET="$6"
          NEXTAUTH_URL="$7"
          NEXT_PUBLIC_API_BASE_URL="$8"

          # Create uploads directory if it doesn't exist
          mkdir -p /home/<USER>/ecom-uploads

          echo "$GITHUB_TOKEN" | docker login ghcr.io -u "$GITHUB_ACTOR" --password-stdin
          docker pull "$DOCKER_IMAGE":latest
          docker stop ecom || true
          docker rm ecom || true

          # Run with volume mount for uploads persistence and environment variables
          docker run -d \
            --name ecom \
            --restart unless-stopped \
            -p 9105:3000 \
            -v /home/<USER>/ecom-uploads:/app/public/uploads \
            -e MONGODB_URI="$MONGODB_URI" \
            -e JWT_SECRET="$JWT_SECRET" \
            -e NEXTAUTH_SECRET="$NEXTAUTH_SECRET" \
            -e NEXTAUTH_URL="$NEXTAUTH_URL" \
            -e NEXT_PUBLIC_API_BASE_URL="$NEXT_PUBLIC_API_BASE_URL" \
            "$DOCKER_IMAGE":latest

          # Fix permissions on the uploads directory
          docker exec ecom sh -c "mkdir -p /app/public/uploads && chmod -R 777 /app/public/uploads"

          # Print status
          echo "E-commerce container started with persisted uploads directory"
          docker ps | grep ecom
          EOF

          chmod +x deploy.sh
          scp -o StrictHostKeyChecking=no deploy.sh ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }}:/tmp/deploy.sh
          ssh -o StrictHostKeyChecking=no ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} "bash /tmp/deploy.sh '$DOCKER_IMAGE' '${{ secrets.GITHUB_TOKEN }}' '${{ github.actor }}' '${{ secrets.MONGODB_URI }}' '${{ secrets.JWT_SECRET }}' '${{ secrets.NEXTAUTH_SECRET }}' '${{ secrets.NEXTAUTH_URL }}' '${{ secrets.NEXT_PUBLIC_API_BASE_URL }}' && rm /tmp/deploy.sh"
