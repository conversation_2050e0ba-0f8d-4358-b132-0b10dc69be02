"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Icon } from "@iconify/react";

const CONFIRMATION_SENTENCES = [
  "I understand this action cannot be undone",
  "Delete my account permanently",
  "I want to remove all my data",
  "Confirm account deletion process",
  "Remove my profile completely",
  "I acknowledge data loss",
  "Permanently delete everything",
  "I accept account termination",
];

export default function DeleteAccount() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [confirmationSentence, setConfirmationSentence] = useState("");
  const [userInput, setUserInput] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState("");

  const generateRandomSentence = () => {
    // Use a deterministic approach based on current time to avoid hydration issues
    // but still provide variety
    const now = new Date();
    const dayOfYear = Math.floor(
      (now.getTime() - new Date(now.getFullYear(), 0, 0).getTime()) /
        (1000 * 60 * 60 * 24)
    );
    const randomIndex = dayOfYear % CONFIRMATION_SENTENCES.length;
    return CONFIRMATION_SENTENCES[randomIndex];
  };

  // Ensure sentence is generated when dialog opens
  useEffect(() => {
    if (isDialogOpen && !confirmationSentence) {
      const sentence = generateRandomSentence();
      console.log("useEffect generated sentence:", sentence);
      setConfirmationSentence(sentence);
    }
  }, [isDialogOpen, confirmationSentence]);

  const handleDeleteClick = () => {
    const sentence = generateRandomSentence();
    console.log("Generated sentence:", sentence); // Debug log
    setConfirmationSentence(sentence);
    setUserInput("");
    setError("");
    setIsDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (userInput.trim() !== confirmationSentence) {
      setError("The text you entered doesn't match. Please try again.");
      return;
    }

    setIsDeleting(true);
    setError("");

    try {
      // Simulate API call for account deletion
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Here you would typically call your API to delete the account
      // await deleteUserAccount();

      alert(
        "Account deleted successfully. You will be redirected to the homepage."
      );
      // Redirect to homepage or login page
      window.location.href = "/";
    } catch (_err) {
      setError("Failed to delete account. Please try again.");
      setIsDeleting(false);
    }
  };

  const handleCancel = () => {
    setIsDialogOpen(false);
    setUserInput("");
    setError("");
    setConfirmationSentence("");
  };

  const isInputValid = userInput.trim() === confirmationSentence;

  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-6">
      <div className="flex items-start space-x-3">
        <Icon
          icon="lucide:alert-triangle"
          className="h-6 w-6 text-red-600 mt-0.5"
        />
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-red-900 mb-2">
            Danger Zone
          </h3>
          <p className="text-red-700 mb-4">
            Once you delete your account, there is no going back. Please be
            certain. All your data, including your profile, listings, and
            transaction history will be permanently removed.
          </p>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button
                variant="destructive"
                onClick={handleDeleteClick}
                className="bg-red-600 text-white hover:bg-red-700"
              >
                <Icon
                  icon="lucide:trash-2"
                  className="h-4 text-white w-4 mr-2"
                />
                Delete Account
              </Button>
            </DialogTrigger>

            <DialogContent className="p-4 sm:max-w-md">
              <DialogHeader>
                <DialogTitle className="text-2xl">
                  Confirm Account Deletion
                </DialogTitle>
                <DialogDescription className="text-gray-600">
                  This action cannot be undone. To confirm, please type the
                  following sentence exactly:
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                <div className="bg-red-50 border-2 border-red-200 p-4 rounded-lg">
                  <p className="text-xs text-red-600 mb-1 font-semibold">
                    PLEASE TYPE THIS SENTENCE EXACTLY:
                  </p>
                  <p className="font-mono text-base text-red-800 select-all font-bold">
                    {confirmationSentence || "Loading..."}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmation-input">
                    Type the sentence above to confirm:
                  </Label>
                  <Input
                    id="confirmation-input"
                    value={userInput}
                    onChange={(e) => setUserInput(e.target.value)}
                    placeholder="Type the confirmation sentence..."
                    className={error ? "border-red-500" : ""}
                    disabled={isDeleting}
                  />
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
              </div>

              <DialogFooter className="flex mt-4 space-x-2">
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isDeleting}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleConfirmDelete}
                  disabled={!isInputValid || isDeleting}
                  className="bg-red-600 text-white hover:bg-red-700"
                >
                  {isDeleting ? "Deleting..." : "Delete Account"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
}
