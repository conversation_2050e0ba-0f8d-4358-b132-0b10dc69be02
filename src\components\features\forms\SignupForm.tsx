"use client";
import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Formik, Form, Field, ErrorMessage } from "formik";
import { Icon } from "@iconify/react";
import { LoadingButton } from "@/components/ui";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { AuthService } from "@/services/auth-service";
import {
  registerSchema,
  type RegisterFormValues,
} from "@/schemas/auth-schemas";
import { handleFormError, logError } from "@/utils/error-handling";
import { ApiError } from "@/lib/api";

export default function SignupForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [apiError, setApiError] = useState<string>("");
  const router = useRouter();

  const initialValues: RegisterFormValues = {
    username: "",
    email: "",
    firstName: "",
    lastName: "",
    phone: "",
    password: "",
    confirmPassword: "",
  };

  const handleSubmit = async (
    values: RegisterFormValues,
    {
      setSubmitting,
      setFieldError,
    }: {
      setSubmitting: (isSubmitting: boolean) => void;
      setFieldError: (field: string, message: string) => void;
    }
  ) => {
    try {
      setApiError("");
      const { confirmPassword: _confirmPassword, ...registerData } = values;
      await AuthService.register(registerData);
      await new Promise((resolve) => setTimeout(resolve, 500));
      router.push("/");
    } catch (error) {
      handleFormError(error as ApiError | Error, setFieldError, setApiError);
      logError(error as Error, "Signup form submission");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="mx-auto max-w-2xl space-y-8 p-8 bg-white">
      <div className="text-center">
        <h1 className="text-4xl font-normal text-black mb-12">
          Create Account
        </h1>
      </div>

      <Formik
        initialValues={initialValues}
        validationSchema={registerSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting }) => (
          <Form className="space-y-6">
            {apiError && (
              <div className="rounded-md bg-red-50 p-4 border border-red-200">
                <div className="text-sm text-red-600">{apiError}</div>
              </div>
            )}

            {/* Full Name Field */}
            <div className="flex items-center gap-4">
              <Label
                htmlFor="firstName"
                className="text-base font-normal text-black w-32 flex-shrink-0"
              >
                Full Name
              </Label>
              <div className="flex-1">
                <Field
                  as={Input}
                  id="firstName"
                  name="firstName"
                  type="text"
                  placeholder="Ram Shrestha"
                  className="h-12"
                />
                <ErrorMessage
                  name="firstName"
                  component="div"
                  className="text-sm text-red-600 mt-1"
                />
              </div>
            </div>

            {/* Mobile Number Field */}
            <div className="flex items-center gap-4">
              <Label
                htmlFor="phone"
                className="text-base font-normal text-black w-32 flex-shrink-0"
              >
                Mobile Number
              </Label>
              <div className="flex-1">
                <Field
                  as={Input}
                  id="phone"
                  name="phone"
                  type="tel"
                  placeholder="9876543210"
                  className="h-12"
                />
                <ErrorMessage
                  name="phone"
                  component="div"
                  className="text-sm text-red-600 mt-1"
                />
              </div>
            </div>

            {/* Email Field */}
            <div className="flex items-center gap-4">
              <Label
                htmlFor="email"
                className="text-base font-normal text-black w-32 flex-shrink-0"
              >
                Email
              </Label>
              <div className="flex-1">
                <Field
                  as={Input}
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  className="h-12"
                />
                <ErrorMessage
                  name="email"
                  component="div"
                  className="text-sm text-red-600 mt-1"
                />
              </div>
            </div>

            {/* Password Field */}
            <div className="flex items-center gap-4">
              <Label
                htmlFor="password"
                className="text-base font-normal text-black w-32 flex-shrink-0"
              >
                Password
              </Label>
              <div className="flex-1">
                <div className="relative">
                  <Field
                    as={Input}
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="***********"
                    className="h-12 pr-12"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? (
                      <Icon icon="lucide:eye-off" className="h-5 w-5" />
                    ) : (
                      <Icon icon="lucide:eye" className="h-5 w-5" />
                    )}
                  </button>
                </div>
                <ErrorMessage
                  name="password"
                  component="div"
                  className="text-sm text-red-600 mt-1"
                />
              </div>
            </div>

            {/* Confirm Password Field */}
            <div className="flex items-center gap-4">
              <Label
                htmlFor="confirmPassword"
                className="text-base font-normal text-black w-32 flex-shrink-0"
              >
                Confirm Password
              </Label>
              <div className="flex-1">
                <div className="relative">
                  <Field
                    as={Input}
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="***********"
                    className="h-12 pr-12"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showConfirmPassword ? (
                      <Icon icon="lucide:eye-off" className="h-5 w-5" />
                    ) : (
                      <Icon icon="lucide:eye" className="h-5 w-5" />
                    )}
                  </button>
                </div>
                <ErrorMessage
                  name="confirmPassword"
                  component="div"
                  className="text-sm text-red-600 mt-1"
                />
              </div>
            </div>

            {/* Address Field */}
            <div className="flex items-center gap-4">
              <Label
                htmlFor="lastName"
                className="text-base font-normal text-black w-32 flex-shrink-0"
              >
                Address
              </Label>
              <div className="flex-1">
                <Field
                  as={Input}
                  id="lastName"
                  name="lastName"
                  type="text"
                  placeholder="Madhyapur Thimi"
                  className="h-12"
                />
                <ErrorMessage
                  name="lastName"
                  component="div"
                  className="text-sm text-red-600 mt-1"
                />
              </div>
            </div>

            {/* I'm not a robot */}
            <div className="flex items-center space-x-3 p-4 border border-gray-300 rounded-lg bg-white">
              <Checkbox id="robot" className="h-4 w-4" />
              <Label
                htmlFor="robot"
                className="text-base font-normal text-black cursor-pointer"
              >
                {"I'm not a robot"}
              </Label>
            </div>

            <LoadingButton
              type="submit"
              isLoading={isSubmitting}
              loadingText="Creating Account..."
              className="h-14 w-full bg-teal-600 text-white hover:bg-teal-700 rounded-lg font-normal text-lg mt-6"
            >
              Create Account
            </LoadingButton>
          </Form>
        )}
      </Formik>

      <div className="text-center text-base text-gray-500 mt-8">
        Already Have an Account?{" "}
        <Link
          href="/login"
          className="text-black underline hover:text-gray-700"
        >
          Login Now
        </Link>
      </div>
    </div>
  );
}
