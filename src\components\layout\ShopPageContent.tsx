"use client";

import React, { useState, ReactNode } from "react";
import { CategorySidebar, CategoryProductSections } from "../features/category";
import { ProductGrid } from "../features/product";
import { BannerCarousel } from "../dynamic";
import { SortDropdown } from "../common";
import { MobileControlBar } from "./MobileControlBar";
import { SimpleErrorBoundary } from "../common";
import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react";
import {
  useAppSelector,
  useEcommerceActions,
  useAppDispatch,
} from "@/store/hooks";
import {
  selectSortBy,
  selectFilteredProducts,
  selectSelectedCategory,
  selectShowFilters,
  selectProductsLoading,
} from "@/store/selectors";
import { updateSortAndProducts } from "@/store/thunks/ecommerceThunks";
import type { SortBy } from "@/types/ecommerce";

interface ShopPageContentProps {
  showHero?: boolean;
  showCategoryProductSections?: boolean;
  categoryProductSectionIds?: string[];
  maxProducts?: number;
  customHeader?: ReactNode;
  className?: string;
  renderCategoryProductSections?: boolean; // New prop to control rendering
}

export default function ShopPageContent({
  showHero = false,
  showCategoryProductSections = true,
  categoryProductSectionIds = [
    "property",
    "vehicles",
    "electronics",
    "art-crafts",
    "home-garden",
  ],
  maxProducts = 12,
  customHeader,
  className = "",
  renderCategoryProductSections = true,
}: ShopPageContentProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Redux selectors
  const dispatch = useAppDispatch();
  const sortBy = useAppSelector(selectSortBy);
  const filteredProducts = useAppSelector(selectFilteredProducts);
  const selectedCategory = useAppSelector(selectSelectedCategory);
  const showFilters = useAppSelector(selectShowFilters);
  const isLoading = useAppSelector(selectProductsLoading);

  // Redux actions
  const { toggleFilters } = useEcommerceActions();

  const handleSortChange = (newSortBy: SortBy) => {
    dispatch(updateSortAndProducts(newSortBy));
  };

  return (
    <div className={className}>
      {/* Custom Header (for category pages) */}
      {/* {customHeader} */}

      {/* Hero Section with Banner Carousel */}
      {showHero && (
        <div className="mb-6">
          <BannerCarousel
            autoSlide={true}
            slideInterval={5000}
            showDots={true}
            showArrows={true}
          />
        </div>
      )}

      {/* Desktop Sort - Only show on desktop */}
      <div className="hidden md:flex justify-between items-center">
        <div className="div"></div>
        <div>
          <SortDropdown sortBy={sortBy} onSortChange={handleSortChange} />
        </div>
      </div>

      {/* Mobile Control Bar - Only show on mobile */}
      <div className="md:hidden">
        <MobileControlBar
          onFilterClick={() => setIsSidebarOpen(true)}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
        />
      </div>

      {/* Main Layout */}
      <div className="flex gap-6">
        {/* Left Sidebar - Desktop */}
        <div
          className={`
          ${showFilters ? "w-80" : "w-0"}
          transition-all duration-300 overflow-hidden
          hidden lg:block
        `}
        >
          <div className="sticky top-4">
            <SimpleErrorBoundary>
              <CategorySidebar />
            </SimpleErrorBoundary>
          </div>
        </div>

        {/* Mobile Sidebar Overlay */}
        {isSidebarOpen && (
          <div className="fixed inset-0 z-50 lg:hidden">
            <div
              className="absolute inset-0 bg-black bg-opacity-50"
              onClick={() => setIsSidebarOpen(false)}
            />
            <div className="absolute left-0 top-0 h-full w-80 bg-white overflow-y-auto">
              <div className="sticky top-0 bg-white z-10 p-4 border-b border-gray-200">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsSidebarOpen(false)}
                  className="ml-auto flex"
                >
                  <Icon icon="lucide:x" className="w-4 h-4" />
                </Button>
              </div>
              <div className="p-4" style={{ height: "calc(100vh - 60px)" }}>
                <SimpleErrorBoundary>
                  <CategorySidebar />
                </SimpleErrorBoundary>
              </div>
            </div>
          </div>
        )}

        {/* Right Content - Product Grid */}
        <div className="flex-1 min-w-0">
          {/* Loading state for products */}
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              <span className="ml-2">Loading products...</span>
            </div>
          ) : (
            <ProductGrid viewMode={viewMode} maxProducts={maxProducts} />
          )}
        </div>
      </div>

      {/* Full Width Category Product Sections */}
      {showCategoryProductSections && renderCategoryProductSections && (
        <div className="mx-0">
          <SimpleErrorBoundary>
            <CategoryProductSections categoryIds={categoryProductSectionIds} />
          </SimpleErrorBoundary>
        </div>
      )}
    </div>
  );
}

// Separate component for full-width category product sections
interface CategoryProductSectionsWrapperProps {
  categoryProductSectionIds?: string[];
}

export function CategoryProductSectionsWrapper({
  categoryProductSectionIds,
}: CategoryProductSectionsWrapperProps) {
  console.log(
    "CategoryProductSectionsWrapper rendering with IDs:",
    categoryProductSectionIds || "all categories (first 5)"
  );

  return (
    <div className="mx-0">
      <SimpleErrorBoundary>
        <CategoryProductSections categoryIds={categoryProductSectionIds} />
      </SimpleErrorBoundary>
    </div>
  );
}
