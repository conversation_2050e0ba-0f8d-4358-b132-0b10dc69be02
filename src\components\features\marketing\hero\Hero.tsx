"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Icon } from "@iconify/react";
import { LoadingSpinner } from "@/components/ui";
import { useEcommerce } from "@/store/compatibility";
import { useEcommerceActions } from "@/store/hooks";
import { useState, useEffect } from "react";
import { useDebouncedSearch } from "@/hooks/use-debounce";
import type { Category } from "@/types/ecommerce";

export default function Hero() {
  const { state } = useEcommerce();
  const { setSearchQuery, selectCategory, setSearchLocation } =
    useEcommerceActions();
  const [localQuery, setLocalQuery] = useState(state.search.query);
  const [localCategory, setLocalCategory] = useState(
    state.search.category || "all"
  );
  const [localLocation, setLocalLocation] = useState(
    state.search.location || "all"
  );

  // Use debounced search for better performance
  const { debouncedSearchTerm, isSearching } = useDebouncedSearch(
    localQuery,
    300
  );

  // Auto-search when debounced term changes
  useEffect(() => {
    setSearchQuery(debouncedSearchTerm);
  }, [debouncedSearchTerm, setSearchQuery]);

  const handleSearch = () => {
    setSearchQuery(localQuery);
    if (localCategory && localCategory !== "all") {
      selectCategory(localCategory);
    } else {
      selectCategory(null);
    }
  };

  const handleCategoryChange = (value: string) => {
    setLocalCategory(value);
    // Immediately update the global state when category changes
    if (value && value !== "all") {
      selectCategory(value);
    } else {
      selectCategory(null);
    }
  };

  const handleLocationChange = (value: string) => {
    setLocalLocation(value);
    // Update the global search location state
    setSearchLocation(value === "all" ? "" : value);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  return (
    <section
      className="text-white"
      style={{
        background: "linear-gradient(180deg, #356267 0%, #69C3CD 183.32%)",
      }}
    >
      <div className="container mx-auto px-4 py-12">
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Find Everything You Need In Nepal
          </h1>
          <p className="text-xl text-teal-100 mb-8">
            Buy, Sell and Discover Amazing Deals Near You
          </p>
        </div>

        {/* Search Section */}
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row gap-3">
            <div className="flex-1 relative">
              <Icon
                icon="lucide:search"
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-7 w-5"
              />
              {isSearching && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <LoadingSpinner size="sm" />
                </div>
              )}
              <Input
                placeholder="Search for Cars, Phones, Jobs, Property, etc"
                className="pl-10 pr-10 h-12 text-2xl text-gray-800 border-gray-200 focus:border-teal-500 focus:ring-teal-500"
                value={localQuery}
                onChange={(e) => setLocalQuery(e.target.value)}
                onKeyDown={handleKeyPress}
              />
            </div>
            <Select value={localCategory} onValueChange={handleCategoryChange}>
              <SelectTrigger className="w-full md:w-48 h-12 border-gray-200 focus:border-teal-500 focus:ring-teal-500 text-xl text-gray-800">
                <Icon
                  icon="lucide:grid-3x3"
                  className="h-7 w-6 mr-2 text-gray-800 "
                />
                <SelectValue placeholder="All Category" />
              </SelectTrigger>
              <SelectContent className="text-2xl bg-white text-black">
                <SelectItem value="all">All Category</SelectItem>
                {state.category.categories.map((category: Category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={localLocation} onValueChange={handleLocationChange}>
              <SelectTrigger className="w-full md:w-48 h-12 border-gray-200 focus:border-teal-500 focus:ring-teal-500 text-xl text-gray-800">
                <Icon
                  icon="lucide:map-pin"
                  className="h-6 w-6 mr-2 text-gray-800"
                />
                <SelectValue placeholder="All Nepal" />
              </SelectTrigger>
              <SelectContent className="text-2xl bg-white text-black">
                <SelectItem value="all">All Nepal</SelectItem>
                {state.filter.availableFilters.location.map(
                  (location: string) => (
                    <SelectItem key={location} value={location.toLowerCase()}>
                      {location}
                    </SelectItem>
                  )
                )}
              </SelectContent>
            </Select>
            <Button
              className="bg-teal-600 hover:bg-teal-700 text-xl text-white px-8 h-12 font-medium shadow-sm"
              onClick={handleSearch}
            >
              Search
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
