import { Icon } from "@iconify/react";

export default function WhyChooseUsPage() {
  const features = [
    {
      icon: "lucide:shield-check",
      title: "Secure Transactions",
      description:
        "Your payments and personal information are protected with bank-level security.",
    },
    {
      icon: "lucide:truck",
      title: "Fast Delivery",
      description:
        "Quick and reliable delivery across Nepal with real-time tracking.",
    },
    {
      icon: "lucide:headphones",
      title: "24/7 Support",
      description:
        "Our customer support team is available round the clock to help you.",
    },
    {
      icon: "lucide:star",
      title: "Quality Products",
      description:
        "We ensure all products meet our high quality standards before listing.",
    },
    {
      icon: "lucide:users",
      title: "Trusted Community",
      description:
        "Join thousands of satisfied customers who trust our marketplace.",
    },
    {
      icon: "lucide:credit-card",
      title: "Easy Payments",
      description:
        "Multiple payment options including digital wallets and cash on delivery.",
    },
  ];

  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <div className="container-responsive spacing-responsive-lg">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Why Choose Nepal Marketplace?
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Discover what makes us the most trusted e-commerce platform in
            Nepal. We&apos;re committed to providing you with the best shopping
            experience.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
            >
              <div className="flex items-center mb-4">
                <div className="bg-teal-100 p-3 rounded-lg mr-4">
                  <Icon icon={feature.icon} className="h-6 w-6 text-teal-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800">
                  {feature.title}
                </h3>
              </div>
              <p className="text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="bg-white rounded-xl p-8 shadow-lg mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-8">
            Our Numbers Speak
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-teal-600 mb-2">50K+</div>
              <div className="text-gray-600">Happy Customers</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-teal-600 mb-2">100K+</div>
              <div className="text-gray-600">Products Listed</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-teal-600 mb-2">500+</div>
              <div className="text-gray-600">Verified Sellers</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-teal-600 mb-2">99%</div>
              <div className="text-gray-600">Customer Satisfaction</div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-gradient-to-r from-teal-600 to-teal-700 rounded-xl p-8 text-white">
          <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
          <p className="text-lg mb-6 opacity-90">
            Join thousands of satisfied customers and start shopping today!
          </p>
          <button className="bg-white text-teal-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300">
            Start Shopping Now
          </button>
        </div>
      </div>
    </div>
  );
}
