/**
 * Test script to verify API integration is working
 * Run this in browser console to test the API endpoints
 */

const testAPIIntegration = async () => {
  const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://sasto-api.webstudiomatrix.com/api/v1';
  
  console.log('🧪 Testing API Integration...');
  console.log('Base URL:', baseUrl);
  
  try {
    // Test 1: Get advertisements
    console.log('\n📦 Test 1: Get advertisements');
    const adsResponse = await fetch(`${baseUrl}/advertisements?page=1&limit=12&sortBy=createdAt&sortOrder=DESC`);
    const adsData = await adsResponse.json();
    console.log('Status:', adsResponse.status);
    console.log('Advertisements count:', adsData.data?.length || 0);
    console.log('Total:', adsData.total || 0);
    console.log('Sample ad:', adsData.data?.[0]);
    
    // Test 2: Get categories
    console.log('\n📂 Test 2: Get categories');
    const categoriesResponse = await fetch(`${baseUrl}/categories`);
    const categoriesData = await categoriesResponse.json();
    console.log('Status:', categoriesResponse.status);
    console.log('Categories count:', categoriesData?.length || 0);
    console.log('Sample category:', categoriesData?.[0]);
    
    // Test 3: Get advertisements for a specific category (if categories exist)
    if (categoriesData?.length > 0) {
      const firstCategory = categoriesData[0];
      console.log(`\n🏷️ Test 3: Get advertisements for category "${firstCategory.name}"`);
      const categoryAdsResponse = await fetch(`${baseUrl}/advertisements?categoryId=${firstCategory.id}&page=1&limit=5&status=active`);
      const categoryAdsData = await categoryAdsResponse.json();
      console.log('Status:', categoryAdsResponse.status);
      console.log('Category ads count:', categoryAdsData.data?.length || 0);
      console.log('Sample category ad:', categoryAdsData.data?.[0]);
    }
    
    // Test 4: Get featured advertisements
    console.log('\n⭐ Test 4: Get featured advertisements');
    const featuredResponse = await fetch(`${baseUrl}/advertisements/featured?page=1&limit=5`);
    const featuredData = await featuredResponse.json();
    console.log('Status:', featuredResponse.status);
    console.log('Featured ads count:', featuredData.data?.length || 0);
    console.log('Sample featured ad:', featuredData.data?.[0]);
    
    console.log('\n✅ API Integration Test Complete!');
    
    return {
      advertisements: adsData,
      categories: categoriesData,
      featured: featuredData,
    };
    
  } catch (error) {
    console.error('❌ API Test Error:', error);
    return null;
  }
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).testAPIIntegration = testAPIIntegration;
}

export default testAPIIntegration;
