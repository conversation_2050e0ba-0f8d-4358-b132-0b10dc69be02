import React from "react"
import { Icon } from "@iconify/react"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { SectionHeader } from "../components/SectionHeader"
import { type FormData, VALIDATION_RULES } from "../types"

interface DescriptionSectionProps {
  formData: FormData
  updateFormData: (updates: Partial<FormData>) => void
}

export const DescriptionSection: React.FC<DescriptionSectionProps> = ({
  formData,
  updateFormData,
}) => {
  return (
    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
      <CardHeader className="pb-4">
        <SectionHeader
          icon={<Icon icon="lucide:file-text" className="w-6 h-6 text-white" />}
          title="Product Description"
          subtitle="Provide detailed information about your product"
          required
          step={4}
          totalSteps={6}
        />
      </CardHeader>
      <CardContent>
        <div className="relative">
          <Textarea
            id="description"
            placeholder="Describe your product in detail - condition, features, specifications, reason for selling, any defects or issues..."
            value={formData.description}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
              updateFormData({ description: e.target.value })
            }
            className={`min-h-[180px] text-base border-2 focus:ring-4 focus:ring-blue-100 rounded-xl resize-none transition-all duration-200 p-4 ${
              formData.description.length > 0 && formData.description.length < VALIDATION_RULES.description.min
                ? "border-red-300 focus:border-red-400"
                : "border-gray-200 focus:border-blue-500"
            }`}
            maxLength={VALIDATION_RULES.description.max}
          />
          <div className="absolute bottom-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-2 rounded-lg shadow-sm">
            <span
              className={
                formData.description.length < VALIDATION_RULES.description.min
                  ? "text-red-500 font-medium"
                  : "text-gray-500"
              }
            >
              {formData.description.length}/{VALIDATION_RULES.description.max}
            </span>
            {formData.description.length < VALIDATION_RULES.description.min && (
              <div className="text-xs text-red-500 mt-1">
                Minimum {VALIDATION_RULES.description.min} characters required
              </div>
            )}
          </div>
        </div>
        {formData.description.length >= VALIDATION_RULES.description.min && (
          <div className="mt-3 flex items-center gap-2 text-green-600">
            <Icon icon="lucide:check-circle" className="w-4 h-4" />
            <span className="text-sm">Perfect! Your description is detailed</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
