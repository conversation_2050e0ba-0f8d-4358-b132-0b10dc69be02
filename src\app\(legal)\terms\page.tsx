import Link from "next/link";

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <div className="container-responsive spacing-responsive-lg">
        <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-lg p-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-6">
            Terms & Conditions
          </h1>
          <p className="text-gray-600 mb-8">
            <strong>Last updated:</strong> January 1, 2024
          </p>

          <div className="prose prose-lg max-w-none">
            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                Quick Overview
              </h2>
              <p className="text-gray-600 leading-relaxed">
                Welcome to Nepal Marketplace! These terms govern your use of our
                platform. By using our services, you agree to these terms.
                Please read them carefully.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                Key Terms
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">
                    For Buyers
                  </h3>
                  <ul className="text-gray-600 space-y-2 text-sm">
                    <li>• Create account with valid information</li>
                    <li>• Pay for purchases promptly</li>
                    <li>• Follow return policy guidelines</li>
                    <li>• Respect seller terms and conditions</li>
                  </ul>
                </div>
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">
                    For Sellers
                  </h3>
                  <ul className="text-gray-600 space-y-2 text-sm">
                    <li>• Provide accurate product information</li>
                    <li>• Honor all confirmed sales</li>
                    <li>• Pay applicable platform fees</li>
                    <li>• Maintain good customer service</li>
                  </ul>
                </div>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                Platform Rules
              </h2>
              <div className="bg-red-50 border-l-4 border-red-400 p-6 mb-6">
                <h3 className="text-lg font-semibold text-red-800 mb-2">
                  Prohibited Activities
                </h3>
                <ul className="text-red-700 space-y-1 text-sm">
                  <li>• Selling counterfeit or illegal items</li>
                  <li>• Creating fake accounts or reviews</li>
                  <li>• Harassment or abusive behavior</li>
                  <li>• Violating intellectual property rights</li>
                </ul>
              </div>

              <div className="bg-green-50 border-l-4 border-green-400 p-6">
                <h3 className="text-lg font-semibold text-green-800 mb-2">
                  Encouraged Practices
                </h3>
                <ul className="text-green-700 space-y-1 text-sm">
                  <li>• Honest product descriptions and photos</li>
                  <li>• Prompt communication with buyers/sellers</li>
                  <li>• Fair pricing and quality products</li>
                  <li>• Constructive feedback and reviews</li>
                </ul>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                Payment & Fees
              </h2>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="border border-gray-300 p-3 text-left">
                        Service
                      </th>
                      <th className="border border-gray-300 p-3 text-left">
                        Fee
                      </th>
                      <th className="border border-gray-300 p-3 text-left">
                        When Charged
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="border border-gray-300 p-3">
                        Buying Products
                      </td>
                      <td className="border border-gray-300 p-3">Free</td>
                      <td className="border border-gray-300 p-3">Never</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 p-3">
                        Selling Products
                      </td>
                      <td className="border border-gray-300 p-3">
                        5-8% Commission
                      </td>
                      <td className="border border-gray-300 p-3">
                        On successful sale
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 p-3">
                        Premium Seller Features
                      </td>
                      <td className="border border-gray-300 p-3">
                        Rs. 500/month
                      </td>
                      <td className="border border-gray-300 p-3">
                        Monthly subscription
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                Dispute Resolution
              </h2>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="bg-teal-100 text-teal-600 rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 flex-shrink-0">
                    1
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">
                      Direct Communication
                    </h3>
                    <p className="text-gray-600 text-sm">
                      Try to resolve issues directly with the other party first.
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-teal-100 text-teal-600 rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 flex-shrink-0">
                    2
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">
                      Platform Mediation
                    </h3>
                    <p className="text-gray-600 text-sm">
                      Contact our support team for assistance and mediation.
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-teal-100 text-teal-600 rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 flex-shrink-0">
                    3
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">
                      Formal Resolution
                    </h3>
                    <p className="text-gray-600 text-sm">
                      Escalate to formal dispute resolution if needed.
                    </p>
                  </div>
                </div>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                Liability & Disclaimers
              </h2>
              <div className="bg-yellow-50 border border-yellow-200 p-6 rounded-lg">
                <p className="text-gray-600 leading-relaxed text-sm">
                  Nepal Marketplace acts as a platform connecting buyers and
                  sellers. We are not responsible for the quality, safety, or
                  legality of items listed, the truth or accuracy of listings,
                  or the ability of sellers to sell items or buyers to pay for
                  items.
                </p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                Related Policies
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Link
                  href="/privacy-policy"
                  className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <h3 className="font-semibold text-gray-800 mb-2">
                    Privacy Policy
                  </h3>
                  <p className="text-gray-600 text-sm">
                    How we handle your personal information
                  </p>
                </Link>
                <Link
                  href="/cookies-policy"
                  className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <h3 className="font-semibold text-gray-800 mb-2">
                    Cookies Policy
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Our use of cookies and tracking
                  </p>
                </Link>
                <Link
                  href="/terms-of-service"
                  className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <h3 className="font-semibold text-gray-800 mb-2">
                    Full Terms of Service
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Complete legal terms and conditions
                  </p>
                </Link>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                Contact Us
              </h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                Questions about these terms? We&apos;re here to help:
              </p>
              <div className="p-4 bg-gray-50 rounded-lg">
                <p className="text-gray-600">
                  <strong>Email:</strong> <EMAIL>
                  <br />
                  <strong>Phone:</strong> +977-1-4567890
                  <br />
                  <strong>Support:</strong>{" "}
                  <Link
                    href="/support"
                    className="text-teal-600 hover:underline"
                  >
                    Visit Support Center
                  </Link>
                </p>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}
