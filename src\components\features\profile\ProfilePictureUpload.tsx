"use client";

import React, { useState, useRef } from "react";
import { SmartImage } from "@/components/common/SmartImage";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/components/ui/toast";
import { useUploadProfilePictureMutation } from "@/store/api/userApi";

interface ProfilePictureUploadProps {
  currentImage?: string;
  onImageChange?: (file: File | null) => void;
  onUploadSuccess?: (imageUrl: string) => void;
  className?: string;
  autoUpload?: boolean; // If true, automatically upload when file is selected
}

export default function ProfilePictureUpload({
  currentImage,
  onImageChange,
  onUploadSuccess,
  className = "",
  autoUpload = true,
}: ProfilePictureUploadProps) {
  const { toast } = useToast();
  const [previewUrl, setPreviewUrl] = useState<string | null>(
    currentImage || null
  );
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [uploadProfilePicture] = useUploadProfilePictureMutation();

  const uploadFile = async (file: File) => {
    try {
      setIsUploading(true);
      setUploadProgress(0);

      const formData = new FormData();
      formData.append("file", file);

      // Simulate upload progress (since we can't track real progress with RTK Query)
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const result = await uploadProfilePicture(formData).unwrap();

      clearInterval(progressInterval);
      setUploadProgress(100);

      // Update preview with the uploaded image URL
      setPreviewUrl(result.profilePictureUrl);

      // Call success callback
      onUploadSuccess?.(result.profilePictureUrl);

      toast({
        type: "success",
        title: "Profile Picture Updated",
        description: "Your profile picture has been uploaded successfully.",
      });
    } catch (error) {
      console.error("Upload failed:", error);
      toast({
        type: "error",
        title: "Upload Failed",
        description: "Failed to upload profile picture. Please try again.",
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleFileSelect = async (file: File | null) => {
    if (file) {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        toast({
          type: "error",
          title: "Invalid File Type",
          description: "Please select an image file (JPG, PNG, GIF, etc.)",
        });
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          type: "error",
          title: "File Too Large",
          description: "File size must be less than 5MB",
        });
        return;
      }

      // Set preview immediately
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);

      // Call the legacy callback if provided
      onImageChange?.(file);

      // Auto-upload if enabled
      if (autoUpload) {
        await uploadFile(file);
      }
    } else {
      setPreviewUrl(currentImage || null);
      onImageChange?.(null);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    handleFileSelect(file);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    const file = e.dataTransfer.files?.[0] || null;
    handleFileSelect(file);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const removeImage = () => {
    setPreviewUrl(null);
    onImageChange?.(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <Label className="text-md font-medium text-gray-700">
        Profile Picture
      </Label>

      <div className="flex flex-col items-center space-y-4">
        {/* Preview Area */}
        <div className="relative">
          {previewUrl ? (
            <div className="relative">
              <SmartImage
                src={previewUrl}
                alt="Profile preview"
                width={128}
                height={128}
                className="w-32 h-32 rounded-full object-cover border-4 border-gray-200"
              />
              <button
                type="button"
                onClick={removeImage}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
              >
                <Icon icon="lucide:x" className="w-4 h-4" />
              </button>
            </div>
          ) : (
            <div className="w-32 h-32 rounded-full bg-gray-100 border-4 border-gray-200 flex items-center justify-center">
              <Icon icon="lucide:camera" className="w-8 h-8 text-gray-400" />
            </div>
          )}
        </div>

        {/* Upload Area */}
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            isDragging
              ? "border-teal-500 bg-teal-50"
              : "border-gray-300 hover:border-gray-400"
          }`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          <Icon
            icon="lucide:upload"
            className="w-8 h-8 text-gray-400 mx-auto mb-2"
          />
          <p className="text-sm text-gray-600 mb-2">
            Drag and drop your image here, or{" "}
            <button
              type="button"
              onClick={openFileDialog}
              className="text-teal-600 hover:text-teal-700 underline"
            >
              browse
            </button>
          </p>
          <p className="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
        </div>

        {/* Upload Progress */}
        {isUploading && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Uploading...</span>
              <span className="text-gray-600">{uploadProgress}%</span>
            </div>
            <Progress value={uploadProgress} className="w-full" />
          </div>
        )}

        {/* Upload Button */}
        <Button
          type="button"
          variant="outline"
          onClick={openFileDialog}
          className="flex items-center gap-2"
        >
          <Icon icon="lucide:upload" className="w-4 h-4" />
          Choose File
        </Button>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileInputChange}
          className="hidden"
        />
      </div>
    </div>
  );
}
