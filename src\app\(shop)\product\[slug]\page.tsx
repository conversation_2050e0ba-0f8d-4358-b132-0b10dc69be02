"use client";

import { usePara<PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Product } from "@/types/ecommerce";
import { mockProducts, getProductBySlug } from "@/constants/mock-data";
import { ProductDetailView, JobDetailView } from "@/components/dynamic";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react";
import { Header } from "@/components";
import { useGetAdvertisementByIdQuery, useGetSimilarAdvertisementsQuery } from "@/store/api/advertisementApi";
import { convertAdvertisementToProduct, convertAdvertisementsToProducts, extractIdFromSlug } from "@/utils/slug-utils";

export default function ProductDetailPage() {
  const params = useParams();
  const router = useRouter();
  const productSlug = params.slug as string;

  // Extract product ID from slug
  const productId = extractIdFromSlug(productSlug);

  // Try to fetch from API first, fallback to mock data
  const {
    data: advertisementData,
    isLoading: apiLoading,
    error: apiError
  } = useGetAdvertisementByIdQuery(productId || '', {
    skip: !productId, // Skip API call if no ID found
  });

  // Fetch similar products if we have a product
  const {
    data: similarAdsData
  } = useGetSimilarAdvertisementsQuery(
    { id: productId || '', limit: 4 },
    { skip: !productId }
  );

  // State for fallback to mock data
  const [mockProduct, setMockProduct] = useState<Product | null>(null);
  const [mockLoading, setMockLoading] = useState(false);

  // Handle fallback to mock data when API fails or no ID found
  useEffect(() => {
    if (!productId || (apiError && !apiLoading)) {
      setMockLoading(true);
      // Try to find in mock data
      const timer = setTimeout(() => {
        const foundProduct = getProductBySlug(productSlug);
        setMockProduct(foundProduct);
        setMockLoading(false);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [productSlug, productId, apiError, apiLoading]);

  // Determine which data to use
  const product = advertisementData
    ? convertAdvertisementToProduct(advertisementData)
    : mockProduct;

  const loading = apiLoading || mockLoading;
  const error = !loading && !product ? "Product not found" : null;

  const handleBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#EFEFEF]">
        <Header />
        <div className="flex items-center justify-center min-h-[calc(100vh-80px)]">
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-[#EFEFEF]">
        <Header />
        <div className="flex flex-col items-center justify-center p-4 min-h-[calc(100vh-80px)]">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {error || "Product not found"}
            </h1>
            <p className="text-gray-600 mb-6">
              The product you&apos;re looking for doesn&apos;t exist or has been
              removed.
            </p>
            <Button onClick={handleBack} className="flex items-center gap-2">
              <Icon icon="lucide:arrow-left" className="w-4 h-4" />
              Go Back
            </Button>

            {/* Debug info in development */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-4 p-4 bg-gray-100 rounded-lg text-left text-sm">
                <p><strong>Debug Info:</strong></p>
                <p>Slug: {productSlug}</p>
                <p>Extracted ID: {productId || 'None'}</p>
                <p>API Loading: {apiLoading ? 'Yes' : 'No'}</p>
                <p>API Error: {apiError ? JSON.stringify(apiError) : 'None'}</p>
                <p>Mock Loading: {mockLoading ? 'Yes' : 'No'}</p>
                <p>Mock Product: {mockProduct ? 'Found' : 'Not found'}</p>
                <p>Advertisement Data: {advertisementData ? 'Found' : 'Not found'}</p>
                <p>API Base URL: {process.env.NEXT_PUBLIC_API_BASE_URL}</p>

                {/* Test the slug extraction */}
                <div className="mt-2 p-2 bg-white rounded border">
                  <p><strong>Slug Test:</strong></p>
                  <p>Input: "iphone-14-pro-550e8400-e29b-41d4-a716-446655440000"</p>
                  <p>Output: {extractIdFromSlug("iphone-14-pro-550e8400-e29b-41d4-a716-446655440000")}</p>
                  <p>Current Slug Test: {extractIdFromSlug(productSlug)}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Get similar products - use API data if available, otherwise fallback to mock
  const similarProducts = similarAdsData
    ? convertAdvertisementsToProducts(similarAdsData)
    : mockProducts
        .filter((p) => p.category === product.category && p.id !== product.id)
        .slice(0, 4);

  // Check if this is a job listing
  const isJobListing = product.category === "jobs";

  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <Header />
      <div className="container mx-auto py-6">
        {isJobListing ? (
          <JobDetailView product={product} onBack={handleBack} />
        ) : (
          <ProductDetailView
            product={product}
            similarProducts={similarProducts}
            onBack={handleBack}
          />
        )}
      </div>
    </div>
  );
}
