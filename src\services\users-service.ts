import { apiClient, apiRequest, API_ENDPOINTS } from "@/lib/api";
import {
  User,
  UpdateProfileRequest,
  UpdatePrivacySettingsRequest,
  UpdateNotificationPreferencesRequest,
  PrivacySettings,
  NotificationPreferences,
} from "@/types/auth";

/**
 * User-related interfaces matching backend DTOs
 */
export interface AssignVendorRoleDto {
  userId: string;
  reason?: string;
}

export interface RemoveVendorRoleDto {
  userId: string;
  reason?: string;
}

export interface VendorRoleResponseDto {
  id: string;
  userId: string;
  role: string;
  isActive: boolean;
  assignedAt: string;
  assignedBy: string;
  reason?: string;
}

export interface VendorStatusResponseDto {
  hasVendorRole: boolean;
  isActive: boolean;
  assignedAt?: string;
  canCreateAds: boolean;
  canManageInventory: boolean;
}

/**
 * Users Service
 * Handles all user profile and management API calls
 */
export class UsersService {
  /**
   * Get current user profile
   */
  static async getCurrentUserProfile(): Promise<User> {
    return await apiRequest<User>(() =>
      apiClient.get(API_ENDPOINTS.USER_PROFILE)
    );
  }

  /**
   * Get user profile by username
   */
  static async getUserProfileByUsername(username: string): Promise<User> {
    return await apiRequest<User>(() =>
      apiClient.get(API_ENDPOINTS.USER_PROFILE_BY_USERNAME(username))
    );
  }

  /**
   * Update user profile
   */
  static async updateProfile(profileData: UpdateProfileRequest): Promise<User> {
    return await apiRequest<User>(() =>
      apiClient.put(API_ENDPOINTS.UPDATE_PROFILE, profileData)
    );
  }

  /**
   * Update privacy settings
   */
  static async updatePrivacySettings(
    settings: UpdatePrivacySettingsRequest
  ): Promise<User> {
    return await apiRequest<User>(() =>
      apiClient.put(API_ENDPOINTS.UPDATE_PRIVACY_SETTINGS, settings)
    );
  }

  /**
   * Update notification preferences
   */
  static async updateNotificationPreferences(
    preferences: UpdateNotificationPreferencesRequest
  ): Promise<User> {
    return await apiRequest<User>(() =>
      apiClient.put(API_ENDPOINTS.UPDATE_NOTIFICATION_PREFERENCES, preferences)
    );
  }

  /**
   * Follow a user
   */
  static async followUser(userId: string): Promise<{ message: string }> {
    return await apiRequest<{ message: string }>(() =>
      apiClient.post(API_ENDPOINTS.FOLLOW_USER(userId))
    );
  }

  /**
   * Unfollow a user
   */
  static async unfollowUser(userId: string): Promise<{ message: string }> {
    return await apiRequest<{ message: string }>(() =>
      apiClient.delete(API_ENDPOINTS.UNFOLLOW_USER(userId))
    );
  }

  /**
   * Get user's followers
   */
  static async getFollowers(): Promise<User[]> {
    return await apiRequest<User[]>(() =>
      apiClient.get(API_ENDPOINTS.GET_FOLLOWERS)
    );
  }

  /**
   * Get users that current user is following
   */
  static async getFollowing(): Promise<User[]> {
    return await apiRequest<User[]>(() =>
      apiClient.get(API_ENDPOINTS.GET_FOLLOWING)
    );
  }

  /**
   * Assign vendor role to user (Admin only)
   */
  static async assignVendorRole(data: AssignVendorRoleDto): Promise<VendorRoleResponseDto> {
    return await apiRequest<VendorRoleResponseDto>(() =>
      apiClient.post("/users/vendor-role/assign", data)
    );
  }

  /**
   * Remove vendor role from user (Admin only)
   */
  static async removeVendorRole(data: RemoveVendorRoleDto): Promise<{ message: string }> {
    return await apiRequest<{ message: string }>(() =>
      apiClient.delete("/users/vendor-role/remove", { data })
    );
  }

  /**
   * Get vendor role status
   */
  static async getVendorStatus(): Promise<VendorStatusResponseDto> {
    return await apiRequest<VendorStatusResponseDto>(() =>
      apiClient.get("/users/vendor-role/status")
    );
  }

  /**
   * Helper method to check if user has vendor role
   */
  static async hasVendorRole(): Promise<boolean> {
    try {
      const status = await this.getVendorStatus();
      return status.hasVendorRole && status.isActive;
    } catch (error) {
      return false;
    }
  }

  /**
   * Helper method to get user's full profile with additional data
   */
  static async getFullUserProfile(): Promise<User & {
    vendorStatus?: VendorStatusResponseDto;
    followersCount: number;
    followingCount: number;
  }> {
    const profile = await this.getCurrentUserProfile();
    
    try {
      const vendorStatus = await this.getVendorStatus();
      return {
        ...profile,
        vendorStatus,
      };
    } catch (error) {
      return profile;
    }
  }

  /**
   * Helper method to validate profile data before update
   */
  static validateProfileData(data: UpdateProfileRequest): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (data.firstName && data.firstName.length > 100) {
      errors.push('First name must not exceed 100 characters');
    }

    if (data.lastName && data.lastName.length > 100) {
      errors.push('Last name must not exceed 100 characters');
    }

    if (data.bio && data.bio.length > 500) {
      errors.push('Bio must not exceed 500 characters');
    }

    if (data.phone && !/^\+?[1-9]\d{1,14}$/.test(data.phone)) {
      errors.push('Please provide a valid phone number');
    }

    if (data.latitude && (data.latitude < -90 || data.latitude > 90)) {
      errors.push('Latitude must be between -90 and 90');
    }

    if (data.longitude && (data.longitude < -180 || data.longitude > 180)) {
      errors.push('Longitude must be between -180 and 180');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Helper method to get default privacy settings
   */
  static getDefaultPrivacySettings(): PrivacySettings {
    return {
      showEmail: false,
      showPhone: false,
      showFullName: true,
      showProfilePicture: true,
      showBio: true,
      showLocation: false,
      showLastSeen: false,
      allowDirectMessages: true,
      searchableByEmail: false,
      searchableByPhone: false,
    };
  }

  /**
   * Helper method to get default notification preferences
   */
  static getDefaultNotificationPreferences(): NotificationPreferences {
    return {
      email: true,
      sms: false,
      push: true,
      marketing: false,
      orderUpdates: true,
      messages: true,
      advertisements: false,
      security: true,
    };
  }

  /**
   * Helper method to format user display name
   */
  static formatUserDisplayName(user: User): string {
    if (user.fullName) {
      return user.fullName;
    }
    
    if (user.firstName && user.lastName) {
      return `${user.firstName} ${user.lastName}`;
    }
    
    if (user.firstName) {
      return user.firstName;
    }
    
    return user.username;
  }

  /**
   * Helper method to check if user profile is complete
   */
  static isProfileComplete(user: User): {
    isComplete: boolean;
    missingFields: string[];
    completionPercentage: number;
  } {
    const requiredFields = [
      'firstName',
      'lastName',
      'email',
      'phone',
      'bio',
      'profilePictureUrl',
      'city',
      'country',
    ];

    const missingFields: string[] = [];
    let filledFields = 0;

    requiredFields.forEach(field => {
      const value = (user as any)[field];
      if (value && value.trim() !== '') {
        filledFields++;
      } else {
        missingFields.push(field);
      }
    });

    const completionPercentage = Math.round((filledFields / requiredFields.length) * 100);

    return {
      isComplete: missingFields.length === 0,
      missingFields,
      completionPercentage,
    };
  }

  /**
   * Helper method to search users (if search endpoint is available)
   */
  static async searchUsers(query: string, limit: number = 10): Promise<User[]> {
    try {
      return await apiRequest<User[]>(() =>
        apiClient.get("/search/users", {
          params: { q: query, limit },
        })
      );
    } catch (error) {
      console.warn('User search not available:', error);
      return [];
    }
  }

  /**
   * Helper method to get user statistics for profile
   */
  static async getUserStatistics(userId?: string): Promise<{
    totalAds: number;
    activeAds: number;
    soldAds: number;
    totalViews: number;
    joinedDate: string;
  }> {
    try {
      // This would need to be implemented based on available endpoints
      // For now, return default values
      return {
        totalAds: 0,
        activeAds: 0,
        soldAds: 0,
        totalViews: 0,
        joinedDate: new Date().toISOString(),
      };
    } catch (error) {
      return {
        totalAds: 0,
        activeAds: 0,
        soldAds: 0,
        totalViews: 0,
        joinedDate: new Date().toISOString(),
      };
    }
  }
}

export default UsersService;
