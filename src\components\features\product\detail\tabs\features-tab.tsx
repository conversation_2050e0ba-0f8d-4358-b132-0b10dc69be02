import type { Product } from "@/types/ecommerce";
import { Icon } from "@iconify/react";

interface FeaturesTabProps {
  product: Product;
}

export function FeaturesTab({}: FeaturesTabProps) {
  const features = [
    { name: "Air Conditioning", available: true },
    { name: "Power Steering", available: true },
    { name: "Central Locking", available: true },
    { name: "Electric Windows", available: true },
    { name: "ABS Brakes", available: true },
    { name: "Airbags", available: true },
    { name: "Bluetooth Connectivity", available: true },
    { name: "Backup Camera", available: true },
    { name: "Sunroof", available: false },
    { name: "Leather Seats", available: false },
    { name: "Navigation System", available: true },
    { name: "Cruise Control", available: false },
  ];

  return (
    <div className="space-y-6 min-h-[478px]">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Features & Equipment
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {features.map((feature, index) => (
          <div
            key={index}
            className="flex items-center gap-3 p-3 rounded-lg border"
          >
            {feature.available ? (
              <Icon
                icon="lucide:check-circle"
                className="h-5 w-5 text-green-500 flex-shrink-0"
              />
            ) : (
              <Icon
                icon="lucide:x"
                className="h-5 w-5 text-gray-400 flex-shrink-0"
              />
            )}
            <span
              className={`${
                feature.available ? "text-gray-900" : "text-gray-500"
              }`}
            >
              {feature.name}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}
