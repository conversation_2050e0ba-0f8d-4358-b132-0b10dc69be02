"use client";
import React from "react";
import { ImageCropModalProvider } from "./ImageCropModal/ImageCropModalProvider";
import { ImageCropModalContent } from "./ImageCropModal/ImageCropModalContent";

interface ImageCropModalProps {
  isOpen: boolean;
  imageSrc: string;
  onCropComplete: (croppedImageUrl: string) => void;
  onCancel: () => void;
}

export const ImageCropModal: React.FC<ImageCropModalProps> = ({
  isOpen,
  imageSrc,
  onCropComplete,
  onCancel,
}) => {
  if (!isOpen) return null;

  return (
    <ImageCropModalProvider
      imageSrc={imageSrc}
      onCropComplete={onCropComplete}
      onCancel={onCancel}
    >
      <ImageCropModalContent />
    </ImageCropModalProvider>
  );
};

// Default export for dynamic imports
export default ImageCropModal;
