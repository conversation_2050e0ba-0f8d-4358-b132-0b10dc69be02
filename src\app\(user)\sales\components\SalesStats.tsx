import { Icon } from "@iconify/react";
import type { StatCardProps, MetricCardProps } from "../types";

const StatCard = ({
  title,
  value,
  change,
  changeType,
  iconName,
  bgColor,
  iconColor,
}: StatCardProps) => (
  <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
    <div className="flex items-center justify-between mb-4">
      <h3 className="text-sm font-medium text-gray-600">{title}</h3>
      <div className={`p-2 rounded-lg ${bgColor}`}>
        <Icon icon={iconName} className={`w-5 h-5 ${iconColor}`} />
      </div>
    </div>
    <div className="space-y-2">
      <p className="text-2xl font-bold text-gray-900">{value}</p>
      <p
        className={`text-sm flex items-center ${
          changeType === "increase" ? "text-green-600" : "text-red-600"
        }`}
      >
        <Icon icon="mdi:trending-up" className="w-4 h-4 mr-1" />
        {change}
      </p>
    </div>
  </div>
);

const MetricCard = ({
  title,
  value,
  subtitle,
  iconName,
  bgColor,
  iconColor,
}: MetricCardProps) => (
  <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
    <div className="flex items-center justify-between mb-4">
      <h3 className="text-sm font-medium text-gray-600">{title}</h3>
      <div className={`p-2 rounded-lg ${bgColor}`}>
        <Icon icon={iconName} className={`w-5 h-5 ${iconColor}`} />
      </div>
    </div>
    <div className="space-y-2">
      <div className="flex items-center space-x-2">
        <p className="text-2xl font-bold text-gray-900">{value}</p>
        {title === "Average Rating" && (
          <div className="flex">
            {[1, 2, 3, 4, 5].map((star) => (
              <Icon
                key={star}
                icon="mdi:star"
                className="w-5 h-5 text-yellow-400"
              />
            ))}
          </div>
        )}
      </div>
      <p className="text-sm text-gray-500">{subtitle}</p>
    </div>
  </div>
);

export const SalesStats = () => {
  const statsData = [
    {
      title: "Total Revenue",
      value: "Rs 485,000",
      change: "+12.5% from last month",
      changeType: "increase" as const,
      iconName: "mdi:currency-usd",
      bgColor: "bg-green-100",
      iconColor: "text-green-600",
    },
    {
      title: "Item Sold",
      value: "24",
      change: "+8 this month",
      changeType: "increase" as const,
      iconName: "mdi:trending-up",
      bgColor: "bg-blue-100",
      iconColor: "text-blue-600",
    },
    {
      title: "Item Bought",
      value: "18",
      change: "+5 this month",
      changeType: "increase" as const,
      iconName: "mdi:shopping",
      bgColor: "bg-purple-100",
      iconColor: "text-purple-600",
    },
    {
      title: "Active Listings",
      value: "12",
      change: "-2 this week",
      changeType: "decrease" as const,
      iconName: "mdi:target",
      bgColor: "bg-red-100",
      iconColor: "text-red-600",
    },
  ];

  const metricsData = [
    {
      title: "Average Rating",
      value: "4",
      subtitle: "Based on 42 reviews",
      iconName: "mdi:star",
      bgColor: "bg-yellow-100",
      iconColor: "text-yellow-600",
    },
    {
      title: "Response Rate",
      value: "95%",
      subtitle: "Average response time: 2 hours",
      iconName: "mdi:message-text",
      bgColor: "bg-teal-100",
      iconColor: "text-teal-600",
    },
    {
      title: "Success Rate",
      value: "92%",
      subtitle: "Completed transactions",
      iconName: "mdi:check-circle",
      bgColor: "bg-blue-100",
      iconColor: "text-blue-600",
    },
  ];

  return (
    <>
      {/* Stats Cards Row 1 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsData.map((stat) => (
          <StatCard key={stat.title} {...stat} />
        ))}
      </div>

      {/* Stats Cards Row 2 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {metricsData.map((metric) => (
          <MetricCard key={metric.title} {...metric} />
        ))}
      </div>
    </>
  );
};
