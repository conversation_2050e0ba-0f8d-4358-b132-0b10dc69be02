"use client";

import React, { useState } from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";

interface SkillsManagerProps {
  label: string;
  skills: string[];
  onSkillsChange: (skills: string[]) => void;
  placeholder?: string;
  badgeClassName?: string;
}

export function SkillsManager({
  label,
  skills,
  onSkillsChange,
  placeholder = "Add skill",
  badgeClassName = "bg-blue-50 text-blue-700 border-blue-200",
}: SkillsManagerProps) {
  const [newSkill, setNewSkill] = useState("");

  const addSkill = () => {
    if (!newSkill.trim()) return;
    
    if (!skills.includes(newSkill.trim())) {
      onSkillsChange([...skills, newSkill.trim()]);
    }
    setNewSkill("");
  };

  const removeSkill = (skillToRemove: string) => {
    onSkillsChange(skills.filter((skill) => skill !== skillToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addSkill();
    }
  };

  return (
    <div className="space-y-2">
      <Label className="text-sm font-medium">{label}</Label>
      <div className="flex flex-wrap gap-2 mb-2">
        {skills.map((skill, index) => (
          <Badge
            key={index}
            variant="outline"
            className={`cursor-pointer hover:opacity-80 ${badgeClassName}`}
            onClick={() => removeSkill(skill)}
          >
            {skill} ×
          </Badge>
        ))}
      </div>
      <div className="flex gap-2">
        <Input
          value={newSkill}
          onChange={(e) => setNewSkill(e.target.value)}
          placeholder={placeholder}
          onKeyDown={handleKeyDown}
        />
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addSkill}
        >
          <Icon icon="lucide:plus" className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
