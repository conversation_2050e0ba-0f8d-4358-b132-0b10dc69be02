"use client";

import React, { ReactNode, useEffect, useState } from "react";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { store, persistor } from "./index";

interface ReduxProviderProps {
  children: ReactNode;
}

// Loading component for PersistGate - matches app layout
const PersistGateLoading = () => (
  <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#478085]"></div>
    </div>
  </div>
);

export function ReduxProvider({ children }: ReduxProviderProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <Provider store={store}>
      {isClient && persistor ? (
        <PersistGate loading={<PersistGateLoading />} persistor={persistor}>
          {children}
        </PersistGate>
      ) : (
        children
      )}
    </Provider>
  );
}
