import React from "react"
import { Icon } from "@iconify/react"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { SectionHeader } from "../components/SectionHeader"
import { type FormData, MAX_TITLE_LENGTH } from "../types"

interface ProductTitleSectionProps {
  formData: FormData
  updateFormData: (updates: Partial<FormData>) => void
}

export const ProductTitleSection: React.FC<ProductTitleSectionProps> = ({
  formData,
  updateFormData,
}) => {
  return (
    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
      <CardHeader className="pb-4">
        <SectionHeader
          icon={<Icon icon="lucide:edit-3" className="w-6 h-6 text-white" />}
          title="Product Title"
          subtitle="Create an eye-catching title for your product"
          required
          step={1}
          totalSteps={6}
        />
      </CardHeader>
      <CardContent>
        <div className="relative">
          <Input
            id="adTitle"
            type="text"
            placeholder="e.g., iPhone 14 Pro Max - Space Black, 256GB, Excellent Condition"
            value={formData.adTitle}
            onChange={(e) => updateFormData({ adTitle: e.target.value })}
            className="h-14 text-lg border-2 border-gray-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 rounded-xl transition-all duration-200 pl-4 pr-20"
            maxLength={MAX_TITLE_LENGTH}
          />
          <div className="absolute right-4 top-4 text-sm text-gray-400 bg-gray-100 px-2 py-1 rounded-md">
            {formData.adTitle.length}/{MAX_TITLE_LENGTH}
          </div>
        </div>
        {formData.adTitle.length > 0 && (
          <div className="mt-3 flex items-center gap-2 text-green-600">
            <Icon icon="lucide:check-circle" className="w-4 h-4" />
            <span className="text-sm">Great! Your title looks good</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
