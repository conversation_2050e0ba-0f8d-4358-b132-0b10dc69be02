"use client";

import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { useUser } from "@/store/compatibility";

interface AuthActionOptions {
  redirectTo?: string;
  showModal?: boolean;
  onSuccess?: () => void;
  onFailure?: () => void;
  requireRole?: string[];
}

interface UseAuthActionReturn {
  requireAuth: (action: () => void, options?: AuthActionOptions) => boolean;
  isAuthenticated: boolean;
  isLoading: boolean;
  user: { id: string; email: string; name?: string } | null;
}

/**
 * useAuthAction Hook
 *
 * Provides programmatic authentication checks for user actions.
 * Returns a function that checks authentication before executing actions.
 *
 * @returns Object with requireAuth function and authentication state
 */
export function useAuthAction(): UseAuthActionReturn {
  const { isAuthenticated, isLoading, currentUser } = useUser();
  const router = useRouter();

  /**
   * Requires authentication before executing an action
   *
   * @param action - The action to execute if authenticated
   * @param options - Configuration options for the auth check
   * @returns boolean - true if action was executed, false if auth required
   */
  const requireAuth = useCallback(
    (action: () => void, options: AuthActionOptions = {}) => {
      const {
        redirectTo = "/login",
        showModal = false,
        onSuccess,
        onFailure,
        requireRole,
      } = options;

      // If still loading, don't execute action
      if (isLoading) {
        return false;
      }

      // Check if user is authenticated
      if (!isAuthenticated) {
        // Call failure callback if provided
        if (onFailure) {
          onFailure();
        }

        // Handle authentication requirement
        if (showModal) {
          // In a real implementation, you might open a login modal here
          // For now, we'll redirect to login page
          router.push(redirectTo);
        } else {
          // Redirect to login page
          router.push(redirectTo);
        }

        return false;
      }

      // Check role-based access if required (future enhancement)
      if (requireRole && currentUser) {
        // This would check user roles against required roles
        // For now, we'll assume all authenticated users have access
        // In a real implementation, you'd check currentUser.roles against requireRole
        // Example implementation:
        // const userRoles = currentUser.roles || [];
        // const hasRequiredRole = requireRole.some(role => userRoles.includes(role));
        // if (!hasRequiredRole) {
        //   if (onFailure) onFailure();
        //   return false;
        // }
      }

      // User is authenticated and has required permissions
      try {
        action();

        // Call success callback if provided
        if (onSuccess) {
          onSuccess();
        }

        return true;
      } catch (error) {
        console.error("Error executing authenticated action:", error);

        // Call failure callback if provided
        if (onFailure) {
          onFailure();
        }

        return false;
      }
    },
    [isAuthenticated, isLoading, currentUser, router]
  );

  return {
    requireAuth,
    isAuthenticated,
    isLoading,
    user: currentUser,
  };
}

/**
 * Convenience hook for comment-related actions
 */
export function useCommentAuth() {
  const authAction = useAuthAction();

  const requireCommentAuth = useCallback(
    (action: () => void) => {
      return authAction.requireAuth(action, {
        redirectTo: "/login?redirect=comment",
        onFailure: () => {
          // Could show a toast notification here
          console.log("Please log in to comment");
        },
      });
    },
    [authAction]
  );

  return {
    ...authAction,
    requireCommentAuth,
  };
}

/**
 * Convenience hook for wishlist-related actions
 */
export function useWishlistAuth() {
  const authAction = useAuthAction();

  const requireWishlistAuth = useCallback(
    (action: () => void) => {
      return authAction.requireAuth(action, {
        redirectTo: "/login?redirect=wishlist",
        onFailure: () => {
          // Could show a toast notification here
          console.log("Please log in to save items to your wishlist");
        },
      });
    },
    [authAction]
  );

  return {
    ...authAction,
    requireWishlistAuth,
  };
}

/**
 * Convenience hook for posting ads
 */
export function usePostAdAuth() {
  const authAction = useAuthAction();

  const requirePostAdAuth = useCallback(
    (action: () => void) => {
      return authAction.requireAuth(action, {
        redirectTo: "/login?redirect=postad",
        onFailure: () => {
          // Could show a toast notification here
          console.log("Please log in to post an ad");
        },
      });
    },
    [authAction]
  );

  return {
    ...authAction,
    requirePostAdAuth,
  };
}

/**
 * Convenience hook for profile-related actions
 */
export function useProfileAuth() {
  const authAction = useAuthAction();

  const requireProfileAuth = useCallback(
    (action: () => void) => {
      return authAction.requireAuth(action, {
        redirectTo: "/login?redirect=profile",
        onFailure: () => {
          // Could show a toast notification here
          console.log("Please log in to access your profile");
        },
      });
    },
    [authAction]
  );

  return {
    ...authAction,
    requireProfileAuth,
  };
}
