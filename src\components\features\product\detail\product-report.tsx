"use client";

import { useState } from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Product } from "@/types/ecommerce";

interface ProductReportProps {
  product: Product;
  className?: string;
}

interface ReportReason {
  id: string;
  label: string;
  description: string;
}

const reportReasons: ReportReason[] = [
  {
    id: "inappropriate_content",
    label: "Inappropriate Content",
    description: "Contains offensive, adult, or inappropriate material",
  },
  {
    id: "misleading_info",
    label: "Misleading Information",
    description: "Product description or images are misleading or false",
  },
  {
    id: "spam",
    label: "Spam or Duplicate",
    description: "This listing appears to be spam or a duplicate",
  },
  {
    id: "prohibited_item",
    label: "Prohibited Item",
    description: "Item is not allowed to be sold on this platform",
  },
  {
    id: "price_issue",
    label: "Price Issue",
    description: "Unrealistic pricing or potential scam",
  },
  {
    id: "seller_issue",
    label: "Seller Behavior",
    description: "Issues with seller conduct or communication",
  },
  {
    id: "copyright",
    label: "Copyright Violation",
    description: "Unauthorized use of copyrighted material",
  },
  {
    id: "other",
    label: "Other",
    description: "Other issues not listed above",
  },
];

export function ProductReport({ product, className = "" }: ProductReportProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedReasons, setSelectedReasons] = useState<string[]>([]);
  const [additionalInfo, setAdditionalInfo] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleReasonToggle = (reasonId: string) => {
    setSelectedReasons((prev) =>
      prev.includes(reasonId)
        ? prev.filter((id) => id !== reasonId)
        : [...prev, reasonId]
    );
  };

  const handleSubmit = async () => {
    if (selectedReasons.length === 0) return;

    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      setIsSubmitted(true);
      setIsSubmitting(false);

      // Reset form after showing success
      setTimeout(() => {
        setIsOpen(false);
        setIsSubmitted(false);
        setSelectedReasons([]);
        setAdditionalInfo("");
      }, 2000);
    }, 1000);
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setIsOpen(false);
      setSelectedReasons([]);
      setAdditionalInfo("");
      setIsSubmitted(false);
    }
  };

  if (isSubmitted) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md">
          <div className="text-center py-6">
            <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <Icon icon="lucide:check" className="w-6 h-6 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Report Submitted
            </h3>
            <p className="text-gray-600">
              Thank you for your report. We&apos;ll review this listing and take
              appropriate action if necessary.
            </p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={`text-gray-500 hover:text-red-600 transition-colors ${className}`}
        >
          <Icon icon="lucide:flag" className="h-4 w-4 mr-1" />
          Report
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-lg max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Icon
              icon="lucide:alert-triangle"
              className="h-5 w-5 text-red-600"
            />
            Report This Listing
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Product Info */}
          <div className="bg-gray-50 rounded-lg p-3">
            <p className="text-sm font-medium text-gray-900 truncate">
              {product.title}
            </p>
            <p className="text-sm text-gray-600">
              Listed by {product.seller.name}
            </p>
          </div>

          {/* Report Reasons */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">
              What&apos;s wrong with this listing? (Select all that apply)
            </h4>
            <div className="space-y-3">
              {reportReasons.map((reason) => (
                <div key={reason.id} className="flex items-start space-x-3">
                  <Checkbox
                    id={reason.id}
                    checked={selectedReasons.includes(reason.id)}
                    onCheckedChange={() => handleReasonToggle(reason.id)}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <label
                      htmlFor={reason.id}
                      className="text-sm font-medium text-gray-900 cursor-pointer"
                    >
                      {reason.label}
                    </label>
                    <p className="text-xs text-gray-600 mt-1">
                      {reason.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Additional Information */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-2">
              Additional Information (Optional)
            </label>
            <Textarea
              value={additionalInfo}
              onChange={(e) => setAdditionalInfo(e.target.value)}
              placeholder="Please provide any additional details that might help us understand the issue..."
              rows={4}
              className="resize-none"
            />
          </div>

          {/* Guidelines */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">
              Reporting Guidelines
            </h4>
            <ul className="text-xs text-blue-800 space-y-1">
              <li>
                • Only report listings that violate our community guidelines
              </li>
              <li>• Provide accurate and honest information</li>
              <li>• False reports may result in account restrictions</li>
              <li>• We&apos;ll review your report within 24-48 hours</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={selectedReasons.length === 0 || isSubmitting}
              className="flex-1 bg-red-600 hover:bg-red-700 text-white"
            >
              {isSubmitting ? "Submitting..." : "Submit Report"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
