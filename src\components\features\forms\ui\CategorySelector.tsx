// Category selector component for PostAdsForm

import type React from "react";
import { Icon } from "@iconify/react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { SectionHeader } from "./SectionHeader";
import { useGetCategoriesQuery } from "@/store/api/categoriesApi";
import type { Category } from "@/types/ecommerce";

interface CategorySelectorProps {
  selectedCategoryId: string;
  selectedSubcategoryId?: string;
  onCategoryChange: (categoryId: string) => void;
  onSubcategoryChange: (subcategoryId?: string) => void;
}

export const CategorySelector: React.FC<CategorySelectorProps> = ({
  selectedCategoryId,
  selectedSubcategoryId,
  onCategoryChange,
  onSubcategoryChange,
}) => {
  // Fetch categories from API
  const { data: categories = [], isLoading, error } = useGetCategoriesQuery();

  // Find selected category and its subcategories
  const selectedCategory = categories.find(
    (cat) => cat.id === selectedCategoryId
  );
  const subcategories = selectedCategory?.subcategories || [];

  const handleCategoryChange = (categoryId: string) => {
    if (process.env.NODE_ENV === "development") {
      console.log("CategorySelector - Category changed:", {
        newCategoryId: categoryId,
        previousSubcategoryId: selectedSubcategoryId,
      });
    }
    onCategoryChange(categoryId);
    // Reset subcategory when category changes
    onSubcategoryChange(undefined);
  };

  const handleSubcategoryChange = (subcategoryId?: string) => {
    if (process.env.NODE_ENV === "development") {
      console.log("CategorySelector - Subcategory changed:", {
        categoryId: selectedCategoryId,
        newSubcategoryId: subcategoryId,
        previousSubcategoryId: selectedSubcategoryId,
      });
    }
    onSubcategoryChange(subcategoryId);
  };

  if (error) {
    return (
      <div className="bg-white rounded-xl border border-gray-100 p-6 shadow-sm">
        <SectionHeader
          icon={<Icon icon="lucide:tag" className="w-5 h-5 text-red-500" />}
          title="Category"
          required
          iconBgColor="bg-red-50"
        />
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm">
            Failed to load categories. Please refresh the page and try again.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl border border-gray-100 p-6 shadow-sm">
      <SectionHeader
        icon={<Icon icon="lucide:tag" className="w-5 h-5 text-[#478085]" />}
        title="Category"
        required
        iconBgColor="bg-[#478085]/10"
      />

      <div className="mt-6 space-y-4">
        {/* Main Category Selection */}
        <div className="space-y-2">
          <Label
            htmlFor="category"
            className="text-sm font-medium text-gray-700"
          >
            Select Category *
          </Label>
          <Select
            value={selectedCategoryId || ""}
            onValueChange={handleCategoryChange}
            disabled={isLoading}
          >
            <SelectTrigger className="w-full h-12 border-2 border-gray-200 focus:border-[#478085] focus:ring-2 focus:ring-[#478085]/20 rounded-lg">
              <div className="flex items-center gap-3">
                {selectedCategory && (
                  <span className="text-lg">{selectedCategory.icon}</span>
                )}
                <SelectValue
                  placeholder={
                    isLoading ? "Loading categories..." : "Choose a category"
                  }
                />
              </div>
            </SelectTrigger>
            <SelectContent className="max-h-60">
              {isLoading ? (
                <SelectItem value="loading" disabled>
                  <div className="flex items-center gap-2">
                    <Icon
                      icon="lucide:loader-2"
                      className="w-4 h-4 animate-spin"
                    />
                    <span>Loading categories...</span>
                  </div>
                </SelectItem>
              ) : (
                categories
                  .filter(
                    (category: Category) =>
                      category.id && category.id.trim() !== ""
                  )
                  .map((category: Category) => (
                    <SelectItem key={category.id} value={category.id}>
                      <div className="flex items-center gap-3">
                        <span className="text-lg">{category.icon}</span>
                        <div className="flex flex-col">
                          <span className="font-medium">{category.name}</span>
                          {category.description && (
                            <span className="text-xs text-gray-500">
                              {category.description}
                            </span>
                          )}
                        </div>
                      </div>
                    </SelectItem>
                  ))
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Subcategory Selection */}
        {selectedCategory && subcategories.length > 0 && (
          <div className="space-y-2">
            <Label
              htmlFor="subcategory"
              className="text-sm font-medium text-gray-700"
            >
              Select Subcategory (Optional)
            </Label>
            <Select
              value={selectedSubcategoryId || "none"}
              onValueChange={(value) =>
                handleSubcategoryChange(value === "none" ? undefined : value)
              }
            >
              <SelectTrigger className="w-full h-12 border-2 border-gray-200 focus:border-[#478085] focus:ring-2 focus:ring-[#478085]/20 rounded-lg">
                <SelectValue placeholder="Choose a subcategory (optional)" />
              </SelectTrigger>
              <SelectContent className="max-h-60">
                <SelectItem value="none">
                  <span className="text-gray-500">No subcategory</span>
                </SelectItem>
                {subcategories
                  .filter(
                    (subcategory) =>
                      subcategory.id && subcategory.id.trim() !== ""
                  )
                  .map((subcategory) => (
                    <SelectItem key={subcategory.id} value={subcategory.id}>
                      <div className="flex flex-col">
                        <span className="font-medium">{subcategory.name}</span>
                        {subcategory.description && (
                          <span className="text-xs text-gray-500">
                            {subcategory.description}
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Category Info */}
        {selectedCategory && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start gap-2">
              <Icon
                icon="lucide:info"
                className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0"
              />
              <div className="text-sm text-blue-800">
                <p className="font-medium">Selected: {selectedCategory.name}</p>
                {selectedCategory.description && (
                  <p className="mt-1 text-blue-700">
                    {selectedCategory.description}
                  </p>
                )}
                {selectedSubcategoryId && (
                  <p className="mt-1 text-blue-700">
                    Subcategory:{" "}
                    {
                      subcategories.find(
                        (sub) => sub.id === selectedSubcategoryId
                      )?.name
                    }
                  </p>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
