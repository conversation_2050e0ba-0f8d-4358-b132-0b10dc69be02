# SastoBazar API Endpoints Reference

## Base URL
- **Development**: `http://localhost:3000/api/v1`
- **Production**: `https://sasto-api.webstudiomatrix.com/api/v1`

## Authentication Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| POST | `/auth/register` | No | Register new user |
| POST | `/auth/login` | No | Login user |
| POST | `/auth/logout` | Yes | Logout user |
| GET | `/auth/profile` | Yes | Get current user profile |
| POST | `/auth/refresh` | No | Refresh access token |
| POST | `/auth/forgot-password` | No | Request password reset |
| POST | `/auth/reset-password` | No | Reset password with token |
| POST | `/auth/change-password` | Yes | Change password |

## Categories Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| GET | `/categories` | No | Get all categories |
| GET | `/categories/:id` | No | Get category by ID |
| GET | `/categories/slug/:slug` | No | Get category by slug |
| GET | `/categories/:categoryId/subcategories` | No | Get subcategories |
| POST | `/categories` | Admin | Create category |
| PATCH | `/categories/:id` | Admin | Update category |
| DELETE | `/categories/:id` | Admin | Delete category |
| POST | `/categories/subcategories` | Admin | Create subcategory |
| PATCH | `/categories/subcategories/:id` | Admin | Update subcategory |
| DELETE | `/categories/subcategories/:id` | Admin | Delete subcategory |

## Advertisements Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| GET | `/advertisements` | No | Get all advertisements |
| POST | `/advertisements` | Yes | Create advertisement |
| GET | `/advertisements/:id` | No | Get advertisement by ID |
| PATCH | `/advertisements/:id` | Owner/Admin | Update advertisement |
| DELETE | `/advertisements/:id` | Owner/Admin | Delete advertisement |
| GET | `/advertisements/my-ads` | Yes | Get user's advertisements |
| GET | `/advertisements/favorites` | Yes | Get user's favorites |
| POST | `/advertisements/:id/favorite` | Yes | Add to favorites |
| DELETE | `/advertisements/:id/favorite` | Yes | Remove from favorites |
| POST | `/advertisements/:id/images` | Owner | Upload images |
| POST | `/advertisements/:id/submit-for-approval` | Owner | Submit for approval |
| POST | `/advertisements/:id/mark-sold` | Owner | Mark as sold |
| GET | `/advertisements/:id/stats` | Owner/Admin | Get statistics |
| POST | `/advertisements/:id/report` | Yes | Report advertisement |
| GET | `/advertisements/featured` | No | Get featured ads |
| GET | `/advertisements/popular` | No | Get popular ads |
| GET | `/advertisements/recent` | No | Get recent ads |
| GET | `/advertisements/:id/similar` | No | Get similar ads |
| GET | `/advertisements/search/location` | No | Location-based search |

## Admin Advertisement Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| POST | `/advertisements/:id/approve` | Admin | Approve advertisement |
| POST | `/advertisements/:id/reject` | Admin | Reject advertisement |
| GET | `/advertisements/admin/pending` | Admin | Get pending ads |
| POST | `/advertisements/:id/suspend` | Admin | Suspend advertisement |
| POST | `/advertisements/:id/reactivate` | Admin | Reactivate advertisement |
| GET | `/advertisements/admin/stats` | Admin | Get admin statistics |
| POST | `/advertisements/admin/bulk-approve` | Admin | Bulk approve ads |
| POST | `/advertisements/admin/bulk-reject` | Admin | Bulk reject ads |

## Image Management Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| DELETE | `/advertisements/images/:imageId` | Owner/Admin | Remove image |
| PATCH | `/advertisements/images/:imageId/set-primary` | Owner | Set primary image |
| PATCH | `/advertisements/:id/images/reorder` | Owner | Reorder images |

## Cart Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| GET | `/cart` | Yes | Get user cart |
| POST | `/cart/items` | Yes | Add item to cart |
| PUT | `/cart/items/:itemId` | Yes | Update cart item |
| DELETE | `/cart/items/:itemId` | Yes | Remove cart item |
| DELETE | `/cart/items/bulk` | Yes | Bulk remove items |
| DELETE | `/cart` | Yes | Clear cart |
| POST | `/cart/sync` | Yes | Sync cart |

## Users Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| GET | `/users/profile` | Yes | Get current user profile |
| GET | `/users/profile/:username` | Yes | Get user profile by username |
| PUT | `/users/profile` | Yes | Update profile |
| PUT | `/users/privacy-settings` | Yes | Update privacy settings |
| PUT | `/users/notification-preferences` | Yes | Update notifications |
| POST | `/users/follow/:userId` | Yes | Follow user |
| DELETE | `/users/follow/:userId` | Yes | Unfollow user |
| GET | `/users/followers` | Yes | Get followers |
| GET | `/users/following` | Yes | Get following |

## Vendor Role Management

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| POST | `/users/vendor-role/assign` | Admin | Assign vendor role |
| DELETE | `/users/vendor-role/remove` | Admin | Remove vendor role |
| GET | `/users/vendor-role/status` | Yes | Get vendor status |

## File Upload Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| POST | `/files/upload` | Yes | Upload files |
| POST | `/files/upload/profile-picture` | Yes | Upload profile picture |
| POST | `/files/upload/advertisement-images` | Yes | Upload ad images |

## Search Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| GET | `/search` | No | Global search |
| GET | `/search/advertisements` | No | Search advertisements |
| GET | `/search/users` | No | Search users |
| GET | `/search/suggestions` | No | Get search suggestions |

## Orders Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| GET | `/orders` | Yes | Get user orders |
| POST | `/orders` | Yes | Create order |
| GET | `/orders/:id` | Yes | Get order by ID |
| PATCH | `/orders/:id/status` | Vendor/Admin | Update order status |

## Payments Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| POST | `/payments/create-intent` | Yes | Create payment intent |
| POST | `/payments/confirm` | Yes | Confirm payment |
| GET | `/payments/history` | Yes | Get payment history |

## Communications Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| GET | `/communications/messages` | Yes | Get messages |
| POST | `/communications/messages` | Yes | Send message |
| GET | `/communications/conversations` | Yes | Get conversations |
| PATCH | `/communications/messages/:id/read` | Yes | Mark as read |

## Subscriptions Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| GET | `/subscriptions/plans` | No | Get subscription plans |
| POST | `/subscriptions/subscribe` | Yes | Subscribe to plan |
| GET | `/subscriptions/current` | Yes | Get current subscription |
| POST | `/subscriptions/cancel` | Yes | Cancel subscription |

## Health Check Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| GET | `/health` | No | Health check |
| GET | `/health/db` | No | Database health |
| GET | `/health/redis` | No | Redis health |

## Email Verification Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| POST | `/email/verify` | Yes | Verify email |
| POST | `/email/resend-verification` | Yes | Resend verification |

## Admin Endpoints

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| GET | `/admin/users` | Admin | Get all users |
| PATCH | `/admin/users/:id/status` | Admin | Update user status |
| GET | `/admin/statistics` | Admin | Get admin statistics |
| GET | `/admin/reports` | Admin | Get reports |

## Query Parameters for Advertisements

### Filtering
- `categoryId` - Filter by category UUID
- `subcategoryId` - Filter by subcategory UUID
- `minPrice` - Minimum price filter
- `maxPrice` - Maximum price filter
- `condition` - Item condition (new, used, refurbished)
- `location` - Location filter
- `city` - City filter
- `state` - State filter
- `status` - Advertisement status
- `isFeatured` - Filter featured ads
- `isUrgent` - Filter urgent ads
- `userId` - Filter by user ID

### Pagination
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)

### Sorting
- `sortBy` - Sort field (createdAt, price, title, views)
- `sortOrder` - Sort direction (ASC, DESC)

### Search
- `search` - Search term for title and description

## Response Status Codes

- `200` - OK
- `201` - Created
- `204` - No Content
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Unprocessable Entity
- `429` - Too Many Requests
- `500` - Internal Server Error

## Authentication Header Format

```
Authorization: Bearer <jwt_token>
```

## Content-Type Headers

- JSON requests: `Content-Type: application/json`
- File uploads: `Content-Type: multipart/form-data`

## Rate Limiting

- Default: 100 requests per 15 minutes per IP
- Auth endpoints: 10 requests per 15 minutes per IP
- File uploads: 20 requests per hour per user
