"use client";

import { useRouter } from "next/navigation";
import { BackButton } from "@/components/ui/back-button";
import { Icon } from "@iconify/react";

export default function CommunityForumPage() {
  const router = useRouter();

  const forumCategories = [
    {
      title: "General Discussion",
      icon: "material-symbols:forum",
      description: "General topics and marketplace discussions",
      posts: 1234,
      lastActivity: "2 hours ago",
    },
    {
      title: "Buying & Selling Tips",
      icon: "material-symbols:tips-and-updates",
      description: "Share tips and best practices for trading",
      posts: 856,
      lastActivity: "1 hour ago",
    },
    {
      title: "Product Reviews",
      icon: "material-symbols:rate-review",
      description: "Share your experience with products and sellers",
      posts: 642,
      lastActivity: "30 minutes ago",
    },
    {
      title: "Technical Support",
      icon: "material-symbols:support-agent",
      description: "Get help with technical issues",
      posts: 423,
      lastActivity: "45 minutes ago",
    },
  ];

  const recentTopics = [
    {
      title: "Best practices for product photography",
      author: "PhotoPro123",
      replies: 23,
      views: 156,
      lastReply: "2 hours ago",
      category: "Tips",
    },
    {
      title: "How to handle difficult buyers?",
      author: "SellerExpert",
      replies: 45,
      views: 289,
      lastReply: "1 hour ago",
      category: "Discussion",
    },
    {
      title: "New payment method suggestions",
      author: "TechUser99",
      replies: 12,
      views: 78,
      lastReply: "3 hours ago",
      category: "Suggestions",
    },
  ];

  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Back Button */}
        <div className="mb-6">
          <BackButton onClick={() => router.back()} size="lg" />
        </div>

        {/* Main Content Card */}
        <div className="bg-white rounded-xl shadow-lg px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Community Forum
            </h1>
            <p className="text-gray-600 text-lg">
              Connect with other users and discuss topics.
            </p>
          </div>

          {/* Welcome Message */}
          <div className="mb-8 p-6 bg-gray-50 rounded-lg">
            <p className="text-gray-700 leading-relaxed mb-4">
              Join our vibrant community forum to connect with other buyers and
              sellers, share tips, ask questions, and discuss various topics
              related to our marketplace.
            </p>
            <button
              onClick={() => router.push("/community/forum")}
              className="bg-[#478085] text-white px-6 py-2 rounded-lg hover:bg-[#356267] transition-colors font-medium"
            >
              Visit The Forum
            </button>
          </div>

          {/* Forum Categories */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Forum Categories
            </h2>
            <div className="space-y-4">
              {forumCategories.map((category, index) => (
                <div
                  key={index}
                  className="border border-gray-200 rounded-lg p-6 hover:bg-gray-50 transition-colors cursor-pointer group"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <Icon
                        icon={category.icon}
                        className="w-8 h-8 text-[#478085] mt-1 group-hover:text-[#478085]"
                      />
                      <div>
                        <h3 className="font-semibold text-gray-900 text-lg group-hover:text-[#478085]">
                          {category.title}
                        </h3>
                        <p className="text-gray-600 mt-1">
                          {category.description}
                        </p>
                      </div>
                    </div>
                    <div className="text-right text-sm text-gray-500">
                      <div>{category.posts} posts</div>
                      <div>Last: {category.lastActivity}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recent Topics */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Recent Topics
            </h2>
            <div className="space-y-4">
              {recentTopics.map((topic, index) => (
                <div
                  key={index}
                  className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 hover:text-blue-700">
                        {topic.title}
                      </h3>
                      <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                        <span>by {topic.author}</span>
                        <span className="bg-gray-200 px-2 py-1 rounded text-xs">
                          {topic.category}
                        </span>
                      </div>
                    </div>
                    <div className="text-right text-sm text-gray-500">
                      <div>{topic.replies} replies</div>
                      <div>{topic.views} views</div>
                      <div>Last: {topic.lastReply}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Community Stats */}
          <div className="border-t border-gray-200 pt-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Community Stats
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-3xl font-bold text-blue-600">2,847</div>
                <div className="text-gray-600">Total Members</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-3xl font-bold text-green-600">3,155</div>
                <div className="text-gray-600">Total Posts</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-3xl font-bold text-purple-600">156</div>
                <div className="text-gray-600">Active Today</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-3xl font-bold text-orange-600">42</div>
                <div className="text-gray-600">New This Week</div>
              </div>
            </div>
          </div>

          {/* Join Community CTA */}
          <div className="border-t border-gray-200 pt-8 mt-8">
            <div className="text-center">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Ready to join the conversation?
              </h3>
              <p className="text-gray-600 mb-6">
                Create an account or log in to start participating in our
                community discussions.
              </p>
              <div className="space-x-4">
                <button
                  onClick={() => router.push("/community/join")}
                  className="bg-[#478085] text-white px-6 py-3 rounded-lg hover:bg-[#356267] transition-colors font-medium"
                >
                  Join Community
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
