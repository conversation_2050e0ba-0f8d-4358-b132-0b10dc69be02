"use client";

import React, { useRef, useState } from "react";
import Image from "next/image";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";

// Mock UserProfile type for demonstration
interface UserProfile {
  username: string;
  email: string;
  profilePicture?: string;
  isVerified: boolean;
  address: {
    city: string;
    country: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface ProfileHeaderProps {
  userProfile: UserProfile;
  onProfilePictureChange?: (file: File) => void;
}

export default function EnhancedProfileHeader({
  userProfile,
  onProfilePictureChange,
}: ProfileHeaderProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const coverInputRef = useRef<HTMLInputElement>(null);
  const [isHoveringProfile, setIsHoveringProfile] = useState(false);
  const [isHoveringCover, setIsHoveringCover] = useState(false);
  const [profileCompletion] = useState(85);

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
    });
  };

  const formatLastLogin = (date: string) => {
    const now = new Date();
    const targetDate = new Date(date);
    const diffInHours = Math.floor(
      (now.getTime() - targetDate.getTime()) / (1000 * 60 * 60)
    );
    if (diffInHours < 1) return "Just now";
    else if (diffInHours < 24) return `${diffInHours}h ago`;
    else return `${Math.floor(diffInHours / 24)}d ago`;
  };

  const handleEditClick = () => fileInputRef.current?.click();
  const handleCoverEditClick = () => coverInputRef.current?.click();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && onProfilePictureChange) onProfilePictureChange(file);
  };

  const stats = [
    {
      label: "Rating",
      value: "4.8",
      icon: "lucide:star",
      color: "text-yellow-500",
      bgColor: "bg-yellow-50",
    },
    {
      label: "Reviews",
      value: "127",
      icon: "lucide:message-circle",
      color: "text-blue-500",
      bgColor: "bg-blue-50",
    },
    {
      label: "Response Rate",
      value: "89%",
      icon: "lucide:trending-up",
      color: "text-green-500",
      bgColor: "bg-green-50",
    },
    {
      label: "Followers",
      value: "1.2K",
      icon: "lucide:users",
      color: "text-purple-500",
      bgColor: "bg-purple-50",
    },
  ];

  return (
    <div className="relative mb-6">
      {/* Cover Photo */}
      <div
        className="relative h-64 bg-gradient-to-br from-blue-600 via-purple-600 to-teal-500 rounded-2xl overflow-hidden shadow-xl"
        onMouseEnter={() => setIsHoveringCover(true)}
        onMouseLeave={() => setIsHoveringCover(false)}
      >
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute bottom-10 right-10 w-24 h-24 bg-white/10 rounded-full blur-xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 w-40 h-40 bg-white/5 rounded-full blur-2xl animate-pulse delay-500"></div>
        </div>

        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>

        {/* Edit Cover Button */}
        <div
          className={`absolute top-6 right-6 transition-all duration-300 ${
            isHoveringCover
              ? "opacity-100 translate-y-0"
              : "opacity-0 translate-y-2"
          }`}
        >
          <Button
            onClick={handleCoverEditClick}
            size="sm"
            className="bg-white/20 backdrop-blur-md border-white/30 text-white hover:bg-white/30 transition-all duration-200"
          >
            <Icon icon="lucide:camera" className="w-4 h-4 mr-2" />
            Edit Cover
          </Button>
        </div>

        {/* Profile Completion */}
        <div className="absolute top-6 left-6">
          <div className="bg-white/20 backdrop-blur-md rounded-full px-3 py-1.5 border border-white/30">
            <div className="flex items-center gap-2 text-white text-sm">
              <div className="relative w-4 h-4">
                <div className="absolute inset-0 rounded-full border-2 border-white/30"></div>
                <div
                  className="absolute inset-0 rounded-full border-2 border-white border-r-transparent transition-all duration-1000"
                  style={{
                    transform: `rotate(${(profileCompletion / 100) * 360}deg)`,
                    clipPath: "polygon(50% 0%, 100% 0%, 100% 100%, 50% 100%)",
                  }}
                ></div>
              </div>
              <span className="font-medium">{profileCompletion}% Complete</span>
            </div>
          </div>
        </div>

        <input
          ref={coverInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="hidden"
        />
      </div>

      {/* Profile Card */}
      <Card className="relative -mt-20 mx-6 shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
        <CardContent className="p-8">
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-8">
            {/* Left */}
            <div className="flex flex-col sm:flex-row items-center sm:items-start gap-6">
              <div
                className="relative group"
                onMouseEnter={() => setIsHoveringProfile(true)}
                onMouseLeave={() => setIsHoveringProfile(false)}
              >
                <div className="relative w-36 h-36 rounded-2xl overflow-hidden shadow-xl ring-4 ring-white">
                  {userProfile.profilePicture ? (
                    <Image
                      src={userProfile.profilePicture || "/placeholder.svg"}
                      alt={userProfile.username}
                      width={144}
                      height={144}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                      <span className="text-white text-4xl font-bold">
                        {userProfile.username.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}

                  <div
                    className={`absolute inset-0 bg-black/40 flex items-center justify-center transition-opacity duration-300 ${
                      isHoveringProfile ? "opacity-100" : "opacity-0"
                    }`}
                  >
                    <Button
                      onClick={handleEditClick}
                      size="sm"
                      className="bg-white/20 backdrop-blur-sm border-white/30 text-white hover:bg-white/30"
                    >
                      <Icon icon="lucide:upload" className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                <div className="absolute -bottom-1 -right-1 w-8 h-8 bg-green-500 rounded-full border-4 border-white shadow-lg flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                </div>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="hidden"
                />
              </div>

              <div className="text-center sm:text-left flex-1">
                <div className="flex flex-col sm:flex-row sm:items-center gap-3 mb-3">
                  <h1 className="text-3xl font-bold text-gray-900 bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text">
                    {userProfile.username}
                  </h1>
                  <div className="flex items-center gap-2 justify-center sm:justify-start">
                    {userProfile.isVerified && (
                      <Badge className="bg-blue-100 text-blue-700 border-blue-200 hover:bg-blue-200 transition-colors">
                        <Icon icon="lucide:shield" className="w-3 h-3 mr-1" />
                        Verified
                      </Badge>
                    )}
                    <Badge className="bg-green-100 text-green-700 border-green-200">
                      <Icon icon="lucide:award" className="w-3 h-3 mr-1" />
                      Pro Seller
                    </Badge>
                  </div>
                </div>

                <p className="text-gray-600 text-lg mb-4 font-medium">
                  {userProfile.email}
                </p>

                <div className="flex flex-col sm:flex-row gap-4 text-sm text-gray-500 mb-4">
                  <div className="flex items-center gap-2">
                    <Icon
                      icon="lucide:map-pin"
                      className="w-4 h-4 text-gray-400"
                    />
                    <span>
                      {userProfile.address.city}, {userProfile.address.country}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Icon
                      icon="lucide:calendar"
                      className="w-4 h-4 text-gray-400"
                    />
                    <span>Joined {formatDate(userProfile.createdAt)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Icon
                      icon="lucide:clock"
                      className="w-4 h-4 text-green-400"
                    />
                    <span className="text-green-600 font-medium">
                      Active {formatLastLogin(userProfile.updatedAt)}
                    </span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-3 justify-center sm:justify-start">
                  <Button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-200">
                    <Icon icon="lucide:edit-3" className="w-4 h-4 mr-2" />
                    Edit Profile
                  </Button>
                  <Button
                    variant="outline"
                    className="border-gray-300 hover:border-gray-400 hover:bg-gray-50"
                  >
                    <Icon icon="lucide:share-2" className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    className="border-gray-300 hover:border-gray-400 hover:bg-gray-50"
                  >
                    <Icon icon="lucide:more-horizontal" className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Right Section */}
            <div className="lg:min-w-[300px]">
              <div className="grid grid-cols-2 gap-4">
                {stats.map((stat) => (
                  <div
                    key={stat.label}
                    className={`${stat.bgColor} rounded-xl p-4 border border-gray-100 hover:shadow-md transition-all duration-200 hover:scale-105`}
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className={`w-10 h-10 ${stat.bgColor} rounded-lg flex items-center justify-center`}
                      >
                        <Icon
                          icon={stat.icon}
                          className={`w-5 h-5 ${stat.color}`}
                        />
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-gray-900">
                          {stat.value}
                        </div>
                        <div className="text-sm text-gray-600">
                          {stat.label}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl border border-yellow-200">
                <div className="flex items-center gap-2 mb-2">
                  <Icon
                    icon="lucide:award"
                    className="w-4 h-4 text-yellow-600"
                  />
                  <span className="text-sm font-medium text-yellow-800">
                    Recent Achievement
                  </span>
                </div>
                <p className="text-sm text-yellow-700">
                  🎉 Reached 100+ successful transactions!
                </p>
              </div>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="mt-8 pt-6 border-t border-gray-100">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <div className="absolute inset-0 w-3 h-3 bg-green-400 rounded-full animate-ping opacity-75"></div>
                  </div>
                  <span className="text-sm text-gray-600 font-medium">
                    Online now
                  </span>
                </div>
                <div className="h-4 w-px bg-gray-300"></div>
                <div className="text-sm text-gray-500">
                  Typically responds within 2 hours
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="text-sm text-gray-500">Profile strength:</div>
                <div className="flex items-center gap-2">
                  <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-green-400 to-green-500 rounded-full transition-all duration-1000"
                      style={{ width: `${profileCompletion}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-green-600">
                    {profileCompletion}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
